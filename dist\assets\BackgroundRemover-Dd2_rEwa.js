import{_ as Rt,d as vr,r as J,a4 as gr,a2 as _r,c as E,a as u,t as k,f as B,S as yr,R as nt,a3 as Ot,e as ne,v as br,T as it,g as ot,o as Z,W as xr}from"./index-CkZTMFXG.js";import{u as wr}from"./useToast-virEbLJw.js";var kr=Object.create,Ut=Object.defineProperty,Tr=Object.getOwnPropertyDescriptor,zt=Object.getOwnPropertyNames,jr=Object.getPrototypeOf,Cr=Object.prototype.hasOwnProperty,xt=(t,e)=>function(){return e||(0,t[zt(t)[0]])((e={exports:{}}).exports,e),e.exports},Rr=(t,e,r,a)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of zt(e))!Cr.call(t,s)&&s!==r&&Ut(t,s,{get:()=>e[s],enumerable:!(a=Tr(e,s))||a.enumerable});return t},tt=(t,e,r)=>(r=t!=null?kr(jr(t)):{},Rr(!t||!t.__esModule?Ut(r,"default",{value:t,enumerable:!0}):r,t)),Or=xt({"../../node_modules/.pnpm/iota-array@1.0.0/node_modules/iota-array/iota.js"(t,e){function r(a){for(var s=new Array(a),n=0;n<a;++n)s[n]=n;return s}e.exports=r}}),Sr=xt({"../../node_modules/.pnpm/is-buffer@1.1.6/node_modules/is-buffer/index.js"(t,e){e.exports=function(s){return s!=null&&(r(s)||a(s)||!!s._isBuffer)};function r(s){return!!s.constructor&&typeof s.constructor.isBuffer=="function"&&s.constructor.isBuffer(s)}function a(s){return typeof s.readFloatLE=="function"&&typeof s.slice=="function"&&r(s.slice(0,0))}}}),rt=xt({"../../node_modules/.pnpm/ndarray@1.0.19/node_modules/ndarray/ndarray.js"(t,e){var r=Or(),a=Sr(),s=typeof Float64Array<"u";function n(y,_){return y[0]-_[0]}function i(){var y=this.stride,_=new Array(y.length),v;for(v=0;v<_.length;++v)_[v]=[Math.abs(y[v]),v];_.sort(n);var S=new Array(_.length);for(v=0;v<S.length;++v)S[v]=_[v][1];return S}function o(y,_){var v=["View",_,"d",y].join("");_<0&&(v="View_Nil"+y);var S=y==="generic";if(_===-1){var w="function "+v+"(a){this.data=a;};var proto="+v+".prototype;proto.dtype='"+y+"';proto.index=function(){return -1};proto.size=0;proto.dimension=-1;proto.shape=proto.stride=proto.order=[];proto.lo=proto.hi=proto.transpose=proto.step=function(){return new "+v+"(this.data);};proto.get=proto.set=function(){};proto.pick=function(){return null};return function construct_"+v+"(a){return new "+v+"(a);}",te=new Function(w);return te()}else if(_===0){var w="function "+v+"(a,d) {this.data = a;this.offset = d};var proto="+v+".prototype;proto.dtype='"+y+"';proto.index=function(){return this.offset};proto.dimension=0;proto.size=1;proto.shape=proto.stride=proto.order=[];proto.lo=proto.hi=proto.transpose=proto.step=function "+v+"_copy() {return new "+v+"(this.data,this.offset)};proto.pick=function "+v+"_pick(){return TrivialArray(this.data);};proto.valueOf=proto.get=function "+v+"_get(){return "+(S?"this.data.get(this.offset)":"this.data[this.offset]")+"};proto.set=function "+v+"_set(v){return "+(S?"this.data.set(this.offset,v)":"this.data[this.offset]=v")+"};return function construct_"+v+"(a,b,c,d){return new "+v+"(a,d)}",te=new Function("TrivialArray",w);return te(c[y][0])}var w=["'use strict'"],b=r(_),N=b.map(function(d){return"i"+d}),z="this.offset+"+b.map(function(d){return"this.stride["+d+"]*i"+d}).join("+"),D=b.map(function(d){return"b"+d}).join(","),M=b.map(function(d){return"c"+d}).join(",");w.push("function "+v+"(a,"+D+","+M+",d){this.data=a","this.shape=["+D+"]","this.stride=["+M+"]","this.offset=d|0}","var proto="+v+".prototype","proto.dtype='"+y+"'","proto.dimension="+_),w.push("Object.defineProperty(proto,'size',{get:function "+v+"_size(){return "+b.map(function(d){return"this.shape["+d+"]"}).join("*"),"}})"),_===1?w.push("proto.order=[0]"):(w.push("Object.defineProperty(proto,'order',{get:"),_<4?(w.push("function "+v+"_order(){"),_===2?w.push("return (Math.abs(this.stride[0])>Math.abs(this.stride[1]))?[1,0]:[0,1]}})"):_===3&&w.push("var s0=Math.abs(this.stride[0]),s1=Math.abs(this.stride[1]),s2=Math.abs(this.stride[2]);if(s0>s1){if(s1>s2){return [2,1,0];}else if(s0>s2){return [1,2,0];}else{return [1,0,2];}}else if(s0>s2){return [2,0,1];}else if(s2>s1){return [0,1,2];}else{return [0,2,1];}}})")):w.push("ORDER})")),w.push("proto.set=function "+v+"_set("+N.join(",")+",v){"),S?w.push("return this.data.set("+z+",v)}"):w.push("return this.data["+z+"]=v}"),w.push("proto.get=function "+v+"_get("+N.join(",")+"){"),S?w.push("return this.data.get("+z+")}"):w.push("return this.data["+z+"]}"),w.push("proto.index=function "+v+"_index(",N.join(),"){return "+z+"}"),w.push("proto.hi=function "+v+"_hi("+N.join(",")+"){return new "+v+"(this.data,"+b.map(function(d){return["(typeof i",d,"!=='number'||i",d,"<0)?this.shape[",d,"]:i",d,"|0"].join("")}).join(",")+","+b.map(function(d){return"this.stride["+d+"]"}).join(",")+",this.offset)}");var be=b.map(function(d){return"a"+d+"=this.shape["+d+"]"}),xe=b.map(function(d){return"c"+d+"=this.stride["+d+"]"});w.push("proto.lo=function "+v+"_lo("+N.join(",")+"){var b=this.offset,d=0,"+be.join(",")+","+xe.join(","));for(var O=0;O<_;++O)w.push("if(typeof i"+O+"==='number'&&i"+O+">=0){d=i"+O+"|0;b+=c"+O+"*d;a"+O+"-=d}");w.push("return new "+v+"(this.data,"+b.map(function(d){return"a"+d}).join(",")+","+b.map(function(d){return"c"+d}).join(",")+",b)}"),w.push("proto.step=function "+v+"_step("+N.join(",")+"){var "+b.map(function(d){return"a"+d+"=this.shape["+d+"]"}).join(",")+","+b.map(function(d){return"b"+d+"=this.stride["+d+"]"}).join(",")+",c=this.offset,d=0,ceil=Math.ceil");for(var O=0;O<_;++O)w.push("if(typeof i"+O+"==='number'){d=i"+O+"|0;if(d<0){c+=b"+O+"*(a"+O+"-1);a"+O+"=ceil(-a"+O+"/d)}else{a"+O+"=ceil(a"+O+"/d)}b"+O+"*=d}");w.push("return new "+v+"(this.data,"+b.map(function(d){return"a"+d}).join(",")+","+b.map(function(d){return"b"+d}).join(",")+",c)}");for(var ue=new Array(_),ce=new Array(_),O=0;O<_;++O)ue[O]="a[i"+O+"]",ce[O]="b[i"+O+"]";w.push("proto.transpose=function "+v+"_transpose("+N+"){"+N.map(function(d,m){return d+"=("+d+"===undefined?"+m+":"+d+"|0)"}).join(";"),"var a=this.shape,b=this.stride;return new "+v+"(this.data,"+ue.join(",")+","+ce.join(",")+",this.offset)}"),w.push("proto.pick=function "+v+"_pick("+N+"){var a=[],b=[],c=this.offset");for(var O=0;O<_;++O)w.push("if(typeof i"+O+"==='number'&&i"+O+">=0){c=(c+this.stride["+O+"]*i"+O+")|0}else{a.push(this.shape["+O+"]);b.push(this.stride["+O+"])}");w.push("var ctor=CTOR_LIST[a.length+1];return ctor(this.data,a,b,c)}"),w.push("return function construct_"+v+"(data,shape,stride,offset){return new "+v+"(data,"+b.map(function(d){return"shape["+d+"]"}).join(",")+","+b.map(function(d){return"stride["+d+"]"}).join(",")+",offset)}");var te=new Function("CTOR_LIST","ORDER",w.join(`
`));return te(c[y],i)}function l(y){if(a(y))return"buffer";if(s)switch(Object.prototype.toString.call(y)){case"[object Float64Array]":return"float64";case"[object Float32Array]":return"float32";case"[object Int8Array]":return"int8";case"[object Int16Array]":return"int16";case"[object Int32Array]":return"int32";case"[object Uint8Array]":return"uint8";case"[object Uint16Array]":return"uint16";case"[object Uint32Array]":return"uint32";case"[object Uint8ClampedArray]":return"uint8_clamped";case"[object BigInt64Array]":return"bigint64";case"[object BigUint64Array]":return"biguint64"}return Array.isArray(y)?"array":"generic"}var c={float32:[],float64:[],int8:[],int16:[],int32:[],uint8:[],uint16:[],uint32:[],array:[],uint8_clamped:[],bigint64:[],biguint64:[],buffer:[],generic:[]};function h(y,_,v,S){if(y===void 0){var M=c.array[0];return M([])}else typeof y=="number"&&(y=[y]);_===void 0&&(_=[y.length]);var w=_.length;if(v===void 0){v=new Array(w);for(var b=w-1,N=1;b>=0;--b)v[b]=N,N*=_[b]}if(S===void 0){S=0;for(var b=0;b<w;++b)v[b]<0&&(S-=(_[b]-1)*v[b])}for(var z=l(y),D=c[z];D.length<=w+1;)D.push(o(z,D.length-1));var M=D[w+1];return M(y,_,v,S)}e.exports=h}}),Ar=typeof global=="object"&&global&&global.Object===Object&&global,Ir=Ar,Er=typeof self=="object"&&self&&self.Object===Object&&self,Zr=Ir||Er||Function("return this")(),wt=Zr,$r=wt.Symbol,qe=$r,Dt=Object.prototype,Nr=Dt.hasOwnProperty,Pr=Dt.toString,we=qe?qe.toStringTag:void 0;function Mr(t){var e=Nr.call(t,we),r=t[we];try{t[we]=void 0;var a=!0}catch{}var s=Pr.call(t);return a&&(e?t[we]=r:delete t[we]),s}var Ur=Mr,zr=Object.prototype,Dr=zr.toString;function Lr(t){return Dr.call(t)}var Fr=Lr,Vr="[object Null]",Br="[object Undefined]",St=qe?qe.toStringTag:void 0;function Wr(t){return t==null?t===void 0?Br:Vr:St&&St in Object(t)?Ur(t):Fr(t)}var qr=Wr;function Gr(t){var e=typeof t;return t!=null&&(e=="object"||e=="function")}var Lt=Gr,Hr="[object AsyncFunction]",Jr="[object Function]",Yr="[object GeneratorFunction]",Kr="[object Proxy]";function Xr(t){if(!Lt(t))return!1;var e=qr(t);return e==Jr||e==Yr||e==Hr||e==Kr}var Qr=Xr,ea=wt["__core-js_shared__"],dt=ea,At=(function(){var t=/[^.]+$/.exec(dt&&dt.keys&&dt.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""})();function ta(t){return!!At&&At in t}var ra=ta,aa=Function.prototype,sa=aa.toString;function na(t){if(t!=null){try{return sa.call(t)}catch{}try{return t+""}catch{}}return""}var ia=na,oa=/[\\^$.*+?()[\]{}|]/g,da=/^\[object .+?Constructor\]$/,ua=Function.prototype,ca=Object.prototype,la=ua.toString,fa=ca.hasOwnProperty,ha=RegExp("^"+la.call(fa).replace(oa,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function pa(t){if(!Lt(t)||ra(t))return!1;var e=Qr(t)?ha:da;return e.test(ia(t))}var ma=pa;function va(t,e){return t?.[e]}var ga=va;function _a(t,e){var r=ga(t,e);return ma(r)?r:void 0}var Ft=_a,ya=Ft(Object,"create"),Re=ya;function ba(){this.__data__=Re?Re(null):{},this.size=0}var xa=ba;function wa(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}var ka=wa,Ta="__lodash_hash_undefined__",ja=Object.prototype,Ca=ja.hasOwnProperty;function Ra(t){var e=this.__data__;if(Re){var r=e[t];return r===Ta?void 0:r}return Ca.call(e,t)?e[t]:void 0}var Oa=Ra,Sa=Object.prototype,Aa=Sa.hasOwnProperty;function Ia(t){var e=this.__data__;return Re?e[t]!==void 0:Aa.call(e,t)}var Ea=Ia,Za="__lodash_hash_undefined__";function $a(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=Re&&e===void 0?Za:e,this}var Na=$a;function ge(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}ge.prototype.clear=xa;ge.prototype.delete=ka;ge.prototype.get=Oa;ge.prototype.has=Ea;ge.prototype.set=Na;var It=ge;function Pa(){this.__data__=[],this.size=0}var Ma=Pa;function Ua(t,e){return t===e||t!==t&&e!==e}var za=Ua;function Da(t,e){for(var r=t.length;r--;)if(za(t[r][0],e))return r;return-1}var at=Da,La=Array.prototype,Fa=La.splice;function Va(t){var e=this.__data__,r=at(e,t);if(r<0)return!1;var a=e.length-1;return r==a?e.pop():Fa.call(e,r,1),--this.size,!0}var Ba=Va;function Wa(t){var e=this.__data__,r=at(e,t);return r<0?void 0:e[r][1]}var qa=Wa;function Ga(t){return at(this.__data__,t)>-1}var Ha=Ga;function Ja(t,e){var r=this.__data__,a=at(r,t);return a<0?(++this.size,r.push([t,e])):r[a][1]=e,this}var Ya=Ja;function _e(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}_e.prototype.clear=Ma;_e.prototype.delete=Ba;_e.prototype.get=qa;_e.prototype.has=Ha;_e.prototype.set=Ya;var Ka=_e,Xa=Ft(wt,"Map"),Qa=Xa;function es(){this.size=0,this.__data__={hash:new It,map:new(Qa||Ka),string:new It}}var ts=es;function rs(t){var e=typeof t;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?t!=="__proto__":t===null}var as=rs;function ss(t,e){var r=t.__data__;return as(e)?r[typeof e=="string"?"string":"hash"]:r.map}var st=ss;function ns(t){var e=st(this,t).delete(t);return this.size-=e?1:0,e}var is=ns;function os(t){return st(this,t).get(t)}var ds=os;function us(t){return st(this,t).has(t)}var cs=us;function ls(t,e){var r=st(this,t),a=r.size;return r.set(t,e),this.size+=r.size==a?0:1,this}var fs=ls;function ye(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}ye.prototype.clear=ts;ye.prototype.delete=is;ye.prototype.get=ds;ye.prototype.has=cs;ye.prototype.set=fs;var Vt=ye,hs="Expected a function";function kt(t,e){if(typeof t!="function"||e!=null&&typeof e!="function")throw new TypeError(hs);var r=function(){var a=arguments,s=e?e.apply(this,a):a[0],n=r.cache;if(n.has(s))return n.get(s);var i=t.apply(this,a);return r.cache=n.set(s,i)||n,i};return r.cache=new(kt.Cache||Vt),r}kt.Cache=Vt;var ps=kt,Tt=tt(rt()),Bt=class lt{constructor(e,r){this.type="application/octet-stream",this.params={},this.type=e,this.params=r}toString(){const e=[];for(const r in this.params){const a=this.params[r];e.push(`${r}=${a}`)}return[this.type,...e].join(";")}static create(e,r){return new lt(e,r)}isIdentical(e){return this.type===e.type&&this.params===e.params}isEqual(e){return this.type===e.type}static fromString(e){const[r,...a]=e.split(";"),s={};for(const n of a){const[i,o]=n.split("=");s[i.trim()]=o.trim()}return new lt(r,s)}},ut=tt(rt());async function ms(t){const e=Bt.fromString(t.type);switch(e.type){case"image/x-alpha8":{const r=parseInt(e.params.width),a=parseInt(e.params.height);return(0,ut.default)(new Uint8Array(await t.arrayBuffer()),[a,r,1])}case"image/x-rgba8":{const r=parseInt(e.params.width),a=parseInt(e.params.height);return(0,ut.default)(new Uint8Array(await t.arrayBuffer()),[a,r,4])}case"application/octet-stream":case"image/png":case"image/jpeg":case"image/jpg":case"image/webp":{const r=await createImageBitmap(t),a=ys(r);return(0,ut.default)(new Uint8Array(a.data),[a.height,a.width,4])}default:throw new Error(`Invalid format: ${e.type} with params: ${e.params}`)}}async function vs(t,e=.8,r="image/png"){const[a,s,n]=t.shape;switch(r){case"image/x-alpha8":case"image/x-rgba8":{const l=Bt.create(r,{width:s.toString(),height:a.toString()});return new Blob([t.data],{type:l.toString()})}case"image/png":case"image/jpeg":case"image/webp":{const l=new ImageData(new Uint8ClampedArray(t.data),s,a);var i=Wt(l.width,l.height),o=i.getContext("2d");return o.putImageData(l,0,0),i.convertToBlob({quality:e,type:r})}default:throw new Error(`Invalid format: ${r}`)}}function gs(t){return new RegExp("^(?:[a-z+]+:)?//","i").test(t)}function _s(t,e){return gs(t)?t:new URL(t,e).href}function ys(t){var e=Wt(t.width,t.height),r=e.getContext("2d");return r.drawImage(t,0,0),r.getImageData(0,0,e.width,e.height)}function bs(t){if(typeof Uint8Array<"u")return new Uint8Array(t);if(typeof Uint8ClampedArray<"u")return new Uint8ClampedArray(t);if(typeof Uint16Array<"u")return new Uint16Array(t);if(typeof Uint32Array<"u")return new Uint32Array(t);if(typeof Float32Array<"u")return new Float32Array(t);if(typeof Float64Array<"u")return new Float64Array(t);throw new Error("TypedArray not supported")}function Et(t,e,r,a=!1){const[s,n,i]=t.shape;let o=n/e,l=s/r;a&&(o=l=Math.max(o,l)>1?Math.max(o,l):Math.min(o,l));const c=(0,Tt.default)(bs(i*e*r),[r,e,i]);for(let h=0;h<r;h++)for(let y=0;y<e;y++){const _=y*o,v=h*l,S=Math.max(Math.floor(_),0),w=Math.min(Math.ceil(_),n-1),b=Math.max(Math.floor(v),0),N=Math.min(Math.ceil(v),s-1),z=_-S,D=v-b;for(let M=0;M<i;M++){const be=t.get(b,S,M),xe=t.get(b,w,M),O=t.get(N,S,M),ue=t.get(N,w,M),ce=(1-z)*(1-D)*be+z*(1-D)*xe+(1-z)*D*O+z*D*ue;c.set(h,y,M,ce)}}return c}function xs(t,e=[128,128,128],r=[256,256,256]){var a=t.data;const[s,n,i]=t.shape,o=s*n,l=new Float32Array(3*o);for(let c=0,h=0;c<a.length;c+=4,h+=1)l[h]=(a[c]-e[0])/r[0],l[h+o]=(a[c+1]-e[1])/r[1],l[h+o+o]=(a[c+2]-e[2])/r[2];return(0,Tt.default)(l,[1,3,s,n])}async function ws(t,e){return typeof t=="string"&&(t=_s(t,e.publicPath),t=new URL(t)),t instanceof URL&&(t=await(await fetch(t,{})).blob()),(t instanceof ArrayBuffer||ArrayBuffer.isView(t))&&(t=new Blob([t])),t instanceof Blob&&(t=await ms(t)),t}function ks(t){const e=new Uint8Array(t.data.length);for(let r=0;r<t.data.length;r++)e[r]=t.data[r]*255;return(0,Tt.default)(e,t.shape)}function Wt(t,e){let r;if(typeof OffscreenCanvas<"u"?r=new OffscreenCanvas(t,e):r=document.createElement("canvas"),!r)throw new Error("Canvas nor OffscreenCanvas are available in the current context.");return r}var Ts=tt(rt()),qt=async()=>navigator.gpu===void 0?!1:await navigator.gpu.requestAdapter()!==null,js=()=>navigator.hardwareConcurrency??4;async function Zt(t,e){return URL.createObjectURL(await Gt(t,e))}async function Gt(t,e){const r=new URL("resources.json",e.publicPath),a=await fetch(r);if(!a.ok)throw new Error("Resource metadata not found. Ensure that the config.publicPath is configured correctly.");const n=(await a.json())[t];if(!n)throw new Error(`Resource ${t} not found. Ensure that the config.publicPath is configured correctly.`);const i=n.chunks;let o=0;const l=i.map(async y=>{const _=y.offsets[1]-y.offsets[0],v=e.publicPath?new URL(y.name,e.publicPath).toString():y.name,w=await(await fetch(v,e.fetchArgs)).blob();if(_!==w.size)throw new Error(`Failed to fetch ${t} with size ${_} but got ${w.size}`);return e.progress&&(o+=_,e.progress(`fetch:${t}`,o,n.size)),w}),c=await Promise.all(l),h=new Blob(c,{type:n.mime});if(h.size!==n.size)throw new Error(`Failed to fetch ${t} with size ${n.size} but got ${h.size}`);return h}var ke=null,Ht=async t=>(ke!==null||(t?ke=(await Rt(async()=>{const{default:e}=await import("./ort.webgpu.bundle.min-CVzBb6MU.js");return{default:e}},[])).default:ke=(await Rt(async()=>{const{default:e}=await import("./ort.bundle.min-B5M2MB43.js");return{default:e}},[])).default),ke);async function Cs(t,e){const r=e.device==="gpu"&&await qt(),a=r&&e.proxyToWorker,s=[r?"webgpu":"wasm"],n=await Ht(r);e.debug&&(console.debug("	Using WebGPU:",r),console.debug("	Proxy to Worker:",a),n.env.debug=!0,n.env.logLevel="verbose"),n.env.wasm.numThreads=js(),n.env.wasm.proxy=a;const i=r?"/onnxruntime-web/ort-wasm-simd-threaded.jsep":"/onnxruntime-web/ort-wasm-simd-threaded",o=await Zt(`${i}.wasm`,e),l=await Zt(`${i}.mjs`,e);n.env.wasm.wasmPaths={mjs:l,wasm:o},e.debug&&console.debug("ort.env.wasm:",n.env.wasm);const c={executionProviders:s,graphOptimizationLevel:"all",executionMode:"parallel",enableCpuMemArena:!0};return await n.InferenceSession.create(t,c).catch(y=>{throw new Error(`Failed to create session: "${y}". Please check if the publicPath is set correctly.`)})}async function Rs(t,e,r,a){const s=a.device==="gpu"&&await qt(),n=await Ht(s),i={};for(const[c,h]of e)i[c]=new n.Tensor("float32",new Float32Array(h.data),h.shape);const o=await t.run(i,{}),l=[];for(const c of r){const h=o[c],y=h.dims,_=h.data,v=(0,Ts.default)(_,y);l.push(v)}return l}var A;(function(t){t.assertEqual=s=>s;function e(s){}t.assertIs=e;function r(s){throw new Error}t.assertNever=r,t.arrayToEnum=s=>{const n={};for(const i of s)n[i]=i;return n},t.getValidEnumValues=s=>{const n=t.objectKeys(s).filter(o=>typeof s[s[o]]!="number"),i={};for(const o of n)i[o]=s[o];return t.objectValues(i)},t.objectValues=s=>t.objectKeys(s).map(function(n){return s[n]}),t.objectKeys=typeof Object.keys=="function"?s=>Object.keys(s):s=>{const n=[];for(const i in s)Object.prototype.hasOwnProperty.call(s,i)&&n.push(i);return n},t.find=(s,n)=>{for(const i of s)if(n(i))return i},t.isInteger=typeof Number.isInteger=="function"?s=>Number.isInteger(s):s=>typeof s=="number"&&isFinite(s)&&Math.floor(s)===s;function a(s,n=" | "){return s.map(i=>typeof i=="string"?`'${i}'`:i).join(n)}t.joinValues=a,t.jsonStringifyReplacer=(s,n)=>typeof n=="bigint"?n.toString():n})(A||(A={}));var ft;(function(t){t.mergeShapes=(e,r)=>({...e,...r})})(ft||(ft={}));var g=A.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Q=t=>{switch(typeof t){case"undefined":return g.undefined;case"string":return g.string;case"number":return isNaN(t)?g.nan:g.number;case"boolean":return g.boolean;case"function":return g.function;case"bigint":return g.bigint;case"symbol":return g.symbol;case"object":return Array.isArray(t)?g.array:t===null?g.null:t.then&&typeof t.then=="function"&&t.catch&&typeof t.catch=="function"?g.promise:typeof Map<"u"&&t instanceof Map?g.map:typeof Set<"u"&&t instanceof Set?g.set:typeof Date<"u"&&t instanceof Date?g.date:g.object;default:return g.unknown}},f=A.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),Os=t=>JSON.stringify(t,null,2).replace(/"([^"]+)":/g,"$1:"),G=class Jt extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=a=>{this.issues=[...this.issues,a]},this.addIssues=(a=[])=>{this.issues=[...this.issues,...a]};const r=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,r):this.__proto__=r,this.name="ZodError",this.issues=e}format(e){const r=e||function(n){return n.message},a={_errors:[]},s=n=>{for(const i of n.issues)if(i.code==="invalid_union")i.unionErrors.map(s);else if(i.code==="invalid_return_type")s(i.returnTypeError);else if(i.code==="invalid_arguments")s(i.argumentsError);else if(i.path.length===0)a._errors.push(r(i));else{let o=a,l=0;for(;l<i.path.length;){const c=i.path[l];l===i.path.length-1?(o[c]=o[c]||{_errors:[]},o[c]._errors.push(r(i))):o[c]=o[c]||{_errors:[]},o=o[c],l++}}};return s(this),a}static assert(e){if(!(e instanceof Jt))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,A.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=r=>r.message){const r={},a=[];for(const s of this.issues)s.path.length>0?(r[s.path[0]]=r[s.path[0]]||[],r[s.path[0]].push(e(s))):a.push(e(s));return{formErrors:a,fieldErrors:r}}get formErrors(){return this.flatten()}};G.create=t=>new G(t);var he=(t,e)=>{let r;switch(t.code){case f.invalid_type:t.received===g.undefined?r="Required":r=`Expected ${t.expected}, received ${t.received}`;break;case f.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(t.expected,A.jsonStringifyReplacer)}`;break;case f.unrecognized_keys:r=`Unrecognized key(s) in object: ${A.joinValues(t.keys,", ")}`;break;case f.invalid_union:r="Invalid input";break;case f.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${A.joinValues(t.options)}`;break;case f.invalid_enum_value:r=`Invalid enum value. Expected ${A.joinValues(t.options)}, received '${t.received}'`;break;case f.invalid_arguments:r="Invalid function arguments";break;case f.invalid_return_type:r="Invalid function return type";break;case f.invalid_date:r="Invalid date";break;case f.invalid_string:typeof t.validation=="object"?"includes"in t.validation?(r=`Invalid input: must include "${t.validation.includes}"`,typeof t.validation.position=="number"&&(r=`${r} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?r=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?r=`Invalid input: must end with "${t.validation.endsWith}"`:A.assertNever(t.validation):t.validation!=="regex"?r=`Invalid ${t.validation}`:r="Invalid";break;case f.too_small:t.type==="array"?r=`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:t.type==="string"?r=`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:t.type==="number"?r=`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:t.type==="date"?r=`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:r="Invalid input";break;case f.too_big:t.type==="array"?r=`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:t.type==="string"?r=`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:t.type==="number"?r=`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="bigint"?r=`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="date"?r=`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:r="Invalid input";break;case f.custom:r="Invalid input";break;case f.invalid_intersection_types:r="Intersection results could not be merged";break;case f.not_multiple_of:r=`Number must be a multiple of ${t.multipleOf}`;break;case f.not_finite:r="Number must be finite";break;default:r=e.defaultError,A.assertNever(t)}return{message:r}},Yt=he;function Ss(t){Yt=t}function Ge(){return Yt}var He=t=>{const{data:e,path:r,errorMaps:a,issueData:s}=t,n=[...r,...s.path||[]],i={...s,path:n};if(s.message!==void 0)return{...s,path:n,message:s.message};let o="";const l=a.filter(c=>!!c).slice().reverse();for(const c of l)o=c(i,{data:e,defaultError:o}).message;return{...s,path:n,message:o}},As=[];function p(t,e){const r=Ge(),a=He({issueData:e,data:t.data,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,r,r===he?void 0:he].filter(s=>!!s)});t.common.issues.push(a)}var F=class Kt{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,r){const a=[];for(const s of r){if(s.status==="aborted")return j;s.status==="dirty"&&e.dirty(),a.push(s.value)}return{status:e.value,value:a}}static async mergeObjectAsync(e,r){const a=[];for(const s of r){const n=await s.key,i=await s.value;a.push({key:n,value:i})}return Kt.mergeObjectSync(e,a)}static mergeObjectSync(e,r){const a={};for(const s of r){const{key:n,value:i}=s;if(n.status==="aborted"||i.status==="aborted")return j;n.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),n.value!=="__proto__"&&(typeof i.value<"u"||s.alwaysSet)&&(a[n.value]=i.value)}return{status:e.value,value:a}}},j=Object.freeze({status:"aborted"}),fe=t=>({status:"dirty",value:t}),L=t=>({status:"valid",value:t}),ht=t=>t.status==="aborted",pt=t=>t.status==="dirty",oe=t=>t.status==="valid",Oe=t=>typeof Promise<"u"&&t instanceof Promise;function Je(t,e,r,a){if(typeof e=="function"?t!==e||!0:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(t)}function Xt(t,e,r,a,s){if(typeof e=="function"?t!==e||!0:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(t,r),r}var x;(function(t){t.errToObj=e=>typeof e=="string"?{message:e}:e||{},t.toString=e=>typeof e=="string"?e:e?.message})(x||(x={}));var Te,je,K=class{constructor(t,e,r,a){this._cachedPath=[],this.parent=t,this.data=e,this._path=r,this._key=a}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}},$t=(t,e)=>{if(oe(e))return{success:!0,data:e.value};if(!t.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const r=new G(t.common.issues);return this._error=r,this._error}}};function C(t){if(!t)return{};const{errorMap:e,invalid_type_error:r,required_error:a,description:s}=t;if(e&&(r||a))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:s}:{errorMap:(i,o)=>{var l,c;const{message:h}=t;return i.code==="invalid_enum_value"?{message:h??o.defaultError}:typeof o.data>"u"?{message:(l=h??a)!==null&&l!==void 0?l:o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:(c=h??r)!==null&&c!==void 0?c:o.defaultError}},description:s}}var R=class{get description(){return this._def.description}_getType(t){return Q(t.data)}_getOrReturnCtx(t,e){return e||{common:t.parent.common,data:t.data,parsedType:Q(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new F,ctx:{common:t.parent.common,data:t.data,parsedType:Q(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const e=this._parse(t);if(Oe(e))throw new Error("Synchronous parse encountered promise.");return e}_parseAsync(t){const e=this._parse(t);return Promise.resolve(e)}parse(t,e){const r=this.safeParse(t,e);if(r.success)return r.data;throw r.error}safeParse(t,e){var r;const a={common:{issues:[],async:(r=e?.async)!==null&&r!==void 0?r:!1,contextualErrorMap:e?.errorMap},path:e?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Q(t)},s=this._parseSync({data:t,path:a.path,parent:a});return $t(a,s)}"~validate"(t){var e,r;const a={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Q(t)};if(!this["~standard"].async)try{const s=this._parseSync({data:t,path:[],parent:a});return oe(s)?{value:s.value}:{issues:a.common.issues}}catch(s){!((r=(e=s?.message)===null||e===void 0?void 0:e.toLowerCase())===null||r===void 0)&&r.includes("encountered")&&(this["~standard"].async=!0),a.common={issues:[],async:!0}}return this._parseAsync({data:t,path:[],parent:a}).then(s=>oe(s)?{value:s.value}:{issues:a.common.issues})}async parseAsync(t,e){const r=await this.safeParseAsync(t,e);if(r.success)return r.data;throw r.error}async safeParseAsync(t,e){const r={common:{issues:[],contextualErrorMap:e?.errorMap,async:!0},path:e?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Q(t)},a=this._parse({data:t,path:r.path,parent:r}),s=await(Oe(a)?a:Promise.resolve(a));return $t(r,s)}refine(t,e){const r=a=>typeof e=="string"||typeof e>"u"?{message:e}:typeof e=="function"?e(a):e;return this._refinement((a,s)=>{const n=t(a),i=()=>s.addIssue({code:f.custom,...r(a)});return typeof Promise<"u"&&n instanceof Promise?n.then(o=>o?!0:(i(),!1)):n?!0:(i(),!1)})}refinement(t,e){return this._refinement((r,a)=>t(r)?!0:(a.addIssue(typeof e=="function"?e(r,a):e),!1))}_refinement(t){return new H({schema:this,typeName:T.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return Y.create(this,this._def)}nullable(){return ae.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return de.create(this)}promise(){return ve.create(this,this._def)}or(t){return Ne.create([this,t],this._def)}and(t){return Pe.create(this,t,this._def)}transform(t){return new H({...C(this._def),schema:this,typeName:T.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const e=typeof t=="function"?t:()=>t;return new De({...C(this._def),innerType:this,defaultValue:e,typeName:T.ZodDefault})}brand(){return new jt({typeName:T.ZodBranded,type:this,...C(this._def)})}catch(t){const e=typeof t=="function"?t:()=>t;return new Le({...C(this._def),innerType:this,catchValue:e,typeName:T.ZodCatch})}describe(t){const e=this.constructor;return new e({...this._def,description:t})}pipe(t){return Ct.create(this,t)}readonly(){return Fe.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}},Is=/^c[^\s-]{8,}$/i,Es=/^[0-9a-z]+$/,Zs=/^[0-9A-HJKMNP-TV-Z]{26}$/i,$s=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Ns=/^[a-z0-9_-]{21}$/i,Ps=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Ms=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Us=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,zs="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$",ct,Ds=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Ls=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Fs=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Vs=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Bs=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Ws=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Qt="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",qs=new RegExp(`^${Qt}$`);function er(t){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return t.precision?e=`${e}\\.\\d{${t.precision}}`:t.precision==null&&(e=`${e}(\\.\\d+)?`),e}function Gs(t){return new RegExp(`^${er(t)}$`)}function tr(t){let e=`${Qt}T${er(t)}`;const r=[];return r.push(t.local?"Z?":"Z"),t.offset&&r.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${r.join("|")})`,new RegExp(`^${e}$`)}function Hs(t,e){return!!((e==="v4"||!e)&&Ds.test(t)||(e==="v6"||!e)&&Fs.test(t))}function Js(t,e){if(!Ps.test(t))return!1;try{const[r]=t.split("."),a=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),s=JSON.parse(atob(a));return!(typeof s!="object"||s===null||!s.typ||!s.alg||e&&s.alg!==e)}catch{return!1}}function Ys(t,e){return!!((e==="v4"||!e)&&Ls.test(t)||(e==="v6"||!e)&&Vs.test(t))}var pe=class Ce extends R{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==g.string){const n=this._getOrReturnCtx(e);return p(n,{code:f.invalid_type,expected:g.string,received:n.parsedType}),j}const a=new F;let s;for(const n of this._def.checks)if(n.kind==="min")e.data.length<n.value&&(s=this._getOrReturnCtx(e,s),p(s,{code:f.too_small,minimum:n.value,type:"string",inclusive:!0,exact:!1,message:n.message}),a.dirty());else if(n.kind==="max")e.data.length>n.value&&(s=this._getOrReturnCtx(e,s),p(s,{code:f.too_big,maximum:n.value,type:"string",inclusive:!0,exact:!1,message:n.message}),a.dirty());else if(n.kind==="length"){const i=e.data.length>n.value,o=e.data.length<n.value;(i||o)&&(s=this._getOrReturnCtx(e,s),i?p(s,{code:f.too_big,maximum:n.value,type:"string",inclusive:!0,exact:!0,message:n.message}):o&&p(s,{code:f.too_small,minimum:n.value,type:"string",inclusive:!0,exact:!0,message:n.message}),a.dirty())}else if(n.kind==="email")Us.test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"email",code:f.invalid_string,message:n.message}),a.dirty());else if(n.kind==="emoji")ct||(ct=new RegExp(zs,"u")),ct.test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"emoji",code:f.invalid_string,message:n.message}),a.dirty());else if(n.kind==="uuid")$s.test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"uuid",code:f.invalid_string,message:n.message}),a.dirty());else if(n.kind==="nanoid")Ns.test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"nanoid",code:f.invalid_string,message:n.message}),a.dirty());else if(n.kind==="cuid")Is.test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"cuid",code:f.invalid_string,message:n.message}),a.dirty());else if(n.kind==="cuid2")Es.test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"cuid2",code:f.invalid_string,message:n.message}),a.dirty());else if(n.kind==="ulid")Zs.test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"ulid",code:f.invalid_string,message:n.message}),a.dirty());else if(n.kind==="url")try{new URL(e.data)}catch{s=this._getOrReturnCtx(e,s),p(s,{validation:"url",code:f.invalid_string,message:n.message}),a.dirty()}else n.kind==="regex"?(n.regex.lastIndex=0,n.regex.test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"regex",code:f.invalid_string,message:n.message}),a.dirty())):n.kind==="trim"?e.data=e.data.trim():n.kind==="includes"?e.data.includes(n.value,n.position)||(s=this._getOrReturnCtx(e,s),p(s,{code:f.invalid_string,validation:{includes:n.value,position:n.position},message:n.message}),a.dirty()):n.kind==="toLowerCase"?e.data=e.data.toLowerCase():n.kind==="toUpperCase"?e.data=e.data.toUpperCase():n.kind==="startsWith"?e.data.startsWith(n.value)||(s=this._getOrReturnCtx(e,s),p(s,{code:f.invalid_string,validation:{startsWith:n.value},message:n.message}),a.dirty()):n.kind==="endsWith"?e.data.endsWith(n.value)||(s=this._getOrReturnCtx(e,s),p(s,{code:f.invalid_string,validation:{endsWith:n.value},message:n.message}),a.dirty()):n.kind==="datetime"?tr(n).test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{code:f.invalid_string,validation:"datetime",message:n.message}),a.dirty()):n.kind==="date"?qs.test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{code:f.invalid_string,validation:"date",message:n.message}),a.dirty()):n.kind==="time"?Gs(n).test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{code:f.invalid_string,validation:"time",message:n.message}),a.dirty()):n.kind==="duration"?Ms.test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"duration",code:f.invalid_string,message:n.message}),a.dirty()):n.kind==="ip"?Hs(e.data,n.version)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"ip",code:f.invalid_string,message:n.message}),a.dirty()):n.kind==="jwt"?Js(e.data,n.alg)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"jwt",code:f.invalid_string,message:n.message}),a.dirty()):n.kind==="cidr"?Ys(e.data,n.version)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"cidr",code:f.invalid_string,message:n.message}),a.dirty()):n.kind==="base64"?Bs.test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"base64",code:f.invalid_string,message:n.message}),a.dirty()):n.kind==="base64url"?Ws.test(e.data)||(s=this._getOrReturnCtx(e,s),p(s,{validation:"base64url",code:f.invalid_string,message:n.message}),a.dirty()):A.assertNever(n);return{status:a.value,value:e.data}}_regex(e,r,a){return this.refinement(s=>e.test(s),{validation:r,code:f.invalid_string,...x.errToObj(a)})}_addCheck(e){return new Ce({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...x.errToObj(e)})}url(e){return this._addCheck({kind:"url",...x.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...x.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...x.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...x.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...x.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...x.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...x.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...x.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...x.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...x.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...x.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...x.errToObj(e)})}datetime(e){var r,a;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof e?.precision>"u"?null:e?.precision,offset:(r=e?.offset)!==null&&r!==void 0?r:!1,local:(a=e?.local)!==null&&a!==void 0?a:!1,...x.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof e?.precision>"u"?null:e?.precision,...x.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...x.errToObj(e)})}regex(e,r){return this._addCheck({kind:"regex",regex:e,...x.errToObj(r)})}includes(e,r){return this._addCheck({kind:"includes",value:e,position:r?.position,...x.errToObj(r?.message)})}startsWith(e,r){return this._addCheck({kind:"startsWith",value:e,...x.errToObj(r)})}endsWith(e,r){return this._addCheck({kind:"endsWith",value:e,...x.errToObj(r)})}min(e,r){return this._addCheck({kind:"min",value:e,...x.errToObj(r)})}max(e,r){return this._addCheck({kind:"max",value:e,...x.errToObj(r)})}length(e,r){return this._addCheck({kind:"length",value:e,...x.errToObj(r)})}nonempty(e){return this.min(1,x.errToObj(e))}trim(){return new Ce({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Ce({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Ce({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxLength(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}};pe.create=t=>{var e;return new pe({checks:[],typeName:T.ZodString,coerce:(e=t?.coerce)!==null&&e!==void 0?e:!1,...C(t)})};function Ks(t,e){const r=(t.toString().split(".")[1]||"").length,a=(e.toString().split(".")[1]||"").length,s=r>a?r:a,n=parseInt(t.toFixed(s).replace(".","")),i=parseInt(e.toFixed(s).replace(".",""));return n%i/Math.pow(10,s)}var Se=class mt extends R{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==g.number){const n=this._getOrReturnCtx(e);return p(n,{code:f.invalid_type,expected:g.number,received:n.parsedType}),j}let a;const s=new F;for(const n of this._def.checks)n.kind==="int"?A.isInteger(e.data)||(a=this._getOrReturnCtx(e,a),p(a,{code:f.invalid_type,expected:"integer",received:"float",message:n.message}),s.dirty()):n.kind==="min"?(n.inclusive?e.data<n.value:e.data<=n.value)&&(a=this._getOrReturnCtx(e,a),p(a,{code:f.too_small,minimum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),s.dirty()):n.kind==="max"?(n.inclusive?e.data>n.value:e.data>=n.value)&&(a=this._getOrReturnCtx(e,a),p(a,{code:f.too_big,maximum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),s.dirty()):n.kind==="multipleOf"?Ks(e.data,n.value)!==0&&(a=this._getOrReturnCtx(e,a),p(a,{code:f.not_multiple_of,multipleOf:n.value,message:n.message}),s.dirty()):n.kind==="finite"?Number.isFinite(e.data)||(a=this._getOrReturnCtx(e,a),p(a,{code:f.not_finite,message:n.message}),s.dirty()):A.assertNever(n);return{status:s.value,value:e.data}}gte(e,r){return this.setLimit("min",e,!0,x.toString(r))}gt(e,r){return this.setLimit("min",e,!1,x.toString(r))}lte(e,r){return this.setLimit("max",e,!0,x.toString(r))}lt(e,r){return this.setLimit("max",e,!1,x.toString(r))}setLimit(e,r,a,s){return new mt({...this._def,checks:[...this._def.checks,{kind:e,value:r,inclusive:a,message:x.toString(s)}]})}_addCheck(e){return new mt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:x.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:x.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:x.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:x.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:x.toString(e)})}multipleOf(e,r){return this._addCheck({kind:"multipleOf",value:e,message:x.toString(r)})}finite(e){return this._addCheck({kind:"finite",message:x.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:x.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:x.toString(e)})}get minValue(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxValue(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&A.isInteger(e.value))}get isFinite(){let e=null,r=null;for(const a of this._def.checks){if(a.kind==="finite"||a.kind==="int"||a.kind==="multipleOf")return!0;a.kind==="min"?(r===null||a.value>r)&&(r=a.value):a.kind==="max"&&(e===null||a.value<e)&&(e=a.value)}return Number.isFinite(r)&&Number.isFinite(e)}};Se.create=t=>new Se({checks:[],typeName:T.ZodNumber,coerce:t?.coerce||!1,...C(t)});var Ae=class vt extends R{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==g.bigint)return this._getInvalidInput(e);let a;const s=new F;for(const n of this._def.checks)n.kind==="min"?(n.inclusive?e.data<n.value:e.data<=n.value)&&(a=this._getOrReturnCtx(e,a),p(a,{code:f.too_small,type:"bigint",minimum:n.value,inclusive:n.inclusive,message:n.message}),s.dirty()):n.kind==="max"?(n.inclusive?e.data>n.value:e.data>=n.value)&&(a=this._getOrReturnCtx(e,a),p(a,{code:f.too_big,type:"bigint",maximum:n.value,inclusive:n.inclusive,message:n.message}),s.dirty()):n.kind==="multipleOf"?e.data%n.value!==BigInt(0)&&(a=this._getOrReturnCtx(e,a),p(a,{code:f.not_multiple_of,multipleOf:n.value,message:n.message}),s.dirty()):A.assertNever(n);return{status:s.value,value:e.data}}_getInvalidInput(e){const r=this._getOrReturnCtx(e);return p(r,{code:f.invalid_type,expected:g.bigint,received:r.parsedType}),j}gte(e,r){return this.setLimit("min",e,!0,x.toString(r))}gt(e,r){return this.setLimit("min",e,!1,x.toString(r))}lte(e,r){return this.setLimit("max",e,!0,x.toString(r))}lt(e,r){return this.setLimit("max",e,!1,x.toString(r))}setLimit(e,r,a,s){return new vt({...this._def,checks:[...this._def.checks,{kind:e,value:r,inclusive:a,message:x.toString(s)}]})}_addCheck(e){return new vt({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:x.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:x.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:x.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:x.toString(e)})}multipleOf(e,r){return this._addCheck({kind:"multipleOf",value:e,message:x.toString(r)})}get minValue(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxValue(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}};Ae.create=t=>{var e;return new Ae({checks:[],typeName:T.ZodBigInt,coerce:(e=t?.coerce)!==null&&e!==void 0?e:!1,...C(t)})};var Ie=class extends R{_parse(t){if(this._def.coerce&&(t.data=!!t.data),this._getType(t)!==g.boolean){const r=this._getOrReturnCtx(t);return p(r,{code:f.invalid_type,expected:g.boolean,received:r.parsedType}),j}return L(t.data)}};Ie.create=t=>new Ie({typeName:T.ZodBoolean,coerce:t?.coerce||!1,...C(t)});var Ee=class rr extends R{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==g.date){const n=this._getOrReturnCtx(e);return p(n,{code:f.invalid_type,expected:g.date,received:n.parsedType}),j}if(isNaN(e.data.getTime())){const n=this._getOrReturnCtx(e);return p(n,{code:f.invalid_date}),j}const a=new F;let s;for(const n of this._def.checks)n.kind==="min"?e.data.getTime()<n.value&&(s=this._getOrReturnCtx(e,s),p(s,{code:f.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:"date"}),a.dirty()):n.kind==="max"?e.data.getTime()>n.value&&(s=this._getOrReturnCtx(e,s),p(s,{code:f.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:"date"}),a.dirty()):A.assertNever(n);return{status:a.value,value:new Date(e.data.getTime())}}_addCheck(e){return new rr({...this._def,checks:[...this._def.checks,e]})}min(e,r){return this._addCheck({kind:"min",value:e.getTime(),message:x.toString(r)})}max(e,r){return this._addCheck({kind:"max",value:e.getTime(),message:x.toString(r)})}get minDate(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e!=null?new Date(e):null}};Ee.create=t=>new Ee({checks:[],coerce:t?.coerce||!1,typeName:T.ZodDate,...C(t)});var Ye=class extends R{_parse(t){if(this._getType(t)!==g.symbol){const r=this._getOrReturnCtx(t);return p(r,{code:f.invalid_type,expected:g.symbol,received:r.parsedType}),j}return L(t.data)}};Ye.create=t=>new Ye({typeName:T.ZodSymbol,...C(t)});var Ze=class extends R{_parse(t){if(this._getType(t)!==g.undefined){const r=this._getOrReturnCtx(t);return p(r,{code:f.invalid_type,expected:g.undefined,received:r.parsedType}),j}return L(t.data)}};Ze.create=t=>new Ze({typeName:T.ZodUndefined,...C(t)});var $e=class extends R{_parse(t){if(this._getType(t)!==g.null){const r=this._getOrReturnCtx(t);return p(r,{code:f.invalid_type,expected:g.null,received:r.parsedType}),j}return L(t.data)}};$e.create=t=>new $e({typeName:T.ZodNull,...C(t)});var me=class extends R{constructor(){super(...arguments),this._any=!0}_parse(t){return L(t.data)}};me.create=t=>new me({typeName:T.ZodAny,...C(t)});var ie=class extends R{constructor(){super(...arguments),this._unknown=!0}_parse(t){return L(t.data)}};ie.create=t=>new ie({typeName:T.ZodUnknown,...C(t)});var ee=class extends R{_parse(t){const e=this._getOrReturnCtx(t);return p(e,{code:f.invalid_type,expected:g.never,received:e.parsedType}),j}};ee.create=t=>new ee({typeName:T.ZodNever,...C(t)});var Ke=class extends R{_parse(t){if(this._getType(t)!==g.undefined){const r=this._getOrReturnCtx(t);return p(r,{code:f.invalid_type,expected:g.void,received:r.parsedType}),j}return L(t.data)}};Ke.create=t=>new Ke({typeName:T.ZodVoid,...C(t)});var de=class Be extends R{_parse(e){const{ctx:r,status:a}=this._processInputParams(e),s=this._def;if(r.parsedType!==g.array)return p(r,{code:f.invalid_type,expected:g.array,received:r.parsedType}),j;if(s.exactLength!==null){const i=r.data.length>s.exactLength.value,o=r.data.length<s.exactLength.value;(i||o)&&(p(r,{code:i?f.too_big:f.too_small,minimum:o?s.exactLength.value:void 0,maximum:i?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),a.dirty())}if(s.minLength!==null&&r.data.length<s.minLength.value&&(p(r,{code:f.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),a.dirty()),s.maxLength!==null&&r.data.length>s.maxLength.value&&(p(r,{code:f.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),a.dirty()),r.common.async)return Promise.all([...r.data].map((i,o)=>s.type._parseAsync(new K(r,i,r.path,o)))).then(i=>F.mergeArray(a,i));const n=[...r.data].map((i,o)=>s.type._parseSync(new K(r,i,r.path,o)));return F.mergeArray(a,n)}get element(){return this._def.type}min(e,r){return new Be({...this._def,minLength:{value:e,message:x.toString(r)}})}max(e,r){return new Be({...this._def,maxLength:{value:e,message:x.toString(r)}})}length(e,r){return new Be({...this._def,exactLength:{value:e,message:x.toString(r)}})}nonempty(e){return this.min(1,e)}};de.create=(t,e)=>new de({type:t,minLength:null,maxLength:null,exactLength:null,typeName:T.ZodArray,...C(e)});function le(t){if(t instanceof W){const e={};for(const r in t.shape){const a=t.shape[r];e[r]=Y.create(le(a))}return new W({...t._def,shape:()=>e})}else return t instanceof de?new de({...t._def,type:le(t.element)}):t instanceof Y?Y.create(le(t.unwrap())):t instanceof ae?ae.create(le(t.unwrap())):t instanceof re?re.create(t.items.map(e=>le(e))):t}var W=class q extends R{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),r=A.objectKeys(e);return this._cached={shape:e,keys:r}}_parse(e){if(this._getType(e)!==g.object){const c=this._getOrReturnCtx(e);return p(c,{code:f.invalid_type,expected:g.object,received:c.parsedType}),j}const{status:a,ctx:s}=this._processInputParams(e),{shape:n,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof ee&&this._def.unknownKeys==="strip"))for(const c in s.data)i.includes(c)||o.push(c);const l=[];for(const c of i){const h=n[c],y=s.data[c];l.push({key:{status:"valid",value:c},value:h._parse(new K(s,y,s.path,c)),alwaysSet:c in s.data})}if(this._def.catchall instanceof ee){const c=this._def.unknownKeys;if(c==="passthrough")for(const h of o)l.push({key:{status:"valid",value:h},value:{status:"valid",value:s.data[h]}});else if(c==="strict")o.length>0&&(p(s,{code:f.unrecognized_keys,keys:o}),a.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const h of o){const y=s.data[h];l.push({key:{status:"valid",value:h},value:c._parse(new K(s,y,s.path,h)),alwaysSet:h in s.data})}}return s.common.async?Promise.resolve().then(async()=>{const c=[];for(const h of l){const y=await h.key,_=await h.value;c.push({key:y,value:_,alwaysSet:h.alwaysSet})}return c}).then(c=>F.mergeObjectSync(a,c)):F.mergeObjectSync(a,l)}get shape(){return this._def.shape()}strict(e){return x.errToObj,new q({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(r,a)=>{var s,n,i,o;const l=(i=(n=(s=this._def).errorMap)===null||n===void 0?void 0:n.call(s,r,a).message)!==null&&i!==void 0?i:a.defaultError;return r.code==="unrecognized_keys"?{message:(o=x.errToObj(e).message)!==null&&o!==void 0?o:l}:{message:l}}}:{}})}strip(){return new q({...this._def,unknownKeys:"strip"})}passthrough(){return new q({...this._def,unknownKeys:"passthrough"})}extend(e){return new q({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new q({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:T.ZodObject})}setKey(e,r){return this.augment({[e]:r})}catchall(e){return new q({...this._def,catchall:e})}pick(e){const r={};return A.objectKeys(e).forEach(a=>{e[a]&&this.shape[a]&&(r[a]=this.shape[a])}),new q({...this._def,shape:()=>r})}omit(e){const r={};return A.objectKeys(this.shape).forEach(a=>{e[a]||(r[a]=this.shape[a])}),new q({...this._def,shape:()=>r})}deepPartial(){return le(this)}partial(e){const r={};return A.objectKeys(this.shape).forEach(a=>{const s=this.shape[a];e&&!e[a]?r[a]=s:r[a]=s.optional()}),new q({...this._def,shape:()=>r})}required(e){const r={};return A.objectKeys(this.shape).forEach(a=>{if(e&&!e[a])r[a]=this.shape[a];else{let n=this.shape[a];for(;n instanceof Y;)n=n._def.innerType;r[a]=n}}),new q({...this._def,shape:()=>r})}keyof(){return dr(A.objectKeys(this.shape))}};W.create=(t,e)=>new W({shape:()=>t,unknownKeys:"strip",catchall:ee.create(),typeName:T.ZodObject,...C(e)});W.strictCreate=(t,e)=>new W({shape:()=>t,unknownKeys:"strict",catchall:ee.create(),typeName:T.ZodObject,...C(e)});W.lazycreate=(t,e)=>new W({shape:t,unknownKeys:"strip",catchall:ee.create(),typeName:T.ZodObject,...C(e)});var Ne=class extends R{_parse(t){const{ctx:e}=this._processInputParams(t),r=this._def.options;function a(s){for(const i of s)if(i.result.status==="valid")return i.result;for(const i of s)if(i.result.status==="dirty")return e.common.issues.push(...i.ctx.common.issues),i.result;const n=s.map(i=>new G(i.ctx.common.issues));return p(e,{code:f.invalid_union,unionErrors:n}),j}if(e.common.async)return Promise.all(r.map(async s=>{const n={...e,common:{...e.common,issues:[]},parent:null};return{result:await s._parseAsync({data:e.data,path:e.path,parent:n}),ctx:n}})).then(a);{let s;const n=[];for(const o of r){const l={...e,common:{...e.common,issues:[]},parent:null},c=o._parseSync({data:e.data,path:e.path,parent:l});if(c.status==="valid")return c;c.status==="dirty"&&!s&&(s={result:c,ctx:l}),l.common.issues.length&&n.push(l.common.issues)}if(s)return e.common.issues.push(...s.ctx.common.issues),s.result;const i=n.map(o=>new G(o));return p(e,{code:f.invalid_union,unionErrors:i}),j}}get options(){return this._def.options}};Ne.create=(t,e)=>new Ne({options:t,typeName:T.ZodUnion,...C(e)});var X=t=>t instanceof Me?X(t.schema):t instanceof H?X(t.innerType()):t instanceof Ue?[t.value]:t instanceof Ve?t.options:t instanceof ze?A.objectValues(t.enum):t instanceof De?X(t._def.innerType):t instanceof Ze?[void 0]:t instanceof $e?[null]:t instanceof Y?[void 0,...X(t.unwrap())]:t instanceof ae?[null,...X(t.unwrap())]:t instanceof jt||t instanceof Fe?X(t.unwrap()):t instanceof Le?X(t._def.innerType):[],ar=class sr extends R{_parse(e){const{ctx:r}=this._processInputParams(e);if(r.parsedType!==g.object)return p(r,{code:f.invalid_type,expected:g.object,received:r.parsedType}),j;const a=this.discriminator,s=r.data[a],n=this.optionsMap.get(s);return n?r.common.async?n._parseAsync({data:r.data,path:r.path,parent:r}):n._parseSync({data:r.data,path:r.path,parent:r}):(p(r,{code:f.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[a]}),j)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,r,a){const s=new Map;for(const n of r){const i=X(n.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(s.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);s.set(o,n)}}return new sr({typeName:T.ZodDiscriminatedUnion,discriminator:e,options:r,optionsMap:s,...C(a)})}};function gt(t,e){const r=Q(t),a=Q(e);if(t===e)return{valid:!0,data:t};if(r===g.object&&a===g.object){const s=A.objectKeys(e),n=A.objectKeys(t).filter(o=>s.indexOf(o)!==-1),i={...t,...e};for(const o of n){const l=gt(t[o],e[o]);if(!l.valid)return{valid:!1};i[o]=l.data}return{valid:!0,data:i}}else if(r===g.array&&a===g.array){if(t.length!==e.length)return{valid:!1};const s=[];for(let n=0;n<t.length;n++){const i=t[n],o=e[n],l=gt(i,o);if(!l.valid)return{valid:!1};s.push(l.data)}return{valid:!0,data:s}}else return r===g.date&&a===g.date&&+t==+e?{valid:!0,data:t}:{valid:!1}}var Pe=class extends R{_parse(t){const{status:e,ctx:r}=this._processInputParams(t),a=(s,n)=>{if(ht(s)||ht(n))return j;const i=gt(s.value,n.value);return i.valid?((pt(s)||pt(n))&&e.dirty(),{status:e.value,value:i.data}):(p(r,{code:f.invalid_intersection_types}),j)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([s,n])=>a(s,n)):a(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}};Pe.create=(t,e,r)=>new Pe({left:t,right:e,typeName:T.ZodIntersection,...C(r)});var re=class nr extends R{_parse(e){const{status:r,ctx:a}=this._processInputParams(e);if(a.parsedType!==g.array)return p(a,{code:f.invalid_type,expected:g.array,received:a.parsedType}),j;if(a.data.length<this._def.items.length)return p(a,{code:f.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),j;!this._def.rest&&a.data.length>this._def.items.length&&(p(a,{code:f.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),r.dirty());const n=[...a.data].map((i,o)=>{const l=this._def.items[o]||this._def.rest;return l?l._parse(new K(a,i,a.path,o)):null}).filter(i=>!!i);return a.common.async?Promise.all(n).then(i=>F.mergeArray(r,i)):F.mergeArray(r,n)}get items(){return this._def.items}rest(e){return new nr({...this._def,rest:e})}};re.create=(t,e)=>{if(!Array.isArray(t))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new re({items:t,typeName:T.ZodTuple,rest:null,...C(e)})};var ir=class _t extends R{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:r,ctx:a}=this._processInputParams(e);if(a.parsedType!==g.object)return p(a,{code:f.invalid_type,expected:g.object,received:a.parsedType}),j;const s=[],n=this._def.keyType,i=this._def.valueType;for(const o in a.data)s.push({key:n._parse(new K(a,o,a.path,o)),value:i._parse(new K(a,a.data[o],a.path,o)),alwaysSet:o in a.data});return a.common.async?F.mergeObjectAsync(r,s):F.mergeObjectSync(r,s)}get element(){return this._def.valueType}static create(e,r,a){return r instanceof R?new _t({keyType:e,valueType:r,typeName:T.ZodRecord,...C(a)}):new _t({keyType:pe.create(),valueType:e,typeName:T.ZodRecord,...C(r)})}},Xe=class extends R{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:e,ctx:r}=this._processInputParams(t);if(r.parsedType!==g.map)return p(r,{code:f.invalid_type,expected:g.map,received:r.parsedType}),j;const a=this._def.keyType,s=this._def.valueType,n=[...r.data.entries()].map(([i,o],l)=>({key:a._parse(new K(r,i,r.path,[l,"key"])),value:s._parse(new K(r,o,r.path,[l,"value"]))}));if(r.common.async){const i=new Map;return Promise.resolve().then(async()=>{for(const o of n){const l=await o.key,c=await o.value;if(l.status==="aborted"||c.status==="aborted")return j;(l.status==="dirty"||c.status==="dirty")&&e.dirty(),i.set(l.value,c.value)}return{status:e.value,value:i}})}else{const i=new Map;for(const o of n){const l=o.key,c=o.value;if(l.status==="aborted"||c.status==="aborted")return j;(l.status==="dirty"||c.status==="dirty")&&e.dirty(),i.set(l.value,c.value)}return{status:e.value,value:i}}}};Xe.create=(t,e,r)=>new Xe({valueType:e,keyType:t,typeName:T.ZodMap,...C(r)});var Qe=class yt extends R{_parse(e){const{status:r,ctx:a}=this._processInputParams(e);if(a.parsedType!==g.set)return p(a,{code:f.invalid_type,expected:g.set,received:a.parsedType}),j;const s=this._def;s.minSize!==null&&a.data.size<s.minSize.value&&(p(a,{code:f.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),r.dirty()),s.maxSize!==null&&a.data.size>s.maxSize.value&&(p(a,{code:f.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),r.dirty());const n=this._def.valueType;function i(l){const c=new Set;for(const h of l){if(h.status==="aborted")return j;h.status==="dirty"&&r.dirty(),c.add(h.value)}return{status:r.value,value:c}}const o=[...a.data.values()].map((l,c)=>n._parse(new K(a,l,a.path,c)));return a.common.async?Promise.all(o).then(l=>i(l)):i(o)}min(e,r){return new yt({...this._def,minSize:{value:e,message:x.toString(r)}})}max(e,r){return new yt({...this._def,maxSize:{value:e,message:x.toString(r)}})}size(e,r){return this.min(e,r).max(e,r)}nonempty(e){return this.min(1,e)}};Qe.create=(t,e)=>new Qe({valueType:t,minSize:null,maxSize:null,typeName:T.ZodSet,...C(e)});var or=class We extends R{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:r}=this._processInputParams(e);if(r.parsedType!==g.function)return p(r,{code:f.invalid_type,expected:g.function,received:r.parsedType}),j;function a(o,l){return He({data:o,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,Ge(),he].filter(c=>!!c),issueData:{code:f.invalid_arguments,argumentsError:l}})}function s(o,l){return He({data:o,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,Ge(),he].filter(c=>!!c),issueData:{code:f.invalid_return_type,returnTypeError:l}})}const n={errorMap:r.common.contextualErrorMap},i=r.data;if(this._def.returns instanceof ve){const o=this;return L(async function(...l){const c=new G([]),h=await o._def.args.parseAsync(l,n).catch(v=>{throw c.addIssue(a(l,v)),c}),y=await Reflect.apply(i,this,h);return await o._def.returns._def.type.parseAsync(y,n).catch(v=>{throw c.addIssue(s(y,v)),c})})}else{const o=this;return L(function(...l){const c=o._def.args.safeParse(l,n);if(!c.success)throw new G([a(l,c.error)]);const h=Reflect.apply(i,this,c.data),y=o._def.returns.safeParse(h,n);if(!y.success)throw new G([s(h,y.error)]);return y.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new We({...this._def,args:re.create(e).rest(ie.create())})}returns(e){return new We({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,r,a){return new We({args:e||re.create([]).rest(ie.create()),returns:r||ie.create(),typeName:T.ZodFunction,...C(a)})}},Me=class extends R{get schema(){return this._def.getter()}_parse(t){const{ctx:e}=this._processInputParams(t);return this._def.getter()._parse({data:e.data,path:e.path,parent:e})}};Me.create=(t,e)=>new Me({getter:t,typeName:T.ZodLazy,...C(e)});var Ue=class extends R{_parse(t){if(t.data!==this._def.value){const e=this._getOrReturnCtx(t);return p(e,{received:e.data,code:f.invalid_literal,expected:this._def.value}),j}return{status:"valid",value:t.data}}get value(){return this._def.value}};Ue.create=(t,e)=>new Ue({value:t,typeName:T.ZodLiteral,...C(e)});function dr(t,e){return new Ve({values:t,typeName:T.ZodEnum,...C(e)})}var Ve=class bt extends R{constructor(){super(...arguments),Te.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const r=this._getOrReturnCtx(e),a=this._def.values;return p(r,{expected:A.joinValues(a),received:r.parsedType,code:f.invalid_type}),j}if(Je(this,Te)||Xt(this,Te,new Set(this._def.values)),!Je(this,Te).has(e.data)){const r=this._getOrReturnCtx(e),a=this._def.values;return p(r,{received:r.data,code:f.invalid_enum_value,options:a}),j}return L(e.data)}get options(){return this._def.values}get enum(){const e={};for(const r of this._def.values)e[r]=r;return e}get Values(){const e={};for(const r of this._def.values)e[r]=r;return e}get Enum(){const e={};for(const r of this._def.values)e[r]=r;return e}extract(e,r=this._def){return bt.create(e,{...this._def,...r})}exclude(e,r=this._def){return bt.create(this.options.filter(a=>!e.includes(a)),{...this._def,...r})}};Te=new WeakMap;Ve.create=dr;var ze=class extends R{constructor(){super(...arguments),je.set(this,void 0)}_parse(t){const e=A.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(t);if(r.parsedType!==g.string&&r.parsedType!==g.number){const a=A.objectValues(e);return p(r,{expected:A.joinValues(a),received:r.parsedType,code:f.invalid_type}),j}if(Je(this,je)||Xt(this,je,new Set(A.getValidEnumValues(this._def.values))),!Je(this,je).has(t.data)){const a=A.objectValues(e);return p(r,{received:r.data,code:f.invalid_enum_value,options:a}),j}return L(t.data)}get enum(){return this._def.values}};je=new WeakMap;ze.create=(t,e)=>new ze({values:t,typeName:T.ZodNativeEnum,...C(e)});var ve=class extends R{unwrap(){return this._def.type}_parse(t){const{ctx:e}=this._processInputParams(t);if(e.parsedType!==g.promise&&e.common.async===!1)return p(e,{code:f.invalid_type,expected:g.promise,received:e.parsedType}),j;const r=e.parsedType===g.promise?e.data:Promise.resolve(e.data);return L(r.then(a=>this._def.type.parseAsync(a,{path:e.path,errorMap:e.common.contextualErrorMap})))}};ve.create=(t,e)=>new ve({type:t,typeName:T.ZodPromise,...C(e)});var H=class extends R{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===T.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:e,ctx:r}=this._processInputParams(t),a=this._def.effect||null,s={addIssue:n=>{p(r,n),n.fatal?e.abort():e.dirty()},get path(){return r.path}};if(s.addIssue=s.addIssue.bind(s),a.type==="preprocess"){const n=a.transform(r.data,s);if(r.common.async)return Promise.resolve(n).then(async i=>{if(e.value==="aborted")return j;const o=await this._def.schema._parseAsync({data:i,path:r.path,parent:r});return o.status==="aborted"?j:o.status==="dirty"||e.value==="dirty"?fe(o.value):o});{if(e.value==="aborted")return j;const i=this._def.schema._parseSync({data:n,path:r.path,parent:r});return i.status==="aborted"?j:i.status==="dirty"||e.value==="dirty"?fe(i.value):i}}if(a.type==="refinement"){const n=i=>{const o=a.refinement(i,s);if(r.common.async)return Promise.resolve(o);if(o instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return i};if(r.common.async===!1){const i=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return i.status==="aborted"?j:(i.status==="dirty"&&e.dirty(),n(i.value),{status:e.value,value:i.value})}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(i=>i.status==="aborted"?j:(i.status==="dirty"&&e.dirty(),n(i.value).then(()=>({status:e.value,value:i.value}))))}if(a.type==="transform")if(r.common.async===!1){const n=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!oe(n))return n;const i=a.transform(n.value,s);if(i instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:e.value,value:i}}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(n=>oe(n)?Promise.resolve(a.transform(n.value,s)).then(i=>({status:e.value,value:i})):n);A.assertNever(a)}};H.create=(t,e,r)=>new H({schema:t,typeName:T.ZodEffects,effect:e,...C(r)});H.createWithPreprocess=(t,e,r)=>new H({schema:e,effect:{type:"preprocess",transform:t},typeName:T.ZodEffects,...C(r)});var Y=class extends R{_parse(t){return this._getType(t)===g.undefined?L(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}};Y.create=(t,e)=>new Y({innerType:t,typeName:T.ZodOptional,...C(e)});var ae=class extends R{_parse(t){return this._getType(t)===g.null?L(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}};ae.create=(t,e)=>new ae({innerType:t,typeName:T.ZodNullable,...C(e)});var De=class extends R{_parse(t){const{ctx:e}=this._processInputParams(t);let r=e.data;return e.parsedType===g.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:e.path,parent:e})}removeDefault(){return this._def.innerType}};De.create=(t,e)=>new De({innerType:t,typeName:T.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...C(e)});var Le=class extends R{_parse(t){const{ctx:e}=this._processInputParams(t),r={...e,common:{...e.common,issues:[]}},a=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return Oe(a)?a.then(s=>({status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new G(r.common.issues)},input:r.data})})):{status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new G(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}};Le.create=(t,e)=>new Le({innerType:t,typeName:T.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...C(e)});var et=class extends R{_parse(t){if(this._getType(t)!==g.nan){const r=this._getOrReturnCtx(t);return p(r,{code:f.invalid_type,expected:g.nan,received:r.parsedType}),j}return{status:"valid",value:t.data}}};et.create=t=>new et({typeName:T.ZodNaN,...C(t)});var Xs=Symbol("zod_brand"),jt=class extends R{_parse(t){const{ctx:e}=this._processInputParams(t),r=e.data;return this._def.type._parse({data:r,path:e.path,parent:e})}unwrap(){return this._def.type}},Ct=class ur extends R{_parse(e){const{status:r,ctx:a}=this._processInputParams(e);if(a.common.async)return(async()=>{const n=await this._def.in._parseAsync({data:a.data,path:a.path,parent:a});return n.status==="aborted"?j:n.status==="dirty"?(r.dirty(),fe(n.value)):this._def.out._parseAsync({data:n.value,path:a.path,parent:a})})();{const s=this._def.in._parseSync({data:a.data,path:a.path,parent:a});return s.status==="aborted"?j:s.status==="dirty"?(r.dirty(),{status:"dirty",value:s.value}):this._def.out._parseSync({data:s.value,path:a.path,parent:a})}}static create(e,r){return new ur({in:e,out:r,typeName:T.ZodPipeline})}},Fe=class extends R{_parse(t){const e=this._def.innerType._parse(t),r=a=>(oe(a)&&(a.value=Object.freeze(a.value)),a);return Oe(e)?e.then(a=>r(a)):r(e)}unwrap(){return this._def.innerType}};Fe.create=(t,e)=>new Fe({innerType:t,typeName:T.ZodReadonly,...C(e)});function Nt(t,e){const r=typeof t=="function"?t(e):typeof t=="string"?{message:t}:t;return typeof r=="string"?{message:r}:r}function cr(t,e={},r){return t?me.create().superRefine((a,s)=>{var n,i;const o=t(a);if(o instanceof Promise)return o.then(l=>{var c,h;if(!l){const y=Nt(e,a),_=(h=(c=y.fatal)!==null&&c!==void 0?c:r)!==null&&h!==void 0?h:!0;s.addIssue({code:"custom",...y,fatal:_})}});if(!o){const l=Nt(e,a),c=(i=(n=l.fatal)!==null&&n!==void 0?n:r)!==null&&i!==void 0?i:!0;s.addIssue({code:"custom",...l,fatal:c})}}):me.create()}var Qs={object:W.lazycreate},T;(function(t){t.ZodString="ZodString",t.ZodNumber="ZodNumber",t.ZodNaN="ZodNaN",t.ZodBigInt="ZodBigInt",t.ZodBoolean="ZodBoolean",t.ZodDate="ZodDate",t.ZodSymbol="ZodSymbol",t.ZodUndefined="ZodUndefined",t.ZodNull="ZodNull",t.ZodAny="ZodAny",t.ZodUnknown="ZodUnknown",t.ZodNever="ZodNever",t.ZodVoid="ZodVoid",t.ZodArray="ZodArray",t.ZodObject="ZodObject",t.ZodUnion="ZodUnion",t.ZodDiscriminatedUnion="ZodDiscriminatedUnion",t.ZodIntersection="ZodIntersection",t.ZodTuple="ZodTuple",t.ZodRecord="ZodRecord",t.ZodMap="ZodMap",t.ZodSet="ZodSet",t.ZodFunction="ZodFunction",t.ZodLazy="ZodLazy",t.ZodLiteral="ZodLiteral",t.ZodEnum="ZodEnum",t.ZodEffects="ZodEffects",t.ZodNativeEnum="ZodNativeEnum",t.ZodOptional="ZodOptional",t.ZodNullable="ZodNullable",t.ZodDefault="ZodDefault",t.ZodCatch="ZodCatch",t.ZodPromise="ZodPromise",t.ZodBranded="ZodBranded",t.ZodPipeline="ZodPipeline",t.ZodReadonly="ZodReadonly"})(T||(T={}));var en=(t,e={message:`Input not instance of ${t.name}`})=>cr(r=>r instanceof t,e),lr=pe.create,fr=Se.create,tn=et.create,rn=Ae.create,hr=Ie.create,an=Ee.create,sn=Ye.create,nn=Ze.create,on=$e.create,dn=me.create,un=ie.create,cn=ee.create,ln=Ke.create,fn=de.create,hn=W.create,pn=W.strictCreate,mn=Ne.create,vn=ar.create,gn=Pe.create,_n=re.create,yn=ir.create,bn=Xe.create,xn=Qe.create,wn=or.create,kn=Me.create,Tn=Ue.create,jn=Ve.create,Cn=ze.create,Rn=ve.create,Pt=H.create,On=Y.create,Sn=ae.create,An=H.createWithPreprocess,In=Ct.create,En=()=>lr().optional(),Zn=()=>fr().optional(),$n=()=>hr().optional(),Nn={string:t=>pe.create({...t,coerce:!0}),number:t=>Se.create({...t,coerce:!0}),boolean:t=>Ie.create({...t,coerce:!0}),bigint:t=>Ae.create({...t,coerce:!0}),date:t=>Ee.create({...t,coerce:!0})},Pn=j,P=Object.freeze({__proto__:null,defaultErrorMap:he,setErrorMap:Ss,getErrorMap:Ge,makeIssue:He,EMPTY_PATH:As,addIssueToContext:p,ParseStatus:F,INVALID:j,DIRTY:fe,OK:L,isAborted:ht,isDirty:pt,isValid:oe,isAsync:Oe,get util(){return A},get objectUtil(){return ft},ZodParsedType:g,getParsedType:Q,ZodType:R,datetimeRegex:tr,ZodString:pe,ZodNumber:Se,ZodBigInt:Ae,ZodBoolean:Ie,ZodDate:Ee,ZodSymbol:Ye,ZodUndefined:Ze,ZodNull:$e,ZodAny:me,ZodUnknown:ie,ZodNever:ee,ZodVoid:Ke,ZodArray:de,ZodObject:W,ZodUnion:Ne,ZodDiscriminatedUnion:ar,ZodIntersection:Pe,ZodTuple:re,ZodRecord:ir,ZodMap:Xe,ZodSet:Qe,ZodFunction:or,ZodLazy:Me,ZodLiteral:Ue,ZodEnum:Ve,ZodNativeEnum:ze,ZodPromise:ve,ZodEffects:H,ZodTransformer:H,ZodOptional:Y,ZodNullable:ae,ZodDefault:De,ZodCatch:Le,ZodNaN:et,BRAND:Xs,ZodBranded:jt,ZodPipeline:Ct,ZodReadonly:Fe,custom:cr,Schema:R,ZodSchema:R,late:Qs,get ZodFirstPartyTypeKind(){return T},coerce:Nn,any:dn,array:fn,bigint:rn,boolean:hr,date:an,discriminatedUnion:vn,effect:Pt,enum:jn,function:wn,instanceof:en,intersection:gn,lazy:kn,literal:Tn,map:bn,nan:tn,nativeEnum:Cn,never:cn,null:on,nullable:Sn,number:fr,object:hn,oboolean:$n,onumber:Zn,optional:On,ostring:En,pipeline:In,preprocess:An,promise:Rn,record:yn,set:xn,strictObject:pn,string:lr,symbol:sn,transformer:Pt,tuple:_n,undefined:nn,union:mn,unknown:un,void:ln,NEVER:Pn,ZodIssueCode:f,quotelessJson:Os,ZodError:G}),Mt={name:"@imgly/background-removal",version:"1.7.0"},Mn=P.object({publicPath:P.string().optional().describe("The public path to the wasm files and the onnx model.").default("https://staticimgly.com/@imgly/background-removal-data/${PACKAGE_VERSION}/dist/").transform(t=>t.replace("${PACKAGE_NAME}",Mt.name).replace("${PACKAGE_VERSION}",Mt.version)),debug:P.boolean().default(!1).describe("Whether to enable debug logging."),rescale:P.boolean().default(!0).describe("Whether to rescale the image."),device:P.enum(["cpu","gpu"]).default("cpu").describe("The device to run the model on."),proxyToWorker:P.boolean().default(!1).describe("Whether to proxy inference to a web worker."),fetchArgs:P.any().default({}).describe("Arguments to pass to fetch when loading the model."),progress:P.function().args(P.string(),P.number(),P.number()).returns(P.void()).describe("Progress callback.").optional(),model:P.preprocess(t=>{switch(t){case"large":return"isnet";case"small":return"isnet_quint8";case"medium":return"isnet_fp16";default:return t}},P.enum(["isnet","isnet_fp16","isnet_quint8"])).default("medium"),output:P.object({format:P.enum(["image/png","image/jpeg","image/webp","image/x-rgba8","image/x-alpha8"]).default("image/png"),quality:P.number().default(.8)}).default({})}).default({}).transform(t=>(t.debug&&console.log("Config:",t),t.debug&&!t.progress&&(t.progress=t.progress??((e,r,a)=>{console.debug(`Downloading ${e}: ${r} of ${a}`)}),crossOriginIsolated||t.debug&&console.debug("Cross-Origin-Isolated is not enabled. Performance will be degraded. Please see  https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/SharedArrayBuffer.")),t));function Un(t){return Mn.parse(t??{})}var zn=tt(rt());async function Dn(t){t.debug&&console.debug("Loading model...",t.model);const e=t.model,a=await(await Gt(`/models/${e}`,t)).arrayBuffer();return await Cs(a,t)}async function Ln(t){t=Un(t);const e=await Dn(t);return{config:t,session:{base:e}}}async function Fn(t,e,r){const[s,n,i]=t.shape,o=!1;let l=Et(t,1024,1024,o);const c=xs(l);let h=await Rs(r.base,[["input",c]],["output"],e),y=(0,zn.default)(h[0].data,[1024,1024,1]),_=ks(y);return e.rescale?(_=Et(_,n,s,o),[_,t]):[_,l]}var Vn=ps(Ln,t=>JSON.stringify(t));async function Bn(t,e){const{config:r,session:a}=await Vn(e);r.progress&&r.progress("compute:decode",0,4);const s=await ws(t,r);r.progress?.("compute:inference",1,4);const[n,i]=await Fn(s,r,a);r.progress?.("compute:mask",2,4);const o=i,[l,c]=o.shape,h=l*c;for(let _=0;_<h;_+=1)o.data[4*_+3]=n.data[_];r.progress?.("compute:encode",3,4);const y=await vs(o,r.output.quality,r.output.format);return r.progress?.("compute:encode",4,4),y}/*! Bundled license information:

is-buffer/index.js:
  (*!
   * Determine if an object is a Buffer
   *
   * <AUTHOR> Aboukhadijeh <https://feross.org>
   * @license  MIT
   *)
*/const Wn={class:"min-h-screen bg-gray-50 p-6"},qn={class:"max-w-6xl mx-auto space-y-6"},Gn={class:"text-center mb-8"},Hn={class:"text-3xl font-bold text-gray-900 mb-2"},Jn={class:"text-gray-600"},Yn={class:"grid md:grid-cols-3 gap-6 mb-8"},Kn={class:"bg-white p-6 rounded-lg shadow-sm border"},Xn={class:"text-lg font-semibold mb-2"},Qn={class:"text-gray-600 text-sm"},ei={class:"bg-white p-6 rounded-lg shadow-sm border"},ti={class:"text-lg font-semibold mb-2"},ri={class:"text-gray-600 text-sm"},ai={class:"bg-white p-6 rounded-lg shadow-sm border"},si={class:"text-lg font-semibold mb-2"},ni={class:"text-gray-600 text-sm"},ii={class:"grid lg:grid-cols-2 gap-6"},oi={class:"bg-white p-6 rounded-lg shadow-sm border"},di={class:"flex items-center justify-between mb-4"},ui={class:"text-lg font-semibold text-gray-900"},ci={class:"flex space-x-2"},li={key:0},fi={class:"text-lg font-medium text-gray-900 mb-2"},hi={class:"text-gray-600 mb-4"},pi={key:1,class:"space-y-4"},mi={class:"font-medium text-gray-900"},vi={class:"relative"},gi=["src","alt"],_i={key:0,class:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-lg"},yi={class:"text-white text-center"},bi={class:"relative"},xi={key:0,class:"absolute inset-0 flex items-center justify-center"},wi={class:"text-xs font-bold"},ki={key:0,class:"w-32 bg-gray-200 rounded-full h-1.5 mt-2 mx-auto"},Ti={class:"text-sm text-gray-600"},ji={key:0,class:"mt-6 space-y-4"},Ci={class:"font-medium text-gray-900"},Ri={class:"space-y-3"},Oi={class:"text-sm font-medium text-gray-700"},Si={value:"small"},Ai={value:"medium"},Ii={value:"large"},Ei={class:"space-y-3"},Zi={class:"text-sm font-medium text-gray-700"},$i={class:"space-y-2"},Ni={class:"flex items-center space-x-3"},Pi={class:"text-sm font-medium"},Mi={class:"flex items-center space-x-3"},Ui={class:"text-sm font-medium"},zi={class:"flex items-center space-x-3"},Di={class:"text-sm font-medium"},Li={key:0,class:"space-y-2"},Fi={class:"text-sm font-medium text-gray-700"},Vi={key:1,class:"space-y-2"},Bi={class:"text-sm font-medium text-gray-700"},Wi={class:"flex space-x-2"},qi=["disabled"],Gi={key:0},Hi={key:1},Ji={class:"bg-white p-6 rounded-lg shadow-sm border"},Yi={class:"flex items-center justify-between mb-4"},Ki={class:"text-lg font-semibold text-gray-900"},Xi={key:0,class:"flex space-x-2"},Qi={key:0,class:"h-96 flex items-center justify-center text-gray-500 border-2 border-dashed border-gray-200 rounded-lg"},eo={class:"text-center"},to={key:1,class:"h-96 flex items-center justify-center border-2 border-dashed border-gray-200 rounded-lg"},ro={class:"text-center max-w-md"},ao={class:"relative mb-4"},so={key:0,class:"absolute inset-0 flex items-center justify-center"},no={class:"text-sm font-bold text-blue-600"},io={class:"text-gray-600 mb-2 font-medium"},oo={key:0,class:"w-64 bg-gray-200 rounded-full h-2 mx-auto"},uo={class:"text-sm text-gray-500 mt-2"},co={key:2,class:"space-y-4"},lo={class:"bg-green-50 border border-green-200 rounded-lg p-4"},fo={class:"flex items-center"},ho={class:"font-medium text-green-800"},po={class:"text-sm text-green-600"},mo={class:"font-medium text-gray-900"},vo={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},go={class:"space-y-2"},_o={class:"text-sm font-medium text-gray-700"},yo={class:"relative bg-white rounded-lg overflow-hidden shadow-sm border"},bo=["src","alt"],xo={class:"space-y-2"},wo={class:"text-sm font-medium text-gray-700"},ko={class:"relative bg-white rounded-lg overflow-hidden shadow-sm border",style:{"background-image":`linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                      linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                      linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)`,"background-size":"20px 20px","background-position":`0 0,
                      0 10px,
                      10px -10px,
                      -10px 0px`}},To=["src","alt"],jo={class:"grid grid-cols-2 gap-4 text-sm text-gray-600"},Co={key:0},Ro={class:"bg-blue-50 border border-blue-200 rounded-lg p-6"},Oo={class:"text-lg font-semibold text-blue-900 mb-3"},So={class:"space-y-2 text-blue-800"},Ao={class:"flex items-start"},Io={class:"flex items-start"},Eo={class:"flex items-start"},Zo={class:"flex items-start"},$o={class:"flex items-start"},No=vr({__name:"BackgroundRemover",setup(t){const{success:e,error:r,downloadSuccess:a,copySuccess:s,copyError:n}=wr(),i=J(),o=J(null),l=J(""),c=J(null),h=J(""),y=J(0),_=J(!1),v=J(!1),S=J(0),w=J(""),b=gr({model:"medium",outputFormat:"png",quality:90,backgroundColor:"#ffffff"});function N(d){d.preventDefault(),v.value=!1;const m=d.dataTransfer?.files;m&&m.length>0&&D(m[0])}function z(d){const I=d.target.files;I&&I.length>0&&D(I[0])}function D(d){if(!d.type.startsWith("image/")){r("Please select a valid image file");return}const m=10*1024*1024;if(d.size>m){r("Image size must be less than 10MB");return}o.value=d,l.value=URL.createObjectURL(d),c.value&&(URL.revokeObjectURL(h.value),c.value=null,h.value="",y.value=0)}async function M(){if(o.value){_.value=!0,S.value=0,w.value="Initializing...";try{const d=new Image;await new Promise((U,se)=>{d.onload=()=>U(),d.onerror=()=>se(new Error("Failed to load image")),d.src=l.value}),S.value=10,w.value="Loading AI model...";const I={model:b.model==="small"?"isnet_quint8":b.model==="large"?"isnet":"isnet_fp16",output:{format:b.outputFormat==="png"?"image/png":b.outputFormat==="jpg"?"image/jpeg":"image/webp",quality:b.quality/100},progress:(U,se,pr)=>{const mr=10+se/pr*80;S.value=mr,U.includes("model")?w.value="Loading AI model...":U.includes("inference")?w.value="Processing image...":w.value="Analyzing image..."}},$=await Bn(o.value,I);S.value=95,w.value="Finalizing...";let V;b.outputFormat==="jpg"?V=await be($,b.backgroundColor):b.outputFormat==="webp"?V=await xe($,b.quality):V=$,c.value=V,h.value=URL.createObjectURL(V),y.value=V.size,S.value=100,w.value="Complete!",setTimeout(()=>{_.value=!1,e("Background removed successfully!")},500)}catch(d){const m=d instanceof Error?d.message:"Unknown error";r("Failed to remove background: "+m),_.value=!1,S.value=0,w.value=""}}}async function be(d,m){return new Promise(I=>{const $=document.createElement("canvas"),V=$.getContext("2d"),U=new Image;U.onload=()=>{$.width=U.width,$.height=U.height,V.fillStyle=m,V.fillRect(0,0,$.width,$.height),V.drawImage(U,0,0),$.toBlob(se=>{I(se)},"image/jpeg",b.quality/100)},U.src=URL.createObjectURL(d)})}async function xe(d,m){return new Promise(I=>{const $=document.createElement("canvas"),V=$.getContext("2d"),U=new Image;U.onload=()=>{$.width=U.width,$.height=U.height,V.drawImage(U,0,0),$.toBlob(se=>{I(se)},"image/webp",m/100)},U.src=URL.createObjectURL(d)})}async function O(){if(c.value)try{const d=new ClipboardItem({[c.value.type]:c.value});await navigator.clipboard.write([d]),s()}catch{n()}}function ue(){if(!c.value)return;const d=URL.createObjectURL(c.value),m=document.createElement("a");m.href=d;const I=b.outputFormat==="jpg"?"jpg":b.outputFormat;m.download=`background-removed-${Date.now()}.${I}`,document.body.appendChild(m),m.click(),document.body.removeChild(m),URL.revokeObjectURL(d),a()}function ce(){l.value&&URL.revokeObjectURL(l.value),h.value&&URL.revokeObjectURL(h.value),o.value=null,l.value="",c.value=null,h.value="",y.value=0,_.value=!1,S.value=0,w.value="",i.value&&(i.value.value="")}function te(d){if(d===0)return"0 Bytes";const m=1024,I=["Bytes","KB","MB","GB"],$=Math.floor(Math.log(d)/Math.log(m));return parseFloat((d/Math.pow(m,$)).toFixed(2))+" "+I[$]}return _r(()=>{l.value&&URL.revokeObjectURL(l.value),h.value&&URL.revokeObjectURL(h.value)}),(d,m)=>(Z(),E("div",Wn,[u("div",qn,[u("div",Gn,[u("h1",Hn,k(d.$t("tools.backgroundRemover.title")),1),u("p",Jn,k(d.$t("tools.backgroundRemover.description")),1)]),u("div",Yn,[u("div",Kn,[m[11]||(m[11]=u("div",{class:"text-2xl mb-3"},"🎯",-1)),u("h3",Xn,k(d.$t("tools.backgroundRemover.features.aiPowered.title")),1),u("p",Qn,k(d.$t("tools.backgroundRemover.features.aiPowered.description")),1)]),u("div",ei,[m[12]||(m[12]=u("div",{class:"text-2xl mb-3"},"⚡",-1)),u("h3",ti,k(d.$t("tools.backgroundRemover.features.fastProcessing.title")),1),u("p",ri,k(d.$t("tools.backgroundRemover.features.fastProcessing.description")),1)]),u("div",ai,[m[13]||(m[13]=u("div",{class:"text-2xl mb-3"},"🖼️",-1)),u("h3",si,k(d.$t("tools.backgroundRemover.features.highQuality.title")),1),u("p",ni,k(d.$t("tools.backgroundRemover.features.highQuality.description")),1)])]),u("div",ii,[u("div",oi,[u("div",di,[u("h3",ui,k(d.$t("tools.backgroundRemover.upload.title")),1),u("div",ci,[o.value?(Z(),E("button",{key:0,onClick:ce,class:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"},k(d.$t("common.clear")),1)):B("",!0)])]),u("div",{onDrop:N,onDragover:m[1]||(m[1]=nt(()=>{},["prevent"])),onDragenter:m[2]||(m[2]=nt(I=>v.value=!0,["prevent"])),onDragleave:m[3]||(m[3]=nt(I=>v.value=!1,["prevent"])),class:yr(["border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors",{"border-blue-400 bg-blue-50":v.value}])},[o.value?(Z(),E("div",pi,[u("h4",mi,k(d.$t("tools.backgroundRemover.preview.original")),1),u("div",vi,[u("img",{src:l.value,alt:d.$t("tools.backgroundRemover.preview.originalAlt"),class:"max-w-full max-h-96 mx-auto rounded-lg shadow-sm"},null,8,gi),_.value?(Z(),E("div",_i,[u("div",yi,[u("div",bi,[m[15]||(m[15]=u("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"},null,-1)),S.value>0?(Z(),E("div",xi,[u("span",wi,k(Math.round(S.value))+"%",1)])):B("",!0)]),u("p",null,k(w.value),1),S.value>0?(Z(),E("div",ki,[u("div",{class:"bg-blue-600 h-1.5 rounded-full transition-all duration-300",style:Ot({width:S.value+"%"})},null,4)])):B("",!0)])])):B("",!0)]),u("div",Ti,k(d.$t("tools.backgroundRemover.imageInfo.size"))+": "+k(te(o.value.size)),1)])):(Z(),E("div",li,[m[14]||(m[14]=u("div",{class:"text-4xl mb-4"},"📸",-1)),u("h4",fi,k(d.$t("tools.backgroundRemover.upload.dragDrop")),1),u("p",hi,k(d.$t("tools.backgroundRemover.upload.supportedFormats")),1),u("input",{ref_key:"fileInput",ref:i,type:"file",accept:"image/*",onChange:z,class:"hidden"},null,544),u("button",{onClick:m[0]||(m[0]=()=>i.value?.click()),class:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors cursor-pointer"},k(d.$t("tools.backgroundRemover.upload.selectFile")),1)]))],34),o.value&&!c.value?(Z(),E("div",ji,[u("h4",Ci,k(d.$t("tools.backgroundRemover.options.title")),1),u("div",Ri,[u("label",Oi,k(d.$t("tools.backgroundRemover.options.model")),1),ne(u("select",{"onUpdate:modelValue":m[4]||(m[4]=I=>b.model=I),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[u("option",Si,k(d.$t("tools.backgroundRemover.models.small")),1),u("option",Ai,k(d.$t("tools.backgroundRemover.models.medium")),1),u("option",Ii,k(d.$t("tools.backgroundRemover.models.large")),1)],512),[[br,b.model]])]),u("div",Ei,[u("label",Zi,k(d.$t("tools.backgroundRemover.options.outputFormat")),1),u("div",$i,[u("label",Ni,[ne(u("input",{"onUpdate:modelValue":m[5]||(m[5]=I=>b.outputFormat=I),type:"radio",value:"png",class:"text-blue-600 focus:ring-blue-500"},null,512),[[it,b.outputFormat]]),u("span",Pi,"PNG ("+k(d.$t("tools.backgroundRemover.options.transparent"))+")",1)]),u("label",Mi,[ne(u("input",{"onUpdate:modelValue":m[6]||(m[6]=I=>b.outputFormat=I),type:"radio",value:"jpg",class:"text-blue-600 focus:ring-blue-500"},null,512),[[it,b.outputFormat]]),u("span",Ui,"JPG ("+k(d.$t("tools.backgroundRemover.options.whiteBackground"))+")",1)]),u("label",zi,[ne(u("input",{"onUpdate:modelValue":m[7]||(m[7]=I=>b.outputFormat=I),type:"radio",value:"webp",class:"text-blue-600 focus:ring-blue-500"},null,512),[[it,b.outputFormat]]),u("span",Di,"WebP ("+k(d.$t("tools.backgroundRemover.options.transparent"))+")",1)])])]),b.outputFormat!=="png"?(Z(),E("div",Li,[u("label",Fi,k(d.$t("tools.backgroundRemover.options.quality"))+": "+k(b.quality)+"% ",1),ne(u("input",{"onUpdate:modelValue":m[8]||(m[8]=I=>b.quality=I),type:"range",min:"70",max:"100",class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"},null,512),[[ot,b.quality]])])):B("",!0),b.outputFormat==="jpg"?(Z(),E("div",Vi,[u("label",Bi,k(d.$t("tools.backgroundRemover.options.backgroundColor")),1),u("div",Wi,[ne(u("input",{"onUpdate:modelValue":m[9]||(m[9]=I=>b.backgroundColor=I),type:"color",class:"w-12 h-8 border border-gray-300 rounded cursor-pointer"},null,512),[[ot,b.backgroundColor]]),ne(u("input",{"onUpdate:modelValue":m[10]||(m[10]=I=>b.backgroundColor=I),type:"text",class:"flex-1 px-3 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"#ffffff"},null,512),[[ot,b.backgroundColor]])])])):B("",!0),u("button",{onClick:M,disabled:_.value,class:"w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},[_.value?(Z(),E("span",Hi,k(w.value),1)):(Z(),E("span",Gi,k(d.$t("tools.backgroundRemover.actions.remove")),1))],8,qi)])):B("",!0)]),u("div",Ji,[u("div",Yi,[u("h3",Ki,k(d.$t("tools.backgroundRemover.result.title")),1),c.value?(Z(),E("div",Xi,[u("button",{onClick:ue,class:"px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"},k(d.$t("common.download")),1),u("button",{onClick:O,class:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"},k(d.$t("common.copy")),1)])):B("",!0)]),!c.value&&!_.value?(Z(),E("div",Qi,[u("div",eo,[m[16]||(m[16]=u("div",{class:"text-3xl mb-2"},"✨",-1)),u("p",null,k(d.$t("tools.backgroundRemover.result.noResult")),1)])])):_.value?(Z(),E("div",to,[u("div",ro,[u("div",ao,[m[17]||(m[17]=u("div",{class:"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto"},null,-1)),S.value>0?(Z(),E("div",so,[u("span",no,k(Math.round(S.value))+"%",1)])):B("",!0)]),u("p",io,k(w.value),1),S.value>0?(Z(),E("div",oo,[u("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:Ot({width:S.value+"%"})},null,4)])):B("",!0),u("p",uo,k(d.$t("tools.backgroundRemover.processing.pleaseWait")),1)])])):c.value?(Z(),E("div",co,[u("div",lo,[u("div",fo,[m[18]||(m[18]=u("div",{class:"text-green-600 text-2xl mr-3"},"✅",-1)),u("div",null,[u("p",ho,k(d.$t("tools.backgroundRemover.result.complete")),1),u("p",po,k(d.$t("tools.backgroundRemover.result.ready")),1)])])]),u("h4",mo,k(d.$t("tools.backgroundRemover.preview.processed")),1),u("div",vo,[u("div",go,[u("h5",_o,k(d.$t("tools.backgroundRemover.comparison.before")),1),u("div",yo,[u("img",{src:l.value,alt:d.$t("tools.backgroundRemover.preview.originalAlt"),class:"w-full h-48 object-contain"},null,8,bo)])]),u("div",xo,[u("h5",wo,k(d.$t("tools.backgroundRemover.comparison.after")),1),u("div",ko,[u("img",{src:h.value,alt:d.$t("tools.backgroundRemover.preview.processedAlt"),class:"w-full h-48 object-contain"},null,8,To)])])]),u("div",jo,[u("div",null,k(d.$t("tools.backgroundRemover.imageInfo.format"))+": "+k(b.outputFormat.toUpperCase()),1),y.value?(Z(),E("div",Co,k(d.$t("tools.backgroundRemover.imageInfo.size"))+": "+k(te(y.value)),1)):B("",!0)])])):B("",!0)])]),u("div",Ro,[u("h3",Oo," 💡 "+k(d.$t("tools.backgroundRemover.tips.title")),1),u("ul",So,[u("li",Ao,[m[19]||(m[19]=u("span",{class:"mr-2"},"•",-1)),u("span",null,k(d.$t("tools.backgroundRemover.tips.tip1")),1)]),u("li",Io,[m[20]||(m[20]=u("span",{class:"mr-2"},"•",-1)),u("span",null,k(d.$t("tools.backgroundRemover.tips.tip2")),1)]),u("li",Eo,[m[21]||(m[21]=u("span",{class:"mr-2"},"•",-1)),u("span",null,k(d.$t("tools.backgroundRemover.tips.tip3")),1)]),u("li",Zo,[m[22]||(m[22]=u("span",{class:"mr-2"},"•",-1)),u("span",null,k(d.$t("tools.backgroundRemover.tips.tip4")),1)]),u("li",$o,[m[23]||(m[23]=u("span",{class:"mr-2"},"•",-1)),u("span",null,k(d.$t("tools.backgroundRemover.tips.tip5")),1)])])])])]))}}),Uo=xr(No,[["__scopeId","data-v-07d2f609"]]);export{Uo as default};
