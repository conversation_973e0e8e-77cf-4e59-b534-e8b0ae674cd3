import{d as Ln,u as In,r as Xt,a4 as Rn,i as $e,a1 as Bn,a2 as Wn,c as Ot,o as Dt,a as T,t as P,S as qs,f as pe}from"./index-CkZTMFXG.js";import{u as Xn}from"./useToast-virEbLJw.js";function p(a,t,e){return(t=(function(s){var i=(function(r,n){if(typeof r!="object"||!r)return r;var o=r[Symbol.toPrimitive];if(o!==void 0){var h=o.call(r,n);if(typeof h!="object")return h;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(r)})(s,"string");return typeof i=="symbol"?i:i+""})(t))in a?Object.defineProperty(a,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):a[t]=e,a}function Vi(a,t){var e=Object.keys(a);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(a);t&&(s=s.filter((function(i){return Object.getOwnPropertyDescriptor(a,i).enumerable}))),e.push.apply(e,s)}return e}function m(a){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?Vi(Object(e),!0).forEach((function(s){p(a,s,e[s])})):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(e)):Vi(Object(e)).forEach((function(s){Object.defineProperty(a,s,Object.getOwnPropertyDescriptor(e,s))}))}return a}function N(a,t){if(a==null)return{};var e,s,i=(function(n,o){if(n==null)return{};var h={};for(var l in n)if({}.hasOwnProperty.call(n,l)){if(o.indexOf(l)>=0)continue;h[l]=n[l]}return h})(a,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(a);for(s=0;s<r.length;s++)e=r[s],t.indexOf(e)>=0||{}.propertyIsEnumerable.call(a,e)&&(i[e]=a[e])}return i}function Wt(a,t){return t||(t=a.slice(0)),Object.freeze(Object.defineProperties(a,{raw:{value:Object.freeze(t)}}))}class zi{constructor(){p(this,"browserShadowBlurConstant",1),p(this,"DPI",96),p(this,"devicePixelRatio",typeof window<"u"?window.devicePixelRatio:1),p(this,"perfLimitSizeTotal",2097152),p(this,"maxCacheSideLimit",4096),p(this,"minCacheSideLimit",256),p(this,"disableStyleCopyPaste",!1),p(this,"enableGLFiltering",!0),p(this,"textureSize",4096),p(this,"forceGLPutImageData",!1),p(this,"cachesBoundsOfCurve",!1),p(this,"fontPaths",{}),p(this,"NUM_FRACTION_DIGITS",4)}}const B=new class extends zi{constructor(a){super(),this.configure(a)}configure(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Object.assign(this,a)}addFonts(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.fontPaths=m(m({},this.fontPaths),a)}removeFonts(){(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).forEach((a=>{delete this.fontPaths[a]}))}clearFonts(){this.fontPaths={}}restoreDefaults(a){const t=new zi,e=a?.reduce(((s,i)=>(s[i]=t[i],s)),{})||t;this.configure(e)}},se=function(a){for(var t=arguments.length,e=new Array(t>1?t-1:0),s=1;s<t;s++)e[s-1]=arguments[s];return console[a]("fabric",...e)};class Rt extends Error{constructor(t,e){super("fabric: ".concat(t),e)}}class Yn extends Rt{constructor(t){super("".concat(t," 'options.signal' is in 'aborted' state"))}}class Vn{}class zn extends Vn{testPrecision(t,e){const s="precision ".concat(e,` float;
void main(){}`),i=t.createShader(t.FRAGMENT_SHADER);return!!i&&(t.shaderSource(i,s),t.compileShader(i),!!t.getShaderParameter(i,t.COMPILE_STATUS))}queryWebGL(t){const e=t.getContext("webgl");e&&(this.maxTextureSize=e.getParameter(e.MAX_TEXTURE_SIZE),this.GLPrecision=["highp","mediump","lowp"].find((s=>this.testPrecision(e,s))),e.getExtension("WEBGL_lose_context").loseContext(),se("log","WebGL: max texture size ".concat(this.maxTextureSize)))}isSupported(t){return!!this.maxTextureSize&&this.maxTextureSize>=t}}const Hn={};let Hi;const Bt=()=>Hi||(Hi={document,window,isTouchSupported:"ontouchstart"in window||"ontouchstart"in document||window&&window.navigator&&window.navigator.maxTouchPoints>0,WebGLProbe:new zn,dispose(){},copyPasteData:Hn}),Oe=()=>Bt().document,Rs=()=>Bt().window,Mr=()=>{var a;return Math.max((a=B.devicePixelRatio)!==null&&a!==void 0?a:Rs().devicePixelRatio,1)},Ie=new class{constructor(){p(this,"charWidthsCache",{}),p(this,"boundsOfCurveCache",{})}getFontCache(a){let{fontFamily:t,fontStyle:e,fontWeight:s}=a;t=t.toLowerCase(),this.charWidthsCache[t]||(this.charWidthsCache[t]={});const i=this.charWidthsCache[t],r="".concat(e.toLowerCase(),"_").concat((s+"").toLowerCase());return i[r]||(i[r]={}),i[r]}clearFontCache(a){(a=(a||"").toLowerCase())?this.charWidthsCache[a]&&delete this.charWidthsCache[a]:this.charWidthsCache={}}limitDimsByArea(a){const{perfLimitSizeTotal:t}=B,e=Math.sqrt(t*a);return[Math.floor(e),Math.floor(t/e)]}},hi="6.7.1";function bs(){}const Ue=Math.PI/2,Os=2*Math.PI,bi=Math.PI/180,pt=Object.freeze([1,0,0,1,0,0]),Ci=16,te=.4477152502,F="center",X="left",vt="top",li="bottom",Z="right",yt="none",Si=/\r?\n/,Er="moving",Bs="scaling",Pr="rotating",wi="rotate",jr="skewing",Xe="resizing",Gn="modifyPoly",Un="modifyPath",Ds="changed",Ws="scale",xt="scaleX",wt="scaleY",De="skewX",ke="skewY",nt="fill",_t="stroke",ks="modified",me="json",$s="svg",D=new class{constructor(){this[me]=new Map,this[$s]=new Map}has(a){return this[me].has(a)}getClass(a){const t=this[me].get(a);if(!t)throw new Rt("No class registered for ".concat(a));return t}setClass(a,t){t?this[me].set(t,a):(this[me].set(a.type,a),this[me].set(a.type.toLowerCase(),a))}getSVGClass(a){return this[$s].get(a)}setSVGClass(a,t){this[$s].set(t??a.type.toLowerCase(),a)}},Ms=new class extends Array{remove(a){const t=this.indexOf(a);t>-1&&this.splice(t,1)}cancelAll(){const a=this.splice(0);return a.forEach((t=>t.abort())),a}cancelByCanvas(a){if(!a)return[];const t=this.filter((e=>{var s;return e.target===a||typeof e.target=="object"&&((s=e.target)===null||s===void 0?void 0:s.canvas)===a}));return t.forEach((e=>e.abort())),t}cancelByTarget(a){if(!a)return[];const t=this.filter((e=>e.target===a));return t.forEach((e=>e.abort())),t}};class Nn{constructor(){p(this,"__eventListeners",{})}on(t,e){if(this.__eventListeners||(this.__eventListeners={}),typeof t=="object")return Object.entries(t).forEach((s=>{let[i,r]=s;this.on(i,r)})),()=>this.off(t);if(e){const s=t;return this.__eventListeners[s]||(this.__eventListeners[s]=[]),this.__eventListeners[s].push(e),()=>this.off(s,e)}return()=>!1}once(t,e){if(typeof t=="object"){const s=[];return Object.entries(t).forEach((i=>{let[r,n]=i;s.push(this.once(r,n))})),()=>s.forEach((i=>i()))}if(e){const s=this.on(t,(function(){for(var i=arguments.length,r=new Array(i),n=0;n<i;n++)r[n]=arguments[n];e.call(this,...r),s()}));return s}return()=>!1}_removeEventListener(t,e){if(this.__eventListeners[t])if(e){const s=this.__eventListeners[t],i=s.indexOf(e);i>-1&&s.splice(i,1)}else this.__eventListeners[t]=[]}off(t,e){if(this.__eventListeners)if(t===void 0)for(const s in this.__eventListeners)this._removeEventListener(s);else typeof t=="object"?Object.entries(t).forEach((s=>{let[i,r]=s;this._removeEventListener(i,r)})):this._removeEventListener(t,e)}fire(t,e){var s;if(!this.__eventListeners)return;const i=(s=this.__eventListeners[t])===null||s===void 0?void 0:s.concat();if(i)for(let r=0;r<i.length;r++)i[r].call(this,e||{})}}const ye=(a,t)=>{const e=a.indexOf(t);return e!==-1&&a.splice(e,1),a},qt=a=>{if(a===0)return 1;switch(Math.abs(a)/Ue){case 1:case 3:return 0;case 2:return-1}return Math.cos(a)},$t=a=>{if(a===0)return 0;const t=a/Ue,e=Math.sign(a);switch(t){case 1:return e;case 2:return 0;case 3:return-e}return Math.sin(a)};class y{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;typeof t=="object"?(this.x=t.x,this.y=t.y):(this.x=t,this.y=e)}add(t){return new y(this.x+t.x,this.y+t.y)}addEquals(t){return this.x+=t.x,this.y+=t.y,this}scalarAdd(t){return new y(this.x+t,this.y+t)}scalarAddEquals(t){return this.x+=t,this.y+=t,this}subtract(t){return new y(this.x-t.x,this.y-t.y)}subtractEquals(t){return this.x-=t.x,this.y-=t.y,this}scalarSubtract(t){return new y(this.x-t,this.y-t)}scalarSubtractEquals(t){return this.x-=t,this.y-=t,this}multiply(t){return new y(this.x*t.x,this.y*t.y)}scalarMultiply(t){return new y(this.x*t,this.y*t)}scalarMultiplyEquals(t){return this.x*=t,this.y*=t,this}divide(t){return new y(this.x/t.x,this.y/t.y)}scalarDivide(t){return new y(this.x/t,this.y/t)}scalarDivideEquals(t){return this.x/=t,this.y/=t,this}eq(t){return this.x===t.x&&this.y===t.y}lt(t){return this.x<t.x&&this.y<t.y}lte(t){return this.x<=t.x&&this.y<=t.y}gt(t){return this.x>t.x&&this.y>t.y}gte(t){return this.x>=t.x&&this.y>=t.y}lerp(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:.5;return e=Math.max(Math.min(1,e),0),new y(this.x+(t.x-this.x)*e,this.y+(t.y-this.y)*e)}distanceFrom(t){const e=this.x-t.x,s=this.y-t.y;return Math.sqrt(e*e+s*s)}midPointFrom(t){return this.lerp(t)}min(t){return new y(Math.min(this.x,t.x),Math.min(this.y,t.y))}max(t){return new y(Math.max(this.x,t.x),Math.max(this.y,t.y))}toString(){return"".concat(this.x,",").concat(this.y)}setXY(t,e){return this.x=t,this.y=e,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setFromPoint(t){return this.x=t.x,this.y=t.y,this}swap(t){const e=this.x,s=this.y;this.x=t.x,this.y=t.y,t.x=e,t.y=s}clone(){return new y(this.x,this.y)}rotate(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Ti;const s=$t(t),i=qt(t),r=this.subtract(e);return new y(r.x*i-r.y*s,r.x*s+r.y*i).add(e)}transform(t){let e=arguments.length>1&&arguments[1]!==void 0&&arguments[1];return new y(t[0]*this.x+t[2]*this.y+(e?0:t[4]),t[1]*this.x+t[3]*this.y+(e?0:t[5]))}}const Ti=new y(0,0),Cs=a=>!!a&&Array.isArray(a._objects);function Ar(a){class t extends a{constructor(){super(...arguments),p(this,"_objects",[])}_onObjectAdded(s){}_onObjectRemoved(s){}_onStackOrderChanged(s){}add(){for(var s=arguments.length,i=new Array(s),r=0;r<s;r++)i[r]=arguments[r];const n=this._objects.push(...i);return i.forEach((o=>this._onObjectAdded(o))),n}insertAt(s){for(var i=arguments.length,r=new Array(i>1?i-1:0),n=1;n<i;n++)r[n-1]=arguments[n];return this._objects.splice(s,0,...r),r.forEach((o=>this._onObjectAdded(o))),this._objects.length}remove(){const s=this._objects,i=[];for(var r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];return n.forEach((h=>{const l=s.indexOf(h);l!==-1&&(s.splice(l,1),i.push(h),this._onObjectRemoved(h))})),i}forEachObject(s){this.getObjects().forEach(((i,r,n)=>s(i,r,n)))}getObjects(){for(var s=arguments.length,i=new Array(s),r=0;r<s;r++)i[r]=arguments[r];return i.length===0?[...this._objects]:this._objects.filter((n=>n.isType(...i)))}item(s){return this._objects[s]}isEmpty(){return this._objects.length===0}size(){return this._objects.length}contains(s,i){return!!this._objects.includes(s)||!!i&&this._objects.some((r=>r instanceof t&&r.contains(s,!0)))}complexity(){return this._objects.reduce(((s,i)=>s+=i.complexity?i.complexity():0),0)}sendObjectToBack(s){return!(!s||s===this._objects[0])&&(ye(this._objects,s),this._objects.unshift(s),this._onStackOrderChanged(s),!0)}bringObjectToFront(s){return!(!s||s===this._objects[this._objects.length-1])&&(ye(this._objects,s),this._objects.push(s),this._onStackOrderChanged(s),!0)}sendObjectBackwards(s,i){if(!s)return!1;const r=this._objects.indexOf(s);if(r!==0){const n=this.findNewLowerIndex(s,r,i);return ye(this._objects,s),this._objects.splice(n,0,s),this._onStackOrderChanged(s),!0}return!1}bringObjectForward(s,i){if(!s)return!1;const r=this._objects.indexOf(s);if(r!==this._objects.length-1){const n=this.findNewUpperIndex(s,r,i);return ye(this._objects,s),this._objects.splice(n,0,s),this._onStackOrderChanged(s),!0}return!1}moveObjectTo(s,i){return s!==this._objects[i]&&(ye(this._objects,s),this._objects.splice(i,0,s),this._onStackOrderChanged(s),!0)}findNewLowerIndex(s,i,r){let n;if(r){n=i;for(let o=i-1;o>=0;--o)if(s.isOverlapping(this._objects[o])){n=o;break}}else n=i-1;return n}findNewUpperIndex(s,i,r){let n;if(r){n=i;for(let o=i+1;o<this._objects.length;++o)if(s.isOverlapping(this._objects[o])){n=o;break}}else n=i+1;return n}collectObjects(s){let{left:i,top:r,width:n,height:o}=s,{includeIntersecting:h=!0}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const l=[],c=new y(i,r),g=c.add(new y(n,o));for(let u=this._objects.length-1;u>=0;u--){const d=this._objects[u];d.selectable&&d.visible&&(h&&d.intersectsWithRect(c,g)||d.isContainedWithinRect(c,g)||h&&d.containsPoint(c)||h&&d.containsPoint(g))&&l.push(d)}return l}}return t}class Fr extends Nn{_setOptions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};for(const e in t)this.set(e,t[e])}_setObject(t){for(const e in t)this._set(e,t[e])}set(t,e){return typeof t=="object"?this._setObject(t):this._set(t,e),this}_set(t,e){this[t]=e}toggle(t){const e=this.get(t);return typeof e=="boolean"&&this.set(t,!e),this}get(t){return this[t]}}function Ss(a){return Rs().requestAnimationFrame(a)}function qn(a){return Rs().cancelAnimationFrame(a)}let $n=0;const ie=()=>$n++,Kt=()=>{const a=Oe().createElement("canvas");if(!a||a.getContext===void 0)throw new Rt("Failed to create `canvas` element");return a},Kn=()=>Oe().createElement("img"),Tt=a=>{const t=Kt();return t.width=a.width,t.height=a.height,t},Lr=(a,t,e)=>a.toDataURL("image/".concat(t),e),Ir=(a,t,e)=>new Promise(((s,i)=>{a.toBlob(s,"image/".concat(t),e)})),Q=a=>a*bi,Jt=a=>a/bi,Jn=a=>a.every(((t,e)=>t===pt[e])),mt=(a,t,e)=>new y(a).transform(t,e),jt=a=>{const t=1/(a[0]*a[3]-a[1]*a[2]),e=[t*a[3],-t*a[1],-t*a[2],t*a[0],0,0],{x:s,y:i}=new y(a[4],a[5]).transform(e,!0);return e[4]=-s,e[5]=-i,e},lt=(a,t,e)=>[a[0]*t[0]+a[2]*t[1],a[1]*t[0]+a[3]*t[1],a[0]*t[2]+a[2]*t[3],a[1]*t[2]+a[3]*t[3],e?0:a[0]*t[4]+a[2]*t[5]+a[4],e?0:a[1]*t[4]+a[3]*t[5]+a[5]],Oi=(a,t)=>a.reduceRight(((e,s)=>s&&e?lt(s,e,t):s||e),void 0)||pt.concat(),Rr=a=>{let[t,e]=a;return Math.atan2(e,t)},Es=a=>{const t=Rr(a),e=Math.pow(a[0],2)+Math.pow(a[1],2),s=Math.sqrt(e),i=(a[0]*a[3]-a[2]*a[1])/s,r=Math.atan2(a[0]*a[2]+a[1]*a[3],e);return{angle:Jt(t),scaleX:s,scaleY:i,skewX:Jt(r),skewY:0,translateX:a[4]||0,translateY:a[5]||0}},Ne=function(a){return[1,0,0,1,a,arguments.length>1&&arguments[1]!==void 0?arguments[1]:0]};function Me(){let{angle:a=0}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{x:t=0,y:e=0}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const s=Q(a),i=qt(s),r=$t(s);return[i,r,-r,i,t?t-(i*t-r*e):0,e?e-(r*t+i*e):0]}const Di=function(a){return[a,0,0,arguments.length>1&&arguments[1]!==void 0?arguments[1]:a,0,0]},Br=a=>Math.tan(Q(a)),Wr=a=>[1,0,Br(a),1,0,0],Xr=a=>[1,Br(a),0,1,0,0],Xs=a=>{let{scaleX:t=1,scaleY:e=1,flipX:s=!1,flipY:i=!1,skewX:r=0,skewY:n=0}=a,o=Di(s?-t:t,i?-e:e);return r&&(o=lt(o,Wr(r),!0)),n&&(o=lt(o,Xr(n),!0)),o},Zn=a=>{const{translateX:t=0,translateY:e=0,angle:s=0}=a;let i=Ne(t,e);s&&(i=lt(i,Me({angle:s})));const r=Xs(a);return Jn(r)||(i=lt(i,r)),i},ws=function(a){let{signal:t,crossOrigin:e=null}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return new Promise((function(s,i){if(t&&t.aborted)return i(new Yn("loadImage"));const r=Kn();let n;t&&(n=function(h){r.src="",i(h)},t.addEventListener("abort",n,{once:!0}));const o=function(){r.onload=r.onerror=null,n&&t?.removeEventListener("abort",n),s(r)};a?(r.onload=o,r.onerror=function(){n&&t?.removeEventListener("abort",n),i(new Rt("Error loading ".concat(r.src)))},e&&(r.crossOrigin=e),r.src=a):o()}))},Ye=function(a){let{signal:t,reviver:e=bs}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return new Promise(((s,i)=>{const r=[];t&&t.addEventListener("abort",i,{once:!0}),Promise.all(a.map((n=>D.getClass(n.type).fromObject(n,{signal:t}).then((o=>(e(n,o),r.push(o),o)))))).then(s).catch((n=>{r.forEach((o=>{o.dispose&&o.dispose()})),i(n)})).finally((()=>{t&&t.removeEventListener("abort",i)}))}))},Ys=function(a){let{signal:t}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return new Promise(((e,s)=>{const i=[];t&&t.addEventListener("abort",s,{once:!0});const r=Object.values(a).map((o=>o&&o.type&&D.has(o.type)?Ye([o],{signal:t}).then((h=>{let[l]=h;return i.push(l),l})):o)),n=Object.keys(a);Promise.all(r).then((o=>o.reduce(((h,l,c)=>(h[n[c]]=l,h)),{}))).then(e).catch((o=>{i.forEach((h=>{h.dispose&&h.dispose()})),s(o)})).finally((()=>{t&&t.removeEventListener("abort",s)}))}))},Ee=function(a){return(arguments.length>1&&arguments[1]!==void 0?arguments[1]:[]).reduce(((t,e)=>(e in a&&(t[e]=a[e]),t)),{})},ki=(a,t)=>Object.keys(a).reduce(((e,s)=>(t(a[s],s,a)&&(e[s]=a[s]),e)),{}),U=(a,t)=>parseFloat(Number(a).toFixed(t)),Ve=a=>"matrix("+a.map((t=>U(t,B.NUM_FRACTION_DIGITS))).join(" ")+")",St=a=>!!a&&a.toLive!==void 0,Gi=a=>!!a&&typeof a.toObject=="function",Ui=a=>!!a&&a.offsetX!==void 0&&"source"in a,ne=a=>!!a&&"multiSelectionStacking"in a;function Yr(a){const t=a&&Pt(a);let e=0,s=0;if(!a||!t)return{left:e,top:s};let i=a;const r=t.documentElement,n=t.body||{scrollLeft:0,scrollTop:0};for(;i&&(i.parentNode||i.host)&&(i=i.parentNode||i.host,i===t?(e=n.scrollLeft||r.scrollLeft||0,s=n.scrollTop||r.scrollTop||0):(e+=i.scrollLeft||0,s+=i.scrollTop||0),i.nodeType!==1||i.style.position!=="fixed"););return{left:e,top:s}}const Pt=a=>a.ownerDocument||null,Vr=a=>{var t;return((t=a.ownerDocument)===null||t===void 0?void 0:t.defaultView)||null},zr=function(a,t,e){let{width:s,height:i}=e,r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:1;a.width=s,a.height=i,r>1&&(a.setAttribute("width",(s*r).toString()),a.setAttribute("height",(i*r).toString()),t.scale(r,r))},ci=(a,t)=>{let{width:e,height:s}=t;e&&(a.style.width=typeof e=="number"?"".concat(e,"px"):e),s&&(a.style.height=typeof s=="number"?"".concat(s,"px"):s)};function Ni(a){return a.onselectstart!==void 0&&(a.onselectstart=()=>!1),a.style.userSelect=yt,a}class Hr{constructor(t){p(this,"_originalCanvasStyle",void 0),p(this,"lower",void 0);const e=this.createLowerCanvas(t);this.lower={el:e,ctx:e.getContext("2d")}}createLowerCanvas(t){const e=(s=t)&&s.getContext!==void 0?t:t&&Oe().getElementById(t)||Kt();var s;if(e.hasAttribute("data-fabric"))throw new Rt("Trying to initialize a canvas that has already been initialized. Did you forget to dispose the canvas?");return this._originalCanvasStyle=e.style.cssText,e.setAttribute("data-fabric","main"),e.classList.add("lower-canvas"),e}cleanupDOM(t){let{width:e,height:s}=t;const{el:i}=this.lower;i.classList.remove("lower-canvas"),i.removeAttribute("data-fabric"),i.setAttribute("width","".concat(e)),i.setAttribute("height","".concat(s)),i.style.cssText=this._originalCanvasStyle||"",this._originalCanvasStyle=void 0}setDimensions(t,e){const{el:s,ctx:i}=this.lower;zr(s,i,t,e)}setCSSDimensions(t){ci(this.lower.el,t)}calcOffset(){return(function(t){var e;const s=t&&Pt(t),i={left:0,top:0};if(!s)return i;const r=((e=Vr(t))===null||e===void 0?void 0:e.getComputedStyle(t,null))||{};i.left+=parseInt(r.borderLeftWidth,10)||0,i.top+=parseInt(r.borderTopWidth,10)||0,i.left+=parseInt(r.paddingLeft,10)||0,i.top+=parseInt(r.paddingTop,10)||0;let n={left:0,top:0};const o=s.documentElement;t.getBoundingClientRect!==void 0&&(n=t.getBoundingClientRect());const h=Yr(t);return{left:n.left+h.left-(o.clientLeft||0)+i.left,top:n.top+h.top-(o.clientTop||0)+i.top}})(this.lower.el)}dispose(){Bt().dispose(this.lower.el),delete this.lower}}const Qn={backgroundVpt:!0,backgroundColor:"",overlayVpt:!0,overlayColor:"",includeDefaultValues:!0,svgViewportTransformation:!0,renderOnAddRemove:!0,skipOffscreen:!0,enableRetinaScaling:!0,imageSmoothingEnabled:!0,controlsAboveOverlay:!1,allowTouchScrolling:!1,viewportTransform:[...pt]};class qe extends Ar(Fr){get lowerCanvasEl(){var t;return(t=this.elements.lower)===null||t===void 0?void 0:t.el}get contextContainer(){var t;return(t=this.elements.lower)===null||t===void 0?void 0:t.ctx}static getDefaults(){return qe.ownDefaults}constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),Object.assign(this,this.constructor.getDefaults()),this.set(e),this.initElements(t),this._setDimensionsImpl({width:this.width||this.elements.lower.el.width||0,height:this.height||this.elements.lower.el.height||0}),this.skipControlsDrawing=!1,this.viewportTransform=[...this.viewportTransform],this.calcViewportBoundaries()}initElements(t){this.elements=new Hr(t)}add(){const t=super.add(...arguments);return arguments.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),t}insertAt(t){for(var e=arguments.length,s=new Array(e>1?e-1:0),i=1;i<e;i++)s[i-1]=arguments[i];const r=super.insertAt(t,...s);return s.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),r}remove(){const t=super.remove(...arguments);return t.length>0&&this.renderOnAddRemove&&this.requestRenderAll(),t}_onObjectAdded(t){t.canvas&&t.canvas!==this&&(se("warn",`Canvas is trying to add an object that belongs to a different canvas.
Resulting to default behavior: removing object from previous canvas and adding to new canvas`),t.canvas.remove(t)),t._set("canvas",this),t.setCoords(),this.fire("object:added",{target:t}),t.fire("added",{target:this})}_onObjectRemoved(t){t._set("canvas",void 0),this.fire("object:removed",{target:t}),t.fire("removed",{target:this})}_onStackOrderChanged(){this.renderOnAddRemove&&this.requestRenderAll()}getRetinaScaling(){return this.enableRetinaScaling?Mr():1}calcOffset(){return this._offset=this.elements.calcOffset()}getWidth(){return this.width}getHeight(){return this.height}setWidth(t,e){return this.setDimensions({width:t},e)}setHeight(t,e){return this.setDimensions({height:t},e)}_setDimensionsImpl(t){let{cssOnly:e=!1,backstoreOnly:s=!1}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!e){const i=m({width:this.width,height:this.height},t);this.elements.setDimensions(i,this.getRetinaScaling()),this.hasLostContext=!0,this.width=i.width,this.height=i.height}s||this.elements.setCSSDimensions(t),this.calcOffset()}setDimensions(t,e){this._setDimensionsImpl(t,e),e&&e.cssOnly||this.requestRenderAll()}getZoom(){return this.viewportTransform[0]}setViewportTransform(t){this.viewportTransform=t,this.calcViewportBoundaries(),this.renderOnAddRemove&&this.requestRenderAll()}zoomToPoint(t,e){const s=t,i=[...this.viewportTransform],r=mt(t,jt(i));i[0]=e,i[3]=e;const n=mt(r,i);i[4]+=s.x-n.x,i[5]+=s.y-n.y,this.setViewportTransform(i)}setZoom(t){this.zoomToPoint(new y(0,0),t)}absolutePan(t){const e=[...this.viewportTransform];return e[4]=-t.x,e[5]=-t.y,this.setViewportTransform(e)}relativePan(t){return this.absolutePan(new y(-t.x-this.viewportTransform[4],-t.y-this.viewportTransform[5]))}getElement(){return this.elements.lower.el}clearContext(t){t.clearRect(0,0,this.width,this.height)}getContext(){return this.elements.lower.ctx}clear(){this.remove(...this.getObjects()),this.backgroundImage=void 0,this.overlayImage=void 0,this.backgroundColor="",this.overlayColor="",this.clearContext(this.getContext()),this.fire("canvas:cleared"),this.renderOnAddRemove&&this.requestRenderAll()}renderAll(){this.cancelRequestedRender(),this.destroyed||this.renderCanvas(this.getContext(),this._objects)}renderAndReset(){this.nextRenderHandle=0,this.renderAll()}requestRenderAll(){this.nextRenderHandle||this.disposed||this.destroyed||(this.nextRenderHandle=Ss((()=>this.renderAndReset())))}calcViewportBoundaries(){const t=this.width,e=this.height,s=jt(this.viewportTransform),i=mt({x:0,y:0},s),r=mt({x:t,y:e},s),n=i.min(r),o=i.max(r);return this.vptCoords={tl:n,tr:new y(o.x,n.y),bl:new y(n.x,o.y),br:o}}cancelRequestedRender(){this.nextRenderHandle&&(qn(this.nextRenderHandle),this.nextRenderHandle=0)}drawControls(t){}renderCanvas(t,e){if(this.destroyed)return;const s=this.viewportTransform,i=this.clipPath;this.calcViewportBoundaries(),this.clearContext(t),t.imageSmoothingEnabled=this.imageSmoothingEnabled,t.patternQuality="best",this.fire("before:render",{ctx:t}),this._renderBackground(t),t.save(),t.transform(s[0],s[1],s[2],s[3],s[4],s[5]),this._renderObjects(t,e),t.restore(),this.controlsAboveOverlay||this.skipControlsDrawing||this.drawControls(t),i&&(i._set("canvas",this),i.shouldCache(),i._transformDone=!0,i.renderCache({forClipping:!0}),this.drawClipPathOnCanvas(t,i)),this._renderOverlay(t),this.controlsAboveOverlay&&!this.skipControlsDrawing&&this.drawControls(t),this.fire("after:render",{ctx:t}),this.__cleanupTask&&(this.__cleanupTask(),this.__cleanupTask=void 0)}drawClipPathOnCanvas(t,e){const s=this.viewportTransform;t.save(),t.transform(...s),t.globalCompositeOperation="destination-in",e.transform(t),t.scale(1/e.zoomX,1/e.zoomY),t.drawImage(e._cacheCanvas,-e.cacheTranslationX,-e.cacheTranslationY),t.restore()}_renderObjects(t,e){for(let s=0,i=e.length;s<i;++s)e[s]&&e[s].render(t)}_renderBackgroundOrOverlay(t,e){const s=this["".concat(e,"Color")],i=this["".concat(e,"Image")],r=this.viewportTransform,n=this["".concat(e,"Vpt")];if(!s&&!i)return;const o=St(s);if(s){if(t.save(),t.beginPath(),t.moveTo(0,0),t.lineTo(this.width,0),t.lineTo(this.width,this.height),t.lineTo(0,this.height),t.closePath(),t.fillStyle=o?s.toLive(t):s,n&&t.transform(...r),o){t.transform(1,0,0,1,s.offsetX||0,s.offsetY||0);const h=s.gradientTransform||s.patternTransform;h&&t.transform(...h)}t.fill(),t.restore()}if(i){t.save();const{skipOffscreen:h}=this;this.skipOffscreen=n,n&&t.transform(...r),i.render(t),this.skipOffscreen=h,t.restore()}}_renderBackground(t){this._renderBackgroundOrOverlay(t,"background")}_renderOverlay(t){this._renderBackgroundOrOverlay(t,"overlay")}getCenter(){return{top:this.height/2,left:this.width/2}}getCenterPoint(){return new y(this.width/2,this.height/2)}centerObjectH(t){return this._centerObject(t,new y(this.getCenterPoint().x,t.getCenterPoint().y))}centerObjectV(t){return this._centerObject(t,new y(t.getCenterPoint().x,this.getCenterPoint().y))}centerObject(t){return this._centerObject(t,this.getCenterPoint())}viewportCenterObject(t){return this._centerObject(t,this.getVpCenter())}viewportCenterObjectH(t){return this._centerObject(t,new y(this.getVpCenter().x,t.getCenterPoint().y))}viewportCenterObjectV(t){return this._centerObject(t,new y(t.getCenterPoint().x,this.getVpCenter().y))}getVpCenter(){return mt(this.getCenterPoint(),jt(this.viewportTransform))}_centerObject(t,e){t.setXY(e,F,F),t.setCoords(),this.renderOnAddRemove&&this.requestRenderAll()}toDatalessJSON(t){return this.toDatalessObject(t)}toObject(t){return this._toObjectMethod("toObject",t)}toJSON(){return this.toObject()}toDatalessObject(t){return this._toObjectMethod("toDatalessObject",t)}_toObjectMethod(t,e){const s=this.clipPath,i=s&&!s.excludeFromExport?this._toObject(s,t,e):null;return m(m(m({version:hi},Ee(this,e)),{},{objects:this._objects.filter((r=>!r.excludeFromExport)).map((r=>this._toObject(r,t,e)))},this.__serializeBgOverlay(t,e)),i?{clipPath:i}:null)}_toObject(t,e,s){let i;this.includeDefaultValues||(i=t.includeDefaultValues,t.includeDefaultValues=!1);const r=t[e](s);return this.includeDefaultValues||(t.includeDefaultValues=!!i),r}__serializeBgOverlay(t,e){const s={},i=this.backgroundImage,r=this.overlayImage,n=this.backgroundColor,o=this.overlayColor;return St(n)?n.excludeFromExport||(s.background=n.toObject(e)):n&&(s.background=n),St(o)?o.excludeFromExport||(s.overlay=o.toObject(e)):o&&(s.overlay=o),i&&!i.excludeFromExport&&(s.backgroundImage=this._toObject(i,t,e)),r&&!r.excludeFromExport&&(s.overlayImage=this._toObject(r,t,e)),s}toSVG(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;t.reviver=e;const s=[];return this._setSVGPreamble(s,t),this._setSVGHeader(s,t),this.clipPath&&s.push('<g clip-path="url(#'.concat(this.clipPath.clipPathId,`)" >
`)),this._setSVGBgOverlayColor(s,"background"),this._setSVGBgOverlayImage(s,"backgroundImage",e),this._setSVGObjects(s,e),this.clipPath&&s.push(`</g>
`),this._setSVGBgOverlayColor(s,"overlay"),this._setSVGBgOverlayImage(s,"overlayImage",e),s.push("</svg>"),s.join("")}_setSVGPreamble(t,e){e.suppressPreamble||t.push('<?xml version="1.0" encoding="',e.encoding||"UTF-8",`" standalone="no" ?>
`,'<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" ',`"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
`)}_setSVGHeader(t,e){const s=e.width||"".concat(this.width),i=e.height||"".concat(this.height),r=B.NUM_FRACTION_DIGITS,n=e.viewBox;let o;if(n)o='viewBox="'.concat(n.x," ").concat(n.y," ").concat(n.width," ").concat(n.height,'" ');else if(this.svgViewportTransformation){const h=this.viewportTransform;o='viewBox="'.concat(U(-h[4]/h[0],r)," ").concat(U(-h[5]/h[3],r)," ").concat(U(this.width/h[0],r)," ").concat(U(this.height/h[3],r),'" ')}else o='viewBox="0 0 '.concat(this.width," ").concat(this.height,'" ');t.push("<svg ",'xmlns="http://www.w3.org/2000/svg" ','xmlns:xlink="http://www.w3.org/1999/xlink" ','version="1.1" ','width="',s,'" ','height="',i,'" ',o,`xml:space="preserve">
`,"<desc>Created with Fabric.js ",hi,`</desc>
`,`<defs>
`,this.createSVGFontFacesMarkup(),this.createSVGRefElementsMarkup(),this.createSVGClipPathMarkup(e),`</defs>
`)}createSVGClipPathMarkup(t){const e=this.clipPath;return e?(e.clipPathId="CLIPPATH_".concat(ie()),'<clipPath id="'.concat(e.clipPathId,`" >
`).concat(e.toClipPathSVG(t.reviver),`</clipPath>
`)):""}createSVGRefElementsMarkup(){return["background","overlay"].map((t=>{const e=this["".concat(t,"Color")];if(St(e)){const s=this["".concat(t,"Vpt")],i=this.viewportTransform,r={isType:()=>!1,width:this.width/(s?i[0]:1),height:this.height/(s?i[3]:1)};return e.toSVG(r,{additionalTransform:s?Ve(i):""})}})).join("")}createSVGFontFacesMarkup(){const t=[],e={},s=B.fontPaths;this._objects.forEach((function r(n){t.push(n),Cs(n)&&n._objects.forEach(r)})),t.forEach((r=>{if(!(n=r)||typeof n._renderText!="function")return;var n;const{styles:o,fontFamily:h}=r;!e[h]&&s[h]&&(e[h]=!0,o&&Object.values(o).forEach((l=>{Object.values(l).forEach((c=>{let{fontFamily:g=""}=c;!e[g]&&s[g]&&(e[g]=!0)}))})))}));const i=Object.keys(e).map((r=>`		@font-face {
			font-family: '`.concat(r,`';
			src: url('`).concat(s[r],`');
		}
`))).join("");return i?`	<style type="text/css"><![CDATA[
`.concat(i,`]]></style>
`):""}_setSVGObjects(t,e){this.forEachObject((s=>{s.excludeFromExport||this._setSVGObject(t,s,e)}))}_setSVGObject(t,e,s){t.push(e.toSVG(s))}_setSVGBgOverlayImage(t,e,s){const i=this[e];i&&!i.excludeFromExport&&i.toSVG&&t.push(i.toSVG(s))}_setSVGBgOverlayColor(t,e){const s=this["".concat(e,"Color")];if(s)if(St(s)){const i=s.repeat||"",r=this.width,n=this.height,o=this["".concat(e,"Vpt")]?Ve(jt(this.viewportTransform)):"";t.push('<rect transform="'.concat(o," translate(").concat(r/2,",").concat(n/2,')" x="').concat(s.offsetX-r/2,'" y="').concat(s.offsetY-n/2,'" width="').concat(i!=="repeat-y"&&i!=="no-repeat"||!Ui(s)?r:s.source.width,'" height="').concat(i!=="repeat-x"&&i!=="no-repeat"||!Ui(s)?n:s.source.height,'" fill="url(#SVGID_').concat(s.id,`)"></rect>
`))}else t.push('<rect x="0" y="0" width="100%" height="100%" ','fill="',s,'"',`></rect>
`)}loadFromJSON(t,e){let{signal:s}=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(!t)return Promise.reject(new Rt("`json` is undefined"));const i=typeof t=="string"?JSON.parse(t):t,{objects:r=[],backgroundImage:n,background:o,overlayImage:h,overlay:l,clipPath:c}=i,g=this.renderOnAddRemove;return this.renderOnAddRemove=!1,Promise.all([Ye(r,{reviver:e,signal:s}),Ys({backgroundImage:n,backgroundColor:o,overlayImage:h,overlayColor:l,clipPath:c},{signal:s})]).then((u=>{let[d,f]=u;return this.clear(),this.add(...d),this.set(i),this.set(f),this.renderOnAddRemove=g,this}))}clone(t){const e=this.toObject(t);return this.cloneWithoutData().loadFromJSON(e)}cloneWithoutData(){const t=Tt(this);return new this.constructor(t)}toDataURL(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{format:e="png",quality:s=1,multiplier:i=1,enableRetinaScaling:r=!1}=t,n=i*(r?this.getRetinaScaling():1);return Lr(this.toCanvasElement(n,t),e,s)}toBlob(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{format:e="png",quality:s=1,multiplier:i=1,enableRetinaScaling:r=!1}=t,n=i*(r?this.getRetinaScaling():1);return Ir(this.toCanvasElement(n,t),e,s)}toCanvasElement(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1,{width:e,height:s,left:i,top:r,filter:n}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const o=(e||this.width)*t,h=(s||this.height)*t,l=this.getZoom(),c=this.width,g=this.height,u=this.skipControlsDrawing,d=l*t,f=this.viewportTransform,v=[d,0,0,d,(f[4]-(i||0))*t,(f[5]-(r||0))*t],_=this.enableRetinaScaling,b=Tt({width:o,height:h}),C=n?this._objects.filter((O=>n(O))):this._objects;return this.enableRetinaScaling=!1,this.viewportTransform=v,this.width=o,this.height=h,this.skipControlsDrawing=!0,this.calcViewportBoundaries(),this.renderCanvas(b.getContext("2d"),C),this.viewportTransform=f,this.width=c,this.height=g,this.calcViewportBoundaries(),this.enableRetinaScaling=_,this.skipControlsDrawing=u,b}dispose(){return!this.disposed&&this.elements.cleanupDOM({width:this.width,height:this.height}),Ms.cancelByCanvas(this),this.disposed=!0,new Promise(((t,e)=>{const s=()=>{this.destroy(),t(!0)};s.kill=e,this.__cleanupTask&&this.__cleanupTask.kill("aborted"),this.destroyed?t(!1):this.nextRenderHandle?this.__cleanupTask=s:s()}))}destroy(){this.destroyed=!0,this.cancelRequestedRender(),this.forEachObject((t=>t.dispose())),this._objects=[],this.backgroundImage&&this.backgroundImage.dispose(),this.backgroundImage=void 0,this.overlayImage&&this.overlayImage.dispose(),this.overlayImage=void 0,this.elements.dispose()}toString(){return"#<Canvas (".concat(this.complexity(),"): { objects: ").concat(this._objects.length," }>")}}p(qe,"ownDefaults",Qn);const to=["touchstart","touchmove","touchend"],eo=a=>{const t=Yr(a.target),e=(function(s){const i=s.changedTouches;return i&&i[0]?i[0]:s})(a);return new y(e.clientX+t.left,e.clientY+t.top)},ui=a=>to.includes(a.type)||a.pointerType==="touch",qi=a=>{a.preventDefault(),a.stopPropagation()},Ut=a=>{let t=0,e=0,s=0,i=0;for(let r=0,n=a.length;r<n;r++){const{x:o,y:h}=a[r];(o>s||!r)&&(s=o),(o<t||!r)&&(t=o),(h>i||!r)&&(i=h),(h<e||!r)&&(e=h)}return{left:t,top:e,width:s-t,height:i-e}},so=["translateX","translateY","scaleX","scaleY"],io=(a,t)=>Ps(a,lt(t,a.calcOwnMatrix())),Ps=(a,t)=>{const e=Es(t),{translateX:s,translateY:i,scaleX:r,scaleY:n}=e,o=N(e,so),h=new y(s,i);a.flipX=!1,a.flipY=!1,Object.assign(a,o),a.set({scaleX:r,scaleY:n}),a.setPositionByOrigin(h,F,F)},ro=a=>{a.scaleX=1,a.scaleY=1,a.skewX=0,a.skewY=0,a.flipX=!1,a.flipY=!1,a.rotate(0)},Gr=a=>({scaleX:a.scaleX,scaleY:a.scaleY,skewX:a.skewX,skewY:a.skewY,angle:a.angle,left:a.left,flipX:a.flipX,flipY:a.flipY,top:a.top}),Mi=(a,t,e)=>{const s=a/2,i=t/2,r=[new y(-s,-i),new y(s,-i),new y(-s,i),new y(s,i)].map((o=>o.transform(e))),n=Ut(r);return new y(n.width,n.height)},Vs=function(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:pt;return lt(jt(arguments.length>1&&arguments[1]!==void 0?arguments[1]:pt),a)},Ce=function(a){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pt,e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:pt;return a.transform(Vs(t,e))},no=function(a){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:pt,e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:pt;return a.transform(Vs(t,e),!0)},oo=(a,t,e)=>{const s=Vs(t,e);return Ps(a,lt(s,a.calcOwnMatrix())),s},Ur=(a,t)=>{var e;const{transform:{target:s}}=t;(e=s.canvas)===null||e===void 0||e.fire("object:".concat(a),m(m({},t),{},{target:s})),s.fire(a,t)},ao={left:-.5,top:-.5,center:0,bottom:.5,right:.5},st=a=>typeof a=="string"?ao[a]:a-.5,js="not-allowed";function Nr(a){return st(a.originX)===st(F)&&st(a.originY)===st(F)}function $i(a){return .5-st(a)}const Ft=(a,t)=>a[t],qr=(a,t,e,s)=>({e:a,transform:t,pointer:new y(e,s)});function $r(a,t){const e=a.getTotalAngle()+Jt(Math.atan2(t.y,t.x))+360;return Math.round(e%360/45)}function Ei(a,t,e,s,i){var r;let{target:n,corner:o}=a;const h=n.controls[o],l=((r=n.canvas)===null||r===void 0?void 0:r.getZoom())||1,c=n.padding/l,g=(function(u,d,f,v){const _=u.getRelativeCenterPoint(),b=f!==void 0&&v!==void 0?u.translateToGivenOrigin(_,F,F,f,v):new y(u.left,u.top);return(u.angle?d.rotate(-Q(u.angle),_):d).subtract(b)})(n,new y(s,i),t,e);return g.x>=c&&(g.x-=c),g.x<=-c&&(g.x+=c),g.y>=c&&(g.y-=c),g.y<=c&&(g.y+=c),g.x-=h.offsetX,g.y-=h.offsetY,g}const ho=(a,t,e,s)=>{const{target:i,offsetX:r,offsetY:n}=t,o=e-r,h=s-n,l=!Ft(i,"lockMovementX")&&i.left!==o,c=!Ft(i,"lockMovementY")&&i.top!==h;return l&&i.set(X,o),c&&i.set(vt,h),(l||c)&&Ur(Er,qr(a,t,e,s)),l||c},Ki={aliceblue:"#F0F8FF",antiquewhite:"#FAEBD7",aqua:"#0FF",aquamarine:"#7FFFD4",azure:"#F0FFFF",beige:"#F5F5DC",bisque:"#FFE4C4",black:"#000",blanchedalmond:"#FFEBCD",blue:"#00F",blueviolet:"#8A2BE2",brown:"#A52A2A",burlywood:"#DEB887",cadetblue:"#5F9EA0",chartreuse:"#7FFF00",chocolate:"#D2691E",coral:"#FF7F50",cornflowerblue:"#6495ED",cornsilk:"#FFF8DC",crimson:"#DC143C",cyan:"#0FF",darkblue:"#00008B",darkcyan:"#008B8B",darkgoldenrod:"#B8860B",darkgray:"#A9A9A9",darkgrey:"#A9A9A9",darkgreen:"#006400",darkkhaki:"#BDB76B",darkmagenta:"#8B008B",darkolivegreen:"#556B2F",darkorange:"#FF8C00",darkorchid:"#9932CC",darkred:"#8B0000",darksalmon:"#E9967A",darkseagreen:"#8FBC8F",darkslateblue:"#483D8B",darkslategray:"#2F4F4F",darkslategrey:"#2F4F4F",darkturquoise:"#00CED1",darkviolet:"#9400D3",deeppink:"#FF1493",deepskyblue:"#00BFFF",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1E90FF",firebrick:"#B22222",floralwhite:"#FFFAF0",forestgreen:"#228B22",fuchsia:"#F0F",gainsboro:"#DCDCDC",ghostwhite:"#F8F8FF",gold:"#FFD700",goldenrod:"#DAA520",gray:"#808080",grey:"#808080",green:"#008000",greenyellow:"#ADFF2F",honeydew:"#F0FFF0",hotpink:"#FF69B4",indianred:"#CD5C5C",indigo:"#4B0082",ivory:"#FFFFF0",khaki:"#F0E68C",lavender:"#E6E6FA",lavenderblush:"#FFF0F5",lawngreen:"#7CFC00",lemonchiffon:"#FFFACD",lightblue:"#ADD8E6",lightcoral:"#F08080",lightcyan:"#E0FFFF",lightgoldenrodyellow:"#FAFAD2",lightgray:"#D3D3D3",lightgrey:"#D3D3D3",lightgreen:"#90EE90",lightpink:"#FFB6C1",lightsalmon:"#FFA07A",lightseagreen:"#20B2AA",lightskyblue:"#87CEFA",lightslategray:"#789",lightslategrey:"#789",lightsteelblue:"#B0C4DE",lightyellow:"#FFFFE0",lime:"#0F0",limegreen:"#32CD32",linen:"#FAF0E6",magenta:"#F0F",maroon:"#800000",mediumaquamarine:"#66CDAA",mediumblue:"#0000CD",mediumorchid:"#BA55D3",mediumpurple:"#9370DB",mediumseagreen:"#3CB371",mediumslateblue:"#7B68EE",mediumspringgreen:"#00FA9A",mediumturquoise:"#48D1CC",mediumvioletred:"#C71585",midnightblue:"#191970",mintcream:"#F5FFFA",mistyrose:"#FFE4E1",moccasin:"#FFE4B5",navajowhite:"#FFDEAD",navy:"#000080",oldlace:"#FDF5E6",olive:"#808000",olivedrab:"#6B8E23",orange:"#FFA500",orangered:"#FF4500",orchid:"#DA70D6",palegoldenrod:"#EEE8AA",palegreen:"#98FB98",paleturquoise:"#AFEEEE",palevioletred:"#DB7093",papayawhip:"#FFEFD5",peachpuff:"#FFDAB9",peru:"#CD853F",pink:"#FFC0CB",plum:"#DDA0DD",powderblue:"#B0E0E6",purple:"#800080",rebeccapurple:"#639",red:"#F00",rosybrown:"#BC8F8F",royalblue:"#4169E1",saddlebrown:"#8B4513",salmon:"#FA8072",sandybrown:"#F4A460",seagreen:"#2E8B57",seashell:"#FFF5EE",sienna:"#A0522D",silver:"#C0C0C0",skyblue:"#87CEEB",slateblue:"#6A5ACD",slategray:"#708090",slategrey:"#708090",snow:"#FFFAFA",springgreen:"#00FF7F",steelblue:"#4682B4",tan:"#D2B48C",teal:"#008080",thistle:"#D8BFD8",tomato:"#FF6347",turquoise:"#40E0D0",violet:"#EE82EE",wheat:"#F5DEB3",white:"#FFF",whitesmoke:"#F5F5F5",yellow:"#FF0",yellowgreen:"#9ACD32"},Ks=(a,t,e)=>(e<0&&(e+=1),e>1&&(e-=1),e<1/6?a+6*(t-a)*e:e<.5?t:e<2/3?a+(t-a)*(2/3-e)*6:a),Ji=(a,t,e,s)=>{a/=255,t/=255,e/=255;const i=Math.max(a,t,e),r=Math.min(a,t,e);let n,o;const h=(i+r)/2;if(i===r)n=o=0;else{const l=i-r;switch(o=h>.5?l/(2-i-r):l/(i+r),i){case a:n=(t-e)/l+(t<e?6:0);break;case t:n=(e-a)/l+2;break;case e:n=(a-t)/l+4}n/=6}return[Math.round(360*n),Math.round(100*o),Math.round(100*h),s]},Zi=function(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"1";return parseFloat(a)/(a.endsWith("%")?100:1)},Ke=a=>Math.min(Math.round(a),255).toString(16).toUpperCase().padStart(2,"0"),Qi=a=>{let[t,e,s,i=1]=a;const r=Math.round(.3*t+.59*e+.11*s);return[r,r,r,i]};class z{constructor(t){if(p(this,"isUnrecognised",!1),t)if(t instanceof z)this.setSource([...t._source]);else if(Array.isArray(t)){const[e,s,i,r=1]=t;this.setSource([e,s,i,r])}else this.setSource(this._tryParsingColor(t));else this.setSource([0,0,0,1])}_tryParsingColor(t){return(t=t.toLowerCase())in Ki&&(t=Ki[t]),t==="transparent"?[255,255,255,0]:z.sourceFromHex(t)||z.sourceFromRgb(t)||z.sourceFromHsl(t)||(this.isUnrecognised=!0)&&[0,0,0,1]}getSource(){return this._source}setSource(t){this._source=t}toRgb(){const[t,e,s]=this.getSource();return"rgb(".concat(t,",").concat(e,",").concat(s,")")}toRgba(){return"rgba(".concat(this.getSource().join(","),")")}toHsl(){const[t,e,s]=Ji(...this.getSource());return"hsl(".concat(t,",").concat(e,"%,").concat(s,"%)")}toHsla(){const[t,e,s,i]=Ji(...this.getSource());return"hsla(".concat(t,",").concat(e,"%,").concat(s,"%,").concat(i,")")}toHex(){return this.toHexa().slice(0,6)}toHexa(){const[t,e,s,i]=this.getSource();return"".concat(Ke(t)).concat(Ke(e)).concat(Ke(s)).concat(Ke(Math.round(255*i)))}getAlpha(){return this.getSource()[3]}setAlpha(t){return this._source[3]=t,this}toGrayscale(){return this.setSource(Qi(this.getSource())),this}toBlackWhite(t){const[e,,,s]=Qi(this.getSource()),i=e<(t||127)?0:255;return this.setSource([i,i,i,s]),this}overlayWith(t){t instanceof z||(t=new z(t));const e=this.getSource(),s=t.getSource(),[i,r,n]=e.map(((o,h)=>Math.round(.5*o+.5*s[h])));return this.setSource([i,r,n,e[3]]),this}static fromRgb(t){return z.fromRgba(t)}static fromRgba(t){return new z(z.sourceFromRgb(t))}static sourceFromRgb(t){const e=t.match(/^rgba?\(\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*(?:\s*[,/]\s*(\d{0,3}(?:\.\d+)?%?)\s*)?\)$/i);if(e){const[s,i,r]=e.slice(1,4).map((n=>{const o=parseFloat(n);return n.endsWith("%")?Math.round(2.55*o):o}));return[s,i,r,Zi(e[4])]}}static fromHsl(t){return z.fromHsla(t)}static fromHsla(t){return new z(z.sourceFromHsl(t))}static sourceFromHsl(t){const e=t.match(/^hsla?\(\s*([+-]?\d{0,3}(?:\.\d+)?(?:deg|turn|rad)?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*[\s|,]\s*(\d{0,3}(?:\.\d+)?%?)\s*(?:\s*[,/]\s*(\d*(?:\.\d+)?%?)\s*)?\)$/i);if(!e)return;const s=(z.parseAngletoDegrees(e[1])%360+360)%360/360,i=parseFloat(e[2])/100,r=parseFloat(e[3])/100;let n,o,h;if(i===0)n=o=h=r;else{const l=r<=.5?r*(i+1):r+i-r*i,c=2*r-l;n=Ks(c,l,s+1/3),o=Ks(c,l,s),h=Ks(c,l,s-1/3)}return[Math.round(255*n),Math.round(255*o),Math.round(255*h),Zi(e[4])]}static fromHex(t){return new z(z.sourceFromHex(t))}static sourceFromHex(t){if(t.match(/^#?(([0-9a-f]){3,4}|([0-9a-f]{2}){3,4})$/i)){const e=t.slice(t.indexOf("#")+1);let s;s=e.length<=4?e.split("").map((h=>h+h)):e.match(/.{2}/g);const[i,r,n,o=255]=s.map((h=>parseInt(h,16)));return[i,r,n,o/255]}}static parseAngletoDegrees(t){const e=t.toLowerCase(),s=parseFloat(e);return e.includes("rad")?Jt(s):e.includes("turn")?360*s:s}}const Se=function(a){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Ci;const e=/\D{0,2}$/.exec(a),s=parseFloat(a),i=B.DPI;switch(e?.[0]){case"mm":return s*i/25.4;case"cm":return s*i/2.54;case"in":return s*i;case"pt":return s*i/72;case"pc":return s*i/72*12;case"em":return s*t;default:return s}},lo=a=>{const[t,e]=a.trim().split(" "),[s,i]=(r=t)&&r!==yt?[r.slice(1,4),r.slice(5,8)]:r===yt?[r,r]:["Mid","Mid"];var r;return{meetOrSlice:e||"meet",alignX:s,alignY:i}},ze=function(a,t){let e,s,i=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2];if(t)if(t.toLive)e="url(#SVGID_".concat(t.id,")");else{const r=new z(t),n=r.getAlpha();e=r.toRgb(),n!==1&&(s=n.toString())}else e="none";return i?"".concat(a,": ").concat(e,"; ").concat(s?"".concat(a,"-opacity: ").concat(s,"; "):""):"".concat(a,'="').concat(e,'" ').concat(s?"".concat(a,'-opacity="').concat(s,'" '):"")};class Kr{getSvgStyles(t){const e=this.fillRule?this.fillRule:"nonzero",s=this.strokeWidth?this.strokeWidth:"0",i=this.strokeDashArray?this.strokeDashArray.join(" "):yt,r=this.strokeDashOffset?this.strokeDashOffset:"0",n=this.strokeLineCap?this.strokeLineCap:"butt",o=this.strokeLineJoin?this.strokeLineJoin:"miter",h=this.strokeMiterLimit?this.strokeMiterLimit:"4",l=this.opacity!==void 0?this.opacity:"1",c=this.visible?"":" visibility: hidden;",g=t?"":this.getSvgFilter(),u=ze(nt,this.fill);return[ze(_t,this.stroke),"stroke-width: ",s,"; ","stroke-dasharray: ",i,"; ","stroke-linecap: ",n,"; ","stroke-dashoffset: ",r,"; ","stroke-linejoin: ",o,"; ","stroke-miterlimit: ",h,"; ",u,"fill-rule: ",e,"; ","opacity: ",l,";",g,c].join("")}getSvgFilter(){return this.shadow?"filter: url(#SVGID_".concat(this.shadow.id,");"):""}getSvgCommons(){return[this.id?'id="'.concat(this.id,'" '):"",this.clipPath?'clip-path="url(#'.concat(this.clipPath.clipPathId,')" '):""].join("")}getSvgTransform(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";const s=t?this.calcTransformMatrix():this.calcOwnMatrix(),i='transform="'.concat(Ve(s));return"".concat(i).concat(e,'" ')}_toSVG(t){return[""]}toSVG(t){return this._createBaseSVGMarkup(this._toSVG(t),{reviver:t})}toClipPathSVG(t){return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(t),{reviver:t})}_createBaseClipPathSVGMarkup(t){let{reviver:e,additionalTransform:s=""}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const i=[this.getSvgTransform(!0,s),this.getSvgCommons()].join(""),r=t.indexOf("COMMON_PARTS");return t[r]=i,e?e(t.join("")):t.join("")}_createBaseSVGMarkup(t){let{noStyle:e,reviver:s,withShadow:i,additionalTransform:r}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const n=e?"":'style="'.concat(this.getSvgStyles(),'" '),o=i?'style="'.concat(this.getSvgFilter(),'" '):"",h=this.clipPath,l=this.strokeUniform?'vector-effect="non-scaling-stroke" ':"",c=h&&h.absolutePositioned,g=this.stroke,u=this.fill,d=this.shadow,f=[],v=t.indexOf("COMMON_PARTS");let _;h&&(h.clipPathId="CLIPPATH_".concat(ie()),_='<clipPath id="'.concat(h.clipPathId,`" >
`).concat(h.toClipPathSVG(s),`</clipPath>
`)),c&&f.push("<g ",o,this.getSvgCommons(),` >
`),f.push("<g ",this.getSvgTransform(!1),c?"":o+this.getSvgCommons(),` >
`);const b=[n,l,e?"":this.addPaintOrder()," ",r?'transform="'.concat(r,'" '):""].join("");return t[v]=b,St(u)&&f.push(u.toSVG(this)),St(g)&&f.push(g.toSVG(this)),d&&f.push(d.toSVG(this)),h&&f.push(_),f.push(t.join("")),f.push(`</g>
`),c&&f.push(`</g>
`),s?s(f.join("")):f.join("")}addPaintOrder(){return this.paintFirst!==nt?' paint-order="'.concat(this.paintFirst,'" '):""}}function zs(a){return new RegExp("^("+a.join("|")+")\\b","i")}const ge="textDecorationThickness",Jr=["fontSize","fontWeight","fontFamily","fontStyle"],Zr=["underline","overline","linethrough"],Qr=[...Jr,"lineHeight","text","charSpacing","textAlign","styles","path","pathStartOffset","pathSide","pathAlign"],tn=[...Qr,...Zr,"textBackgroundColor","direction",ge],co=[...Jr,...Zr,_t,"strokeWidth",nt,"deltaY","textBackgroundColor",ge],uo={_reNewline:Si,_reSpacesAndTabs:/[ \t\r]/g,_reSpaceAndTab:/[ \t\r]/,_reWords:/\S+/g,fontSize:40,fontWeight:"normal",fontFamily:"Times New Roman",underline:!1,overline:!1,linethrough:!1,textAlign:X,fontStyle:"normal",lineHeight:1.16,textBackgroundColor:"",stroke:null,shadow:null,path:void 0,pathStartOffset:0,pathSide:X,pathAlign:"baseline",charSpacing:0,deltaY:0,direction:"ltr",CACHE_FONT_SIZE:400,MIN_TEXT_WIDTH:2,superscript:{size:.6,baseline:-.35},subscript:{size:.6,baseline:.11},_fontSizeFraction:.222,offsets:{underline:.1,linethrough:-.28167,overline:-.81333},_fontSizeMult:1.13,[ge]:66.667},It="justify",As="justify-left",Re="justify-right",Be="justify-center";var tr,er,sr;const At=String.raw(tr||(tr=Wt(["[-+]?(?:d*.d+|d+.?)(?:[eE][-+]?d+)?"],["[-+]?(?:\\d*\\.\\d+|\\d+\\.?)(?:[eE][-+]?\\d+)?"]))),Js=String.raw(er||(er=Wt(["(?:s*,?s+|s*,s*)"],["(?:\\s*,?\\s+|\\s*,\\s*)"]))),go=new RegExp("(normal|italic)?\\s*(normal|small-caps)?\\s*(normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900)?\\s*("+At+"(?:px|cm|mm|em|pt|pc|in)*)(?:\\/(normal|"+At+"))?\\s+(.*)"),fo={cx:X,x:X,r:"radius",cy:vt,y:vt,display:"visible",visibility:"visible",transform:"transformMatrix","fill-opacity":"fillOpacity","fill-rule":"fillRule","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","letter-spacing":"charSpacing","paint-order":"paintFirst","stroke-dasharray":"strokeDashArray","stroke-dashoffset":"strokeDashOffset","stroke-linecap":"strokeLineCap","stroke-linejoin":"strokeLineJoin","stroke-miterlimit":"strokeMiterLimit","stroke-opacity":"strokeOpacity","stroke-width":"strokeWidth","text-decoration":"textDecoration","text-anchor":"textAnchor",opacity:"opacity","clip-path":"clipPath","clip-rule":"clipRule","vector-effect":"strokeUniform","image-rendering":"imageSmoothing","text-decoration-thickness":ge},Zs="font-size",Qs="clip-path";zs(["path","circle","polygon","polyline","ellipse","rect","line","image","text"]);zs(["symbol","image","marker","pattern","view","svg"]);const ir=zs(["symbol","g","a","svg","clipPath","defs"]);new RegExp(String.raw(sr||(sr=Wt(["^s*(",")","(",")","(",")","(",")s*$"],["^\\s*(",")","(",")","(",")","(",")\\s*$"])),At,Js,At,Js,At,Js,At));const po=new y(1,0),en=new y,sn=(a,t)=>a.rotate(t),gi=(a,t)=>new y(t).subtract(a),di=a=>a.distanceFrom(en),fi=(a,t)=>Math.atan2(We(a,t),vo(a,t)),mo=a=>fi(po,a),Pi=a=>a.eq(en)?a:a.scalarDivide(di(a)),rn=function(a){let t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1];return Pi(new y(-a.y,a.x).scalarMultiply(t?1:-1))},We=(a,t)=>a.x*t.y-a.y*t.x,vo=(a,t)=>a.x*t.x+a.y*t.y,rr=(a,t,e)=>{if(a.eq(t)||a.eq(e))return!0;const s=We(t,e),i=We(t,a),r=We(e,a);return s>=0?i>=0&&r<=0:!(i<=0&&r>=0)},nr="(-?\\d+(?:\\.\\d*)?(?:px)?(?:\\s?|$))?",or=new RegExp("(?:\\s|^)"+nr+nr+"("+At+"?(?:px)?)?(?:\\s?|$)(?:$|\\s)");class Nt{constructor(t){const e=typeof t=="string"?Nt.parseShadow(t):t;Object.assign(this,Nt.ownDefaults,e),this.id=ie()}static parseShadow(t){const e=t.trim(),[,s=0,i=0,r=0]=(or.exec(e)||[]).map((n=>parseFloat(n)||0));return{color:(e.replace(or,"")||"rgb(0,0,0)").trim(),offsetX:s,offsetY:i,blur:r}}toString(){return[this.offsetX,this.offsetY,this.blur,this.color].join("px ")}toSVG(t){const e=sn(new y(this.offsetX,this.offsetY),Q(-t.angle)),s=new z(this.color);let i=40,r=40;return t.width&&t.height&&(i=100*U((Math.abs(e.x)+this.blur)/t.width,B.NUM_FRACTION_DIGITS)+20,r=100*U((Math.abs(e.y)+this.blur)/t.height,B.NUM_FRACTION_DIGITS)+20),t.flipX&&(e.x*=-1),t.flipY&&(e.y*=-1),'<filter id="SVGID_'.concat(this.id,'" y="-').concat(r,'%" height="').concat(100+2*r,'%" x="-').concat(i,'%" width="').concat(100+2*i,`%" >
	<feGaussianBlur in="SourceAlpha" stdDeviation="`).concat(U(this.blur?this.blur/2:0,B.NUM_FRACTION_DIGITS),`"></feGaussianBlur>
	<feOffset dx="`).concat(U(e.x,B.NUM_FRACTION_DIGITS),'" dy="').concat(U(e.y,B.NUM_FRACTION_DIGITS),`" result="oBlur" ></feOffset>
	<feFlood flood-color="`).concat(s.toRgb(),'" flood-opacity="').concat(s.getAlpha(),`"/>
	<feComposite in2="oBlur" operator="in" />
	<feMerge>
		<feMergeNode></feMergeNode>
		<feMergeNode in="SourceGraphic"></feMergeNode>
	</feMerge>
</filter>
`)}toObject(){const t={color:this.color,blur:this.blur,offsetX:this.offsetX,offsetY:this.offsetY,affectStroke:this.affectStroke,nonScaling:this.nonScaling,type:this.constructor.type},e=Nt.ownDefaults;return this.includeDefaultValues?t:ki(t,((s,i)=>s!==e[i]))}static async fromObject(t){return new this(t)}}p(Nt,"ownDefaults",{color:"rgb(0,0,0)",blur:0,offsetX:0,offsetY:0,affectStroke:!1,includeDefaultValues:!0,nonScaling:!1}),p(Nt,"type","shadow"),D.setClass(Nt,"shadow");const Te=(a,t,e)=>Math.max(a,Math.min(t,e)),yo=[vt,X,xt,wt,"flipX","flipY","originX","originY","angle","opacity","globalCompositeOperation","shadow","visible",De,ke],Zt=[nt,_t,"strokeWidth","strokeDashArray","width","height","paintFirst","strokeUniform","strokeLineCap","strokeDashOffset","strokeLineJoin","strokeMiterLimit","backgroundColor","clipPath"],_o={top:0,left:0,width:0,height:0,angle:0,flipX:!1,flipY:!1,scaleX:1,scaleY:1,minScaleLimit:0,skewX:0,skewY:0,originX:X,originY:vt,strokeWidth:1,strokeUniform:!1,padding:0,opacity:1,paintFirst:nt,fill:"rgb(0,0,0)",fillRule:"nonzero",stroke:null,strokeDashArray:null,strokeDashOffset:0,strokeLineCap:"butt",strokeLineJoin:"miter",strokeMiterLimit:4,globalCompositeOperation:"source-over",backgroundColor:"",shadow:null,visible:!0,includeDefaultValues:!0,excludeFromExport:!1,objectCaching:!0,clipPath:void 0,inverted:!1,absolutePositioned:!1,centeredRotation:!0,centeredScaling:!1,dirty:!0},xo=(a,t,e,s)=>-e*Math.cos(a/s*Ue)+e+t,bo=()=>!1;class ji{constructor(t){let{startValue:e,byValue:s,duration:i=500,delay:r=0,easing:n=xo,onStart:o=bs,onChange:h=bs,onComplete:l=bs,abort:c=bo,target:g}=t;p(this,"_state","pending"),p(this,"durationProgress",0),p(this,"valueProgress",0),this.tick=this.tick.bind(this),this.duration=i,this.delay=r,this.easing=n,this._onStart=o,this._onChange=h,this._onComplete=l,this._abort=c,this.target=g,this.startValue=e,this.byValue=s,this.value=this.startValue,this.endValue=Object.freeze(this.calculate(this.duration).value)}get state(){return this._state}isDone(){return this._state==="aborted"||this._state==="completed"}start(){const t=e=>{this._state==="pending"&&(this.startTime=e||+new Date,this._state="running",this._onStart(),this.tick(this.startTime))};this.register(),this.delay>0?setTimeout((()=>Ss(t)),this.delay):Ss(t)}tick(t){const e=(t||+new Date)-this.startTime,s=Math.min(e,this.duration);this.durationProgress=s/this.duration;const{value:i,valueProgress:r}=this.calculate(s);this.value=Object.freeze(i),this.valueProgress=r,this._state!=="aborted"&&(this._abort(this.value,this.valueProgress,this.durationProgress)?(this._state="aborted",this.unregister()):e>=this.duration?(this.durationProgress=this.valueProgress=1,this._onChange(this.endValue,this.valueProgress,this.durationProgress),this._state="completed",this._onComplete(this.endValue,this.valueProgress,this.durationProgress),this.unregister()):(this._onChange(this.value,this.valueProgress,this.durationProgress),Ss(this.tick)))}register(){Ms.push(this)}unregister(){Ms.remove(this)}abort(){this._state="aborted",this.unregister()}}const Co=["startValue","endValue"];class So extends ji{constructor(t){let{startValue:e=0,endValue:s=100}=t;super(m(m({},N(t,Co)),{},{startValue:e,byValue:s-e}))}calculate(t){const e=this.easing(t,this.startValue,this.byValue,this.duration);return{value:e,valueProgress:Math.abs((e-this.startValue)/this.byValue)}}}const wo=["startValue","endValue"];class To extends ji{constructor(t){let{startValue:e=[0],endValue:s=[100]}=t;super(m(m({},N(t,wo)),{},{startValue:e,byValue:s.map(((i,r)=>i-e[r]))}))}calculate(t){const e=this.startValue.map(((s,i)=>this.easing(t,s,this.byValue[i],this.duration,i)));return{value:e,valueProgress:Math.abs((e[0]-this.startValue[0])/this.byValue[0])}}}const Oo=["startValue","endValue","easing","onChange","onComplete","abort"],Do=(a,t,e,s)=>t+e*(1-Math.cos(a/s*Ue)),ti=a=>a&&((t,e,s)=>a(new z(t).toRgba(),e,s));class ko extends ji{constructor(t){let{startValue:e,endValue:s,easing:i=Do,onChange:r,onComplete:n,abort:o}=t,h=N(t,Oo);const l=new z(e).getSource(),c=new z(s).getSource();super(m(m({},h),{},{startValue:l,byValue:c.map(((g,u)=>g-l[u])),easing:i,onChange:ti(r),onComplete:ti(n),abort:ti(o)}))}calculate(t){const[e,s,i,r]=this.startValue.map(((o,h)=>this.easing(t,o,this.byValue[h],this.duration,h))),n=[...[e,s,i].map(Math.round),Te(0,r,1)];return{value:n,valueProgress:n.map(((o,h)=>this.byValue[h]!==0?Math.abs((o-this.startValue[h])/this.byValue[h]):0)).find((o=>o!==0))||0}}}function nn(a){const t=(e=>Array.isArray(e.startValue)||Array.isArray(e.endValue))(a)?new To(a):new So(a);return t.start(),t}function Mo(a){const t=new ko(a);return t.start(),t}class q{constructor(t){this.status=t,this.points=[]}includes(t){return this.points.some((e=>e.eq(t)))}append(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];return this.points=this.points.concat(e.filter((i=>!this.includes(i)))),this}static isPointContained(t,e,s){let i=arguments.length>3&&arguments[3]!==void 0&&arguments[3];if(e.eq(s))return t.eq(e);if(e.x===s.x)return t.x===e.x&&(i||t.y>=Math.min(e.y,s.y)&&t.y<=Math.max(e.y,s.y));if(e.y===s.y)return t.y===e.y&&(i||t.x>=Math.min(e.x,s.x)&&t.x<=Math.max(e.x,s.x));{const r=gi(e,s),n=gi(e,t).divide(r);return i?Math.abs(n.x)===Math.abs(n.y):n.x===n.y&&n.x>=0&&n.x<=1}}static isPointInPolygon(t,e){const s=new y(t).setX(Math.min(t.x-1,...e.map((r=>r.x))));let i=0;for(let r=0;r<e.length;r++){const n=this.intersectSegmentSegment(e[r],e[(r+1)%e.length],t,s);if(n.includes(t))return!0;i+=+(n.status==="Intersection")}return i%2==1}static intersectLineLine(t,e,s,i){let r=!(arguments.length>4&&arguments[4]!==void 0)||arguments[4],n=!(arguments.length>5&&arguments[5]!==void 0)||arguments[5];const o=e.x-t.x,h=e.y-t.y,l=i.x-s.x,c=i.y-s.y,g=t.x-s.x,u=t.y-s.y,d=l*u-c*g,f=o*u-h*g,v=c*o-l*h;if(v!==0){const _=d/v,b=f/v;return(r||0<=_&&_<=1)&&(n||0<=b&&b<=1)?new q("Intersection").append(new y(t.x+_*o,t.y+_*h)):new q}if(d===0||f===0){const _=r||n||q.isPointContained(t,s,i)||q.isPointContained(e,s,i)||q.isPointContained(s,t,e)||q.isPointContained(i,t,e);return new q(_?"Coincident":void 0)}return new q("Parallel")}static intersectSegmentLine(t,e,s,i){return q.intersectLineLine(t,e,s,i,!1,!0)}static intersectSegmentSegment(t,e,s,i){return q.intersectLineLine(t,e,s,i,!1,!1)}static intersectLinePolygon(t,e,s){let i=!(arguments.length>3&&arguments[3]!==void 0)||arguments[3];const r=new q,n=s.length;for(let o,h,l,c=0;c<n;c++){if(o=s[c],h=s[(c+1)%n],l=q.intersectLineLine(t,e,o,h,i,!1),l.status==="Coincident")return l;r.append(...l.points)}return r.points.length>0&&(r.status="Intersection"),r}static intersectSegmentPolygon(t,e,s){return q.intersectLinePolygon(t,e,s,!1)}static intersectPolygonPolygon(t,e){const s=new q,i=t.length,r=[];for(let n=0;n<i;n++){const o=t[n],h=t[(n+1)%i],l=q.intersectSegmentPolygon(o,h,e);l.status==="Coincident"?(r.push(l),s.append(o,h)):s.append(...l.points)}return r.length>0&&r.length===t.length?new q("Coincident"):(s.points.length>0&&(s.status="Intersection"),s)}static intersectPolygonRectangle(t,e,s){const i=e.min(s),r=e.max(s),n=new y(r.x,i.y),o=new y(i.x,r.y);return q.intersectPolygonPolygon(t,[i,n,r,o])}}class Eo extends Fr{getX(){return this.getXY().x}setX(t){this.setXY(this.getXY().setX(t))}getY(){return this.getXY().y}setY(t){this.setXY(this.getXY().setY(t))}getRelativeX(){return this.left}setRelativeX(t){this.left=t}getRelativeY(){return this.top}setRelativeY(t){this.top=t}getXY(){const t=this.getRelativeXY();return this.group?mt(t,this.group.calcTransformMatrix()):t}setXY(t,e,s){this.group&&(t=mt(t,jt(this.group.calcTransformMatrix()))),this.setRelativeXY(t,e,s)}getRelativeXY(){return new y(this.left,this.top)}setRelativeXY(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.originX,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.originY;this.setPositionByOrigin(t,e,s)}isStrokeAccountedForInDimensions(){return!1}getCoords(){const{tl:t,tr:e,br:s,bl:i}=this.aCoords||(this.aCoords=this.calcACoords()),r=[t,e,s,i];if(this.group){const n=this.group.calcTransformMatrix();return r.map((o=>mt(o,n)))}return r}intersectsWithRect(t,e){return q.intersectPolygonRectangle(this.getCoords(),t,e).status==="Intersection"}intersectsWithObject(t){const e=q.intersectPolygonPolygon(this.getCoords(),t.getCoords());return e.status==="Intersection"||e.status==="Coincident"||t.isContainedWithinObject(this)||this.isContainedWithinObject(t)}isContainedWithinObject(t){return this.getCoords().every((e=>t.containsPoint(e)))}isContainedWithinRect(t,e){const{left:s,top:i,width:r,height:n}=this.getBoundingRect();return s>=t.x&&s+r<=e.x&&i>=t.y&&i+n<=e.y}isOverlapping(t){return this.intersectsWithObject(t)||this.isContainedWithinObject(t)||t.isContainedWithinObject(this)}containsPoint(t){return q.isPointInPolygon(t,this.getCoords())}isOnScreen(){if(!this.canvas)return!1;const{tl:t,br:e}=this.canvas.vptCoords;return!!this.getCoords().some((s=>s.x<=e.x&&s.x>=t.x&&s.y<=e.y&&s.y>=t.y))||!!this.intersectsWithRect(t,e)||this.containsPoint(t.midPointFrom(e))}isPartiallyOnScreen(){if(!this.canvas)return!1;const{tl:t,br:e}=this.canvas.vptCoords;return this.intersectsWithRect(t,e)?!0:this.getCoords().every((s=>(s.x>=e.x||s.x<=t.x)&&(s.y>=e.y||s.y<=t.y)))&&this.containsPoint(t.midPointFrom(e))}getBoundingRect(){return Ut(this.getCoords())}getScaledWidth(){return this._getTransformedDimensions().x}getScaledHeight(){return this._getTransformedDimensions().y}scale(t){this._set(xt,t),this._set(wt,t),this.setCoords()}scaleToWidth(t){const e=this.getBoundingRect().width/this.getScaledWidth();return this.scale(t/this.width/e)}scaleToHeight(t){const e=this.getBoundingRect().height/this.getScaledHeight();return this.scale(t/this.height/e)}getCanvasRetinaScaling(){var t;return((t=this.canvas)===null||t===void 0?void 0:t.getRetinaScaling())||1}getTotalAngle(){return this.group?Jt(Rr(this.calcTransformMatrix())):this.angle}getViewportTransform(){var t;return((t=this.canvas)===null||t===void 0?void 0:t.viewportTransform)||pt.concat()}calcACoords(){const t=Me({angle:this.angle}),{x:e,y:s}=this.getRelativeCenterPoint(),i=Ne(e,s),r=lt(i,t),n=this._getTransformedDimensions(),o=n.x/2,h=n.y/2;return{tl:mt({x:-o,y:-h},r),tr:mt({x:o,y:-h},r),bl:mt({x:-o,y:h},r),br:mt({x:o,y:h},r)}}setCoords(){this.aCoords=this.calcACoords()}transformMatrixKey(){let t=arguments.length>0&&arguments[0]!==void 0&&arguments[0],e=[];return!t&&this.group&&(e=this.group.transformMatrixKey(t)),e.push(this.top,this.left,this.width,this.height,this.scaleX,this.scaleY,this.angle,this.strokeWidth,this.skewX,this.skewY,+this.flipX,+this.flipY,st(this.originX),st(this.originY)),e}calcTransformMatrix(){let t=arguments.length>0&&arguments[0]!==void 0&&arguments[0],e=this.calcOwnMatrix();if(t||!this.group)return e;const s=this.transformMatrixKey(t),i=this.matrixCache;return i&&i.key.every(((r,n)=>r===s[n]))?i.value:(this.group&&(e=lt(this.group.calcTransformMatrix(!1),e)),this.matrixCache={key:s,value:e},e)}calcOwnMatrix(){const t=this.transformMatrixKey(!0),e=this.ownMatrixCache;if(e&&e.key===t)return e.value;const s=this.getRelativeCenterPoint(),i={angle:this.angle,translateX:s.x,translateY:s.y,scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,flipX:this.flipX,flipY:this.flipY},r=Zn(i);return this.ownMatrixCache={key:t,value:r},r}_getNonTransformedDimensions(){return new y(this.width,this.height).scalarAdd(this.strokeWidth)}_calculateCurrentDimensions(t){return this._getTransformedDimensions(t).transform(this.getViewportTransform(),!0).scalarAdd(2*this.padding)}_getTransformedDimensions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const e=m({scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,width:this.width,height:this.height,strokeWidth:this.strokeWidth},t),s=e.strokeWidth;let i=s,r=0;this.strokeUniform&&(i=0,r=s);const n=e.width+i,o=e.height+i;let h;return h=e.skewX===0&&e.skewY===0?new y(n*e.scaleX,o*e.scaleY):Mi(n,o,Xs(e)),h.scalarAdd(r)}translateToGivenOrigin(t,e,s,i,r){let n=t.x,o=t.y;const h=st(i)-st(e),l=st(r)-st(s);if(h||l){const c=this._getTransformedDimensions();n+=h*c.x,o+=l*c.y}return new y(n,o)}translateToCenterPoint(t,e,s){if(e===F&&s===F)return t;const i=this.translateToGivenOrigin(t,e,s,F,F);return this.angle?i.rotate(Q(this.angle),t):i}translateToOriginPoint(t,e,s){const i=this.translateToGivenOrigin(t,F,F,e,s);return this.angle?i.rotate(Q(this.angle),t):i}getCenterPoint(){const t=this.getRelativeCenterPoint();return this.group?mt(t,this.group.calcTransformMatrix()):t}getRelativeCenterPoint(){return this.translateToCenterPoint(new y(this.left,this.top),this.originX,this.originY)}getPointByOrigin(t,e){return this.translateToOriginPoint(this.getRelativeCenterPoint(),t,e)}setPositionByOrigin(t,e,s){const i=this.translateToCenterPoint(t,e,s),r=this.translateToOriginPoint(i,this.originX,this.originY);this.set({left:r.x,top:r.y})}_getLeftTopCoords(){return this.translateToOriginPoint(this.getRelativeCenterPoint(),X,vt)}}const Po=["type"],jo=["extraParam"];let Yt=class Ts extends Eo{static getDefaults(){return Ts.ownDefaults}get type(){const t=this.constructor.type;return t==="FabricObject"?"object":t.toLowerCase()}set type(t){se("warn","Setting type has no effect",t)}constructor(t){super(),p(this,"_cacheContext",null),Object.assign(this,Ts.ownDefaults),this.setOptions(t)}_createCacheCanvas(){this._cacheCanvas=Kt(),this._cacheContext=this._cacheCanvas.getContext("2d"),this._updateCacheCanvas(),this.dirty=!0}_limitCacheSize(t){const e=t.width,s=t.height,i=B.maxCacheSideLimit,r=B.minCacheSideLimit;if(e<=i&&s<=i&&e*s<=B.perfLimitSizeTotal)return e<r&&(t.width=r),s<r&&(t.height=r),t;const n=e/s,[o,h]=Ie.limitDimsByArea(n),l=Te(r,o,i),c=Te(r,h,i);return e>l&&(t.zoomX/=e/l,t.width=l,t.capped=!0),s>c&&(t.zoomY/=s/c,t.height=c,t.capped=!0),t}_getCacheCanvasDimensions(){const t=this.getTotalObjectScaling(),e=this._getTransformedDimensions({skewX:0,skewY:0}),s=e.x*t.x/this.scaleX,i=e.y*t.y/this.scaleY;return{width:Math.ceil(s+2),height:Math.ceil(i+2),zoomX:t.x,zoomY:t.y,x:s,y:i}}_updateCacheCanvas(){const t=this._cacheCanvas,e=this._cacheContext,{width:s,height:i,zoomX:r,zoomY:n,x:o,y:h}=this._limitCacheSize(this._getCacheCanvasDimensions()),l=s!==t.width||i!==t.height,c=this.zoomX!==r||this.zoomY!==n;if(!t||!e)return!1;if(l||c){s!==t.width||i!==t.height?(t.width=s,t.height=i):(e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,t.width,t.height));const g=o/2,u=h/2;return this.cacheTranslationX=Math.round(t.width/2-g)+g,this.cacheTranslationY=Math.round(t.height/2-u)+u,e.translate(this.cacheTranslationX,this.cacheTranslationY),e.scale(r,n),this.zoomX=r,this.zoomY=n,!0}return!1}setOptions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this._setOptions(t)}transform(t){const e=this.group&&!this.group._transformDone||this.group&&this.canvas&&t===this.canvas.contextTop,s=this.calcTransformMatrix(!e);t.transform(s[0],s[1],s[2],s[3],s[4],s[5])}getObjectScaling(){if(!this.group)return new y(Math.abs(this.scaleX),Math.abs(this.scaleY));const t=Es(this.calcTransformMatrix());return new y(Math.abs(t.scaleX),Math.abs(t.scaleY))}getTotalObjectScaling(){const t=this.getObjectScaling();if(this.canvas){const e=this.canvas.getZoom(),s=this.getCanvasRetinaScaling();return t.scalarMultiply(e*s)}return t}getObjectOpacity(){let t=this.opacity;return this.group&&(t*=this.group.getObjectOpacity()),t}_constrainScale(t){return Math.abs(t)<this.minScaleLimit?t<0?-this.minScaleLimit:this.minScaleLimit:t===0?1e-4:t}_set(t,e){t!==xt&&t!==wt||(e=this._constrainScale(e)),t===xt&&e<0?(this.flipX=!this.flipX,e*=-1):t==="scaleY"&&e<0?(this.flipY=!this.flipY,e*=-1):t!=="shadow"||!e||e instanceof Nt||(e=new Nt(e));const s=this[t]!==e;return this[t]=e,s&&this.constructor.cacheProperties.includes(t)&&(this.dirty=!0),this.parent&&(this.dirty||s&&this.constructor.stateProperties.includes(t))&&this.parent._set("dirty",!0),this}isNotVisible(){return this.opacity===0||!this.width&&!this.height&&this.strokeWidth===0||!this.visible}render(t){this.isNotVisible()||this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(t.save(),this._setupCompositeOperation(t),this.drawSelectionBackground(t),this.transform(t),this._setOpacity(t),this._setShadow(t),this.shouldCache()?(this.renderCache(),this.drawCacheOnCanvas(t)):(this._removeCacheCanvas(),this.drawObject(t,!1,{}),this.dirty=!1),t.restore())}drawSelectionBackground(t){}renderCache(t){if(t=t||{},this._cacheCanvas&&this._cacheContext||this._createCacheCanvas(),this.isCacheDirty()&&this._cacheContext){const{zoomX:e,zoomY:s,cacheTranslationX:i,cacheTranslationY:r}=this,{width:n,height:o}=this._cacheCanvas;this.drawObject(this._cacheContext,t.forClipping,{zoomX:e,zoomY:s,cacheTranslationX:i,cacheTranslationY:r,width:n,height:o,parentClipPaths:[]}),this.dirty=!1}}_removeCacheCanvas(){this._cacheCanvas=void 0,this._cacheContext=null}hasStroke(){return this.stroke&&this.stroke!=="transparent"&&this.strokeWidth!==0}hasFill(){return this.fill&&this.fill!=="transparent"}needsItsOwnCache(){return!!(this.paintFirst===_t&&this.hasFill()&&this.hasStroke()&&this.shadow)||!!this.clipPath}shouldCache(){return this.ownCaching=this.objectCaching&&(!this.parent||!this.parent.isOnACache())||this.needsItsOwnCache(),this.ownCaching}willDrawShadow(){return!!this.shadow&&(this.shadow.offsetX!==0||this.shadow.offsetY!==0)}drawClipPathOnCache(t,e,s){t.save(),e.inverted?t.globalCompositeOperation="destination-out":t.globalCompositeOperation="destination-in",t.setTransform(1,0,0,1,0,0),t.drawImage(s,0,0),t.restore()}drawObject(t,e,s){const i=this.fill,r=this.stroke;e?(this.fill="black",this.stroke="",this._setClippingProperties(t)):this._renderBackground(t),this._render(t),this._drawClipPath(t,this.clipPath,s),this.fill=i,this.stroke=r}createClipPathLayer(t,e){const s=Tt(e),i=s.getContext("2d");if(i.translate(e.cacheTranslationX,e.cacheTranslationY),i.scale(e.zoomX,e.zoomY),t._cacheCanvas=s,e.parentClipPaths.forEach((r=>{r.transform(i)})),e.parentClipPaths.push(t),t.absolutePositioned){const r=jt(this.calcTransformMatrix());i.transform(r[0],r[1],r[2],r[3],r[4],r[5])}return t.transform(i),t.drawObject(i,!0,e),s}_drawClipPath(t,e,s){if(!e)return;e._transformDone=!0;const i=this.createClipPathLayer(e,s);this.drawClipPathOnCache(t,e,i)}drawCacheOnCanvas(t){t.scale(1/this.zoomX,1/this.zoomY),t.drawImage(this._cacheCanvas,-this.cacheTranslationX,-this.cacheTranslationY)}isCacheDirty(){let t=arguments.length>0&&arguments[0]!==void 0&&arguments[0];if(this.isNotVisible())return!1;const e=this._cacheCanvas,s=this._cacheContext;return!(!e||!s||t||!this._updateCacheCanvas())||!!(this.dirty||this.clipPath&&this.clipPath.absolutePositioned)&&(e&&s&&!t&&(s.save(),s.setTransform(1,0,0,1,0,0),s.clearRect(0,0,e.width,e.height),s.restore()),!0)}_renderBackground(t){if(!this.backgroundColor)return;const e=this._getNonTransformedDimensions();t.fillStyle=this.backgroundColor,t.fillRect(-e.x/2,-e.y/2,e.x,e.y),this._removeShadow(t)}_setOpacity(t){this.group&&!this.group._transformDone?t.globalAlpha=this.getObjectOpacity():t.globalAlpha*=this.opacity}_setStrokeStyles(t,e){const s=e.stroke;s&&(t.lineWidth=e.strokeWidth,t.lineCap=e.strokeLineCap,t.lineDashOffset=e.strokeDashOffset,t.lineJoin=e.strokeLineJoin,t.miterLimit=e.strokeMiterLimit,St(s)?s.gradientUnits==="percentage"||s.gradientTransform||s.patternTransform?this._applyPatternForTransformedGradient(t,s):(t.strokeStyle=s.toLive(t),this._applyPatternGradientTransform(t,s)):t.strokeStyle=e.stroke)}_setFillStyles(t,e){let{fill:s}=e;s&&(St(s)?(t.fillStyle=s.toLive(t),this._applyPatternGradientTransform(t,s)):t.fillStyle=s)}_setClippingProperties(t){t.globalAlpha=1,t.strokeStyle="transparent",t.fillStyle="#000000"}_setLineDash(t,e){e&&e.length!==0&&t.setLineDash(e)}_setShadow(t){if(!this.shadow)return;const e=this.shadow,s=this.canvas,i=this.getCanvasRetinaScaling(),[r,,,n]=s?.viewportTransform||pt,o=r*i,h=n*i,l=e.nonScaling?new y(1,1):this.getObjectScaling();t.shadowColor=e.color,t.shadowBlur=e.blur*B.browserShadowBlurConstant*(o+h)*(l.x+l.y)/4,t.shadowOffsetX=e.offsetX*o*l.x,t.shadowOffsetY=e.offsetY*h*l.y}_removeShadow(t){this.shadow&&(t.shadowColor="",t.shadowBlur=t.shadowOffsetX=t.shadowOffsetY=0)}_applyPatternGradientTransform(t,e){if(!St(e))return{offsetX:0,offsetY:0};const s=e.gradientTransform||e.patternTransform,i=-this.width/2+e.offsetX||0,r=-this.height/2+e.offsetY||0;return e.gradientUnits==="percentage"?t.transform(this.width,0,0,this.height,i,r):t.transform(1,0,0,1,i,r),s&&t.transform(s[0],s[1],s[2],s[3],s[4],s[5]),{offsetX:i,offsetY:r}}_renderPaintInOrder(t){this.paintFirst===_t?(this._renderStroke(t),this._renderFill(t)):(this._renderFill(t),this._renderStroke(t))}_render(t){}_renderFill(t){this.fill&&(t.save(),this._setFillStyles(t,this),this.fillRule==="evenodd"?t.fill("evenodd"):t.fill(),t.restore())}_renderStroke(t){if(this.stroke&&this.strokeWidth!==0){if(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this.strokeUniform){const e=this.getObjectScaling();t.scale(1/e.x,1/e.y)}this._setLineDash(t,this.strokeDashArray),this._setStrokeStyles(t,this),t.stroke(),t.restore()}}_applyPatternForTransformedGradient(t,e){var s;const i=this._limitCacheSize(this._getCacheCanvasDimensions()),r=this.getCanvasRetinaScaling(),n=i.x/this.scaleX/r,o=i.y/this.scaleY/r,h=Tt({width:Math.ceil(n),height:Math.ceil(o)}),l=h.getContext("2d");l&&(l.beginPath(),l.moveTo(0,0),l.lineTo(n,0),l.lineTo(n,o),l.lineTo(0,o),l.closePath(),l.translate(n/2,o/2),l.scale(i.zoomX/this.scaleX/r,i.zoomY/this.scaleY/r),this._applyPatternGradientTransform(l,e),l.fillStyle=e.toLive(t),l.fill(),t.translate(-this.width/2-this.strokeWidth/2,-this.height/2-this.strokeWidth/2),t.scale(r*this.scaleX/i.zoomX,r*this.scaleY/i.zoomY),t.strokeStyle=(s=l.createPattern(h,"no-repeat"))!==null&&s!==void 0?s:"")}_findCenterFromElement(){return new y(this.left+this.width/2,this.top+this.height/2)}clone(t){const e=this.toObject(t);return this.constructor.fromObject(e)}cloneAsImage(t){const e=this.toCanvasElement(t);return new(D.getClass("image"))(e)}toCanvasElement(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const e=Gr(this),s=this.group,i=this.shadow,r=Math.abs,n=t.enableRetinaScaling?Mr():1,o=(t.multiplier||1)*n,h=t.canvasProvider||(C=>new qe(C,{enableRetinaScaling:!1,renderOnAddRemove:!1,skipOffscreen:!1}));delete this.group,t.withoutTransform&&ro(this),t.withoutShadow&&(this.shadow=null),t.viewportTransform&&oo(this,this.getViewportTransform()),this.setCoords();const l=Kt(),c=this.getBoundingRect(),g=this.shadow,u=new y;if(g){const C=g.blur,O=g.nonScaling?new y(1,1):this.getObjectScaling();u.x=2*Math.round(r(g.offsetX)+C)*r(O.x),u.y=2*Math.round(r(g.offsetY)+C)*r(O.y)}const d=c.width+u.x,f=c.height+u.y;l.width=Math.ceil(d),l.height=Math.ceil(f);const v=h(l);t.format==="jpeg"&&(v.backgroundColor="#fff"),this.setPositionByOrigin(new y(v.width/2,v.height/2),F,F);const _=this.canvas;v._objects=[this],this.set("canvas",v),this.setCoords();const b=v.toCanvasElement(o||1,t);return this.set("canvas",_),this.shadow=i,s&&(this.group=s),this.set(e),this.setCoords(),v._objects=[],v.destroy(),b}toDataURL(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Lr(this.toCanvasElement(t),t.format||"png",t.quality||1)}toBlob(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Ir(this.toCanvasElement(t),t.format||"png",t.quality||1)}isType(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];return e.includes(this.constructor.type)||e.includes(this.type)}complexity(){return 1}toJSON(){return this.toObject()}rotate(t){const{centeredRotation:e,originX:s,originY:i}=this;if(e){const{x:r,y:n}=this.getRelativeCenterPoint();this.originX=F,this.originY=F,this.left=r,this.top=n}if(this.set("angle",t),e){const{x:r,y:n}=this.translateToOriginPoint(this.getRelativeCenterPoint(),s,i);this.left=r,this.top=n,this.originX=s,this.originY=i}}setOnGroup(){}_setupCompositeOperation(t){this.globalCompositeOperation&&(t.globalCompositeOperation=this.globalCompositeOperation)}dispose(){Ms.cancelByTarget(this),this.off(),this._set("canvas",void 0),this._cacheCanvas&&Bt().dispose(this._cacheCanvas),this._cacheCanvas=void 0,this._cacheContext=null}animate(t,e){return Object.entries(t).reduce(((s,i)=>{let[r,n]=i;return s[r]=this._animate(r,n,e),s}),{})}_animate(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const i=t.split("."),r=this.constructor.colorProperties.includes(i[i.length-1]),{abort:n,startValue:o,onChange:h,onComplete:l}=s,c=m(m({},s),{},{target:this,startValue:o??i.reduce(((g,u)=>g[u]),this),endValue:e,abort:n?.bind(this),onChange:(g,u,d)=>{i.reduce(((f,v,_)=>(_===i.length-1&&(f[v]=g),f[v])),this),h&&h(g,u,d)},onComplete:(g,u,d)=>{this.setCoords(),l&&l(g,u,d)}});return r?Mo(c):nn(c)}isDescendantOf(t){const{parent:e,group:s}=this;return e===t||s===t||!!e&&e.isDescendantOf(t)||!!s&&s!==e&&s.isDescendantOf(t)}getAncestors(){const t=[];let e=this;do e=e.parent,e&&t.push(e);while(e);return t}findCommonAncestors(t){if(this===t)return{fork:[],otherFork:[],common:[this,...this.getAncestors()]};const e=this.getAncestors(),s=t.getAncestors();if(e.length===0&&s.length>0&&this===s[s.length-1])return{fork:[],otherFork:[t,...s.slice(0,s.length-1)],common:[this]};for(let i,r=0;r<e.length;r++){if(i=e[r],i===t)return{fork:[this,...e.slice(0,r)],otherFork:[],common:e.slice(r)};for(let n=0;n<s.length;n++){if(this===s[n])return{fork:[],otherFork:[t,...s.slice(0,n)],common:[this,...e]};if(i===s[n])return{fork:[this,...e.slice(0,r)],otherFork:[t,...s.slice(0,n)],common:e.slice(r)}}}return{fork:[this,...e],otherFork:[t,...s],common:[]}}hasCommonAncestors(t){const e=this.findCommonAncestors(t);return e&&!!e.common.length}isInFrontOf(t){if(this===t)return;const e=this.findCommonAncestors(t);if(e.fork.includes(t))return!0;if(e.otherFork.includes(this))return!1;const s=e.common[0]||this.canvas;if(!s)return;const i=e.fork.pop(),r=e.otherFork.pop(),n=s._objects.indexOf(i),o=s._objects.indexOf(r);return n>-1&&n>o}toObject(){const t=(arguments.length>0&&arguments[0]!==void 0?arguments[0]:[]).concat(Ts.customProperties,this.constructor.customProperties||[]);let e;const s=B.NUM_FRACTION_DIGITS,{clipPath:i,fill:r,stroke:n,shadow:o,strokeDashArray:h,left:l,top:c,originX:g,originY:u,width:d,height:f,strokeWidth:v,strokeLineCap:_,strokeDashOffset:b,strokeLineJoin:C,strokeUniform:O,strokeMiterLimit:w,scaleX:S,scaleY:M,angle:L,flipX:E,flipY:W,opacity:$,visible:tt,backgroundColor:H,fillRule:Y,paintFirst:R,globalCompositeOperation:it,skewX:et,skewY:rt}=this;i&&!i.excludeFromExport&&(e=i.toObject(t.concat("inverted","absolutePositioned")));const V=k=>U(k,s),x=m(m({},Ee(this,t)),{},{type:this.constructor.type,version:hi,originX:g,originY:u,left:V(l),top:V(c),width:V(d),height:V(f),fill:Gi(r)?r.toObject():r,stroke:Gi(n)?n.toObject():n,strokeWidth:V(v),strokeDashArray:h&&h.concat(),strokeLineCap:_,strokeDashOffset:b,strokeLineJoin:C,strokeUniform:O,strokeMiterLimit:V(w),scaleX:V(S),scaleY:V(M),angle:V(L),flipX:E,flipY:W,opacity:V($),shadow:o&&o.toObject(),visible:tt,backgroundColor:H,fillRule:Y,paintFirst:R,globalCompositeOperation:it,skewX:V(et),skewY:V(rt)},e?{clipPath:e}:null);return this.includeDefaultValues?x:this._removeDefaultValues(x)}toDatalessObject(t){return this.toObject(t)}_removeDefaultValues(t){const e=this.constructor.getDefaults(),s=Object.keys(e).length>0?e:Object.getPrototypeOf(this);return ki(t,((i,r)=>{if(r===X||r===vt||r==="type")return!0;const n=s[r];return i!==n&&!(Array.isArray(i)&&Array.isArray(n)&&i.length===0&&n.length===0)}))}toString(){return"#<".concat(this.constructor.type,">")}static _fromObject(t){let e=N(t,Po),s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{extraParam:i}=s,r=N(s,jo);return Ys(e,r).then((n=>i?(delete n[i],new this(e[i],n)):new this(n)))}static fromObject(t,e){return this._fromObject(t,e)}};p(Yt,"stateProperties",yo),p(Yt,"cacheProperties",Zt),p(Yt,"ownDefaults",_o),p(Yt,"type","FabricObject"),p(Yt,"colorProperties",[nt,_t,"backgroundColor"]),p(Yt,"customProperties",[]),D.setClass(Yt),D.setClass(Yt,"object");const Pe=(a,t,e)=>(s,i,r,n)=>{const o=t(s,i,r,n);return o&&Ur(a,m(m({},qr(s,i,r,n)),e)),o};function je(a){return(t,e,s,i)=>{const{target:r,originX:n,originY:o}=e,h=r.getRelativeCenterPoint(),l=r.translateToOriginPoint(h,n,o),c=a(t,e,s,i);return r.setPositionByOrigin(l,e.originX,e.originY),c}}const ar=Pe(Xe,je(((a,t,e,s)=>{const i=Ei(t,t.originX,t.originY,e,s);if(st(t.originX)===st(F)||st(t.originX)===st(Z)&&i.x<0||st(t.originX)===st(X)&&i.x>0){const{target:r}=t,n=r.strokeWidth/(r.strokeUniform?r.scaleX:1),o=Nr(t)?2:1,h=r.width,l=Math.abs(i.x*o/r.scaleX)-n;return r.set("width",Math.max(l,1)),h!==r.width}return!1})));function Ao(a,t,e,s,i){s=s||{};const r=this.sizeX||s.cornerSize||i.cornerSize,n=this.sizeY||s.cornerSize||i.cornerSize,o=s.transparentCorners!==void 0?s.transparentCorners:i.transparentCorners,h=o?_t:nt,l=!o&&(s.cornerStrokeColor||i.cornerStrokeColor);let c,g=t,u=e;a.save(),a.fillStyle=s.cornerColor||i.cornerColor||"",a.strokeStyle=s.cornerStrokeColor||i.cornerStrokeColor||"",r>n?(c=r,a.scale(1,n/r),u=e*r/n):n>r?(c=n,a.scale(r/n,1),g=t*n/r):c=r,a.beginPath(),a.arc(g,u,c/2,0,Os,!1),a[h](),l&&a.stroke(),a.restore()}function Fo(a,t,e,s,i){s=s||{};const r=this.sizeX||s.cornerSize||i.cornerSize,n=this.sizeY||s.cornerSize||i.cornerSize,o=s.transparentCorners!==void 0?s.transparentCorners:i.transparentCorners,h=o?_t:nt,l=!o&&(s.cornerStrokeColor||i.cornerStrokeColor),c=r/2,g=n/2;a.save(),a.fillStyle=s.cornerColor||i.cornerColor||"",a.strokeStyle=s.cornerStrokeColor||i.cornerStrokeColor||"",a.translate(t,e);const u=i.getTotalAngle();a.rotate(Q(u)),a["".concat(h,"Rect")](-c,-g,r,n),l&&a.strokeRect(-c,-g,r,n),a.restore()}class Mt{constructor(t){p(this,"visible",!0),p(this,"actionName",Ws),p(this,"angle",0),p(this,"x",0),p(this,"y",0),p(this,"offsetX",0),p(this,"offsetY",0),p(this,"sizeX",0),p(this,"sizeY",0),p(this,"touchSizeX",0),p(this,"touchSizeY",0),p(this,"cursorStyle","crosshair"),p(this,"withConnection",!1),Object.assign(this,t)}shouldActivate(t,e,s,i){var r;let{tl:n,tr:o,br:h,bl:l}=i;return((r=e.canvas)===null||r===void 0?void 0:r.getActiveObject())===e&&e.isControlVisible(t)&&q.isPointInPolygon(s,[n,o,h,l])}getActionHandler(t,e,s){return this.actionHandler}getMouseDownHandler(t,e,s){return this.mouseDownHandler}getMouseUpHandler(t,e,s){return this.mouseUpHandler}cursorStyleHandler(t,e,s){return e.cursorStyle}getActionName(t,e,s){return e.actionName}getVisibility(t,e){var s,i;return(s=(i=t._controlsVisibility)===null||i===void 0?void 0:i[e])!==null&&s!==void 0?s:this.visible}setVisibility(t,e,s){this.visible=t}positionHandler(t,e,s,i){return new y(this.x*t.x+this.offsetX,this.y*t.y+this.offsetY).transform(e)}calcCornerCoords(t,e,s,i,r,n){const o=Oi([Ne(s,i),Me({angle:t}),Di((r?this.touchSizeX:this.sizeX)||e,(r?this.touchSizeY:this.sizeY)||e)]);return{tl:new y(-.5,-.5).transform(o),tr:new y(.5,-.5).transform(o),br:new y(.5,.5).transform(o),bl:new y(-.5,.5).transform(o)}}render(t,e,s,i,r){((i=i||{}).cornerStyle||r.cornerStyle)==="circle"?Ao.call(this,t,e,s,i,r):Fo.call(this,t,e,s,i,r)}}const Lo=(a,t,e)=>e.lockRotation?js:t.cursorStyle,Io=Pe(Pr,je(((a,t,e,s)=>{let{target:i,ex:r,ey:n,theta:o,originX:h,originY:l}=t;const c=i.translateToOriginPoint(i.getRelativeCenterPoint(),h,l);if(Ft(i,"lockRotation"))return!1;const g=Math.atan2(n-c.y,r-c.x),u=Math.atan2(s-c.y,e-c.x);let d=Jt(u-g+o);if(i.snapAngle&&i.snapAngle>0){const v=i.snapAngle,_=i.snapThreshold||v,b=Math.ceil(d/v)*v,C=Math.floor(d/v)*v;Math.abs(d-C)<_?d=C:Math.abs(d-b)<_&&(d=b)}d<0&&(d=360+d),d%=360;const f=i.angle!==d;return i.angle=d,f})));function on(a,t){const e=t.canvas,s=a[e.uniScaleKey];return e.uniformScaling&&!s||!e.uniformScaling&&s}function an(a,t,e){const s=Ft(a,"lockScalingX"),i=Ft(a,"lockScalingY");if(s&&i||!t&&(s||i)&&e||s&&t==="x"||i&&t==="y")return!0;const{width:r,height:n,strokeWidth:o}=a;return r===0&&o===0&&t!=="y"||n===0&&o===0&&t!=="x"}const Ro=["e","se","s","sw","w","nw","n","ne","e"],Le=(a,t,e)=>{const s=on(a,e);if(an(e,t.x!==0&&t.y===0?"x":t.x===0&&t.y!==0?"y":"",s))return js;const i=$r(e,t);return"".concat(Ro[i],"-resize")};function Ai(a,t,e,s){let i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{};const r=t.target,n=i.by,o=on(a,r);let h,l,c,g,u,d;if(an(r,n,o))return!1;if(t.gestureScale)l=t.scaleX*t.gestureScale,c=t.scaleY*t.gestureScale;else{if(h=Ei(t,t.originX,t.originY,e,s),u=n!=="y"?Math.sign(h.x||t.signX||1):1,d=n!=="x"?Math.sign(h.y||t.signY||1):1,t.signX||(t.signX=u),t.signY||(t.signY=d),Ft(r,"lockScalingFlip")&&(t.signX!==u||t.signY!==d))return!1;if(g=r._getTransformedDimensions(),o&&!n){const _=Math.abs(h.x)+Math.abs(h.y),{original:b}=t,C=_/(Math.abs(g.x*b.scaleX/r.scaleX)+Math.abs(g.y*b.scaleY/r.scaleY));l=b.scaleX*C,c=b.scaleY*C}else l=Math.abs(h.x*r.scaleX/g.x),c=Math.abs(h.y*r.scaleY/g.y);Nr(t)&&(l*=2,c*=2),t.signX!==u&&n!=="y"&&(t.originX=$i(t.originX),l*=-1,t.signX=u),t.signY!==d&&n!=="x"&&(t.originY=$i(t.originY),c*=-1,t.signY=d)}const f=r.scaleX,v=r.scaleY;return n?(n==="x"&&r.set(xt,l),n==="y"&&r.set(wt,c)):(!Ft(r,"lockScalingX")&&r.set(xt,l),!Ft(r,"lockScalingY")&&r.set(wt,c)),f!==r.scaleX||v!==r.scaleY}const Je=Pe(Bs,je(((a,t,e,s)=>Ai(a,t,e,s)))),Bo=Pe(Bs,je(((a,t,e,s)=>Ai(a,t,e,s,{by:"x"})))),Wo=Pe(Bs,je(((a,t,e,s)=>Ai(a,t,e,s,{by:"y"})))),Xo=["target","ex","ey","skewingSide"],ei={x:{counterAxis:"y",scale:xt,skew:De,lockSkewing:"lockSkewingX",origin:"originX",flip:"flipX"},y:{counterAxis:"x",scale:wt,skew:ke,lockSkewing:"lockSkewingY",origin:"originY",flip:"flipY"}},Yo=["ns","nesw","ew","nwse"],Vo=(a,t,e)=>{if(t.x!==0&&Ft(e,"lockSkewingY")||t.y!==0&&Ft(e,"lockSkewingX"))return js;const s=$r(e,t)%4;return"".concat(Yo[s],"-resize")};function hn(a,t,e,s,i){const{target:r}=e,{counterAxis:n,origin:o,lockSkewing:h,skew:l,flip:c}=ei[a];if(Ft(r,h))return!1;const{origin:g,flip:u}=ei[n],d=st(e[g])*(r[u]?-1:1),f=-Math.sign(d)*(r[c]?-1:1),v=.5*-((r[l]===0&&Ei(e,F,F,s,i)[a]>0||r[l]>0?1:-1)*f)+.5;return Pe(jr,je(((b,C,O,w)=>(function(S,M,L){let{target:E,ex:W,ey:$,skewingSide:tt}=M,H=N(M,Xo);const{skew:Y}=ei[S],R=L.subtract(new y(W,$)).divide(new y(E.scaleX,E.scaleY))[S],it=E[Y],et=H[Y],rt=Math.tan(Q(et)),V=S==="y"?E._getTransformedDimensions({scaleX:1,scaleY:1,skewX:0}).x:E._getTransformedDimensions({scaleX:1,scaleY:1}).y,x=2*R*tt/Math.max(V,1)+rt,k=Jt(Math.atan(x));E.set(Y,k);const A=it!==E[Y];if(A&&S==="y"){const{skewX:G,scaleX:I}=E,K=E._getTransformedDimensions({skewY:it}),j=E._getTransformedDimensions(),J=G!==0?K.x/j.x:1;J!==1&&E.set(xt,J*I)}return A})(a,C,new y(O,w)))))(t,m(m({},e),{},{[o]:v,skewingSide:f}),s,i)}const zo=(a,t,e,s)=>hn("x",a,t,e,s),Ho=(a,t,e,s)=>hn("y",a,t,e,s);function Hs(a,t){return a[t.canvas.altActionKey]}const Ze=(a,t,e)=>{const s=Hs(a,e);return t.x===0?s?De:wt:t.y===0?s?ke:xt:""},_e=(a,t,e)=>Hs(a,e)?Vo(0,t,e):Le(a,t,e),hr=(a,t,e,s)=>Hs(a,t.target)?Ho(a,t,e,s):Bo(a,t,e,s),lr=(a,t,e,s)=>Hs(a,t.target)?zo(a,t,e,s):Wo(a,t,e,s),ln=()=>({ml:new Mt({x:-.5,y:0,cursorStyleHandler:_e,actionHandler:hr,getActionName:Ze}),mr:new Mt({x:.5,y:0,cursorStyleHandler:_e,actionHandler:hr,getActionName:Ze}),mb:new Mt({x:0,y:.5,cursorStyleHandler:_e,actionHandler:lr,getActionName:Ze}),mt:new Mt({x:0,y:-.5,cursorStyleHandler:_e,actionHandler:lr,getActionName:Ze}),tl:new Mt({x:-.5,y:-.5,cursorStyleHandler:Le,actionHandler:Je}),tr:new Mt({x:.5,y:-.5,cursorStyleHandler:Le,actionHandler:Je}),bl:new Mt({x:-.5,y:.5,cursorStyleHandler:Le,actionHandler:Je}),br:new Mt({x:.5,y:.5,cursorStyleHandler:Le,actionHandler:Je}),mtr:new Mt({x:0,y:-.5,actionHandler:Io,cursorStyleHandler:Lo,offsetY:-40,withConnection:!0,actionName:wi})}),Go=()=>({mr:new Mt({x:.5,y:0,actionHandler:ar,cursorStyleHandler:_e,actionName:Xe}),ml:new Mt({x:-.5,y:0,actionHandler:ar,cursorStyleHandler:_e,actionName:Xe})}),Uo=()=>m(m({},ln()),Go());class He extends Yt{static getDefaults(){return m(m({},super.getDefaults()),He.ownDefaults)}constructor(t){super(),Object.assign(this,this.constructor.createControls(),He.ownDefaults),this.setOptions(t)}static createControls(){return{controls:ln()}}_updateCacheCanvas(){const t=this.canvas;if(this.noScaleCache&&t&&t._currentTransform){const e=t._currentTransform,s=e.target,i=e.action;if(this===s&&i&&i.startsWith(Ws))return!1}return super._updateCacheCanvas()}getActiveControl(){const t=this.__corner;return t?{key:t,control:this.controls[t],coord:this.oCoords[t]}:void 0}findControl(t){let e=arguments.length>1&&arguments[1]!==void 0&&arguments[1];if(!this.hasControls||!this.canvas)return;this.__corner=void 0;const s=Object.entries(this.oCoords);for(let i=s.length-1;i>=0;i--){const[r,n]=s[i],o=this.controls[r];if(o.shouldActivate(r,this,t,e?n.touchCorner:n.corner))return this.__corner=r,{key:r,control:o,coord:this.oCoords[r]}}}calcOCoords(){const t=this.getViewportTransform(),e=this.getCenterPoint(),s=Ne(e.x,e.y),i=Me({angle:this.getTotalAngle()-(this.group&&this.flipX?180:0)}),r=lt(s,i),n=lt(t,r),o=lt(n,[1/t[0],0,0,1/t[3],0,0]),h=this.group?Es(this.calcTransformMatrix()):void 0;h&&(h.scaleX=Math.abs(h.scaleX),h.scaleY=Math.abs(h.scaleY));const l=this._calculateCurrentDimensions(h),c={};return this.forEachControl(((g,u)=>{const d=g.positionHandler(l,o,this,g);c[u]=Object.assign(d,this._calcCornerCoords(g,d))})),c}_calcCornerCoords(t,e){const s=this.getTotalAngle();return{corner:t.calcCornerCoords(s,this.cornerSize,e.x,e.y,!1,this),touchCorner:t.calcCornerCoords(s,this.touchCornerSize,e.x,e.y,!0,this)}}setCoords(){super.setCoords(),this.canvas&&(this.oCoords=this.calcOCoords())}forEachControl(t){for(const e in this.controls)t(this.controls[e],e,this)}drawSelectionBackground(t){if(!this.selectionBackgroundColor||this.canvas&&this.canvas._activeObject!==this)return;t.save();const e=this.getRelativeCenterPoint(),s=this._calculateCurrentDimensions(),i=this.getViewportTransform();t.translate(e.x,e.y),t.scale(1/i[0],1/i[3]),t.rotate(Q(this.angle)),t.fillStyle=this.selectionBackgroundColor,t.fillRect(-s.x/2,-s.y/2,s.x,s.y),t.restore()}strokeBorders(t,e){t.strokeRect(-e.x/2,-e.y/2,e.x,e.y)}_drawBorders(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const i=m({hasControls:this.hasControls,borderColor:this.borderColor,borderDashArray:this.borderDashArray},s);t.save(),t.strokeStyle=i.borderColor,this._setLineDash(t,i.borderDashArray),this.strokeBorders(t,e),i.hasControls&&this.drawControlsConnectingLines(t,e),t.restore()}_renderControls(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{hasBorders:s,hasControls:i}=this,r=m({hasBorders:s,hasControls:i},e),n=this.getViewportTransform(),o=r.hasBorders,h=r.hasControls,l=lt(n,this.calcTransformMatrix()),c=Es(l);t.save(),t.translate(c.translateX,c.translateY),t.lineWidth=this.borderScaleFactor,this.group===this.parent&&(t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1),this.flipX&&(c.angle-=180),t.rotate(Q(this.group?c.angle:this.angle)),o&&this.drawBorders(t,c,e),h&&this.drawControls(t,e),t.restore()}drawBorders(t,e,s){let i;if(s&&s.forActiveSelection||this.group){const r=Mi(this.width,this.height,Xs(e)),n=this.isStrokeAccountedForInDimensions()?Ti:(this.strokeUniform?new y().scalarAdd(this.canvas?this.canvas.getZoom():1):new y(e.scaleX,e.scaleY)).scalarMultiply(this.strokeWidth);i=r.add(n).scalarAdd(this.borderScaleFactor).scalarAdd(2*this.padding)}else i=this._calculateCurrentDimensions().scalarAdd(this.borderScaleFactor);this._drawBorders(t,i,s)}drawControlsConnectingLines(t,e){let s=!1;t.beginPath(),this.forEachControl(((i,r)=>{i.withConnection&&i.getVisibility(this,r)&&(s=!0,t.moveTo(i.x*e.x,i.y*e.y),t.lineTo(i.x*e.x+i.offsetX,i.y*e.y+i.offsetY))})),s&&t.stroke()}drawControls(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};t.save();const s=this.getCanvasRetinaScaling(),{cornerStrokeColor:i,cornerDashArray:r,cornerColor:n}=this,o=m({cornerStrokeColor:i,cornerDashArray:r,cornerColor:n},e);t.setTransform(s,0,0,s,0,0),t.strokeStyle=t.fillStyle=o.cornerColor,this.transparentCorners||(t.strokeStyle=o.cornerStrokeColor),this._setLineDash(t,o.cornerDashArray),this.forEachControl(((h,l)=>{if(h.getVisibility(this,l)){const c=this.oCoords[l];h.render(t,c.x,c.y,o,this)}})),t.restore()}isControlVisible(t){return this.controls[t]&&this.controls[t].getVisibility(this,t)}setControlVisible(t,e){this._controlsVisibility||(this._controlsVisibility={}),this._controlsVisibility[t]=e}setControlsVisibility(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};Object.entries(t).forEach((e=>{let[s,i]=e;return this.setControlVisible(s,i)}))}clearContextTop(t){if(!this.canvas)return;const e=this.canvas.contextTop;if(!e)return;const s=this.canvas.viewportTransform;e.save(),e.transform(s[0],s[1],s[2],s[3],s[4],s[5]),this.transform(e);const i=this.width+4,r=this.height+4;return e.clearRect(-i/2,-r/2,i,r),t||e.restore(),e}onDeselect(t){return!1}onSelect(t){return!1}shouldStartDragging(t){return!1}onDragStart(t){return!1}canDrop(t){return!1}renderDragSourceEffect(t){}renderDropTargetEffect(t){}}function cn(a,t){return t.forEach((e=>{Object.getOwnPropertyNames(e.prototype).forEach((s=>{s!=="constructor"&&Object.defineProperty(a.prototype,s,Object.getOwnPropertyDescriptor(e.prototype,s)||Object.create(null))}))})),a}p(He,"ownDefaults",{noScaleCache:!0,lockMovementX:!1,lockMovementY:!1,lockRotation:!1,lockScalingX:!1,lockScalingY:!1,lockSkewingX:!1,lockSkewingY:!1,lockScalingFlip:!1,cornerSize:13,touchCornerSize:24,transparentCorners:!0,cornerColor:"rgb(178,204,255)",cornerStrokeColor:"",cornerStyle:"rect",cornerDashArray:null,hasControls:!0,borderColor:"rgb(178,204,255)",borderDashArray:null,borderOpacityWhenMoving:.4,borderScaleFactor:1,hasBorders:!0,selectionBackgroundColor:"",selectable:!0,evented:!0,perPixelTargetFind:!1,activeOn:"down",hoverCursor:null,moveCursor:null});class ut extends He{}cn(ut,[Kr]),D.setClass(ut),D.setClass(ut,"object");const No=(a,t,e,s)=>{const i=2*(s=Math.round(s))+1,{data:r}=a.getImageData(t-s,e-s,i,i);for(let n=3;n<r.length;n+=4)if(r[n]>0)return!1;return!0};class un{constructor(t){this.options=t,this.strokeProjectionMagnitude=this.options.strokeWidth/2,this.scale=new y(this.options.scaleX,this.options.scaleY),this.strokeUniformScalar=this.options.strokeUniform?new y(1/this.options.scaleX,1/this.options.scaleY):new y(1,1)}createSideVector(t,e){const s=gi(t,e);return this.options.strokeUniform?s.multiply(this.scale):s}projectOrthogonally(t,e,s){return this.applySkew(t.add(this.calcOrthogonalProjection(t,e,s)))}isSkewed(){return this.options.skewX!==0||this.options.skewY!==0}applySkew(t){const e=new y(t);return e.y+=e.x*Math.tan(Q(this.options.skewY)),e.x+=e.y*Math.tan(Q(this.options.skewX)),e}scaleUnitVector(t,e){return t.multiply(this.strokeUniformScalar).scalarMultiply(e)}}const qo=new y;class we extends un{static getOrthogonalRotationFactor(t,e){const s=e?fi(t,e):mo(t);return Math.abs(s)<Ue?-1:1}constructor(t,e,s,i){super(i),p(this,"AB",void 0),p(this,"AC",void 0),p(this,"alpha",void 0),p(this,"bisector",void 0),this.A=new y(t),this.B=new y(e),this.C=new y(s),this.AB=this.createSideVector(this.A,this.B),this.AC=this.createSideVector(this.A,this.C),this.alpha=fi(this.AB,this.AC),this.bisector=Pi(sn(this.AB.eq(qo)?this.AC:this.AB,this.alpha/2))}calcOrthogonalProjection(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.strokeProjectionMagnitude;const i=this.createSideVector(t,e),r=rn(i),n=we.getOrthogonalRotationFactor(r,this.bisector);return this.scaleUnitVector(r,s*n)}projectBevel(){const t=[];return(this.alpha%Os==0?[this.B]:[this.B,this.C]).forEach((e=>{t.push(this.projectOrthogonally(this.A,e)),t.push(this.projectOrthogonally(this.A,e,-this.strokeProjectionMagnitude))})),t}projectMiter(){const t=[],e=Math.abs(this.alpha),s=1/Math.sin(e/2),i=this.scaleUnitVector(this.bisector,-this.strokeProjectionMagnitude*s),r=this.options.strokeUniform?di(this.scaleUnitVector(this.bisector,this.options.strokeMiterLimit)):this.options.strokeMiterLimit;return di(i)/this.strokeProjectionMagnitude<=r&&t.push(this.applySkew(this.A.add(i))),t.push(...this.projectBevel()),t}projectRoundNoSkew(t,e){const s=[],i=new y(we.getOrthogonalRotationFactor(this.bisector),we.getOrthogonalRotationFactor(new y(this.bisector.y,this.bisector.x)));return[new y(1,0).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar).multiply(i),new y(0,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar).multiply(i)].forEach((r=>{rr(r,t,e)&&s.push(this.A.add(r))})),s}projectRoundWithSkew(t,e){const s=[],{skewX:i,skewY:r,scaleX:n,scaleY:o,strokeUniform:h}=this.options,l=new y(Math.tan(Q(i)),Math.tan(Q(r))),c=this.strokeProjectionMagnitude,g=h?c/o/Math.sqrt(1/o**2+1/n**2*l.y**2):c/Math.sqrt(1+l.y**2),u=new y(Math.sqrt(Math.max(c**2-g**2,0)),g),d=h?c/Math.sqrt(1+l.x**2*(1/o)**2/(1/n+1/n*l.x*l.y)**2):c/Math.sqrt(1+l.x**2/(1+l.x*l.y)**2),f=new y(d,Math.sqrt(Math.max(c**2-d**2,0)));return[f,f.scalarMultiply(-1),u,u.scalarMultiply(-1)].map((v=>this.applySkew(h?v.multiply(this.strokeUniformScalar):v))).forEach((v=>{rr(v,t,e)&&s.push(this.applySkew(this.A).add(v))})),s}projectRound(){const t=[];t.push(...this.projectBevel());const e=this.alpha%Os==0,s=this.applySkew(this.A),i=t[e?0:2].subtract(s),r=t[e?1:0].subtract(s),n=e?this.applySkew(this.AB.scalarMultiply(-1)):this.applySkew(this.bisector.multiply(this.strokeUniformScalar).scalarMultiply(-1)),o=We(i,n)>0,h=o?i:r,l=o?r:i;return this.isSkewed()?t.push(...this.projectRoundWithSkew(h,l)):t.push(...this.projectRoundNoSkew(h,l)),t}projectPoints(){switch(this.options.strokeLineJoin){case"miter":return this.projectMiter();case"round":return this.projectRound();default:return this.projectBevel()}}project(){return this.projectPoints().map((t=>({originPoint:this.A,projectedPoint:t,angle:this.alpha,bisector:this.bisector})))}}class cr extends un{constructor(t,e,s){super(s),this.A=new y(t),this.T=new y(e)}calcOrthogonalProjection(t,e){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.strokeProjectionMagnitude;const i=this.createSideVector(t,e);return this.scaleUnitVector(rn(i),s)}projectButt(){return[this.projectOrthogonally(this.A,this.T,this.strokeProjectionMagnitude),this.projectOrthogonally(this.A,this.T,-this.strokeProjectionMagnitude)]}projectRound(){const t=[];if(!this.isSkewed()&&this.A.eq(this.T)){const e=new y(1,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar);t.push(this.applySkew(this.A.add(e)),this.applySkew(this.A.subtract(e)))}else t.push(...new we(this.A,this.T,this.T,this.options).projectRound());return t}projectSquare(){const t=[];if(this.A.eq(this.T)){const e=new y(1,1).scalarMultiply(this.strokeProjectionMagnitude).multiply(this.strokeUniformScalar);t.push(this.A.add(e),this.A.subtract(e))}else{const e=this.calcOrthogonalProjection(this.A,this.T,this.strokeProjectionMagnitude),s=this.scaleUnitVector(Pi(this.createSideVector(this.A,this.T)),-this.strokeProjectionMagnitude),i=this.A.add(s);t.push(i.add(e),i.subtract(e))}return t.map((e=>this.applySkew(e)))}projectPoints(){switch(this.options.strokeLineCap){case"round":return this.projectRound();case"square":return this.projectSquare();default:return this.projectButt()}}project(){return this.projectPoints().map((t=>({originPoint:this.A,projectedPoint:t})))}}const $o=function(a,t){let e=arguments.length>2&&arguments[2]!==void 0&&arguments[2];const s=[];if(a.length===0)return s;const i=a.reduce(((r,n)=>(r[r.length-1].eq(n)||r.push(new y(n)),r)),[new y(a[0])]);if(i.length===1)e=!0;else if(!e){const r=i[0],n=((o,h)=>{for(let l=o.length-1;l>=0;l--)if(h(o[l],l,o))return l;return-1})(i,(o=>!o.eq(r)));i.splice(n+1)}return i.forEach(((r,n,o)=>{let h,l;n===0?(l=o[1],h=e?r:o[o.length-1]):n===o.length-1?(h=o[n-1],l=e?r:o[0]):(h=o[n-1],l=o[n+1]),e&&o.length===1?s.push(...new cr(r,r,t).project()):!e||n!==0&&n!==o.length-1?s.push(...new we(r,h,l,t).project()):s.push(...new cr(r,n===0?l:h,t).project())})),s},Fi=a=>{const t={};return Object.keys(a).forEach((e=>{t[e]={},Object.keys(a[e]).forEach((s=>{t[e][s]=m({},a[e][s])}))})),t},Ko=a=>a.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;");let Ae;const Li=a=>{if(Ae||Ae||(Ae="Intl"in Rs()&&"Segmenter"in Intl&&new Intl.Segmenter(void 0,{granularity:"grapheme"})),Ae){const t=Ae.segment(a);return Array.from(t).map((e=>{let{segment:s}=e;return s}))}return Jo(a)},Jo=a=>{const t=[];for(let e,s=0;s<a.length;s++)(e=Zo(a,s))!==!1&&t.push(e);return t},Zo=(a,t)=>{const e=a.charCodeAt(t);if(isNaN(e))return"";if(e<55296||e>57343)return a.charAt(t);if(55296<=e&&e<=56319){if(a.length<=t+1)throw"High surrogate without following low surrogate";const i=a.charCodeAt(t+1);if(56320>i||i>57343)throw"High surrogate without following low surrogate";return a.charAt(t)+a.charAt(t+1)}if(t===0)throw"Low surrogate without preceding high surrogate";const s=a.charCodeAt(t-1);if(55296>s||s>56319)throw"Low surrogate without preceding high surrogate";return!1},Ii=function(a,t){let e=arguments.length>2&&arguments[2]!==void 0&&arguments[2];return a.fill!==t.fill||a.stroke!==t.stroke||a.strokeWidth!==t.strokeWidth||a.fontSize!==t.fontSize||a.fontFamily!==t.fontFamily||a.fontWeight!==t.fontWeight||a.fontStyle!==t.fontStyle||a.textDecorationThickness!==t.textDecorationThickness||a.textBackgroundColor!==t.textBackgroundColor||a.deltaY!==t.deltaY||e&&(a.overline!==t.overline||a.underline!==t.underline||a.linethrough!==t.linethrough)},Qo=(a,t)=>{const e=t.split(`
`),s=[];let i=-1,r={};a=Fi(a);for(let n=0;n<e.length;n++){const o=Li(e[n]);if(a[n])for(let h=0;h<o.length;h++){i++;const l=a[n][h];l&&Object.keys(l).length>0&&(Ii(r,l,!0)?s.push({start:i,end:i+1,style:l}):s[s.length-1].end++),r=l||{}}else i+=o.length,r={}}return s},ta=(a,t)=>{if(!Array.isArray(a))return Fi(a);const e=t.split(Si),s={};let i=-1,r=0;for(let n=0;n<e.length;n++){const o=Li(e[n]);for(let h=0;h<o.length;h++)i++,a[r]&&a[r].start<=i&&i<a[r].end&&(s[n]=s[n]||{},s[n][h]=m({},a[r].style),i===a[r].end-1&&r++)}return s},re=["display","transform",nt,"fill-opacity","fill-rule","opacity",_t,"stroke-dasharray","stroke-linecap","stroke-dashoffset","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","id","paint-order","vector-effect","instantiated_by_use","clip-path"];function ur(a,t){const e=a.nodeName,s=a.getAttribute("class"),i=a.getAttribute("id"),r="(?![a-zA-Z\\-]+)";let n;if(n=new RegExp("^"+e,"i"),t=t.replace(n,""),i&&t.length&&(n=new RegExp("#"+i+r,"i"),t=t.replace(n,"")),s&&t.length){const o=s.split(" ");for(let h=o.length;h--;)n=new RegExp("\\."+o[h]+r,"i"),t=t.replace(n,"")}return t.length===0}function ea(a,t){let e=!0;const s=ur(a,t.pop());return s&&t.length&&(e=(function(i,r){let n,o=!0;for(;i.parentElement&&i.parentElement.nodeType===1&&r.length;)o&&(n=r.pop()),o=ur(i=i.parentElement,n);return r.length===0})(a,t)),s&&e&&t.length===0}const sa=a=>{var t;return(t=fo[a])!==null&&t!==void 0?t:a},ia=new RegExp("(".concat(At,")"),"gi"),ra=a=>a.replace(ia," $1 ").replace(/,/gi," ").replace(/\s+/gi," ");var gr,dr,fr,pr,mr,vr,yr;const ft="(".concat(At,")"),na=String.raw(gr||(gr=Wt(["(skewX)(",")"],["(skewX)\\(","\\)"])),ft),oa=String.raw(dr||(dr=Wt(["(skewY)(",")"],["(skewY)\\(","\\)"])),ft),aa=String.raw(fr||(fr=Wt(["(rotate)(","(?: "," ",")?)"],["(rotate)\\(","(?: "," ",")?\\)"])),ft,ft,ft),ha=String.raw(pr||(pr=Wt(["(scale)(","(?: ",")?)"],["(scale)\\(","(?: ",")?\\)"])),ft,ft),la=String.raw(mr||(mr=Wt(["(translate)(","(?: ",")?)"],["(translate)\\(","(?: ",")?\\)"])),ft,ft),ca=String.raw(vr||(vr=Wt(["(matrix)("," "," "," "," "," ",")"],["(matrix)\\("," "," "," "," "," ","\\)"])),ft,ft,ft,ft,ft,ft),Ri="(?:".concat(ca,"|").concat(la,"|").concat(aa,"|").concat(ha,"|").concat(na,"|").concat(oa,")"),ua="(?:".concat(Ri,"*)"),ga=String.raw(yr||(yr=Wt(["^s*(?:","?)s*$"],["^\\s*(?:","?)\\s*$"])),ua),da=new RegExp(ga),fa=new RegExp(Ri),pa=new RegExp(Ri,"g");function pi(a){const t=[];if(!(a=ra(a).replace(/\s*([()])\s*/gi,"$1"))||a&&!da.test(a))return[...pt];for(const e of a.matchAll(pa)){const s=fa.exec(e[0]);if(!s)continue;let i=pt;const r=s.filter((f=>!!f)),[,n,...o]=r,[h,l,c,g,u,d]=o.map((f=>parseFloat(f)));switch(n){case"translate":i=Ne(h,l);break;case wi:i=Me({angle:h},{x:l,y:c});break;case Ws:i=Di(h,l);break;case De:i=Wr(h);break;case ke:i=Xr(h);break;case"matrix":i=[h,l,c,g,u,d]}t.push(i)}return Oi(t)}function ma(a,t,e,s){const i=Array.isArray(t);let r,n=t;if(a!==nt&&a!==_t||t!==yt){if(a==="strokeUniform")return t==="non-scaling-stroke";if(a==="strokeDashArray")n=t===yt?null:t.replace(/,/g," ").split(/\s+/).map(parseFloat);else if(a==="transformMatrix")n=e&&e.transformMatrix?lt(e.transformMatrix,pi(t)):pi(t);else if(a==="visible")n=t!==yt&&t!=="hidden",e&&e.visible===!1&&(n=!1);else if(a==="opacity")n=parseFloat(t),e&&e.opacity!==void 0&&(n*=e.opacity);else if(a==="textAnchor")n=t==="start"?X:t==="end"?Z:F;else if(a==="charSpacing"||a===ge)r=Se(t,s)/s*1e3;else if(a==="paintFirst"){const o=t.indexOf(nt),h=t.indexOf(_t);n=nt,(o>-1&&h>-1&&h<o||o===-1&&h>-1)&&(n=_t)}else{if(a==="href"||a==="xlink:href"||a==="font"||a==="id")return t;if(a==="imageSmoothing")return t==="optimizeQuality";r=i?t.map(Se):Se(t,s)}}else n="";return!i&&isNaN(r)?n:r}function va(a,t){const e=a.match(go);if(!e)return;const s=e[1],i=e[3],r=e[4],n=e[5],o=e[6];s&&(t.fontStyle=s),i&&(t.fontWeight=isNaN(parseFloat(i))?i:parseFloat(i)),r&&(t.fontSize=Se(r)),o&&(t.fontFamily=o),n&&(t.lineHeight=n==="normal"?1:n)}function ya(a,t){a.replace(/;\s*$/,"").split(";").forEach((e=>{if(!e)return;const[s,i]=e.split(":");t[s.trim().toLowerCase()]=i.trim()}))}function _a(a){const t={},e=a.getAttribute("style");return e&&(typeof e=="string"?ya(e,t):(function(s,i){Object.entries(s).forEach((r=>{let[n,o]=r;o!==void 0&&(i[n.toLowerCase()]=o)}))})(e,t)),t}const xa={stroke:"strokeOpacity",fill:"fillOpacity"};function Qt(a,t,e){if(!a)return{};let s,i={},r=Ci;a.parentNode&&ir.test(a.parentNode.nodeName)&&(i=Qt(a.parentElement,t,e),i.fontSize&&(s=r=Se(i.fontSize)));const n=m(m(m({},t.reduce(((l,c)=>{const g=a.getAttribute(c);return g&&(l[c]=g),l}),{})),(function(l){let c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},g={};for(const u in c)ea(l,u.split(" "))&&(g=m(m({},g),c[u]));return g})(a,e)),_a(a));n[Qs]&&a.setAttribute(Qs,n[Qs]),n[Zs]&&(s=Se(n[Zs],r),n[Zs]="".concat(s));const o={};for(const l in n){const c=sa(l),g=ma(c,n[l],i,s);o[c]=g}o&&o.font&&va(o.font,o);const h=m(m({},i),o);return ir.test(a.nodeName)?h:(function(l){const c=ut.getDefaults();return Object.entries(xa).forEach((g=>{let[u,d]=g;if(l[d]===void 0||l[u]==="")return;if(l[u]===void 0){if(!c[u])return;l[u]=c[u]}if(l[u].indexOf("url(")===0)return;const f=new z(l[u]);l[u]=f.setAlpha(U(f.getAlpha()*l[d],2)).toRgba()})),l})(h)}const ba=["left","top","width","height","visible"],gn=["rx","ry"];class Lt extends ut{static getDefaults(){return m(m({},super.getDefaults()),Lt.ownDefaults)}constructor(t){super(),Object.assign(this,Lt.ownDefaults),this.setOptions(t),this._initRxRy()}_initRxRy(){const{rx:t,ry:e}=this;t&&!e?this.ry=t:e&&!t&&(this.rx=e)}_render(t){const{width:e,height:s}=this,i=-e/2,r=-s/2,n=this.rx?Math.min(this.rx,e/2):0,o=this.ry?Math.min(this.ry,s/2):0,h=n!==0||o!==0;t.beginPath(),t.moveTo(i+n,r),t.lineTo(i+e-n,r),h&&t.bezierCurveTo(i+e-te*n,r,i+e,r+te*o,i+e,r+o),t.lineTo(i+e,r+s-o),h&&t.bezierCurveTo(i+e,r+s-te*o,i+e-te*n,r+s,i+e-n,r+s),t.lineTo(i+n,r+s),h&&t.bezierCurveTo(i+te*n,r+s,i,r+s-te*o,i,r+s-o),t.lineTo(i,r+o),h&&t.bezierCurveTo(i,r+te*o,i+te*n,r,i+n,r),t.closePath(),this._renderPaintInOrder(t)}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return super.toObject([...gn,...t])}_toSVG(){const{width:t,height:e,rx:s,ry:i}=this;return["<rect ","COMMON_PARTS",'x="'.concat(-t/2,'" y="').concat(-e/2,'" rx="').concat(s,'" ry="').concat(i,'" width="').concat(t,'" height="').concat(e,`" />
`)]}static async fromElement(t,e,s){const i=Qt(t,this.ATTRIBUTE_NAMES,s),{left:r=0,top:n=0,width:o=0,height:h=0,visible:l=!0}=i,c=N(i,ba);return new this(m(m(m({},e),c),{},{left:r,top:n,width:o,height:h,visible:!!(l&&o&&h)}))}}p(Lt,"type","Rect"),p(Lt,"cacheProperties",[...Zt,...gn]),p(Lt,"ownDefaults",{rx:0,ry:0}),p(Lt,"ATTRIBUTE_NAMES",[...re,"x","y","rx","ry","width","height"]),D.setClass(Lt),D.setSVGClass(Lt);const Ht="initialization",Fs="added",Bi="removed",Ls="imperative",dn=(a,t)=>{const{strokeUniform:e,strokeWidth:s,width:i,height:r,group:n}=t,o=n&&n!==a?Vs(n.calcTransformMatrix(),a.calcTransformMatrix()):null,h=o?t.getRelativeCenterPoint().transform(o):t.getRelativeCenterPoint(),l=!t.isStrokeAccountedForInDimensions(),c=e&&l?no(new y(s,s),void 0,a.calcTransformMatrix()):Ti,g=!e&&l?s:0,u=Mi(i+g,r+g,Oi([o,t.calcOwnMatrix()],!0)).add(c).scalarDivide(2);return[h.subtract(u),h.add(u)]};class Gs{calcLayoutResult(t,e){if(this.shouldPerformLayout(t))return this.calcBoundingBox(e,t)}shouldPerformLayout(t){let{type:e,prevStrategy:s,strategy:i}=t;return e===Ht||e===Ls||!!s&&i!==s}shouldLayoutClipPath(t){let{type:e,target:{clipPath:s}}=t;return e!==Ht&&s&&!s.absolutePositioned}getInitialSize(t,e){return e.size}calcBoundingBox(t,e){const{type:s,target:i}=e;if(s===Ls&&e.overrides)return e.overrides;if(t.length===0)return;const{left:r,top:n,width:o,height:h}=Ut(t.map((g=>dn(i,g))).reduce(((g,u)=>g.concat(u)),[])),l=new y(o,h),c=new y(r,n).add(l.scalarDivide(2));if(s===Ht){const g=this.getInitialSize(e,{size:l,center:c});return{center:c,relativeCorrection:new y(0,0),size:g}}return{center:c.transform(i.calcOwnMatrix()),size:l}}}p(Gs,"type","strategy");class mi extends Gs{shouldPerformLayout(t){return!0}}p(mi,"type","fit-content"),D.setClass(mi);const Ca=["strategy"],Sa=["target","strategy","bubbles","prevStrategy"],fn="layoutManager";class Ge{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:new mi;p(this,"strategy",void 0),this.strategy=t,this._subscriptions=new Map}performLayout(t){const e=m(m({bubbles:!0,strategy:this.strategy},t),{},{prevStrategy:this._prevLayoutStrategy,stopPropagation(){this.bubbles=!1}});this.onBeforeLayout(e);const s=this.getLayoutResult(e);s&&this.commitLayout(e,s),this.onAfterLayout(e,s),this._prevLayoutStrategy=e.strategy}attachHandlers(t,e){const{target:s}=e;return[ks,Er,Xe,Pr,Bs,jr,Ds,Gn,Un].map((i=>t.on(i,(r=>this.performLayout(i===ks?{type:"object_modified",trigger:i,e:r,target:s}:{type:"object_modifying",trigger:i,e:r,target:s})))))}subscribe(t,e){this.unsubscribe(t,e);const s=this.attachHandlers(t,e);this._subscriptions.set(t,s)}unsubscribe(t,e){(this._subscriptions.get(t)||[]).forEach((s=>s())),this._subscriptions.delete(t)}unsubscribeTargets(t){t.targets.forEach((e=>this.unsubscribe(e,t)))}subscribeTargets(t){t.targets.forEach((e=>this.subscribe(e,t)))}onBeforeLayout(t){const{target:e,type:s}=t,{canvas:i}=e;if(s===Ht||s===Fs?this.subscribeTargets(t):s===Bi&&this.unsubscribeTargets(t),e.fire("layout:before",{context:t}),i&&i.fire("object:layout:before",{target:e,context:t}),s===Ls&&t.deep){const r=N(t,Ca);e.forEachObject((n=>n.layoutManager&&n.layoutManager.performLayout(m(m({},r),{},{bubbles:!1,target:n}))))}}getLayoutResult(t){const{target:e,strategy:s,type:i}=t,r=s.calcLayoutResult(t,e.getObjects());if(!r)return;const n=i===Ht?new y:e.getRelativeCenterPoint(),{center:o,correction:h=new y,relativeCorrection:l=new y}=r,c=n.subtract(o).add(h).transform(i===Ht?pt:jt(e.calcOwnMatrix()),!0).add(l);return{result:r,prevCenter:n,nextCenter:o,offset:c}}commitLayout(t,e){const{target:s}=t,{result:{size:i},nextCenter:r}=e;var n,o;s.set({width:i.x,height:i.y}),this.layoutObjects(t,e),t.type===Ht?s.set({left:(n=t.x)!==null&&n!==void 0?n:r.x+i.x*st(s.originX),top:(o=t.y)!==null&&o!==void 0?o:r.y+i.y*st(s.originY)}):(s.setPositionByOrigin(r,F,F),s.setCoords(),s.set("dirty",!0))}layoutObjects(t,e){const{target:s}=t;s.forEachObject((i=>{i.group===s&&this.layoutObject(t,e,i)})),t.strategy.shouldLayoutClipPath(t)&&this.layoutObject(t,e,s.clipPath)}layoutObject(t,e,s){let{offset:i}=e;s.set({left:s.left+i.x,top:s.top+i.y})}onAfterLayout(t,e){const{target:s,strategy:i,bubbles:r,prevStrategy:n}=t,o=N(t,Sa),{canvas:h}=s;s.fire("layout:after",{context:t,result:e}),h&&h.fire("object:layout:after",{context:t,result:e,target:s});const l=s.parent;r&&l!=null&&l.layoutManager&&((o.path||(o.path=[])).push(s),l.layoutManager.performLayout(m(m({},o),{},{target:l}))),s.set("dirty",!0)}dispose(){const{_subscriptions:t}=this;t.forEach((e=>e.forEach((s=>s())))),t.clear()}toObject(){return{type:fn,strategy:this.strategy.constructor.type}}toJSON(){return this.toObject()}}D.setClass(Ge,fn);const wa=["type","objects","layoutManager"];class Ta extends Ge{performLayout(){}}class ue extends Ar(ut){static getDefaults(){return m(m({},super.getDefaults()),ue.ownDefaults)}constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),p(this,"_activeObjects",[]),p(this,"__objectSelectionTracker",void 0),p(this,"__objectSelectionDisposer",void 0),Object.assign(this,ue.ownDefaults),this.setOptions(e),this.groupInit(t,e)}groupInit(t,e){var s;this._objects=[...t],this.__objectSelectionTracker=this.__objectSelectionMonitor.bind(this,!0),this.__objectSelectionDisposer=this.__objectSelectionMonitor.bind(this,!1),this.forEachObject((i=>{this.enterGroup(i,!1)})),this.layoutManager=(s=e.layoutManager)!==null&&s!==void 0?s:new Ge,this.layoutManager.performLayout({type:Ht,target:this,targets:[...t],x:e.left,y:e.top})}canEnterGroup(t){return t===this||this.isDescendantOf(t)?(se("error","Group: circular object trees are not supported, this call has no effect"),!1):this._objects.indexOf(t)===-1||(se("error","Group: duplicate objects are not supported inside group, this call has no effect"),!1)}_filterObjectsBeforeEnteringGroup(t){return t.filter(((e,s,i)=>this.canEnterGroup(e)&&i.indexOf(e)===s))}add(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];const i=this._filterObjectsBeforeEnteringGroup(e),r=super.add(...i);return this._onAfterObjectsChange(Fs,i),r}insertAt(t){for(var e=arguments.length,s=new Array(e>1?e-1:0),i=1;i<e;i++)s[i-1]=arguments[i];const r=this._filterObjectsBeforeEnteringGroup(s),n=super.insertAt(t,...r);return this._onAfterObjectsChange(Fs,r),n}remove(){const t=super.remove(...arguments);return this._onAfterObjectsChange(Bi,t),t}_onObjectAdded(t){this.enterGroup(t,!0),this.fire("object:added",{target:t}),t.fire("added",{target:this})}_onObjectRemoved(t,e){this.exitGroup(t,e),this.fire("object:removed",{target:t}),t.fire("removed",{target:this})}_onAfterObjectsChange(t,e){this.layoutManager.performLayout({type:t,targets:e,target:this})}_onStackOrderChanged(){this._set("dirty",!0)}_set(t,e){const s=this[t];return super._set(t,e),t==="canvas"&&s!==e&&(this._objects||[]).forEach((i=>{i._set(t,e)})),this}_shouldSetNestedCoords(){return this.subTargetCheck}removeAll(){return this._activeObjects=[],this.remove(...this._objects)}__objectSelectionMonitor(t,e){let{target:s}=e;const i=this._activeObjects;if(t)i.push(s),this._set("dirty",!0);else if(i.length>0){const r=i.indexOf(s);r>-1&&(i.splice(r,1),this._set("dirty",!0))}}_watchObject(t,e){t&&this._watchObject(!1,e),t?(e.on("selected",this.__objectSelectionTracker),e.on("deselected",this.__objectSelectionDisposer)):(e.off("selected",this.__objectSelectionTracker),e.off("deselected",this.__objectSelectionDisposer))}enterGroup(t,e){t.group&&t.group.remove(t),t._set("parent",this),this._enterGroup(t,e)}_enterGroup(t,e){e&&Ps(t,lt(jt(this.calcTransformMatrix()),t.calcTransformMatrix())),this._shouldSetNestedCoords()&&t.setCoords(),t._set("group",this),t._set("canvas",this.canvas),this._watchObject(!0,t);const s=this.canvas&&this.canvas.getActiveObject&&this.canvas.getActiveObject();s&&(s===t||t.isDescendantOf(s))&&this._activeObjects.push(t)}exitGroup(t,e){this._exitGroup(t,e),t._set("parent",void 0),t._set("canvas",void 0)}_exitGroup(t,e){t._set("group",void 0),e||(Ps(t,lt(this.calcTransformMatrix(),t.calcTransformMatrix())),t.setCoords()),this._watchObject(!1,t);const s=this._activeObjects.length>0?this._activeObjects.indexOf(t):-1;s>-1&&this._activeObjects.splice(s,1)}shouldCache(){const t=ut.prototype.shouldCache.call(this);if(t){for(let e=0;e<this._objects.length;e++)if(this._objects[e].willDrawShadow())return this.ownCaching=!1,!1}return t}willDrawShadow(){if(super.willDrawShadow())return!0;for(let t=0;t<this._objects.length;t++)if(this._objects[t].willDrawShadow())return!0;return!1}isOnACache(){return this.ownCaching||!!this.parent&&this.parent.isOnACache()}drawObject(t,e,s){this._renderBackground(t);for(let r=0;r<this._objects.length;r++){var i;const n=this._objects[r];(i=this.canvas)!==null&&i!==void 0&&i.preserveObjectStacking&&n.group!==this?(t.save(),t.transform(...jt(this.calcTransformMatrix())),n.render(t),t.restore()):n.group===this&&n.render(t)}this._drawClipPath(t,this.clipPath,s)}setCoords(){super.setCoords(),this._shouldSetNestedCoords()&&this.forEachObject((t=>t.setCoords()))}triggerLayout(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.layoutManager.performLayout(m({target:this,type:Ls},t))}render(t){this._transformDone=!0,super.render(t),this._transformDone=!1}__serializeObjects(t,e){const s=this.includeDefaultValues;return this._objects.filter((function(i){return!i.excludeFromExport})).map((function(i){const r=i.includeDefaultValues;i.includeDefaultValues=s;const n=i[t||"toObject"](e);return i.includeDefaultValues=r,n}))}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const e=this.layoutManager.toObject();return m(m(m({},super.toObject(["subTargetCheck","interactive",...t])),e.strategy!=="fit-content"||this.includeDefaultValues?{layoutManager:e}:{}),{},{objects:this.__serializeObjects("toObject",t)})}toString(){return"#<Group: (".concat(this.complexity(),")>")}dispose(){this.layoutManager.unsubscribeTargets({targets:this.getObjects(),target:this}),this._activeObjects=[],this.forEachObject((t=>{this._watchObject(!1,t),t.dispose()})),super.dispose()}_createSVGBgRect(t){if(!this.backgroundColor)return"";const e=Lt.prototype._toSVG.call(this),s=e.indexOf("COMMON_PARTS");e[s]='for="group" ';const i=e.join("");return t?t(i):i}_toSVG(t){const e=["<g ","COMMON_PARTS",` >
`],s=this._createSVGBgRect(t);s&&e.push("		",s);for(let i=0;i<this._objects.length;i++)e.push("		",this._objects[i].toSVG(t));return e.push(`</g>
`),e}getSvgStyles(){const t=this.opacity!==void 0&&this.opacity!==1?"opacity: ".concat(this.opacity,";"):"",e=this.visible?"":" visibility: hidden;";return[t,this.getSvgFilter(),e].join("")}toClipPathSVG(t){const e=[],s=this._createSVGBgRect(t);s&&e.push("	",s);for(let i=0;i<this._objects.length;i++)e.push("	",this._objects[i].toClipPathSVG(t));return this._createBaseClipPathSVGMarkup(e,{reviver:t})}static fromObject(t,e){let{type:s,objects:i=[],layoutManager:r}=t,n=N(t,wa);return Promise.all([Ye(i,e),Ys(n,e)]).then((o=>{let[h,l]=o;const c=new this(h,m(m(m({},n),l),{},{layoutManager:new Ta}));if(r){const g=D.getClass(r.type),u=D.getClass(r.strategy);c.layoutManager=new g(new u)}else c.layoutManager=new Ge;return c.layoutManager.subscribeTargets({type:Ht,target:c,targets:c.getObjects()}),c.setCoords(),c}))}}p(ue,"type","Group"),p(ue,"ownDefaults",{strokeWidth:0,subTargetCheck:!1,interactive:!1}),D.setClass(ue);const Oa=(a,t)=>Math.min(t.width/a.width,t.height/a.height),Da=(a,t)=>Math.max(t.width/a.width,t.height/a.height),vi="\\s*,?\\s*",Fe="".concat(vi,"(").concat(At,")"),ka="".concat(Fe).concat(Fe).concat(Fe).concat(vi,"([01])").concat(vi,"([01])").concat(Fe).concat(Fe),Ma={m:"l",M:"L"},Ea=(a,t,e,s,i,r,n,o,h,l,c)=>{const g=qt(a),u=$t(a),d=qt(t),f=$t(t),v=e*i*d-s*r*f+n,_=s*i*d+e*r*f+o;return["C",l+h*(-e*i*u-s*r*g),c+h*(-s*i*u+e*r*g),v+h*(e*i*f+s*r*d),_+h*(s*i*f-e*r*d),v,_]},_r=(a,t,e,s)=>{const i=Math.atan2(t,a),r=Math.atan2(s,e);return r>=i?r-i:2*Math.PI-(i-r)};function xr(a,t,e,s,i,r,n,o){let h;if(B.cachesBoundsOfCurve&&(h=[...arguments].join(),Ie.boundsOfCurveCache[h]))return Ie.boundsOfCurveCache[h];const l=Math.sqrt,c=Math.abs,g=[],u=[[0,0],[0,0]];let d=6*a-12*e+6*i,f=-3*a+9*e-9*i+3*n,v=3*e-3*a;for(let w=0;w<2;++w){if(w>0&&(d=6*t-12*s+6*r,f=-3*t+9*s-9*r+3*o,v=3*s-3*t),c(f)<1e-12){if(c(d)<1e-12)continue;const W=-v/d;0<W&&W<1&&g.push(W);continue}const S=d*d-4*v*f;if(S<0)continue;const M=l(S),L=(-d+M)/(2*f);0<L&&L<1&&g.push(L);const E=(-d-M)/(2*f);0<E&&E<1&&g.push(E)}let _=g.length;const b=_,C=pn(a,t,e,s,i,r,n,o);for(;_--;){const{x:w,y:S}=C(g[_]);u[0][_]=w,u[1][_]=S}u[0][b]=a,u[1][b]=t,u[0][b+1]=n,u[1][b+1]=o;const O=[new y(Math.min(...u[0]),Math.min(...u[1])),new y(Math.max(...u[0]),Math.max(...u[1]))];return B.cachesBoundsOfCurve&&(Ie.boundsOfCurveCache[h]=O),O}const Pa=(a,t,e)=>{let[s,i,r,n,o,h,l,c]=e;const g=((u,d,f,v,_,b,C)=>{if(f===0||v===0)return[];let O=0,w=0,S=0;const M=Math.PI,L=C*bi,E=$t(L),W=qt(L),$=.5*(-W*u-E*d),tt=.5*(-W*d+E*u),H=f**2,Y=v**2,R=tt**2,it=$**2,et=H*Y-H*R-Y*it;let rt=Math.abs(f),V=Math.abs(v);if(et<0){const ct=Math.sqrt(1-et/(H*Y));rt*=ct,V*=ct}else S=(_===b?-1:1)*Math.sqrt(et/(H*R+Y*it));const x=S*rt*tt/V,k=-S*V*$/rt,A=W*x-E*k+.5*u,G=E*x+W*k+.5*d;let I=_r(1,0,($-x)/rt,(tt-k)/V),K=_r(($-x)/rt,(tt-k)/V,(-$-x)/rt,(-tt-k)/V);b===0&&K>0?K-=2*M:b===1&&K<0&&(K+=2*M);const j=Math.ceil(Math.abs(K/M*2)),J=[],ht=K/j,fe=8/3*Math.sin(ht/4)*Math.sin(ht/4)/Math.sin(ht/2);let at=I+ht;for(let ct=0;ct<j;ct++)J[ct]=Ea(I,at,W,E,rt,V,A,G,fe,O,w),O=J[ct][5],w=J[ct][6],I=at,at+=ht;return J})(l-a,c-t,i,r,o,h,n);for(let u=0,d=g.length;u<d;u++)g[u][1]+=a,g[u][2]+=t,g[u][3]+=a,g[u][4]+=t,g[u][5]+=a,g[u][6]+=t;return g},ja=a=>{let t=0,e=0,s=0,i=0;const r=[];let n,o=0,h=0;for(const l of a){const c=[...l];let g;switch(c[0]){case"l":c[1]+=t,c[2]+=e;case"L":t=c[1],e=c[2],g=["L",t,e];break;case"h":c[1]+=t;case"H":t=c[1],g=["L",t,e];break;case"v":c[1]+=e;case"V":e=c[1],g=["L",t,e];break;case"m":c[1]+=t,c[2]+=e;case"M":t=c[1],e=c[2],s=c[1],i=c[2],g=["M",t,e];break;case"c":c[1]+=t,c[2]+=e,c[3]+=t,c[4]+=e,c[5]+=t,c[6]+=e;case"C":o=c[3],h=c[4],t=c[5],e=c[6],g=["C",c[1],c[2],o,h,t,e];break;case"s":c[1]+=t,c[2]+=e,c[3]+=t,c[4]+=e;case"S":n==="C"?(o=2*t-o,h=2*e-h):(o=t,h=e),t=c[3],e=c[4],g=["C",o,h,c[1],c[2],t,e],o=g[3],h=g[4];break;case"q":c[1]+=t,c[2]+=e,c[3]+=t,c[4]+=e;case"Q":o=c[1],h=c[2],t=c[3],e=c[4],g=["Q",o,h,t,e];break;case"t":c[1]+=t,c[2]+=e;case"T":n==="Q"?(o=2*t-o,h=2*e-h):(o=t,h=e),t=c[1],e=c[2],g=["Q",o,h,t,e];break;case"a":c[6]+=t,c[7]+=e;case"A":Pa(t,e,c).forEach((u=>r.push(u))),t=c[6],e=c[7];break;case"z":case"Z":t=s,e=i,g=["Z"]}g?(r.push(g),n=g[0]):n=""}return r},Is=(a,t,e,s)=>Math.sqrt((e-a)**2+(s-t)**2),pn=(a,t,e,s,i,r,n,o)=>h=>{const l=h**3,c=(d=>3*d**2*(1-d))(h),g=(d=>3*d*(1-d)**2)(h),u=(d=>(1-d)**3)(h);return new y(n*l+i*c+e*g+a*u,o*l+r*c+s*g+t*u)},mn=a=>a**2,vn=a=>2*a*(1-a),yn=a=>(1-a)**2,Aa=(a,t,e,s,i,r,n,o)=>h=>{const l=mn(h),c=vn(h),g=yn(h),u=3*(g*(e-a)+c*(i-e)+l*(n-i)),d=3*(g*(s-t)+c*(r-s)+l*(o-r));return Math.atan2(d,u)},Fa=(a,t,e,s,i,r)=>n=>{const o=mn(n),h=vn(n),l=yn(n);return new y(i*o+e*h+a*l,r*o+s*h+t*l)},La=(a,t,e,s,i,r)=>n=>{const o=1-n,h=2*(o*(e-a)+n*(i-e)),l=2*(o*(s-t)+n*(r-s));return Math.atan2(l,h)},br=(a,t,e)=>{let s=new y(t,e),i=0;for(let r=1;r<=100;r+=1){const n=a(r/100);i+=Is(s.x,s.y,n.x,n.y),s=n}return i},Ia=(a,t)=>{let e,s=0,i=0,r={x:a.x,y:a.y},n=m({},r),o=.01,h=0;const l=a.iterator,c=a.angleFinder;for(;i<t&&o>1e-4;)n=l(s),h=s,e=Is(r.x,r.y,n.x,n.y),e+i>t?(s-=o,o/=2):(r=n,s+=o,i+=e);return m(m({},n),{},{angle:c(h)})},_n=a=>{let t,e,s=0,i=0,r=0,n=0,o=0;const h=[];for(const l of a){const c={x:i,y:r,command:l[0],length:0};switch(l[0]){case"M":e=c,e.x=n=i=l[1],e.y=o=r=l[2];break;case"L":e=c,e.length=Is(i,r,l[1],l[2]),i=l[1],r=l[2];break;case"C":t=pn(i,r,l[1],l[2],l[3],l[4],l[5],l[6]),e=c,e.iterator=t,e.angleFinder=Aa(i,r,l[1],l[2],l[3],l[4],l[5],l[6]),e.length=br(t,i,r),i=l[5],r=l[6];break;case"Q":t=Fa(i,r,l[1],l[2],l[3],l[4]),e=c,e.iterator=t,e.angleFinder=La(i,r,l[1],l[2],l[3],l[4]),e.length=br(t,i,r),i=l[3],r=l[4];break;case"Z":e=c,e.destX=n,e.destY=o,e.length=Is(i,r,n,o),i=n,r=o}s+=e.length,h.push(e)}return h.push({length:s,x:i,y:r}),h},Ra=function(a,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:_n(a),s=0;for(;t-e[s].length>0&&s<e.length-2;)t-=e[s].length,s++;const i=e[s],r=t/i.length,n=a[s];switch(i.command){case"M":return{x:i.x,y:i.y,angle:0};case"Z":return m(m({},new y(i.x,i.y).lerp(new y(i.destX,i.destY),r)),{},{angle:Math.atan2(i.destY-i.y,i.destX-i.x)});case"L":return m(m({},new y(i.x,i.y).lerp(new y(n[1],n[2]),r)),{},{angle:Math.atan2(n[2]-i.y,n[1]-i.x)});case"C":case"Q":return Ia(i,t)}},Ba=new RegExp("[mzlhvcsqta][^mzlhvcsqta]*","gi"),Cr=new RegExp(ka,"g"),Wa=new RegExp(At,"gi"),Xa={m:2,l:2,h:1,v:1,c:6,s:4,q:4,t:2,a:7},Ya=a=>{var t;const e=[],s=(t=a.match(Ba))!==null&&t!==void 0?t:[];for(const i of s){const r=i[0];if(r==="z"||r==="Z"){e.push([r]);continue}const n=Xa[r.toLowerCase()];let o=[];if(r==="a"||r==="A"){Cr.lastIndex=0;for(let h=null;h=Cr.exec(i);)o.push(...h.slice(1))}else o=i.match(Wa)||[];for(let h=0;h<o.length;h+=n){const l=new Array(n),c=Ma[r];l[0]=h>0&&c?c:r;for(let g=0;g<n;g++)l[g+1]=parseFloat(o[h+g]);e.push(l)}}return e},Va=(a,t)=>a.map((e=>e.map(((s,i)=>i===0||t===void 0?s:U(s,t))).join(" "))).join(" ");function yi(a,t){const e=a.style;e&&t&&(typeof t=="string"?e.cssText+=";"+t:Object.entries(t).forEach((s=>{let[i,r]=s;return e.setProperty(i,r)})))}class za extends Hr{constructor(t){let{allowTouchScrolling:e=!1,containerClass:s=""}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(t),p(this,"upper",void 0),p(this,"container",void 0);const{el:i}=this.lower,r=this.createUpperCanvas();this.upper={el:r,ctx:r.getContext("2d")},this.applyCanvasStyle(i,{allowTouchScrolling:e}),this.applyCanvasStyle(r,{allowTouchScrolling:e,styles:{position:"absolute",left:"0",top:"0"}});const n=this.createContainerElement();n.classList.add(s),i.parentNode&&i.parentNode.replaceChild(n,i),n.append(i,r),this.container=n}createUpperCanvas(){const{el:t}=this.lower,e=Kt();return e.className=t.className,e.classList.remove("lower-canvas"),e.classList.add("upper-canvas"),e.setAttribute("data-fabric","top"),e.style.cssText=t.style.cssText,e.setAttribute("draggable","true"),e}createContainerElement(){const t=Oe().createElement("div");return t.setAttribute("data-fabric","wrapper"),yi(t,{position:"relative"}),Ni(t),t}applyCanvasStyle(t,e){const{styles:s,allowTouchScrolling:i}=e;yi(t,m(m({},s),{},{"touch-action":i?"manipulation":yt})),Ni(t)}setDimensions(t,e){super.setDimensions(t,e);const{el:s,ctx:i}=this.upper;zr(s,i,t,e)}setCSSDimensions(t){super.setCSSDimensions(t),ci(this.upper.el,t),ci(this.container,t)}cleanupDOM(t){const e=this.container,{el:s}=this.lower,{el:i}=this.upper;super.cleanupDOM(t),e.removeChild(i),e.removeChild(s),e.parentNode&&e.parentNode.replaceChild(s,e)}dispose(){super.dispose(),Bt().dispose(this.upper.el),delete this.upper,delete this.container}}class Us extends qe{constructor(){super(...arguments),p(this,"targets",[]),p(this,"_hoveredTargets",[]),p(this,"_objectsToRender",void 0),p(this,"_currentTransform",null),p(this,"_groupSelector",null),p(this,"contextTopDirty",!1)}static getDefaults(){return m(m({},super.getDefaults()),Us.ownDefaults)}get upperCanvasEl(){var t;return(t=this.elements.upper)===null||t===void 0?void 0:t.el}get contextTop(){var t;return(t=this.elements.upper)===null||t===void 0?void 0:t.ctx}get wrapperEl(){return this.elements.container}initElements(t){this.elements=new za(t,{allowTouchScrolling:this.allowTouchScrolling,containerClass:this.containerClass}),this._createCacheCanvas()}_onObjectAdded(t){this._objectsToRender=void 0,super._onObjectAdded(t)}_onObjectRemoved(t){this._objectsToRender=void 0,t===this._activeObject&&(this.fire("before:selection:cleared",{deselected:[t]}),this._discardActiveObject(),this.fire("selection:cleared",{deselected:[t]}),t.fire("deselected",{target:t})),t===this._hoveredTarget&&(this._hoveredTarget=void 0,this._hoveredTargets=[]),super._onObjectRemoved(t)}_onStackOrderChanged(){this._objectsToRender=void 0,super._onStackOrderChanged()}_chooseObjectsToRender(){const t=this._activeObject;return!this.preserveObjectStacking&&t?this._objects.filter((e=>!e.group&&e!==t)).concat(t):this._objects}renderAll(){this.cancelRequestedRender(),this.destroyed||(!this.contextTopDirty||this._groupSelector||this.isDrawingMode||(this.clearContext(this.contextTop),this.contextTopDirty=!1),this.hasLostContext&&(this.renderTopLayer(this.contextTop),this.hasLostContext=!1),!this._objectsToRender&&(this._objectsToRender=this._chooseObjectsToRender()),this.renderCanvas(this.getContext(),this._objectsToRender))}renderTopLayer(t){t.save(),this.isDrawingMode&&this._isCurrentlyDrawing&&(this.freeDrawingBrush&&this.freeDrawingBrush._render(),this.contextTopDirty=!0),this.selection&&this._groupSelector&&(this._drawSelection(t),this.contextTopDirty=!0),t.restore()}renderTop(){const t=this.contextTop;this.clearContext(t),this.renderTopLayer(t),this.fire("after:render",{ctx:t})}setTargetFindTolerance(t){t=Math.round(t),this.targetFindTolerance=t;const e=this.getRetinaScaling(),s=Math.ceil((2*t+1)*e);this.pixelFindCanvasEl.width=this.pixelFindCanvasEl.height=s,this.pixelFindContext.scale(e,e)}isTargetTransparent(t,e,s){const i=this.targetFindTolerance,r=this.pixelFindContext;this.clearContext(r),r.save(),r.translate(-e+i,-s+i),r.transform(...this.viewportTransform);const n=t.selectionBackgroundColor;t.selectionBackgroundColor="",t.render(r),t.selectionBackgroundColor=n,r.restore();const o=Math.round(i*this.getRetinaScaling());return No(r,o,o,o)}_isSelectionKeyPressed(t){const e=this.selectionKey;return!!e&&(Array.isArray(e)?!!e.find((s=>!!s&&t[s]===!0)):t[e])}_shouldClearSelection(t,e){const s=this.getActiveObjects(),i=this._activeObject;return!!(!e||e&&i&&s.length>1&&s.indexOf(e)===-1&&i!==e&&!this._isSelectionKeyPressed(t)||e&&!e.evented||e&&!e.selectable&&i&&i!==e)}_shouldCenterTransform(t,e,s){if(!t)return;let i;return e===Ws||e===xt||e===wt||e===Xe?i=this.centeredScaling||t.centeredScaling:e===wi&&(i=this.centeredRotation||t.centeredRotation),i?!s:s}_getOriginFromCorner(t,e){const s={x:t.originX,y:t.originY};return e&&(["ml","tl","bl"].includes(e)?s.x=Z:["mr","tr","br"].includes(e)&&(s.x=X),["tl","mt","tr"].includes(e)?s.y=li:["bl","mb","br"].includes(e)&&(s.y=vt)),s}_setupCurrentTransform(t,e,s){var i;const r=e.group?Ce(this.getScenePoint(t),void 0,e.group.calcTransformMatrix()):this.getScenePoint(t),{key:n="",control:o}=e.getActiveControl()||{},h=s&&o?(i=o.getActionHandler(t,e,o))===null||i===void 0?void 0:i.bind(o):ho,l=((d,f,v,_)=>{if(!f||!d)return"drag";const b=_.controls[f];return b.getActionName(v,b,_)})(s,n,t,e),c=t[this.centeredKey],g=this._shouldCenterTransform(e,l,c)?{x:F,y:F}:this._getOriginFromCorner(e,n),u={target:e,action:l,actionHandler:h,actionPerformed:!1,corner:n,scaleX:e.scaleX,scaleY:e.scaleY,skewX:e.skewX,skewY:e.skewY,offsetX:r.x-e.left,offsetY:r.y-e.top,originX:g.x,originY:g.y,ex:r.x,ey:r.y,lastX:r.x,lastY:r.y,theta:Q(e.angle),width:e.width,height:e.height,shiftKey:t.shiftKey,altKey:c,original:m(m({},Gr(e)),{},{originX:g.x,originY:g.y})};this._currentTransform=u,this.fire("before:transform",{e:t,transform:u})}setCursor(t){this.upperCanvasEl.style.cursor=t}_drawSelection(t){const{x:e,y:s,deltaX:i,deltaY:r}=this._groupSelector,n=new y(e,s).transform(this.viewportTransform),o=new y(e+i,s+r).transform(this.viewportTransform),h=this.selectionLineWidth/2;let l=Math.min(n.x,o.x),c=Math.min(n.y,o.y),g=Math.max(n.x,o.x),u=Math.max(n.y,o.y);this.selectionColor&&(t.fillStyle=this.selectionColor,t.fillRect(l,c,g-l,u-c)),this.selectionLineWidth&&this.selectionBorderColor&&(t.lineWidth=this.selectionLineWidth,t.strokeStyle=this.selectionBorderColor,l+=h,c+=h,g-=h,u-=h,ut.prototype._setLineDash.call(this,t,this.selectionDashArray),t.strokeRect(l,c,g-l,u-c))}findTarget(t){if(this.skipTargetFind)return;const e=this.getViewportPoint(t),s=this._activeObject,i=this.getActiveObjects();if(this.targets=[],s&&i.length>=1){if(s.findControl(e,ui(t))||i.length>1&&this.searchPossibleTargets([s],e))return s;if(s===this.searchPossibleTargets([s],e)){if(this.preserveObjectStacking){const r=this.targets;this.targets=[];const n=this.searchPossibleTargets(this._objects,e);return t[this.altSelectionKey]&&n&&n!==s?(this.targets=r,s):n}return s}}return this.searchPossibleTargets(this._objects,e)}_pointIsInObjectSelectionArea(t,e){let s=t.getCoords();const i=this.getZoom(),r=t.padding/i;if(r){const[n,o,h,l]=s,c=Math.atan2(o.y-n.y,o.x-n.x),g=qt(c)*r,u=$t(c)*r,d=g+u,f=g-u;s=[new y(n.x-f,n.y-d),new y(o.x+d,o.y-f),new y(h.x+f,h.y+d),new y(l.x-d,l.y+f)]}return q.isPointInPolygon(e,s)}_checkTarget(t,e){return!!(t&&t.visible&&t.evented&&this._pointIsInObjectSelectionArea(t,Ce(e,void 0,this.viewportTransform))&&(!this.perPixelTargetFind&&!t.perPixelTargetFind||t.isEditing||!this.isTargetTransparent(t,e.x,e.y)))}_searchPossibleTargets(t,e){let s=t.length;for(;s--;){const i=t[s];if(this._checkTarget(i,e)){if(Cs(i)&&i.subTargetCheck){const r=this._searchPossibleTargets(i._objects,e);r&&this.targets.push(r)}return i}}}searchPossibleTargets(t,e){const s=this._searchPossibleTargets(t,e);if(s&&Cs(s)&&s.interactive&&this.targets[0]){const i=this.targets;for(let r=i.length-1;r>0;r--){const n=i[r];if(!Cs(n)||!n.interactive)return n}return i[0]}return s}getViewportPoint(t){return this._pointer?this._pointer:this.getPointer(t,!0)}getScenePoint(t){return this._absolutePointer?this._absolutePointer:this.getPointer(t)}getPointer(t){let e=arguments.length>1&&arguments[1]!==void 0&&arguments[1];const s=this.upperCanvasEl,i=s.getBoundingClientRect();let r=eo(t),n=i.width||0,o=i.height||0;n&&o||(vt in i&&li in i&&(o=Math.abs(i.top-i.bottom)),Z in i&&X in i&&(n=Math.abs(i.right-i.left))),this.calcOffset(),r.x=r.x-this._offset.left,r.y=r.y-this._offset.top,e||(r=Ce(r,void 0,this.viewportTransform));const h=this.getRetinaScaling();h!==1&&(r.x/=h,r.y/=h);const l=n===0||o===0?new y(1,1):new y(s.width/n,s.height/o);return r.multiply(l)}_setDimensionsImpl(t,e){this._resetTransformEventData(),super._setDimensionsImpl(t,e),this._isCurrentlyDrawing&&this.freeDrawingBrush&&this.freeDrawingBrush._setBrushStyles(this.contextTop)}_createCacheCanvas(){this.pixelFindCanvasEl=Kt(),this.pixelFindContext=this.pixelFindCanvasEl.getContext("2d",{willReadFrequently:!0}),this.setTargetFindTolerance(this.targetFindTolerance)}getTopContext(){return this.elements.upper.ctx}getSelectionContext(){return this.elements.upper.ctx}getSelectionElement(){return this.elements.upper.el}getActiveObject(){return this._activeObject}getActiveObjects(){const t=this._activeObject;return ne(t)?t.getObjects():t?[t]:[]}_fireSelectionEvents(t,e){let s=!1,i=!1;const r=this.getActiveObjects(),n=[],o=[];t.forEach((h=>{r.includes(h)||(s=!0,h.fire("deselected",{e,target:h}),o.push(h))})),r.forEach((h=>{t.includes(h)||(s=!0,h.fire("selected",{e,target:h}),n.push(h))})),t.length>0&&r.length>0?(i=!0,s&&this.fire("selection:updated",{e,selected:n,deselected:o})):r.length>0?(i=!0,this.fire("selection:created",{e,selected:n})):t.length>0&&(i=!0,this.fire("selection:cleared",{e,deselected:o})),i&&(this._objectsToRender=void 0)}setActiveObject(t,e){const s=this.getActiveObjects(),i=this._setActiveObject(t,e);return this._fireSelectionEvents(s,e),i}_setActiveObject(t,e){const s=this._activeObject;return s!==t&&!(!this._discardActiveObject(e,t)&&this._activeObject)&&!t.onSelect({e})&&(this._activeObject=t,ne(t)&&s!==t&&t.set("canvas",this),t.setCoords(),!0)}_discardActiveObject(t,e){const s=this._activeObject;return!!s&&!s.onDeselect({e:t,object:e})&&(this._currentTransform&&this._currentTransform.target===s&&this.endCurrentTransform(t),ne(s)&&s===this._hoveredTarget&&(this._hoveredTarget=void 0),this._activeObject=void 0,!0)}discardActiveObject(t){const e=this.getActiveObjects(),s=this.getActiveObject();e.length&&this.fire("before:selection:cleared",{e:t,deselected:[s]});const i=this._discardActiveObject(t);return this._fireSelectionEvents(e,t),i}endCurrentTransform(t){const e=this._currentTransform;this._finalizeCurrentTransform(t),e&&e.target&&(e.target.isMoving=!1),this._currentTransform=null}_finalizeCurrentTransform(t){const e=this._currentTransform,s=e.target,i={e:t,target:s,transform:e,action:e.action};s._scaling&&(s._scaling=!1),s.setCoords(),e.actionPerformed&&(this.fire("object:modified",i),s.fire(ks,i))}setViewportTransform(t){super.setViewportTransform(t);const e=this._activeObject;e&&e.setCoords()}destroy(){const t=this._activeObject;ne(t)&&(t.removeAll(),t.dispose()),delete this._activeObject,super.destroy(),this.pixelFindContext=null,this.pixelFindCanvasEl=void 0}clear(){this.discardActiveObject(),this._activeObject=void 0,this.clearContext(this.contextTop),super.clear()}drawControls(t){const e=this._activeObject;e&&e._renderControls(t)}_toObject(t,e,s){const i=this._realizeGroupTransformOnObject(t),r=super._toObject(t,e,s);return t.set(i),r}_realizeGroupTransformOnObject(t){const{group:e}=t;if(e&&ne(e)&&this._activeObject===e){const s=Ee(t,["angle","flipX","flipY",X,xt,wt,De,ke,vt]);return io(t,e.calcOwnMatrix()),s}return{}}_setSVGObject(t,e,s){const i=this._realizeGroupTransformOnObject(e);super._setSVGObject(t,e,s),e.set(i)}}p(Us,"ownDefaults",{uniformScaling:!0,uniScaleKey:"shiftKey",centeredScaling:!1,centeredRotation:!1,centeredKey:"altKey",altActionKey:"shiftKey",selection:!0,selectionKey:"shiftKey",selectionColor:"rgba(100, 100, 255, 0.3)",selectionDashArray:[],selectionBorderColor:"rgba(255, 255, 255, 0.3)",selectionLineWidth:1,selectionFullyContained:!1,hoverCursor:"move",moveCursor:"move",defaultCursor:"default",freeDrawingCursor:"crosshair",notAllowedCursor:"not-allowed",perPixelTargetFind:!1,targetFindTolerance:0,skipTargetFind:!1,stopContextMenu:!1,fireRightClick:!1,fireMiddleClick:!1,enablePointerEvents:!1,containerClass:"canvas-container",preserveObjectStacking:!1});class Ha{constructor(t){p(this,"targets",[]),p(this,"__disposer",void 0);const e=()=>{const{hiddenTextarea:i}=t.getActiveObject()||{};i&&i.focus()},s=t.upperCanvasEl;s.addEventListener("click",e),this.__disposer=()=>s.removeEventListener("click",e)}exitTextEditing(){this.target=void 0,this.targets.forEach((t=>{t.isEditing&&t.exitEditing()}))}add(t){this.targets.push(t)}remove(t){this.unregister(t),ye(this.targets,t)}register(t){this.target=t}unregister(t){t===this.target&&(this.target=void 0)}onMouseMove(t){var e;!((e=this.target)===null||e===void 0)&&e.isEditing&&this.target.updateSelectionOnMouseMove(t)}clear(){this.targets=[],this.target=void 0}dispose(){this.clear(),this.__disposer(),delete this.__disposer}}const Ga=["target","oldTarget","fireCanvas","e"],bt={passive:!1},ve=(a,t)=>{const e=a.getViewportPoint(t),s=a.getScenePoint(t);return{viewportPoint:e,scenePoint:s,pointer:e,absolutePointer:s}},ee=function(a){for(var t=arguments.length,e=new Array(t>1?t-1:0),s=1;s<t;s++)e[s-1]=arguments[s];return a.addEventListener(...e)},Ct=function(a){for(var t=arguments.length,e=new Array(t>1?t-1:0),s=1;s<t;s++)e[s-1]=arguments[s];return a.removeEventListener(...e)},Ua={mouse:{in:"over",out:"out",targetIn:"mouseover",targetOut:"mouseout",canvasIn:"mouse:over",canvasOut:"mouse:out"},drag:{in:"enter",out:"leave",targetIn:"dragenter",targetOut:"dragleave",canvasIn:"drag:enter",canvasOut:"drag:leave"}};class _i extends Us{constructor(t){super(t,arguments.length>1&&arguments[1]!==void 0?arguments[1]:{}),p(this,"_isClick",void 0),p(this,"textEditingManager",new Ha(this)),["_onMouseDown","_onTouchStart","_onMouseMove","_onMouseUp","_onTouchEnd","_onResize","_onMouseWheel","_onMouseOut","_onMouseEnter","_onContextMenu","_onClick","_onDragStart","_onDragEnd","_onDragProgress","_onDragOver","_onDragEnter","_onDragLeave","_onDrop"].forEach((e=>{this[e]=this[e].bind(this)})),this.addOrRemove(ee,"add")}_getEventPrefix(){return this.enablePointerEvents?"pointer":"mouse"}addOrRemove(t,e){const s=this.upperCanvasEl,i=this._getEventPrefix();t(Vr(s),"resize",this._onResize),t(s,i+"down",this._onMouseDown),t(s,"".concat(i,"move"),this._onMouseMove,bt),t(s,"".concat(i,"out"),this._onMouseOut),t(s,"".concat(i,"enter"),this._onMouseEnter),t(s,"wheel",this._onMouseWheel),t(s,"contextmenu",this._onContextMenu),t(s,"click",this._onClick),t(s,"dblclick",this._onClick),t(s,"dragstart",this._onDragStart),t(s,"dragend",this._onDragEnd),t(s,"dragover",this._onDragOver),t(s,"dragenter",this._onDragEnter),t(s,"dragleave",this._onDragLeave),t(s,"drop",this._onDrop),this.enablePointerEvents||t(s,"touchstart",this._onTouchStart,bt)}removeListeners(){this.addOrRemove(Ct,"remove");const t=this._getEventPrefix(),e=Pt(this.upperCanvasEl);Ct(e,"".concat(t,"up"),this._onMouseUp),Ct(e,"touchend",this._onTouchEnd,bt),Ct(e,"".concat(t,"move"),this._onMouseMove,bt),Ct(e,"touchmove",this._onMouseMove,bt),clearTimeout(this._willAddMouseDown)}_onMouseWheel(t){this.__onMouseWheel(t)}_onMouseOut(t){const e=this._hoveredTarget,s=m({e:t},ve(this,t));this.fire("mouse:out",m(m({},s),{},{target:e})),this._hoveredTarget=void 0,e&&e.fire("mouseout",m({},s)),this._hoveredTargets.forEach((i=>{this.fire("mouse:out",m(m({},s),{},{target:i})),i&&i.fire("mouseout",m({},s))})),this._hoveredTargets=[]}_onMouseEnter(t){this._currentTransform||this.findTarget(t)||(this.fire("mouse:over",m({e:t},ve(this,t))),this._hoveredTarget=void 0,this._hoveredTargets=[])}_onDragStart(t){this._isClick=!1;const e=this.getActiveObject();if(e&&e.onDragStart(t)){this._dragSource=e;const s={e:t,target:e};return this.fire("dragstart",s),e.fire("dragstart",s),void ee(this.upperCanvasEl,"drag",this._onDragProgress)}qi(t)}_renderDragEffects(t,e,s){let i=!1;const r=this._dropTarget;r&&r!==e&&r!==s&&(r.clearContextTop(),i=!0),e?.clearContextTop(),s!==e&&s?.clearContextTop();const n=this.contextTop;n.save(),n.transform(...this.viewportTransform),e&&(n.save(),e.transform(n),e.renderDragSourceEffect(t),n.restore(),i=!0),s&&(n.save(),s.transform(n),s.renderDropTargetEffect(t),n.restore(),i=!0),n.restore(),i&&(this.contextTopDirty=!0)}_onDragEnd(t){const e=!!t.dataTransfer&&t.dataTransfer.dropEffect!==yt,s=e?this._activeObject:void 0,i={e:t,target:this._dragSource,subTargets:this.targets,dragSource:this._dragSource,didDrop:e,dropTarget:s};Ct(this.upperCanvasEl,"drag",this._onDragProgress),this.fire("dragend",i),this._dragSource&&this._dragSource.fire("dragend",i),delete this._dragSource,this._onMouseUp(t)}_onDragProgress(t){const e={e:t,target:this._dragSource,dragSource:this._dragSource,dropTarget:this._draggedoverTarget};this.fire("drag",e),this._dragSource&&this._dragSource.fire("drag",e)}findDragTargets(t){return this.targets=[],{target:this._searchPossibleTargets(this._objects,this.getViewportPoint(t)),targets:[...this.targets]}}_onDragOver(t){const e="dragover",{target:s,targets:i}=this.findDragTargets(t),r=this._dragSource,n={e:t,target:s,subTargets:i,dragSource:r,canDrop:!1,dropTarget:void 0};let o;this.fire(e,n),this._fireEnterLeaveEvents(s,n),s&&(s.canDrop(t)&&(o=s),s.fire(e,n));for(let h=0;h<i.length;h++){const l=i[h];l.canDrop(t)&&(o=l),l.fire(e,n)}this._renderDragEffects(t,r,o),this._dropTarget=o}_onDragEnter(t){const{target:e,targets:s}=this.findDragTargets(t),i={e:t,target:e,subTargets:s,dragSource:this._dragSource};this.fire("dragenter",i),this._fireEnterLeaveEvents(e,i)}_onDragLeave(t){const e={e:t,target:this._draggedoverTarget,subTargets:this.targets,dragSource:this._dragSource};this.fire("dragleave",e),this._fireEnterLeaveEvents(void 0,e),this._renderDragEffects(t,this._dragSource),this._dropTarget=void 0,this.targets=[],this._hoveredTargets=[]}_onDrop(t){const{target:e,targets:s}=this.findDragTargets(t),i=this._basicEventHandler("drop:before",m({e:t,target:e,subTargets:s,dragSource:this._dragSource},ve(this,t)));i.didDrop=!1,i.dropTarget=void 0,this._basicEventHandler("drop",i),this.fire("drop:after",i)}_onContextMenu(t){const e=this.findTarget(t),s=this.targets||[],i=this._basicEventHandler("contextmenu:before",{e:t,target:e,subTargets:s});return this.stopContextMenu&&qi(t),this._basicEventHandler("contextmenu",i),!1}_onClick(t){const e=t.detail;e>3||e<2||(this._cacheTransformEventData(t),e==2&&t.type==="dblclick"&&this._handleEvent(t,"dblclick"),e==3&&this._handleEvent(t,"tripleclick"),this._resetTransformEventData())}getPointerId(t){const e=t.changedTouches;return e?e[0]&&e[0].identifier:this.enablePointerEvents?t.pointerId:-1}_isMainEvent(t){return t.isPrimary===!0||t.isPrimary!==!1&&(t.type==="touchend"&&t.touches.length===0||!t.changedTouches||t.changedTouches[0].identifier===this.mainTouchId)}_onTouchStart(t){let e=!this.allowTouchScrolling;const s=this._activeObject;this.mainTouchId===void 0&&(this.mainTouchId=this.getPointerId(t)),this.__onMouseDown(t),(this.isDrawingMode||s&&this._target===s)&&(e=!0),e&&t.preventDefault(),this._resetTransformEventData();const i=this.upperCanvasEl,r=this._getEventPrefix(),n=Pt(i);ee(n,"touchend",this._onTouchEnd,bt),e&&ee(n,"touchmove",this._onMouseMove,bt),Ct(i,"".concat(r,"down"),this._onMouseDown)}_onMouseDown(t){this.__onMouseDown(t),this._resetTransformEventData();const e=this.upperCanvasEl,s=this._getEventPrefix();Ct(e,"".concat(s,"move"),this._onMouseMove,bt);const i=Pt(e);ee(i,"".concat(s,"up"),this._onMouseUp),ee(i,"".concat(s,"move"),this._onMouseMove,bt)}_onTouchEnd(t){if(t.touches.length>0)return;this.__onMouseUp(t),this._resetTransformEventData(),delete this.mainTouchId;const e=this._getEventPrefix(),s=Pt(this.upperCanvasEl);Ct(s,"touchend",this._onTouchEnd,bt),Ct(s,"touchmove",this._onMouseMove,bt),this._willAddMouseDown&&clearTimeout(this._willAddMouseDown),this._willAddMouseDown=setTimeout((()=>{ee(this.upperCanvasEl,"".concat(e,"down"),this._onMouseDown),this._willAddMouseDown=0}),400)}_onMouseUp(t){this.__onMouseUp(t),this._resetTransformEventData();const e=this.upperCanvasEl,s=this._getEventPrefix();if(this._isMainEvent(t)){const i=Pt(this.upperCanvasEl);Ct(i,"".concat(s,"up"),this._onMouseUp),Ct(i,"".concat(s,"move"),this._onMouseMove,bt),ee(e,"".concat(s,"move"),this._onMouseMove,bt)}}_onMouseMove(t){const e=this.getActiveObject();!this.allowTouchScrolling&&(!e||!e.shouldStartDragging(t))&&t.preventDefault&&t.preventDefault(),this.__onMouseMove(t)}_onResize(){this.calcOffset(),this._resetTransformEventData()}_shouldRender(t){const e=this.getActiveObject();return!!e!=!!t||e&&t&&e!==t}__onMouseUp(t){var e;this._cacheTransformEventData(t),this._handleEvent(t,"up:before");const s=this._currentTransform,i=this._isClick,r=this._target,{button:n}=t;if(n)return(this.fireMiddleClick&&n===1||this.fireRightClick&&n===2)&&this._handleEvent(t,"up"),void this._resetTransformEventData();if(this.isDrawingMode&&this._isCurrentlyDrawing)return void this._onMouseUpInDrawingMode(t);if(!this._isMainEvent(t))return;let o,h,l=!1;if(s&&(this._finalizeCurrentTransform(t),l=s.actionPerformed),!i){const c=r===this._activeObject;this.handleSelection(t),l||(l=this._shouldRender(r)||!c&&r===this._activeObject)}if(r){const c=r.findControl(this.getViewportPoint(t),ui(t)),{key:g,control:u}=c||{};if(h=g,r.selectable&&r!==this._activeObject&&r.activeOn==="up")this.setActiveObject(r,t),l=!0;else if(u){const d=u.getMouseUpHandler(t,r,u);d&&(o=this.getScenePoint(t),d.call(u,t,s,o.x,o.y))}r.isMoving=!1}if(s&&(s.target!==r||s.corner!==h)){const c=s.target&&s.target.controls[s.corner],g=c&&c.getMouseUpHandler(t,s.target,c);o=o||this.getScenePoint(t),g&&g.call(c,t,s,o.x,o.y)}this._setCursorFromEvent(t,r),this._handleEvent(t,"up"),this._groupSelector=null,this._currentTransform=null,r&&(r.__corner=void 0),l?this.requestRenderAll():i||(e=this._activeObject)!==null&&e!==void 0&&e.isEditing||this.renderTop()}_basicEventHandler(t,e){const{target:s,subTargets:i=[]}=e;this.fire(t,e),s&&s.fire(t,e);for(let r=0;r<i.length;r++)i[r]!==s&&i[r].fire(t,e);return e}_handleEvent(t,e,s){const i=this._target,r=this.targets||[],n=m(m(m({e:t,target:i,subTargets:r},ve(this,t)),{},{transform:this._currentTransform},e==="up:before"||e==="up"?{isClick:this._isClick,currentTarget:this.findTarget(t),currentSubTargets:this.targets}:{}),e==="down:before"||e==="down"?s:{});this.fire("mouse:".concat(e),n),i&&i.fire("mouse".concat(e),n);for(let o=0;o<r.length;o++)r[o]!==i&&r[o].fire("mouse".concat(e),n)}_onMouseDownInDrawingMode(t){this._isCurrentlyDrawing=!0,this.getActiveObject()&&(this.discardActiveObject(t),this.requestRenderAll());const e=this.getScenePoint(t);this.freeDrawingBrush&&this.freeDrawingBrush.onMouseDown(e,{e:t,pointer:e}),this._handleEvent(t,"down",{alreadySelected:!1})}_onMouseMoveInDrawingMode(t){if(this._isCurrentlyDrawing){const e=this.getScenePoint(t);this.freeDrawingBrush&&this.freeDrawingBrush.onMouseMove(e,{e:t,pointer:e})}this.setCursor(this.freeDrawingCursor),this._handleEvent(t,"move")}_onMouseUpInDrawingMode(t){const e=this.getScenePoint(t);this.freeDrawingBrush?this._isCurrentlyDrawing=!!this.freeDrawingBrush.onMouseUp({e:t,pointer:e}):this._isCurrentlyDrawing=!1,this._handleEvent(t,"up")}__onMouseDown(t){this._isClick=!0,this._cacheTransformEventData(t),this._handleEvent(t,"down:before");let e=this._target,s=!!e&&e===this._activeObject;const{button:i}=t;if(i)return(this.fireMiddleClick&&i===1||this.fireRightClick&&i===2)&&this._handleEvent(t,"down",{alreadySelected:s}),void this._resetTransformEventData();if(this.isDrawingMode)return void this._onMouseDownInDrawingMode(t);if(!this._isMainEvent(t)||this._currentTransform)return;let r=this._shouldRender(e),n=!1;if(this.handleMultiSelection(t,e)?(e=this._activeObject,n=!0,r=!0):this._shouldClearSelection(t,e)&&this.discardActiveObject(t),this.selection&&(!e||!e.selectable&&!e.isEditing&&e!==this._activeObject)){const o=this.getScenePoint(t);this._groupSelector={x:o.x,y:o.y,deltaY:0,deltaX:0}}if(s=!!e&&e===this._activeObject,e){e.selectable&&e.activeOn==="down"&&this.setActiveObject(e,t);const o=e.findControl(this.getViewportPoint(t),ui(t));if(e===this._activeObject&&(o||!n)){this._setupCurrentTransform(t,e,s);const h=o?o.control:void 0,l=this.getScenePoint(t),c=h&&h.getMouseDownHandler(t,e,h);c&&c.call(h,t,this._currentTransform,l.x,l.y)}}r&&(this._objectsToRender=void 0),this._handleEvent(t,"down",{alreadySelected:s}),r&&this.requestRenderAll()}_resetTransformEventData(){this._target=this._pointer=this._absolutePointer=void 0}_cacheTransformEventData(t){this._resetTransformEventData(),this._pointer=this.getViewportPoint(t),this._absolutePointer=Ce(this._pointer,void 0,this.viewportTransform),this._target=this._currentTransform?this._currentTransform.target:this.findTarget(t)}__onMouseMove(t){if(this._isClick=!1,this._cacheTransformEventData(t),this._handleEvent(t,"move:before"),this.isDrawingMode)return void this._onMouseMoveInDrawingMode(t);if(!this._isMainEvent(t))return;const e=this._groupSelector;if(e){const s=this.getScenePoint(t);e.deltaX=s.x-e.x,e.deltaY=s.y-e.y,this.renderTop()}else if(this._currentTransform)this._transformObject(t);else{const s=this.findTarget(t);this._setCursorFromEvent(t,s),this._fireOverOutEvents(t,s)}this.textEditingManager.onMouseMove(t),this._handleEvent(t,"move"),this._resetTransformEventData()}_fireOverOutEvents(t,e){const s=this._hoveredTarget,i=this._hoveredTargets,r=this.targets,n=Math.max(i.length,r.length);this.fireSyntheticInOutEvents("mouse",{e:t,target:e,oldTarget:s,fireCanvas:!0});for(let o=0;o<n;o++)this.fireSyntheticInOutEvents("mouse",{e:t,target:r[o],oldTarget:i[o]});this._hoveredTarget=e,this._hoveredTargets=this.targets.concat()}_fireEnterLeaveEvents(t,e){const s=this._draggedoverTarget,i=this._hoveredTargets,r=this.targets,n=Math.max(i.length,r.length);this.fireSyntheticInOutEvents("drag",m(m({},e),{},{target:t,oldTarget:s,fireCanvas:!0}));for(let o=0;o<n;o++)this.fireSyntheticInOutEvents("drag",m(m({},e),{},{target:r[o],oldTarget:i[o]}));this._draggedoverTarget=t}fireSyntheticInOutEvents(t,e){let{target:s,oldTarget:i,fireCanvas:r,e:n}=e,o=N(e,Ga);const{targetIn:h,targetOut:l,canvasIn:c,canvasOut:g}=Ua[t],u=i!==s;if(i&&u){const d=m(m({},o),{},{e:n,target:i,nextTarget:s},ve(this,n));r&&this.fire(g,d),i.fire(l,d)}if(s&&u){const d=m(m({},o),{},{e:n,target:s,previousTarget:i},ve(this,n));r&&this.fire(c,d),s.fire(h,d)}}__onMouseWheel(t){this._cacheTransformEventData(t),this._handleEvent(t,"wheel"),this._resetTransformEventData()}_transformObject(t){const e=this.getScenePoint(t),s=this._currentTransform,i=s.target,r=i.group?Ce(e,void 0,i.group.calcTransformMatrix()):e;s.shiftKey=t.shiftKey,s.altKey=!!this.centeredKey&&t[this.centeredKey],this._performTransformAction(t,s,r),s.actionPerformed&&this.requestRenderAll()}_performTransformAction(t,e,s){const{action:i,actionHandler:r,target:n}=e,o=!!r&&r(t,e,s.x,s.y);o&&n.setCoords(),i==="drag"&&o&&(e.target.isMoving=!0,this.setCursor(e.target.moveCursor||this.moveCursor)),e.actionPerformed=e.actionPerformed||o}_setCursorFromEvent(t,e){if(!e)return void this.setCursor(this.defaultCursor);let s=e.hoverCursor||this.hoverCursor;const i=ne(this._activeObject)?this._activeObject:null,r=(!i||e.group!==i)&&e.findControl(this.getViewportPoint(t));if(r){const n=r.control;this.setCursor(n.cursorStyleHandler(t,n,e))}else e.subTargetCheck&&this.targets.concat().reverse().map((n=>{s=n.hoverCursor||s})),this.setCursor(s)}handleMultiSelection(t,e){const s=this._activeObject,i=ne(s);if(s&&this._isSelectionKeyPressed(t)&&this.selection&&e&&e.selectable&&(s!==e||i)&&(i||!e.isDescendantOf(s)&&!s.isDescendantOf(e))&&!e.onSelect({e:t})&&!s.getActiveControl()){if(i){const r=s.getObjects();if(e===s){const n=this.getViewportPoint(t);if(!(e=this.searchPossibleTargets(r,n)||this.searchPossibleTargets(this._objects,n))||!e.selectable)return!1}e.group===s?(s.remove(e),this._hoveredTarget=e,this._hoveredTargets=[...this.targets],s.size()===1&&this._setActiveObject(s.item(0),t)):(s.multiSelectAdd(e),this._hoveredTarget=s,this._hoveredTargets=[...this.targets]),this._fireSelectionEvents(r,t)}else{s.isEditing&&s.exitEditing();const r=new(D.getClass("ActiveSelection"))([],{canvas:this});r.multiSelectAdd(s,e),this._hoveredTarget=r,this._setActiveObject(r,t),this._fireSelectionEvents([s],t)}return!0}return!1}handleSelection(t){if(!this.selection||!this._groupSelector)return!1;const{x:e,y:s,deltaX:i,deltaY:r}=this._groupSelector,n=new y(e,s),o=n.add(new y(i,r)),h=n.min(o),l=n.max(o).subtract(h),c=this.collectObjects({left:h.x,top:h.y,width:l.x,height:l.y},{includeIntersecting:!this.selectionFullyContained}),g=n.eq(o)?c[0]?[c[0]]:[]:c.length>1?c.filter((u=>!u.onSelect({e:t}))).reverse():c;if(g.length===1)this.setActiveObject(g[0],t);else if(g.length>1){const u=D.getClass("ActiveSelection");this.setActiveObject(new u(g,{canvas:this}),t)}return this._groupSelector=null,!0}clear(){this.textEditingManager.clear(),super.clear()}destroy(){this.removeListeners(),this.textEditingManager.dispose(),super.destroy()}}const xn={x1:0,y1:0,x2:0,y2:0},Na=m(m({},xn),{},{r1:0,r2:0}),xe=(a,t)=>isNaN(a)&&typeof t=="number"?t:a,qa=/^(\d+\.\d+)%|(\d+)%$/;function bn(a){return a&&qa.test(a)}function Cn(a,t){const e=typeof a=="number"?a:typeof a=="string"?parseFloat(a)/(bn(a)?100:1):NaN;return Te(0,xe(e,t),1)}const $a=/\s*;\s*/,Ka=/\s*:\s*/;function Ja(a,t){let e,s;const i=a.getAttribute("style");if(i){const n=i.split($a);n[n.length-1]===""&&n.pop();for(let o=n.length;o--;){const[h,l]=n[o].split(Ka).map((c=>c.trim()));h==="stop-color"?e=l:h==="stop-opacity"&&(s=l)}}const r=new z(e||a.getAttribute("stop-color")||"rgb(0,0,0)");return{offset:Cn(a.getAttribute("offset"),0),color:r.toRgb(),opacity:xe(parseFloat(s||a.getAttribute("stop-opacity")||""),1)*r.getAlpha()*t}}function Za(a,t){const e=[],s=a.getElementsByTagName("stop"),i=Cn(t,1);for(let r=s.length;r--;)e.push(Ja(s[r],i));return e}function Sn(a){return a.nodeName==="linearGradient"||a.nodeName==="LINEARGRADIENT"?"linear":"radial"}function wn(a){return a.getAttribute("gradientUnits")==="userSpaceOnUse"?"pixels":"percentage"}function kt(a,t){return a.getAttribute(t)}function Qa(a,t){return(function(e,s){let i,{width:r,height:n,gradientUnits:o}=s;return Object.keys(e).reduce(((h,l)=>{const c=e[l];return c==="Infinity"?i=1:c==="-Infinity"?i=0:(i=typeof c=="string"?parseFloat(c):c,typeof c=="string"&&bn(c)&&(i*=.01,o==="pixels"&&(l!=="x1"&&l!=="x2"&&l!=="r2"||(i*=r),l!=="y1"&&l!=="y2"||(i*=n)))),h[l]=i,h}),{})})(Sn(a)==="linear"?(function(e){return{x1:kt(e,"x1")||0,y1:kt(e,"y1")||0,x2:kt(e,"x2")||"100%",y2:kt(e,"y2")||0}})(a):(function(e){return{x1:kt(e,"fx")||kt(e,"cx")||"50%",y1:kt(e,"fy")||kt(e,"cy")||"50%",r1:0,x2:kt(e,"cx")||"50%",y2:kt(e,"cy")||"50%",r2:kt(e,"r")||"50%"}})(a),m(m({},t),{},{gradientUnits:wn(a)}))}class Qe{constructor(t){const{type:e="linear",gradientUnits:s="pixels",coords:i={},colorStops:r=[],offsetX:n=0,offsetY:o=0,gradientTransform:h,id:l}=t||{};Object.assign(this,{type:e,gradientUnits:s,coords:m(m({},e==="radial"?Na:xn),i),colorStops:r,offsetX:n,offsetY:o,gradientTransform:h,id:l?"".concat(l,"_").concat(ie()):ie()})}addColorStop(t){for(const e in t){const s=new z(t[e]);this.colorStops.push({offset:parseFloat(e),color:s.toRgb(),opacity:s.getAlpha()})}return this}toObject(t){return m(m({},Ee(this,t)),{},{type:this.type,coords:m({},this.coords),colorStops:this.colorStops.map((e=>m({},e))),offsetX:this.offsetX,offsetY:this.offsetY,gradientUnits:this.gradientUnits,gradientTransform:this.gradientTransform?[...this.gradientTransform]:void 0})}toSVG(t){let{additionalTransform:e}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const s=[],i=this.gradientTransform?this.gradientTransform.concat():pt.concat(),r=this.gradientUnits==="pixels"?"userSpaceOnUse":"objectBoundingBox",n=this.colorStops.map((g=>m({},g))).sort(((g,u)=>g.offset-u.offset));let o=-this.offsetX,h=-this.offsetY;var l;r==="objectBoundingBox"?(o/=t.width,h/=t.height):(o+=t.width/2,h+=t.height/2),(l=t)&&typeof l._renderPathCommands=="function"&&this.gradientUnits!=="percentage"&&(o-=t.pathOffset.x,h-=t.pathOffset.y),i[4]-=o,i[5]-=h;const c=['id="SVGID_'.concat(this.id,'"'),'gradientUnits="'.concat(r,'"'),'gradientTransform="'.concat(e?e+" ":"").concat(Ve(i),'"'),""].join(" ");if(this.type==="linear"){const{x1:g,y1:u,x2:d,y2:f}=this.coords;s.push("<linearGradient ",c,' x1="',g,'" y1="',u,'" x2="',d,'" y2="',f,`">
`)}else if(this.type==="radial"){const{x1:g,y1:u,x2:d,y2:f,r1:v,r2:_}=this.coords,b=v>_;s.push("<radialGradient ",c,' cx="',b?g:d,'" cy="',b?u:f,'" r="',b?v:_,'" fx="',b?d:g,'" fy="',b?f:u,`">
`),b&&(n.reverse(),n.forEach((O=>{O.offset=1-O.offset})));const C=Math.min(v,_);if(C>0){const O=C/Math.max(v,_);n.forEach((w=>{w.offset+=O*(1-w.offset)}))}}return n.forEach((g=>{let{color:u,offset:d,opacity:f}=g;s.push("<stop ",'offset="',100*d+"%",'" style="stop-color:',u,f!==void 0?";stop-opacity: "+f:";",`"/>
`)})),s.push(this.type==="linear"?"</linearGradient>":"</radialGradient>",`
`),s.join("")}toLive(t){const{x1:e,y1:s,x2:i,y2:r,r1:n,r2:o}=this.coords,h=this.type==="linear"?t.createLinearGradient(e,s,i,r):t.createRadialGradient(e,s,n,i,r,o);return this.colorStops.forEach((l=>{let{color:c,opacity:g,offset:u}=l;h.addColorStop(u,g!==void 0?new z(c).setAlpha(g).toRgba():c)})),h}static async fromObject(t){const{colorStops:e,gradientTransform:s}=t;return new this(m(m({},t),{},{colorStops:e?e.map((i=>m({},i))):void 0,gradientTransform:s?[...s]:void 0}))}static fromElement(t,e,s){const i=wn(t),r=e._findCenterFromElement();return new this(m({id:t.getAttribute("id")||void 0,type:Sn(t),coords:Qa(t,{width:s.viewBoxWidth||s.width,height:s.viewBoxHeight||s.height}),colorStops:Za(t,s.opacity),gradientUnits:i,gradientTransform:pi(t.getAttribute("gradientTransform")||"")},i==="pixels"?{offsetX:e.width/2-r.x,offsetY:e.height/2-r.y}:{offsetX:0,offsetY:0}))}}p(Qe,"type","Gradient"),D.setClass(Qe,"gradient"),D.setClass(Qe,"linear"),D.setClass(Qe,"radial");const th=["type","source","patternTransform"];class si{get type(){return"pattern"}set type(t){se("warn","Setting type has no effect",t)}constructor(t){p(this,"repeat","repeat"),p(this,"offsetX",0),p(this,"offsetY",0),p(this,"crossOrigin",""),this.id=ie(),Object.assign(this,t)}isImageSource(){return!!this.source&&typeof this.source.src=="string"}isCanvasSource(){return!!this.source&&!!this.source.toDataURL}sourceToString(){return this.isImageSource()?this.source.src:this.isCanvasSource()?this.source.toDataURL():""}toLive(t){return this.source&&(!this.isImageSource()||this.source.complete&&this.source.naturalWidth!==0&&this.source.naturalHeight!==0)?t.createPattern(this.source,this.repeat):null}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const{repeat:e,crossOrigin:s}=this;return m(m({},Ee(this,t)),{},{type:"pattern",source:this.sourceToString(),repeat:e,crossOrigin:s,offsetX:U(this.offsetX,B.NUM_FRACTION_DIGITS),offsetY:U(this.offsetY,B.NUM_FRACTION_DIGITS),patternTransform:this.patternTransform?[...this.patternTransform]:null})}toSVG(t){let{width:e,height:s}=t;const{source:i,repeat:r,id:n}=this,o=xe(this.offsetX/e,0),h=xe(this.offsetY/s,0),l=r==="repeat-y"||r==="no-repeat"?1+Math.abs(o||0):xe(i.width/e,0),c=r==="repeat-x"||r==="no-repeat"?1+Math.abs(h||0):xe(i.height/s,0);return['<pattern id="SVGID_'.concat(n,'" x="').concat(o,'" y="').concat(h,'" width="').concat(l,'" height="').concat(c,'">'),'<image x="0" y="0" width="'.concat(i.width,'" height="').concat(i.height,'" xlink:href="').concat(this.sourceToString(),'"></image>'),"</pattern>",""].join(`
`)}static async fromObject(t,e){let{type:s,source:i,patternTransform:r}=t,n=N(t,th);const o=await ws(i,m(m({},e),{},{crossOrigin:n.crossOrigin}));return new this(m(m({},n),{},{patternTransform:r&&r.slice(0),source:o}))}}p(si,"type","Pattern"),D.setClass(si),D.setClass(si,"pattern");const eh=["path","left","top"],sh=["d"];class oe extends ut{constructor(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{path:s,left:i,top:r}=e,n=N(e,eh);super(),Object.assign(this,oe.ownDefaults),this.setOptions(n),this._setPath(t||[],!0),typeof i=="number"&&this.set(X,i),typeof r=="number"&&this.set(vt,r)}_setPath(t,e){this.path=ja(Array.isArray(t)?t:Ya(t)),this.setBoundingBox(e)}_findCenterFromElement(){const t=this._calcBoundsFromPath();return new y(t.left+t.width/2,t.top+t.height/2)}_renderPathCommands(t){const e=-this.pathOffset.x,s=-this.pathOffset.y;t.beginPath();for(const i of this.path)switch(i[0]){case"L":t.lineTo(i[1]+e,i[2]+s);break;case"M":t.moveTo(i[1]+e,i[2]+s);break;case"C":t.bezierCurveTo(i[1]+e,i[2]+s,i[3]+e,i[4]+s,i[5]+e,i[6]+s);break;case"Q":t.quadraticCurveTo(i[1]+e,i[2]+s,i[3]+e,i[4]+s);break;case"Z":t.closePath()}}_render(t){this._renderPathCommands(t),this._renderPaintInOrder(t)}toString(){return"#<Path (".concat(this.complexity(),'): { "top": ').concat(this.top,', "left": ').concat(this.left," }>")}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return m(m({},super.toObject(t)),{},{path:this.path.map((e=>e.slice()))})}toDatalessObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const e=this.toObject(t);return this.sourcePath&&(delete e.path,e.sourcePath=this.sourcePath),e}_toSVG(){const t=Va(this.path,B.NUM_FRACTION_DIGITS);return["<path ","COMMON_PARTS",'d="'.concat(t,`" stroke-linecap="round" />
`)]}_getOffsetTransform(){const t=B.NUM_FRACTION_DIGITS;return" translate(".concat(U(-this.pathOffset.x,t),", ").concat(U(-this.pathOffset.y,t),")")}toClipPathSVG(t){const e=this._getOffsetTransform();return"	"+this._createBaseClipPathSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})}toSVG(t){const e=this._getOffsetTransform();return this._createBaseSVGMarkup(this._toSVG(),{reviver:t,additionalTransform:e})}complexity(){return this.path.length}setDimensions(){this.setBoundingBox()}setBoundingBox(t){const{width:e,height:s,pathOffset:i}=this._calcDimensions();this.set({width:e,height:s,pathOffset:i}),t&&this.setPositionByOrigin(i,F,F)}_calcBoundsFromPath(){const t=[];let e=0,s=0,i=0,r=0;for(const n of this.path)switch(n[0]){case"L":i=n[1],r=n[2],t.push({x:e,y:s},{x:i,y:r});break;case"M":i=n[1],r=n[2],e=i,s=r;break;case"C":t.push(...xr(i,r,n[1],n[2],n[3],n[4],n[5],n[6])),i=n[5],r=n[6];break;case"Q":t.push(...xr(i,r,n[1],n[2],n[1],n[2],n[3],n[4])),i=n[3],r=n[4];break;case"Z":i=e,r=s}return Ut(t)}_calcDimensions(){const t=this._calcBoundsFromPath();return m(m({},t),{},{pathOffset:new y(t.left+t.width/2,t.top+t.height/2)})}static fromObject(t){return this._fromObject(t,{extraParam:"path"})}static async fromElement(t,e,s){const i=Qt(t,this.ATTRIBUTE_NAMES,s),{d:r}=i;return new this(r,m(m(m({},N(i,sh)),e),{},{left:void 0,top:void 0}))}}p(oe,"type","Path"),p(oe,"cacheProperties",[...Zt,"path","fillRule"]),p(oe,"ATTRIBUTE_NAMES",[...re,"d"]),D.setClass(oe),D.setSVGClass(oe);const ih=["left","top","radius"],Tn=["radius","startAngle","endAngle","counterClockwise"];class Vt extends ut{static getDefaults(){return m(m({},super.getDefaults()),Vt.ownDefaults)}constructor(t){super(),Object.assign(this,Vt.ownDefaults),this.setOptions(t)}_set(t,e){return super._set(t,e),t==="radius"&&this.setRadius(e),this}_render(t){t.beginPath(),t.arc(0,0,this.radius,Q(this.startAngle),Q(this.endAngle),this.counterClockwise),this._renderPaintInOrder(t)}getRadiusX(){return this.get("radius")*this.get(xt)}getRadiusY(){return this.get("radius")*this.get(wt)}setRadius(t){this.radius=t,this.set({width:2*t,height:2*t})}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return super.toObject([...Tn,...t])}_toSVG(){const t=(this.endAngle-this.startAngle)%360;if(t===0)return["<circle ","COMMON_PARTS",'cx="0" cy="0" ','r="',"".concat(this.radius),`" />
`];{const{radius:e}=this,s=Q(this.startAngle),i=Q(this.endAngle),r=qt(s)*e,n=$t(s)*e,o=qt(i)*e,h=$t(i)*e,l=t>180?1:0,c=this.counterClockwise?0:1;return['<path d="M '.concat(r," ").concat(n," A ").concat(e," ").concat(e," 0 ").concat(l," ").concat(c," ").concat(o," ").concat(h,'" '),"COMMON_PARTS",` />
`]}}static async fromElement(t,e,s){const i=Qt(t,this.ATTRIBUTE_NAMES,s),{left:r=0,top:n=0,radius:o=0}=i;return new this(m(m({},N(i,ih)),{},{radius:o,left:r-o,top:n-o}))}static fromObject(t){return super._fromObject(t)}}p(Vt,"type","Circle"),p(Vt,"cacheProperties",[...Zt,...Tn]),p(Vt,"ownDefaults",{radius:0,startAngle:0,endAngle:360,counterClockwise:!1}),p(Vt,"ATTRIBUTE_NAMES",["cx","cy","r",...re]),D.setClass(Vt),D.setSVGClass(Vt);const rh=["x1","y1","x2","y2"],nh=["x1","y1","x2","y2"],xi=["x1","x2","y1","y2"];class ae extends ut{constructor(){let[t,e,s,i]=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[0,0,0,0],r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),Object.assign(this,ae.ownDefaults),this.setOptions(r),this.x1=t,this.x2=s,this.y1=e,this.y2=i,this._setWidthHeight();const{left:n,top:o}=r;typeof n=="number"&&this.set(X,n),typeof o=="number"&&this.set(vt,o)}_setWidthHeight(){const{x1:t,y1:e,x2:s,y2:i}=this;this.width=Math.abs(s-t),this.height=Math.abs(i-e);const{left:r,top:n,width:o,height:h}=Ut([{x:t,y:e},{x:s,y:i}]),l=new y(r+o/2,n+h/2);this.setPositionByOrigin(l,F,F)}_set(t,e){return super._set(t,e),xi.includes(t)&&this._setWidthHeight(),this}_render(t){t.beginPath();const e=this.calcLinePoints();t.moveTo(e.x1,e.y1),t.lineTo(e.x2,e.y2),t.lineWidth=this.strokeWidth;const s=t.strokeStyle;var i;St(this.stroke)?t.strokeStyle=this.stroke.toLive(t):t.strokeStyle=(i=this.stroke)!==null&&i!==void 0?i:t.fillStyle,this.stroke&&this._renderStroke(t),t.strokeStyle=s}_findCenterFromElement(){return new y((this.x1+this.x2)/2,(this.y1+this.y2)/2)}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return m(m({},super.toObject(t)),this.calcLinePoints())}_getNonTransformedDimensions(){const t=super._getNonTransformedDimensions();return this.strokeLineCap==="butt"&&(this.width===0&&(t.y-=this.strokeWidth),this.height===0&&(t.x-=this.strokeWidth)),t}calcLinePoints(){const{x1:t,x2:e,y1:s,y2:i,width:r,height:n}=this,o=t<=e?-1:1,h=s<=i?-1:1;return{x1:o*r/2,x2:o*-r/2,y1:h*n/2,y2:h*-n/2}}_toSVG(){const{x1:t,x2:e,y1:s,y2:i}=this.calcLinePoints();return["<line ","COMMON_PARTS",'x1="'.concat(t,'" y1="').concat(s,'" x2="').concat(e,'" y2="').concat(i,`" />
`)]}static async fromElement(t,e,s){const i=Qt(t,this.ATTRIBUTE_NAMES,s),{x1:r=0,y1:n=0,x2:o=0,y2:h=0}=i;return new this([r,n,o,h],N(i,rh))}static fromObject(t){let{x1:e,y1:s,x2:i,y2:r}=t,n=N(t,nh);return this._fromObject(m(m({},n),{},{points:[e,s,i,r]}),{extraParam:"points"})}}p(ae,"type","Line"),p(ae,"cacheProperties",[...Zt,...xi]),p(ae,"ATTRIBUTE_NAMES",re.concat(xi)),D.setClass(ae),D.setSVGClass(ae);class he extends ut{static getDefaults(){return m(m({},super.getDefaults()),he.ownDefaults)}constructor(t){super(),Object.assign(this,he.ownDefaults),this.setOptions(t)}_render(t){const e=this.width/2,s=this.height/2;t.beginPath(),t.moveTo(-e,s),t.lineTo(0,-s),t.lineTo(e,s),t.closePath(),this._renderPaintInOrder(t)}_toSVG(){const t=this.width/2,e=this.height/2;return["<polygon ","COMMON_PARTS",'points="',"".concat(-t," ").concat(e,",0 ").concat(-e,",").concat(t," ").concat(e),'" />']}}p(he,"type","Triangle"),p(he,"ownDefaults",{width:100,height:100}),D.setClass(he),D.setSVGClass(he);const On=["rx","ry"];class zt extends ut{static getDefaults(){return m(m({},super.getDefaults()),zt.ownDefaults)}constructor(t){super(),Object.assign(this,zt.ownDefaults),this.setOptions(t)}_set(t,e){switch(super._set(t,e),t){case"rx":this.rx=e,this.set("width",2*e);break;case"ry":this.ry=e,this.set("height",2*e)}return this}getRx(){return this.get("rx")*this.get(xt)}getRy(){return this.get("ry")*this.get(wt)}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return super.toObject([...On,...t])}_toSVG(){return["<ellipse ","COMMON_PARTS",'cx="0" cy="0" rx="'.concat(this.rx,'" ry="').concat(this.ry,`" />
`)]}_render(t){t.beginPath(),t.save(),t.transform(1,0,0,this.ry/this.rx,0,0),t.arc(0,0,this.rx,0,Os,!1),t.restore(),this._renderPaintInOrder(t)}static async fromElement(t,e,s){const i=Qt(t,this.ATTRIBUTE_NAMES,s);return i.left=(i.left||0)-i.rx,i.top=(i.top||0)-i.ry,new this(i)}}function oh(a){if(!a)return[];const t=a.replace(/,/g," ").trim().split(/\s+/),e=[];for(let s=0;s<t.length;s+=2)e.push({x:parseFloat(t[s]),y:parseFloat(t[s+1])});return e}p(zt,"type","Ellipse"),p(zt,"cacheProperties",[...Zt,...On]),p(zt,"ownDefaults",{rx:0,ry:0}),p(zt,"ATTRIBUTE_NAMES",[...re,"cx","cy","rx","ry"]),D.setClass(zt),D.setSVGClass(zt);const ah=["left","top"],Dn={exactBoundingBox:!1};class Et extends ut{static getDefaults(){return m(m({},super.getDefaults()),Et.ownDefaults)}constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),p(this,"strokeDiff",void 0),Object.assign(this,Et.ownDefaults),this.setOptions(e),this.points=t;const{left:s,top:i}=e;this.initialized=!0,this.setBoundingBox(!0),typeof s=="number"&&this.set(X,s),typeof i=="number"&&this.set(vt,i)}isOpen(){return!0}_projectStrokeOnPoints(t){return $o(this.points,t,this.isOpen())}_calcDimensions(t){t=m({scaleX:this.scaleX,scaleY:this.scaleY,skewX:this.skewX,skewY:this.skewY,strokeLineCap:this.strokeLineCap,strokeLineJoin:this.strokeLineJoin,strokeMiterLimit:this.strokeMiterLimit,strokeUniform:this.strokeUniform,strokeWidth:this.strokeWidth},t||{});const e=this.exactBoundingBox?this._projectStrokeOnPoints(t).map((l=>l.projectedPoint)):this.points;if(e.length===0)return{left:0,top:0,width:0,height:0,pathOffset:new y,strokeOffset:new y,strokeDiff:new y};const s=Ut(e),i=Xs(m(m({},t),{},{scaleX:1,scaleY:1})),r=Ut(this.points.map((l=>mt(l,i,!0)))),n=new y(this.scaleX,this.scaleY);let o=s.left+s.width/2,h=s.top+s.height/2;return this.exactBoundingBox&&(o-=h*Math.tan(Q(this.skewX)),h-=o*Math.tan(Q(this.skewY))),m(m({},s),{},{pathOffset:new y(o,h),strokeOffset:new y(r.left,r.top).subtract(new y(s.left,s.top)).multiply(n),strokeDiff:new y(s.width,s.height).subtract(new y(r.width,r.height)).multiply(n)})}_findCenterFromElement(){const t=Ut(this.points);return new y(t.left+t.width/2,t.top+t.height/2)}setDimensions(){this.setBoundingBox()}setBoundingBox(t){const{left:e,top:s,width:i,height:r,pathOffset:n,strokeOffset:o,strokeDiff:h}=this._calcDimensions();this.set({width:i,height:r,pathOffset:n,strokeOffset:o,strokeDiff:h}),t&&this.setPositionByOrigin(new y(e+i/2,s+r/2),F,F)}isStrokeAccountedForInDimensions(){return this.exactBoundingBox}_getNonTransformedDimensions(){return this.exactBoundingBox?new y(this.width,this.height):super._getNonTransformedDimensions()}_getTransformedDimensions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(this.exactBoundingBox){let n;if(Object.keys(t).some((o=>this.strokeUniform||this.constructor.layoutProperties.includes(o)))){var e,s;const{width:o,height:h}=this._calcDimensions(t);n=new y((e=t.width)!==null&&e!==void 0?e:o,(s=t.height)!==null&&s!==void 0?s:h)}else{var i,r;n=new y((i=t.width)!==null&&i!==void 0?i:this.width,(r=t.height)!==null&&r!==void 0?r:this.height)}return n.multiply(new y(t.scaleX||this.scaleX,t.scaleY||this.scaleY))}return super._getTransformedDimensions(t)}_set(t,e){const s=this.initialized&&this[t]!==e,i=super._set(t,e);return this.exactBoundingBox&&s&&((t===xt||t===wt)&&this.strokeUniform&&this.constructor.layoutProperties.includes("strokeUniform")||this.constructor.layoutProperties.includes(t))&&this.setDimensions(),i}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return m(m({},super.toObject(t)),{},{points:this.points.map((e=>{let{x:s,y:i}=e;return{x:s,y:i}}))})}_toSVG(){const t=[],e=this.pathOffset.x,s=this.pathOffset.y,i=B.NUM_FRACTION_DIGITS;for(let r=0,n=this.points.length;r<n;r++)t.push(U(this.points[r].x-e,i),",",U(this.points[r].y-s,i)," ");return["<".concat(this.constructor.type.toLowerCase()," "),"COMMON_PARTS",'points="'.concat(t.join(""),`" />
`)]}_render(t){const e=this.points.length,s=this.pathOffset.x,i=this.pathOffset.y;if(e&&!isNaN(this.points[e-1].y)){t.beginPath(),t.moveTo(this.points[0].x-s,this.points[0].y-i);for(let r=0;r<e;r++){const n=this.points[r];t.lineTo(n.x-s,n.y-i)}!this.isOpen()&&t.closePath(),this._renderPaintInOrder(t)}}complexity(){return this.points.length}static async fromElement(t,e,s){return new this(oh(t.getAttribute("points")),m(m({},N(Qt(t,this.ATTRIBUTE_NAMES,s),ah)),e))}static fromObject(t){return this._fromObject(t,{extraParam:"points"})}}p(Et,"ownDefaults",Dn),p(Et,"type","Polyline"),p(Et,"layoutProperties",[De,ke,"strokeLineCap","strokeLineJoin","strokeMiterLimit","strokeWidth","strokeUniform","points"]),p(Et,"cacheProperties",[...Zt,"points"]),p(Et,"ATTRIBUTE_NAMES",[...re]),D.setClass(Et),D.setSVGClass(Et);class ts extends Et{isOpen(){return!1}}p(ts,"ownDefaults",Dn),p(ts,"type","Polygon"),D.setClass(ts),D.setSVGClass(ts);class kn extends ut{isEmptyStyles(t){if(!this.styles||t!==void 0&&!this.styles[t])return!0;const e=t===void 0?this.styles:{line:this.styles[t]};for(const s in e)for(const i in e[s])for(const r in e[s][i])return!1;return!0}styleHas(t,e){if(!this.styles||e!==void 0&&!this.styles[e])return!1;const s=e===void 0?this.styles:{0:this.styles[e]};for(const i in s)for(const r in s[i])if(s[i][r][t]!==void 0)return!0;return!1}cleanStyle(t){if(!this.styles)return!1;const e=this.styles;let s,i,r=0,n=!0,o=0;for(const h in e){s=0;for(const l in e[h]){const c=e[h][l]||{};r++,c[t]!==void 0?(i?c[t]!==i&&(n=!1):i=c[t],c[t]===this[t]&&delete c[t]):n=!1,Object.keys(c).length!==0?s++:delete e[h][l]}s===0&&delete e[h]}for(let h=0;h<this._textLines.length;h++)o+=this._textLines[h].length;n&&r===o&&(this[t]=i,this.removeStyle(t))}removeStyle(t){if(!this.styles)return;const e=this.styles;let s,i,r;for(i in e){for(r in s=e[i],s)delete s[r][t],Object.keys(s[r]).length===0&&delete s[r];Object.keys(s).length===0&&delete e[i]}}_extendStyles(t,e){const{lineIndex:s,charIndex:i}=this.get2DCursorLocation(t);this._getLineStyle(s)||this._setLineStyle(s);const r=ki(m(m({},this._getStyleDeclaration(s,i)),e),(n=>n!==void 0));this._setStyleDeclaration(s,i,r)}getSelectionStyles(t,e,s){const i=[];for(let r=t;r<(e||t);r++)i.push(this.getStyleAtPosition(r,s));return i}getStyleAtPosition(t,e){const{lineIndex:s,charIndex:i}=this.get2DCursorLocation(t);return e?this.getCompleteStyleDeclaration(s,i):this._getStyleDeclaration(s,i)}setSelectionStyles(t,e,s){for(let i=e;i<(s||e);i++)this._extendStyles(i,t);this._forceClearCache=!0}_getStyleDeclaration(t,e){var s;const i=this.styles&&this.styles[t];return i&&(s=i[e])!==null&&s!==void 0?s:{}}getCompleteStyleDeclaration(t,e){return m(m({},Ee(this,this.constructor._styleProperties)),this._getStyleDeclaration(t,e))}_setStyleDeclaration(t,e,s){this.styles[t][e]=s}_deleteStyleDeclaration(t,e){delete this.styles[t][e]}_getLineStyle(t){return!!this.styles[t]}_setLineStyle(t){this.styles[t]={}}_deleteLineStyle(t){delete this.styles[t]}}p(kn,"_styleProperties",co);const hh=/  +/g,lh=/"/g;function ii(a,t,e,s,i){return"		".concat((function(r,n){let{left:o,top:h,width:l,height:c}=n,g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:B.NUM_FRACTION_DIGITS;const u=ze(nt,r,!1),[d,f,v,_]=[o,h,l,c].map((b=>U(b,g)));return"<rect ".concat(u,' x="').concat(d,'" y="').concat(f,'" width="').concat(v,'" height="').concat(_,'"></rect>')})(a,{left:t,top:e,width:s,height:i}),`
`)}const ch=["textAnchor","textDecoration","dx","dy","top","left","fontSize","strokeWidth"];let ri;class gt extends kn{static getDefaults(){return m(m({},super.getDefaults()),gt.ownDefaults)}constructor(t,e){super(),p(this,"__charBounds",[]),Object.assign(this,gt.ownDefaults),this.setOptions(e),this.styles||(this.styles={}),this.text=t,this.initialized=!0,this.path&&this.setPathInfo(),this.initDimensions(),this.setCoords()}setPathInfo(){const t=this.path;t&&(t.segmentsInfo=_n(t.path))}_splitText(){const t=this._splitTextIntoLines(this.text);return this.textLines=t.lines,this._textLines=t.graphemeLines,this._unwrappedTextLines=t._unwrappedLines,this._text=t.graphemeText,t}initDimensions(){this._splitText(),this._clearCache(),this.dirty=!0,this.path?(this.width=this.path.width,this.height=this.path.height):(this.width=this.calcTextWidth()||this.cursorWidth||this.MIN_TEXT_WIDTH,this.height=this.calcTextHeight()),this.textAlign.includes(It)&&this.enlargeSpaces()}enlargeSpaces(){let t,e,s,i,r,n,o;for(let h=0,l=this._textLines.length;h<l;h++)if((this.textAlign===It||h!==l-1&&!this.isEndOfWrapping(h))&&(i=0,r=this._textLines[h],e=this.getLineWidth(h),e<this.width&&(o=this.textLines[h].match(this._reSpacesAndTabs)))){s=o.length,t=(this.width-e)/s;for(let c=0;c<=r.length;c++)n=this.__charBounds[h][c],this._reSpaceAndTab.test(r[c])?(n.width+=t,n.kernedWidth+=t,n.left+=i,i+=t):n.left+=i}}isEndOfWrapping(t){return t===this._textLines.length-1}missingNewlineOffset(t){return 1}get2DCursorLocation(t,e){const s=e?this._unwrappedTextLines:this._textLines;let i;for(i=0;i<s.length;i++){if(t<=s[i].length)return{lineIndex:i,charIndex:t};t-=s[i].length+this.missingNewlineOffset(i,e)}return{lineIndex:i-1,charIndex:s[i-1].length<t?s[i-1].length:t}}toString(){return"#<Text (".concat(this.complexity(),'): { "text": "').concat(this.text,'", "fontFamily": "').concat(this.fontFamily,'" }>')}_getCacheCanvasDimensions(){const t=super._getCacheCanvasDimensions(),e=this.fontSize;return t.width+=e*t.zoomX,t.height+=e*t.zoomY,t}_render(t){const e=this.path;e&&!e.isNotVisible()&&e._render(t),this._setTextStyles(t),this._renderTextLinesBackground(t),this._renderTextDecoration(t,"underline"),this._renderText(t),this._renderTextDecoration(t,"overline"),this._renderTextDecoration(t,"linethrough")}_renderText(t){this.paintFirst===_t?(this._renderTextStroke(t),this._renderTextFill(t)):(this._renderTextFill(t),this._renderTextStroke(t))}_setTextStyles(t,e,s){if(t.textBaseline="alphabetic",this.path)switch(this.pathAlign){case F:t.textBaseline="middle";break;case"ascender":t.textBaseline=vt;break;case"descender":t.textBaseline=li}t.font=this._getFontDeclaration(e,s)}calcTextWidth(){let t=this.getLineWidth(0);for(let e=1,s=this._textLines.length;e<s;e++){const i=this.getLineWidth(e);i>t&&(t=i)}return t}_renderTextLine(t,e,s,i,r,n){this._renderChars(t,e,s,i,r,n)}_renderTextLinesBackground(t){if(!this.textBackgroundColor&&!this.styleHas("textBackgroundColor"))return;const e=t.fillStyle,s=this._getLeftOffset();let i=this._getTopOffset();for(let r=0,n=this._textLines.length;r<n;r++){const o=this.getHeightOfLine(r);if(!this.textBackgroundColor&&!this.styleHas("textBackgroundColor",r)){i+=o;continue}const h=this._textLines[r].length,l=this._getLineLeftOffset(r);let c,g,u=0,d=0,f=this.getValueOfPropertyAt(r,0,"textBackgroundColor");for(let v=0;v<h;v++){const _=this.__charBounds[r][v];g=this.getValueOfPropertyAt(r,v,"textBackgroundColor"),this.path?(t.save(),t.translate(_.renderLeft,_.renderTop),t.rotate(_.angle),t.fillStyle=g,g&&t.fillRect(-_.width/2,-o/this.lineHeight*(1-this._fontSizeFraction),_.width,o/this.lineHeight),t.restore()):g!==f?(c=s+l+d,this.direction==="rtl"&&(c=this.width-c-u),t.fillStyle=f,f&&t.fillRect(c,i,u,o/this.lineHeight),d=_.left,u=_.width,f=g):u+=_.kernedWidth}g&&!this.path&&(c=s+l+d,this.direction==="rtl"&&(c=this.width-c-u),t.fillStyle=g,t.fillRect(c,i,u,o/this.lineHeight)),i+=o}t.fillStyle=e,this._removeShadow(t)}_measureChar(t,e,s,i){const r=Ie.getFontCache(e),n=this._getFontDeclaration(e),o=s+t,h=s&&n===this._getFontDeclaration(i),l=e.fontSize/this.CACHE_FONT_SIZE;let c,g,u,d;if(s&&r[s]!==void 0&&(u=r[s]),r[t]!==void 0&&(d=c=r[t]),h&&r[o]!==void 0&&(g=r[o],d=g-u),c===void 0||u===void 0||g===void 0){const f=(function(){return ri||(ri=Tt({width:0,height:0}).getContext("2d")),ri})();this._setTextStyles(f,e,!0),c===void 0&&(d=c=f.measureText(t).width,r[t]=c),u===void 0&&h&&s&&(u=f.measureText(s).width,r[s]=u),h&&g===void 0&&(g=f.measureText(o).width,r[o]=g,d=g-u)}return{width:c*l,kernedWidth:d*l}}getHeightOfChar(t,e){return this.getValueOfPropertyAt(t,e,"fontSize")}measureLine(t){const e=this._measureLine(t);return this.charSpacing!==0&&(e.width-=this._getWidthOfCharSpacing()),e.width<0&&(e.width=0),e}_measureLine(t){let e,s,i=0;const r=this.pathSide===Z,n=this.path,o=this._textLines[t],h=o.length,l=new Array(h);this.__charBounds[t]=l;for(let c=0;c<h;c++){const g=o[c];s=this._getGraphemeBox(g,t,c,e),l[c]=s,i+=s.kernedWidth,e=g}if(l[h]={left:s?s.left+s.width:0,width:0,kernedWidth:0,height:this.fontSize,deltaY:0},n&&n.segmentsInfo){let c=0;const g=n.segmentsInfo[n.segmentsInfo.length-1].length;switch(this.textAlign){case X:c=r?g-i:0;break;case F:c=(g-i)/2;break;case Z:c=r?0:g-i}c+=this.pathStartOffset*(r?-1:1);for(let u=r?h-1:0;r?u>=0:u<h;r?u--:u++)s=l[u],c>g?c%=g:c<0&&(c+=g),this._setGraphemeOnPath(c,s),c+=s.kernedWidth}return{width:i,numOfSpaces:0}}_setGraphemeOnPath(t,e){const s=t+e.kernedWidth/2,i=this.path,r=Ra(i.path,s,i.segmentsInfo);e.renderLeft=r.x-i.pathOffset.x,e.renderTop=r.y-i.pathOffset.y,e.angle=r.angle+(this.pathSide===Z?Math.PI:0)}_getGraphemeBox(t,e,s,i,r){const n=this.getCompleteStyleDeclaration(e,s),o=i?this.getCompleteStyleDeclaration(e,s-1):{},h=this._measureChar(t,n,i,o);let l,c=h.kernedWidth,g=h.width;this.charSpacing!==0&&(l=this._getWidthOfCharSpacing(),g+=l,c+=l);const u={width:g,left:0,height:n.fontSize,kernedWidth:c,deltaY:n.deltaY};if(s>0&&!r){const d=this.__charBounds[e][s-1];u.left=d.left+d.width+h.kernedWidth-h.width}return u}getHeightOfLine(t){if(this.__lineHeights[t])return this.__lineHeights[t];let e=this.getHeightOfChar(t,0);for(let s=1,i=this._textLines[t].length;s<i;s++)e=Math.max(this.getHeightOfChar(t,s),e);return this.__lineHeights[t]=e*this.lineHeight*this._fontSizeMult}calcTextHeight(){let t,e=0;for(let s=0,i=this._textLines.length;s<i;s++)t=this.getHeightOfLine(s),e+=s===i-1?t/this.lineHeight:t;return e}_getLeftOffset(){return this.direction==="ltr"?-this.width/2:this.width/2}_getTopOffset(){return-this.height/2}_renderTextCommon(t,e){t.save();let s=0;const i=this._getLeftOffset(),r=this._getTopOffset();for(let n=0,o=this._textLines.length;n<o;n++){const h=this.getHeightOfLine(n),l=h/this.lineHeight,c=this._getLineLeftOffset(n);this._renderTextLine(e,t,this._textLines[n],i+c,r+s+l,n),s+=h}t.restore()}_renderTextFill(t){(this.fill||this.styleHas(nt))&&this._renderTextCommon(t,"fillText")}_renderTextStroke(t){(this.stroke&&this.strokeWidth!==0||!this.isEmptyStyles())&&(this.shadow&&!this.shadow.affectStroke&&this._removeShadow(t),t.save(),this._setLineDash(t,this.strokeDashArray),t.beginPath(),this._renderTextCommon(t,"strokeText"),t.closePath(),t.restore())}_renderChars(t,e,s,i,r,n){const o=this.getHeightOfLine(n),h=this.textAlign.includes(It),l=this.path,c=!h&&this.charSpacing===0&&this.isEmptyStyles(n)&&!l,g=this.direction==="ltr",u=this.direction==="ltr"?1:-1,d=e.direction;let f,v,_,b,C,O="",w=0;if(e.save(),d!==this.direction&&(e.canvas.setAttribute("dir",g?"ltr":"rtl"),e.direction=g?"ltr":"rtl",e.textAlign=g?X:Z),r-=o*this._fontSizeFraction/this.lineHeight,c)return this._renderChar(t,e,n,0,s.join(""),i,r),void e.restore();for(let S=0,M=s.length-1;S<=M;S++)b=S===M||this.charSpacing||l,O+=s[S],_=this.__charBounds[n][S],w===0?(i+=u*(_.kernedWidth-_.width),w+=_.width):w+=_.kernedWidth,h&&!b&&this._reSpaceAndTab.test(s[S])&&(b=!0),b||(f=f||this.getCompleteStyleDeclaration(n,S),v=this.getCompleteStyleDeclaration(n,S+1),b=Ii(f,v,!1)),b&&(l?(e.save(),e.translate(_.renderLeft,_.renderTop),e.rotate(_.angle),this._renderChar(t,e,n,S,O,-w/2,0),e.restore()):(C=i,this._renderChar(t,e,n,S,O,C,r)),O="",f=v,i+=u*w,w=0);e.restore()}_applyPatternGradientTransformText(t){const e=this.width+this.strokeWidth,s=this.height+this.strokeWidth,i=Tt({width:e,height:s}),r=i.getContext("2d");return i.width=e,i.height=s,r.beginPath(),r.moveTo(0,0),r.lineTo(e,0),r.lineTo(e,s),r.lineTo(0,s),r.closePath(),r.translate(e/2,s/2),r.fillStyle=t.toLive(r),this._applyPatternGradientTransform(r,t),r.fill(),r.createPattern(i,"no-repeat")}handleFiller(t,e,s){let i,r;return St(s)?s.gradientUnits==="percentage"||s.gradientTransform||s.patternTransform?(i=-this.width/2,r=-this.height/2,t.translate(i,r),t[e]=this._applyPatternGradientTransformText(s),{offsetX:i,offsetY:r}):(t[e]=s.toLive(t),this._applyPatternGradientTransform(t,s)):(t[e]=s,{offsetX:0,offsetY:0})}_setStrokeStyles(t,e){let{stroke:s,strokeWidth:i}=e;return t.lineWidth=i,t.lineCap=this.strokeLineCap,t.lineDashOffset=this.strokeDashOffset,t.lineJoin=this.strokeLineJoin,t.miterLimit=this.strokeMiterLimit,this.handleFiller(t,"strokeStyle",s)}_setFillStyles(t,e){let{fill:s}=e;return this.handleFiller(t,"fillStyle",s)}_renderChar(t,e,s,i,r,n,o){const h=this._getStyleDeclaration(s,i),l=this.getCompleteStyleDeclaration(s,i),c=t==="fillText"&&l.fill,g=t==="strokeText"&&l.stroke&&l.strokeWidth;if(g||c){if(e.save(),e.font=this._getFontDeclaration(l),h.textBackgroundColor&&this._removeShadow(e),h.deltaY&&(o+=h.deltaY),c){const u=this._setFillStyles(e,l);e.fillText(r,n-u.offsetX,o-u.offsetY)}if(g){const u=this._setStrokeStyles(e,l);e.strokeText(r,n-u.offsetX,o-u.offsetY)}e.restore()}}setSuperscript(t,e){this._setScript(t,e,this.superscript)}setSubscript(t,e){this._setScript(t,e,this.subscript)}_setScript(t,e,s){const i=this.get2DCursorLocation(t,!0),r=this.getValueOfPropertyAt(i.lineIndex,i.charIndex,"fontSize"),n=this.getValueOfPropertyAt(i.lineIndex,i.charIndex,"deltaY"),o={fontSize:r*s.size,deltaY:n+r*s.baseline};this.setSelectionStyles(o,t,e)}_getLineLeftOffset(t){const e=this.getLineWidth(t),s=this.width-e,i=this.textAlign,r=this.direction,n=this.isEndOfWrapping(t);let o=0;return i===It||i===Be&&!n||i===Re&&!n||i===As&&!n?0:(i===F&&(o=s/2),i===Z&&(o=s),i===Be&&(o=s/2),i===Re&&(o=s),r==="rtl"&&(i===Z||i===It||i===Re?o=0:i===X||i===As?o=-s:i!==F&&i!==Be||(o=-s/2)),o)}_clearCache(){this._forceClearCache=!1,this.__lineWidths=[],this.__lineHeights=[],this.__charBounds=[]}getLineWidth(t){if(this.__lineWidths[t]!==void 0)return this.__lineWidths[t];const{width:e}=this.measureLine(t);return this.__lineWidths[t]=e,e}_getWidthOfCharSpacing(){return this.charSpacing!==0?this.fontSize*this.charSpacing/1e3:0}getValueOfPropertyAt(t,e,s){var i;return(i=this._getStyleDeclaration(t,e)[s])!==null&&i!==void 0?i:this[s]}_renderTextDecoration(t,e){if(!this[e]&&!this.styleHas(e))return;let s=this._getTopOffset();const i=this._getLeftOffset(),r=this.path,n=this._getWidthOfCharSpacing(),o=e==="linethrough"?.5:e==="overline"?1:0,h=this.offsets[e];for(let l=0,c=this._textLines.length;l<c;l++){const g=this.getHeightOfLine(l);if(!this[e]&&!this.styleHas(e,l)){s+=g;continue}const u=this._textLines[l],d=g/this.lineHeight,f=this._getLineLeftOffset(l);let v=0,_=0,b=this.getValueOfPropertyAt(l,0,e),C=this.getValueOfPropertyAt(l,0,nt),O=this.getValueOfPropertyAt(l,0,ge),w=b,S=C,M=O;const L=s+d*(1-this._fontSizeFraction);let E=this.getHeightOfChar(l,0),W=this.getValueOfPropertyAt(l,0,"deltaY");for(let H=0,Y=u.length;H<Y;H++){const R=this.__charBounds[l][H];w=this.getValueOfPropertyAt(l,H,e),S=this.getValueOfPropertyAt(l,H,nt),M=this.getValueOfPropertyAt(l,H,ge);const it=this.getHeightOfChar(l,H),et=this.getValueOfPropertyAt(l,H,"deltaY");if(r&&w&&S){const rt=this.fontSize*M/1e3;t.save(),t.fillStyle=C,t.translate(R.renderLeft,R.renderTop),t.rotate(R.angle),t.fillRect(-R.kernedWidth/2,h*it+et-o*rt,R.kernedWidth,rt),t.restore()}else if((w!==b||S!==C||it!==E||M!==O||et!==W)&&_>0){const rt=this.fontSize*O/1e3;let V=i+f+v;this.direction==="rtl"&&(V=this.width-V-_),b&&C&&O&&(t.fillStyle=C,t.fillRect(V,L+h*E+W-o*rt,_,rt)),v=R.left,_=R.width,b=w,O=M,C=S,E=it,W=et}else _+=R.kernedWidth}let $=i+f+v;this.direction==="rtl"&&($=this.width-$-_),t.fillStyle=S;const tt=this.fontSize*M/1e3;w&&S&&M&&t.fillRect($,L+h*E+W-o*tt,_-n,tt),s+=g}this._removeShadow(t)}_getFontDeclaration(){let{fontFamily:t=this.fontFamily,fontStyle:e=this.fontStyle,fontWeight:s=this.fontWeight,fontSize:i=this.fontSize}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;const n=t.includes("'")||t.includes('"')||t.includes(",")||gt.genericFonts.includes(t.toLowerCase())?t:'"'.concat(t,'"');return[e,s,"".concat(r?this.CACHE_FONT_SIZE:i,"px"),n].join(" ")}render(t){this.visible&&(this.canvas&&this.canvas.skipOffscreen&&!this.group&&!this.isOnScreen()||(this._forceClearCache&&this.initDimensions(),super.render(t)))}graphemeSplit(t){return Li(t)}_splitTextIntoLines(t){const e=t.split(this._reNewline),s=new Array(e.length),i=[`
`];let r=[];for(let n=0;n<e.length;n++)s[n]=this.graphemeSplit(e[n]),r=r.concat(s[n],i);return r.pop(),{_unwrappedLines:s,lines:e,graphemeText:r,graphemeLines:s}}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return m(m({},super.toObject([...tn,...t])),{},{styles:Qo(this.styles,this.text)},this.path?{path:this.path.toObject()}:{})}set(t,e){const{textLayoutProperties:s}=this.constructor;super.set(t,e);let i=!1,r=!1;if(typeof t=="object")for(const n in t)n==="path"&&this.setPathInfo(),i=i||s.includes(n),r=r||n==="path";else i=s.includes(t),r=t==="path";return r&&this.setPathInfo(),i&&this.initialized&&(this.initDimensions(),this.setCoords()),this}complexity(){return 1}static async fromElement(t,e,s){const i=Qt(t,gt.ATTRIBUTE_NAMES,s),r=m(m({},e),i),{textAnchor:n=X,textDecoration:o="",dx:h=0,dy:l=0,top:c=0,left:g=0,fontSize:u=Ci,strokeWidth:d=1}=r,f=N(r,ch),v=new this((t.textContent||"").replace(/^\s+|\s+$|\n+/g,"").replace(/\s+/g," "),m({left:g+h,top:c+l,underline:o.includes("underline"),overline:o.includes("overline"),linethrough:o.includes("line-through"),strokeWidth:0,fontSize:u},f)),_=v.getScaledHeight()/v.height,b=((v.height+v.strokeWidth)*v.lineHeight-v.height)*_,C=v.getScaledHeight()+b;let O=0;return n===F&&(O=v.getScaledWidth()/2),n===Z&&(O=v.getScaledWidth()),v.set({left:v.left-O,top:v.top-(C-v.fontSize*(.07+v._fontSizeFraction))/v.lineHeight,strokeWidth:d}),v}static fromObject(t){return this._fromObject(m(m({},t),{},{styles:ta(t.styles||{},t.text)}),{extraParam:"text"})}}p(gt,"textLayoutProperties",Qr),p(gt,"cacheProperties",[...Zt,...tn]),p(gt,"ownDefaults",uo),p(gt,"type","Text"),p(gt,"genericFonts",["serif","sans-serif","monospace","cursive","fantasy","system-ui","ui-serif","ui-sans-serif","ui-monospace","ui-rounded","math","emoji","fangsong"]),p(gt,"ATTRIBUTE_NAMES",re.concat("x","y","dx","dy","font-family","font-style","font-weight","font-size","letter-spacing","text-decoration","text-anchor")),cn(gt,[class extends Kr{_toSVG(){const a=this._getSVGLeftTopOffsets(),t=this._getSVGTextAndBg(a.textTop,a.textLeft);return this._wrapSVGTextAndBg(t)}toSVG(a){const t=this._createBaseSVGMarkup(this._toSVG(),{reviver:a,noStyle:!0,withShadow:!0}),e=this.path;return e?t+e._createBaseSVGMarkup(e._toSVG(),{reviver:a,withShadow:!0,additionalTransform:Ve(this.calcOwnMatrix())}):t}_getSVGLeftTopOffsets(){return{textLeft:-this.width/2,textTop:-this.height/2,lineTop:this.getHeightOfLine(0)}}_wrapSVGTextAndBg(a){let{textBgRects:t,textSpans:e}=a;const s=this.getSvgTextDecoration(this);return[t.join(""),'		<text xml:space="preserve" ','font-family="'.concat(this.fontFamily.replace(lh,"'"),'" '),'font-size="'.concat(this.fontSize,'" '),this.fontStyle?'font-style="'.concat(this.fontStyle,'" '):"",this.fontWeight?'font-weight="'.concat(this.fontWeight,'" '):"",s?'text-decoration="'.concat(s,'" '):"",this.direction==="rtl"?'direction="'.concat(this.direction,'" '):"",'style="',this.getSvgStyles(!0),'"',this.addPaintOrder()," >",e.join(""),`</text>
`]}_getSVGTextAndBg(a,t){const e=[],s=[];let i,r=a;this.backgroundColor&&s.push(...ii(this.backgroundColor,-this.width/2,-this.height/2,this.width,this.height));for(let n=0,o=this._textLines.length;n<o;n++)i=this._getLineLeftOffset(n),this.direction==="rtl"&&(i+=this.width),(this.textBackgroundColor||this.styleHas("textBackgroundColor",n))&&this._setSVGTextLineBg(s,n,t+i,r),this._setSVGTextLineText(e,n,t+i,r),r+=this.getHeightOfLine(n);return{textSpans:e,textBgRects:s}}_createTextCharSpan(a,t,e,s,i){const r=B.NUM_FRACTION_DIGITS,n=this.getSvgSpanStyles(t,a!==a.trim()||!!a.match(hh)),o=n?'style="'.concat(n,'"'):"",h=t.deltaY,l=h?' dy="'.concat(U(h,r),'" '):"",{angle:c,renderLeft:g,renderTop:u,width:d}=i;let f="";if(g!==void 0){const v=d/2;c&&(f=' rotate="'.concat(U(Jt(c),r),'"'));const _=Me({angle:Jt(c)});_[4]=g,_[5]=u;const b=new y(-v,0).transform(_);e=b.x,s=b.y}return'<tspan x="'.concat(U(e,r),'" y="').concat(U(s,r),'" ').concat(l).concat(f).concat(o,">").concat(Ko(a),"</tspan>")}_setSVGTextLineText(a,t,e,s){const i=this.getHeightOfLine(t),r=this.textAlign.includes(It),n=this._textLines[t];let o,h,l,c,g,u="",d=0;s+=i*(1-this._fontSizeFraction)/this.lineHeight;for(let f=0,v=n.length-1;f<=v;f++)g=f===v||this.charSpacing||this.path,u+=n[f],l=this.__charBounds[t][f],d===0?(e+=l.kernedWidth-l.width,d+=l.width):d+=l.kernedWidth,r&&!g&&this._reSpaceAndTab.test(n[f])&&(g=!0),g||(o=o||this.getCompleteStyleDeclaration(t,f),h=this.getCompleteStyleDeclaration(t,f+1),g=Ii(o,h,!0)),g&&(c=this._getStyleDeclaration(t,f),a.push(this._createTextCharSpan(u,c,e,s,l)),u="",o=h,this.direction==="rtl"?e-=d:e+=d,d=0)}_setSVGTextLineBg(a,t,e,s){const i=this._textLines[t],r=this.getHeightOfLine(t)/this.lineHeight;let n,o=0,h=0,l=this.getValueOfPropertyAt(t,0,"textBackgroundColor");for(let c=0;c<i.length;c++){const{left:g,width:u,kernedWidth:d}=this.__charBounds[t][c];n=this.getValueOfPropertyAt(t,c,"textBackgroundColor"),n!==l?(l&&a.push(...ii(l,e+h,s,o,r)),h=g,o=u,l=n):o+=d}n&&a.push(...ii(l,e+h,s,o,r))}_getSVGLineTopOffset(a){let t,e=0;for(t=0;t<a;t++)e+=this.getHeightOfLine(t);const s=this.getHeightOfLine(t);return{lineTop:e,offset:(this._fontSizeMult-this._fontSizeFraction)*s/(this.lineHeight*this._fontSizeMult)}}getSvgStyles(a){return"".concat(super.getSvgStyles(a)," text-decoration-thickness: ").concat(U(this.textDecorationThickness*this.getObjectScaling().y/10,B.NUM_FRACTION_DIGITS),"%; white-space: pre;")}getSvgSpanStyles(a,t){const{fontFamily:e,strokeWidth:s,stroke:i,fill:r,fontSize:n,fontStyle:o,fontWeight:h,deltaY:l,textDecorationThickness:c,linethrough:g,overline:u,underline:d}=a,f=this.getSvgTextDecoration({underline:d??this.underline,overline:u??this.overline,linethrough:g??this.linethrough}),v=c||this.textDecorationThickness;return[i?ze(_t,i):"",s?"stroke-width: ".concat(s,"; "):"",e?"font-family: ".concat(e.includes("'")||e.includes('"')?e:"'".concat(e,"'"),"; "):"",n?"font-size: ".concat(n,"px; "):"",o?"font-style: ".concat(o,"; "):"",h?"font-weight: ".concat(h,"; "):"",f?"text-decoration: ".concat(f,"; text-decoration-thickness: ").concat(U(v*this.getObjectScaling().y/10,B.NUM_FRACTION_DIGITS),"%; "):"",r?ze(nt,r):"",l?"baseline-shift: ".concat(-l,"; "):"",t?"white-space: pre; ":""].join("")}getSvgTextDecoration(a){return["overline","underline","line-through"].filter((t=>a[t.replace("-","")])).join(" ")}}]),D.setClass(gt),D.setSVGClass(gt);class uh{constructor(t){p(this,"target",void 0),p(this,"__mouseDownInPlace",!1),p(this,"__dragStartFired",!1),p(this,"__isDraggingOver",!1),p(this,"__dragStartSelection",void 0),p(this,"__dragImageDisposer",void 0),p(this,"_dispose",void 0),this.target=t;const e=[this.target.on("dragenter",this.dragEnterHandler.bind(this)),this.target.on("dragover",this.dragOverHandler.bind(this)),this.target.on("dragleave",this.dragLeaveHandler.bind(this)),this.target.on("dragend",this.dragEndHandler.bind(this)),this.target.on("drop",this.dropHandler.bind(this))];this._dispose=()=>{e.forEach((s=>s())),this._dispose=void 0}}isPointerOverSelection(t){const e=this.target,s=e.getSelectionStartFromPointer(t);return e.isEditing&&s>=e.selectionStart&&s<=e.selectionEnd&&e.selectionStart<e.selectionEnd}start(t){return this.__mouseDownInPlace=this.isPointerOverSelection(t)}isActive(){return this.__mouseDownInPlace}end(t){const e=this.isActive();return e&&!this.__dragStartFired&&(this.target.setCursorByClick(t),this.target.initDelayedCursor(!0)),this.__mouseDownInPlace=!1,this.__dragStartFired=!1,this.__isDraggingOver=!1,e}getDragStartSelection(){return this.__dragStartSelection}setDragImage(t,e){var s;let{selectionStart:i,selectionEnd:r}=e;const n=this.target,o=n.canvas,h=new y(n.flipX?-1:1,n.flipY?-1:1),l=n._getCursorBoundaries(i),c=new y(l.left+l.leftOffset,l.top+l.topOffset).multiply(h).transform(n.calcTransformMatrix()),g=o.getScenePoint(t).subtract(c),u=n.getCanvasRetinaScaling(),d=n.getBoundingRect(),f=c.subtract(new y(d.left,d.top)),v=o.viewportTransform,_=f.add(g).transform(v,!0),b=n.backgroundColor,C=Fi(n.styles);n.backgroundColor="";const O={stroke:"transparent",fill:"transparent",textBackgroundColor:"transparent"};n.setSelectionStyles(O,0,i),n.setSelectionStyles(O,r,n.text.length),n.dirty=!0;const w=n.toCanvasElement({enableRetinaScaling:o.enableRetinaScaling,viewportTransform:!0});n.backgroundColor=b,n.styles=C,n.dirty=!0,yi(w,{position:"fixed",left:"".concat(-w.width,"px"),border:yt,width:"".concat(w.width/u,"px"),height:"".concat(w.height/u,"px")}),this.__dragImageDisposer&&this.__dragImageDisposer(),this.__dragImageDisposer=()=>{w.remove()},Pt(t.target||this.target.hiddenTextarea).body.appendChild(w),(s=t.dataTransfer)===null||s===void 0||s.setDragImage(w,_.x,_.y)}onDragStart(t){this.__dragStartFired=!0;const e=this.target,s=this.isActive();if(s&&t.dataTransfer){const i=this.__dragStartSelection={selectionStart:e.selectionStart,selectionEnd:e.selectionEnd},r=e._text.slice(i.selectionStart,i.selectionEnd).join(""),n=m({text:e.text,value:r},i);t.dataTransfer.setData("text/plain",r),t.dataTransfer.setData("application/fabric",JSON.stringify({value:r,styles:e.getSelectionStyles(i.selectionStart,i.selectionEnd,!0)})),t.dataTransfer.effectAllowed="copyMove",this.setDragImage(t,n)}return e.abortCursorAnimation(),s}canDrop(t){if(this.target.editable&&!this.target.getActiveControl()&&!t.defaultPrevented){if(this.isActive()&&this.__dragStartSelection){const e=this.target.getSelectionStartFromPointer(t),s=this.__dragStartSelection;return e<s.selectionStart||e>s.selectionEnd}return!0}return!1}targetCanDrop(t){return this.target.canDrop(t)}dragEnterHandler(t){let{e}=t;const s=this.targetCanDrop(e);!this.__isDraggingOver&&s&&(this.__isDraggingOver=!0)}dragOverHandler(t){const{e}=t,s=this.targetCanDrop(e);!this.__isDraggingOver&&s?this.__isDraggingOver=!0:this.__isDraggingOver&&!s&&(this.__isDraggingOver=!1),this.__isDraggingOver&&(e.preventDefault(),t.canDrop=!0,t.dropTarget=this.target)}dragLeaveHandler(){(this.__isDraggingOver||this.isActive())&&(this.__isDraggingOver=!1)}dropHandler(t){var e;const{e:s}=t,i=s.defaultPrevented;this.__isDraggingOver=!1,s.preventDefault();let r=(e=s.dataTransfer)===null||e===void 0?void 0:e.getData("text/plain");if(r&&!i){const n=this.target,o=n.canvas;let h=n.getSelectionStartFromPointer(s);const{styles:l}=s.dataTransfer.types.includes("application/fabric")?JSON.parse(s.dataTransfer.getData("application/fabric")):{},c=r[Math.max(0,r.length-1)],g=0;if(this.__dragStartSelection){const u=this.__dragStartSelection.selectionStart,d=this.__dragStartSelection.selectionEnd;h>u&&h<=d?h=u:h>d&&(h-=d-u),n.removeChars(u,d),delete this.__dragStartSelection}n._reNewline.test(c)&&(n._reNewline.test(n._text[h])||h===n._text.length)&&(r=r.trimEnd()),t.didDrop=!0,t.dropTarget=n,n.insertChars(r,l,h),o.setActiveObject(n),n.enterEditing(s),n.selectionStart=Math.min(h+g,n._text.length),n.selectionEnd=Math.min(n.selectionStart+r.length,n._text.length),n.hiddenTextarea.value=n.text,n._updateTextarea(),n.hiddenTextarea.focus(),n.fire(Ds,{index:h+g,action:"drop"}),o.fire("text:changed",{target:n}),o.contextTopDirty=!0,o.requestRenderAll()}}dragEndHandler(t){let{e}=t;if(this.isActive()&&this.__dragStartFired&&this.__dragStartSelection){var s;const i=this.target,r=this.target.canvas,{selectionStart:n,selectionEnd:o}=this.__dragStartSelection,h=((s=e.dataTransfer)===null||s===void 0?void 0:s.dropEffect)||yt;h===yt?(i.selectionStart=n,i.selectionEnd=o,i._updateTextarea(),i.hiddenTextarea.focus()):(i.clearContextTop(),h==="move"&&(i.removeChars(n,o),i.selectionStart=i.selectionEnd=n,i.hiddenTextarea&&(i.hiddenTextarea.value=i.text),i._updateTextarea(),i.fire(Ds,{index:n,action:"dragend"}),r.fire("text:changed",{target:i}),r.requestRenderAll()),i.exitEditing())}this.__dragImageDisposer&&this.__dragImageDisposer(),delete this.__dragImageDisposer,delete this.__dragStartSelection,this.__isDraggingOver=!1}dispose(){this._dispose&&this._dispose()}}const Sr=/[ \n\.,;!\?\-]/;class gh extends gt{constructor(){super(...arguments),p(this,"_currentCursorOpacity",1)}initBehavior(){this._tick=this._tick.bind(this),this._onTickComplete=this._onTickComplete.bind(this),this.updateSelectionOnMouseMove=this.updateSelectionOnMouseMove.bind(this)}onDeselect(t){return this.isEditing&&this.exitEditing(),this.selected=!1,super.onDeselect(t)}_animateCursor(t){let{toValue:e,duration:s,delay:i,onComplete:r}=t;return nn({startValue:this._currentCursorOpacity,endValue:e,duration:s,delay:i,onComplete:r,abort:()=>!this.canvas||this.selectionStart!==this.selectionEnd,onChange:n=>{this._currentCursorOpacity=n,this.renderCursorOrSelection()}})}_tick(t){this._currentTickState=this._animateCursor({toValue:0,duration:this.cursorDuration/2,delay:Math.max(t||0,100),onComplete:this._onTickComplete})}_onTickComplete(){var t;(t=this._currentTickCompleteState)===null||t===void 0||t.abort(),this._currentTickCompleteState=this._animateCursor({toValue:1,duration:this.cursorDuration,onComplete:this._tick})}initDelayedCursor(t){this.abortCursorAnimation(),this._tick(t?0:this.cursorDelay)}abortCursorAnimation(){let t=!1;[this._currentTickState,this._currentTickCompleteState].forEach((e=>{e&&!e.isDone()&&(t=!0,e.abort())})),this._currentCursorOpacity=1,t&&this.clearContextTop()}restartCursorIfNeeded(){[this._currentTickState,this._currentTickCompleteState].some((t=>!t||t.isDone()))&&this.initDelayedCursor()}selectAll(){return this.selectionStart=0,this.selectionEnd=this._text.length,this._fireSelectionChanged(),this._updateTextarea(),this}cmdAll(){this.selectAll(),this.renderCursorOrSelection()}getSelectedText(){return this._text.slice(this.selectionStart,this.selectionEnd).join("")}findWordBoundaryLeft(t){let e=0,s=t-1;if(this._reSpace.test(this._text[s]))for(;this._reSpace.test(this._text[s]);)e++,s--;for(;/\S/.test(this._text[s])&&s>-1;)e++,s--;return t-e}findWordBoundaryRight(t){let e=0,s=t;if(this._reSpace.test(this._text[s]))for(;this._reSpace.test(this._text[s]);)e++,s++;for(;/\S/.test(this._text[s])&&s<this._text.length;)e++,s++;return t+e}findLineBoundaryLeft(t){let e=0,s=t-1;for(;!/\n/.test(this._text[s])&&s>-1;)e++,s--;return t-e}findLineBoundaryRight(t){let e=0,s=t;for(;!/\n/.test(this._text[s])&&s<this._text.length;)e++,s++;return t+e}searchWordBoundary(t,e){const s=this._text;let i=t>0&&this._reSpace.test(s[t])&&(e===-1||!Si.test(s[t-1]))?t-1:t,r=s[i];for(;i>0&&i<s.length&&!Sr.test(r);)i+=e,r=s[i];return e===-1&&Sr.test(r)&&i++,i}selectWord(t){var e;t=(e=t)!==null&&e!==void 0?e:this.selectionStart;const s=this.searchWordBoundary(t,-1),i=Math.max(s,this.searchWordBoundary(t,1));this.selectionStart=s,this.selectionEnd=i,this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()}selectLine(t){var e;t=(e=t)!==null&&e!==void 0?e:this.selectionStart;const s=this.findLineBoundaryLeft(t),i=this.findLineBoundaryRight(t);this.selectionStart=s,this.selectionEnd=i,this._fireSelectionChanged(),this._updateTextarea()}enterEditing(t){!this.isEditing&&this.editable&&(this.enterEditingImpl(),this.fire("editing:entered",t?{e:t}:void 0),this._fireSelectionChanged(),this.canvas&&(this.canvas.fire("text:editing:entered",{target:this,e:t}),this.canvas.requestRenderAll()))}enterEditingImpl(){this.canvas&&(this.canvas.calcOffset(),this.canvas.textEditingManager.exitTextEditing()),this.isEditing=!0,this.initHiddenTextarea(),this.hiddenTextarea.focus(),this.hiddenTextarea.value=this.text,this._updateTextarea(),this._saveEditingProps(),this._setEditingProps(),this._textBeforeEdit=this.text,this._tick()}updateSelectionOnMouseMove(t){if(this.getActiveControl())return;const e=this.hiddenTextarea;Pt(e).activeElement!==e&&e.focus();const s=this.getSelectionStartFromPointer(t),i=this.selectionStart,r=this.selectionEnd;(s===this.__selectionStartOnMouseDown&&i!==r||i!==s&&r!==s)&&(s>this.__selectionStartOnMouseDown?(this.selectionStart=this.__selectionStartOnMouseDown,this.selectionEnd=s):(this.selectionStart=s,this.selectionEnd=this.__selectionStartOnMouseDown),this.selectionStart===i&&this.selectionEnd===r||(this._fireSelectionChanged(),this._updateTextarea(),this.renderCursorOrSelection()))}_setEditingProps(){this.hoverCursor="text",this.canvas&&(this.canvas.defaultCursor=this.canvas.moveCursor="text"),this.borderColor=this.editingBorderColor,this.hasControls=this.selectable=!1,this.lockMovementX=this.lockMovementY=!0}fromStringToGraphemeSelection(t,e,s){const i=s.slice(0,t),r=this.graphemeSplit(i).length;if(t===e)return{selectionStart:r,selectionEnd:r};const n=s.slice(t,e);return{selectionStart:r,selectionEnd:r+this.graphemeSplit(n).length}}fromGraphemeToStringSelection(t,e,s){const i=s.slice(0,t).join("").length;return t===e?{selectionStart:i,selectionEnd:i}:{selectionStart:i,selectionEnd:i+s.slice(t,e).join("").length}}_updateTextarea(){if(this.cursorOffsetCache={},this.hiddenTextarea){if(!this.inCompositionMode){const t=this.fromGraphemeToStringSelection(this.selectionStart,this.selectionEnd,this._text);this.hiddenTextarea.selectionStart=t.selectionStart,this.hiddenTextarea.selectionEnd=t.selectionEnd}this.updateTextareaPosition()}}updateFromTextArea(){if(!this.hiddenTextarea)return;this.cursorOffsetCache={};const t=this.hiddenTextarea;this.text=t.value,this.set("dirty",!0),this.initDimensions(),this.setCoords();const e=this.fromStringToGraphemeSelection(t.selectionStart,t.selectionEnd,t.value);this.selectionEnd=this.selectionStart=e.selectionEnd,this.inCompositionMode||(this.selectionStart=e.selectionStart),this.updateTextareaPosition()}updateTextareaPosition(){if(this.selectionStart===this.selectionEnd){const t=this._calcTextareaPosition();this.hiddenTextarea.style.left=t.left,this.hiddenTextarea.style.top=t.top}}_calcTextareaPosition(){if(!this.canvas)return{left:"1px",top:"1px"};const t=this.inCompositionMode?this.compositionStart:this.selectionStart,e=this._getCursorBoundaries(t),s=this.get2DCursorLocation(t),i=s.lineIndex,r=s.charIndex,n=this.getValueOfPropertyAt(i,r,"fontSize")*this.lineHeight,o=e.leftOffset,h=this.getCanvasRetinaScaling(),l=this.canvas.upperCanvasEl,c=l.width/h,g=l.height/h,u=c-n,d=g-n,f=new y(e.left+o,e.top+e.topOffset+n).transform(this.calcTransformMatrix()).transform(this.canvas.viewportTransform).multiply(new y(l.clientWidth/c,l.clientHeight/g));return f.x<0&&(f.x=0),f.x>u&&(f.x=u),f.y<0&&(f.y=0),f.y>d&&(f.y=d),f.x+=this.canvas._offset.left,f.y+=this.canvas._offset.top,{left:"".concat(f.x,"px"),top:"".concat(f.y,"px"),fontSize:"".concat(n,"px"),charHeight:n}}_saveEditingProps(){this._savedProps={hasControls:this.hasControls,borderColor:this.borderColor,lockMovementX:this.lockMovementX,lockMovementY:this.lockMovementY,hoverCursor:this.hoverCursor,selectable:this.selectable,defaultCursor:this.canvas&&this.canvas.defaultCursor,moveCursor:this.canvas&&this.canvas.moveCursor}}_restoreEditingProps(){this._savedProps&&(this.hoverCursor=this._savedProps.hoverCursor,this.hasControls=this._savedProps.hasControls,this.borderColor=this._savedProps.borderColor,this.selectable=this._savedProps.selectable,this.lockMovementX=this._savedProps.lockMovementX,this.lockMovementY=this._savedProps.lockMovementY,this.canvas&&(this.canvas.defaultCursor=this._savedProps.defaultCursor||this.canvas.defaultCursor,this.canvas.moveCursor=this._savedProps.moveCursor||this.canvas.moveCursor),delete this._savedProps)}_exitEditing(){const t=this.hiddenTextarea;this.selected=!1,this.isEditing=!1,t&&(t.blur&&t.blur(),t.parentNode&&t.parentNode.removeChild(t)),this.hiddenTextarea=null,this.abortCursorAnimation(),this.selectionStart!==this.selectionEnd&&this.clearContextTop()}exitEditingImpl(){this._exitEditing(),this.selectionEnd=this.selectionStart,this._restoreEditingProps(),this._forceClearCache&&(this.initDimensions(),this.setCoords())}exitEditing(){const t=this._textBeforeEdit!==this.text;return this.exitEditingImpl(),this.fire("editing:exited"),t&&this.fire(ks),this.canvas&&(this.canvas.fire("text:editing:exited",{target:this}),t&&this.canvas.fire("object:modified",{target:this})),this}_removeExtraneousStyles(){for(const t in this.styles)this._textLines[t]||delete this.styles[t]}removeStyleFromTo(t,e){const{lineIndex:s,charIndex:i}=this.get2DCursorLocation(t,!0),{lineIndex:r,charIndex:n}=this.get2DCursorLocation(e,!0);if(s!==r){if(this.styles[s])for(let o=i;o<this._unwrappedTextLines[s].length;o++)delete this.styles[s][o];if(this.styles[r])for(let o=n;o<this._unwrappedTextLines[r].length;o++){const h=this.styles[r][o];h&&(this.styles[s]||(this.styles[s]={}),this.styles[s][i+o-n]=h)}for(let o=s+1;o<=r;o++)delete this.styles[o];this.shiftLineStyles(r,s-r)}else if(this.styles[s]){const o=this.styles[s],h=n-i;for(let l=i;l<n;l++)delete o[l];for(const l in this.styles[s]){const c=parseInt(l,10);c>=n&&(o[c-h]=o[l],delete o[l])}}}shiftLineStyles(t,e){const s=Object.assign({},this.styles);for(const i in this.styles){const r=parseInt(i,10);r>t&&(this.styles[r+e]=s[r],s[r-e]||delete this.styles[r])}}insertNewlineStyleObject(t,e,s,i){const r={},n=this._unwrappedTextLines[t].length,o=n===e;let h=!1;s||(s=1),this.shiftLineStyles(t,s);const l=this.styles[t]?this.styles[t][e===0?e:e-1]:void 0;for(const g in this.styles[t]){const u=parseInt(g,10);u>=e&&(h=!0,r[u-e]=this.styles[t][g],o&&e===0||delete this.styles[t][g])}let c=!1;for(h&&!o&&(this.styles[t+s]=r,c=!0),(c||n>e)&&s--;s>0;)i&&i[s-1]?this.styles[t+s]={0:m({},i[s-1])}:l?this.styles[t+s]={0:m({},l)}:delete this.styles[t+s],s--;this._forceClearCache=!0}insertCharStyleObject(t,e,s,i){this.styles||(this.styles={});const r=this.styles[t],n=r?m({},r):{};s||(s=1);for(const h in n){const l=parseInt(h,10);l>=e&&(r[l+s]=n[l],n[l-s]||delete r[l])}if(this._forceClearCache=!0,i){for(;s--;)Object.keys(i[s]).length&&(this.styles[t]||(this.styles[t]={}),this.styles[t][e+s]=m({},i[s]));return}if(!r)return;const o=r[e?e-1:1];for(;o&&s--;)this.styles[t][e+s]=m({},o)}insertNewStyleBlock(t,e,s){const i=this.get2DCursorLocation(e,!0),r=[0];let n,o=0;for(let h=0;h<t.length;h++)t[h]===`
`?(o++,r[o]=0):r[o]++;for(r[0]>0&&(this.insertCharStyleObject(i.lineIndex,i.charIndex,r[0],s),s=s&&s.slice(r[0]+1)),o&&this.insertNewlineStyleObject(i.lineIndex,i.charIndex+r[0],o),n=1;n<o;n++)r[n]>0?this.insertCharStyleObject(i.lineIndex+n,0,r[n],s):s&&this.styles[i.lineIndex+n]&&s[0]&&(this.styles[i.lineIndex+n][0]=s[0]),s=s&&s.slice(r[n]+1);r[n]>0&&this.insertCharStyleObject(i.lineIndex+n,0,r[n],s)}removeChars(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t+1;this.removeStyleFromTo(t,e),this._text.splice(t,e-t),this.text=this._text.join(""),this.set("dirty",!0),this.initDimensions(),this.setCoords(),this._removeExtraneousStyles()}insertChars(t,e,s){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:s;i>s&&this.removeStyleFromTo(s,i);const r=this.graphemeSplit(t);this.insertNewStyleBlock(r,s,e),this._text=[...this._text.slice(0,s),...r,...this._text.slice(i)],this.text=this._text.join(""),this.set("dirty",!0),this.initDimensions(),this.setCoords(),this._removeExtraneousStyles()}setSelectionStartEndWithShift(t,e,s){s<=t?(e===t?this._selectionDirection=X:this._selectionDirection===Z&&(this._selectionDirection=X,this.selectionEnd=t),this.selectionStart=s):s>t&&s<e?this._selectionDirection===Z?this.selectionEnd=s:this.selectionStart=s:(e===t?this._selectionDirection=Z:this._selectionDirection===X&&(this._selectionDirection=Z,this.selectionStart=e),this.selectionEnd=s)}}class dh extends gh{initHiddenTextarea(){const t=this.canvas&&Pt(this.canvas.getElement())||Oe(),e=t.createElement("textarea");Object.entries({autocapitalize:"off",autocorrect:"off",autocomplete:"off",spellcheck:"false","data-fabric":"textarea",wrap:"off"}).map((n=>{let[o,h]=n;return e.setAttribute(o,h)}));const{top:s,left:i,fontSize:r}=this._calcTextareaPosition();e.style.cssText="position: absolute; top: ".concat(s,"; left: ").concat(i,"; z-index: -999; opacity: 0; width: 1px; height: 1px; font-size: 1px; padding-top: ").concat(r,";"),(this.hiddenTextareaContainer||t.body).appendChild(e),Object.entries({blur:"blur",keydown:"onKeyDown",keyup:"onKeyUp",input:"onInput",copy:"copy",cut:"copy",paste:"paste",compositionstart:"onCompositionStart",compositionupdate:"onCompositionUpdate",compositionend:"onCompositionEnd"}).map((n=>{let[o,h]=n;return e.addEventListener(o,this[h].bind(this))})),this.hiddenTextarea=e}blur(){this.abortCursorAnimation()}onKeyDown(t){if(!this.isEditing)return;const e=this.direction==="rtl"?this.keysMapRtl:this.keysMap;if(t.keyCode in e)this[e[t.keyCode]](t);else{if(!(t.keyCode in this.ctrlKeysMapDown)||!t.ctrlKey&&!t.metaKey)return;this[this.ctrlKeysMapDown[t.keyCode]](t)}t.stopImmediatePropagation(),t.preventDefault(),t.keyCode>=33&&t.keyCode<=40?(this.inCompositionMode=!1,this.clearContextTop(),this.renderCursorOrSelection()):this.canvas&&this.canvas.requestRenderAll()}onKeyUp(t){!this.isEditing||this._copyDone||this.inCompositionMode?this._copyDone=!1:t.keyCode in this.ctrlKeysMapUp&&(t.ctrlKey||t.metaKey)&&(this[this.ctrlKeysMapUp[t.keyCode]](t),t.stopImmediatePropagation(),t.preventDefault(),this.canvas&&this.canvas.requestRenderAll())}onInput(t){const e=this.fromPaste,{value:s,selectionStart:i,selectionEnd:r}=this.hiddenTextarea;if(this.fromPaste=!1,t&&t.stopPropagation(),!this.isEditing)return;const n=()=>{this.updateFromTextArea(),this.fire(Ds),this.canvas&&(this.canvas.fire("text:changed",{target:this}),this.canvas.requestRenderAll())};if(this.hiddenTextarea.value==="")return this.styles={},void n();const o=this._splitTextIntoLines(s).graphemeText,h=this._text.length,l=o.length,c=this.selectionStart,g=this.selectionEnd,u=c!==g;let d,f,v,_,b=l-h;const C=this.fromStringToGraphemeSelection(i,r,s),O=c>C.selectionStart;u?(f=this._text.slice(c,g),b+=g-c):l<h&&(f=O?this._text.slice(g+b,g):this._text.slice(c,c-b));const w=o.slice(C.selectionEnd-b,C.selectionEnd);if(f&&f.length&&(w.length&&(d=this.getSelectionStyles(c,c+1,!1),d=w.map((()=>d[0]))),u?(v=c,_=g):O?(v=g-f.length,_=g):(v=g,_=g+f.length),this.removeStyleFromTo(v,_)),w.length){const{copyPasteData:S}=Bt();e&&w.join("")===S.copiedText&&!B.disableStyleCopyPaste&&(d=S.copiedTextStyle),this.insertNewStyleBlock(w,c,d)}n()}onCompositionStart(){this.inCompositionMode=!0}onCompositionEnd(){this.inCompositionMode=!1}onCompositionUpdate(t){let{target:e}=t;const{selectionStart:s,selectionEnd:i}=e;this.compositionStart=s,this.compositionEnd=i,this.updateTextareaPosition()}copy(){if(this.selectionStart===this.selectionEnd)return;const{copyPasteData:t}=Bt();t.copiedText=this.getSelectedText(),B.disableStyleCopyPaste?t.copiedTextStyle=void 0:t.copiedTextStyle=this.getSelectionStyles(this.selectionStart,this.selectionEnd,!0),this._copyDone=!0}paste(){this.fromPaste=!0}_getWidthBeforeCursor(t,e){let s,i=this._getLineLeftOffset(t);return e>0&&(s=this.__charBounds[t][e-1],i+=s.left+s.width),i}getDownCursorOffset(t,e){const s=this._getSelectionForOffset(t,e),i=this.get2DCursorLocation(s),r=i.lineIndex;if(r===this._textLines.length-1||t.metaKey||t.keyCode===34)return this._text.length-s;const n=i.charIndex,o=this._getWidthBeforeCursor(r,n),h=this._getIndexOnLine(r+1,o);return this._textLines[r].slice(n).length+h+1+this.missingNewlineOffset(r)}_getSelectionForOffset(t,e){return t.shiftKey&&this.selectionStart!==this.selectionEnd&&e?this.selectionEnd:this.selectionStart}getUpCursorOffset(t,e){const s=this._getSelectionForOffset(t,e),i=this.get2DCursorLocation(s),r=i.lineIndex;if(r===0||t.metaKey||t.keyCode===33)return-s;const n=i.charIndex,o=this._getWidthBeforeCursor(r,n),h=this._getIndexOnLine(r-1,o),l=this._textLines[r].slice(0,n),c=this.missingNewlineOffset(r-1);return-this._textLines[r-1].length+h-l.length+(1-c)}_getIndexOnLine(t,e){const s=this._textLines[t];let i,r,n=this._getLineLeftOffset(t),o=0;for(let h=0,l=s.length;h<l;h++)if(i=this.__charBounds[t][h].width,n+=i,n>e){r=!0;const c=n-i,g=n,u=Math.abs(c-e);o=Math.abs(g-e)<u?h:h-1;break}return r||(o=s.length-1),o}moveCursorDown(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorUpOrDown("Down",t)}moveCursorUp(t){this.selectionStart===0&&this.selectionEnd===0||this._moveCursorUpOrDown("Up",t)}_moveCursorUpOrDown(t,e){const s=this["get".concat(t,"CursorOffset")](e,this._selectionDirection===Z);if(e.shiftKey?this.moveCursorWithShift(s):this.moveCursorWithoutShift(s),s!==0){const i=this.text.length;this.selectionStart=Te(0,this.selectionStart,i),this.selectionEnd=Te(0,this.selectionEnd,i),this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea()}}moveCursorWithShift(t){const e=this._selectionDirection===X?this.selectionStart+t:this.selectionEnd+t;return this.setSelectionStartEndWithShift(this.selectionStart,this.selectionEnd,e),t!==0}moveCursorWithoutShift(t){return t<0?(this.selectionStart+=t,this.selectionEnd=this.selectionStart):(this.selectionEnd+=t,this.selectionStart=this.selectionEnd),t!==0}moveCursorLeft(t){this.selectionStart===0&&this.selectionEnd===0||this._moveCursorLeftOrRight("Left",t)}_move(t,e,s){let i;if(t.altKey)i=this["findWordBoundary".concat(s)](this[e]);else{if(!t.metaKey&&t.keyCode!==35&&t.keyCode!==36)return this[e]+=s==="Left"?-1:1,!0;i=this["findLineBoundary".concat(s)](this[e])}return i!==void 0&&this[e]!==i&&(this[e]=i,!0)}_moveLeft(t,e){return this._move(t,e,"Left")}_moveRight(t,e){return this._move(t,e,"Right")}moveCursorLeftWithoutShift(t){let e=!0;return this._selectionDirection=X,this.selectionEnd===this.selectionStart&&this.selectionStart!==0&&(e=this._moveLeft(t,"selectionStart")),this.selectionEnd=this.selectionStart,e}moveCursorLeftWithShift(t){return this._selectionDirection===Z&&this.selectionStart!==this.selectionEnd?this._moveLeft(t,"selectionEnd"):this.selectionStart!==0?(this._selectionDirection=X,this._moveLeft(t,"selectionStart")):void 0}moveCursorRight(t){this.selectionStart>=this._text.length&&this.selectionEnd>=this._text.length||this._moveCursorLeftOrRight("Right",t)}_moveCursorLeftOrRight(t,e){const s="moveCursor".concat(t).concat(e.shiftKey?"WithShift":"WithoutShift");this._currentCursorOpacity=1,this[s](e)&&(this.abortCursorAnimation(),this.initDelayedCursor(),this._fireSelectionChanged(),this._updateTextarea())}moveCursorRightWithShift(t){return this._selectionDirection===X&&this.selectionStart!==this.selectionEnd?this._moveRight(t,"selectionStart"):this.selectionEnd!==this._text.length?(this._selectionDirection=Z,this._moveRight(t,"selectionEnd")):void 0}moveCursorRightWithoutShift(t){let e=!0;return this._selectionDirection=Z,this.selectionStart===this.selectionEnd?(e=this._moveRight(t,"selectionStart"),this.selectionEnd=this.selectionStart):this.selectionStart=this.selectionEnd,e}}const wr=a=>!!a.button;class fh extends dh{constructor(){super(...arguments),p(this,"draggableTextDelegate",void 0)}initBehavior(){this.on("mousedown",this._mouseDownHandler),this.on("mouseup",this.mouseUpHandler),this.on("mousedblclick",this.doubleClickHandler),this.on("mousetripleclick",this.tripleClickHandler),this.draggableTextDelegate=new uh(this),super.initBehavior()}shouldStartDragging(){return this.draggableTextDelegate.isActive()}onDragStart(t){return this.draggableTextDelegate.onDragStart(t)}canDrop(t){return this.draggableTextDelegate.canDrop(t)}doubleClickHandler(t){this.isEditing&&(this.selectWord(this.getSelectionStartFromPointer(t.e)),this.renderCursorOrSelection())}tripleClickHandler(t){this.isEditing&&(this.selectLine(this.getSelectionStartFromPointer(t.e)),this.renderCursorOrSelection())}_mouseDownHandler(t){let{e,alreadySelected:s}=t;this.canvas&&this.editable&&!wr(e)&&!this.getActiveControl()&&(this.draggableTextDelegate.start(e)||(this.canvas.textEditingManager.register(this),s&&(this.inCompositionMode=!1,this.setCursorByClick(e)),this.isEditing&&(this.__selectionStartOnMouseDown=this.selectionStart,this.selectionStart===this.selectionEnd&&this.abortCursorAnimation(),this.renderCursorOrSelection()),this.selected||(this.selected=s||this.isEditing)))}mouseUpHandler(t){let{e,transform:s}=t;const i=this.draggableTextDelegate.end(e);if(this.canvas){this.canvas.textEditingManager.unregister(this);const r=this.canvas._activeObject;if(r&&r!==this)return}!this.editable||this.group&&!this.group.interactive||s&&s.actionPerformed||wr(e)||i||this.selected&&!this.getActiveControl()&&(this.enterEditing(e),this.selectionStart===this.selectionEnd?this.initDelayedCursor(!0):this.renderCursorOrSelection())}setCursorByClick(t){const e=this.getSelectionStartFromPointer(t),s=this.selectionStart,i=this.selectionEnd;t.shiftKey?this.setSelectionStartEndWithShift(s,i,e):(this.selectionStart=e,this.selectionEnd=e),this.isEditing&&(this._fireSelectionChanged(),this._updateTextarea())}getSelectionStartFromPointer(t){const e=this.canvas.getScenePoint(t).transform(jt(this.calcTransformMatrix())).add(new y(-this._getLeftOffset(),-this._getTopOffset()));let s=0,i=0,r=0;for(let l=0;l<this._textLines.length&&s<=e.y;l++)s+=this.getHeightOfLine(l),r=l,l>0&&(i+=this._textLines[l-1].length+this.missingNewlineOffset(l-1));let n=Math.abs(this._getLineLeftOffset(r));const o=this._textLines[r].length,h=this.__charBounds[r];for(let l=0;l<o;l++){const c=n+h[l].kernedWidth;if(e.x<=c){Math.abs(e.x-c)<=Math.abs(e.x-n)&&i++;break}n=c,i++}return Math.min(this.flipX?o-i:i,this._text.length)}}const es="moveCursorUp",ss="moveCursorDown",is="moveCursorLeft",rs="moveCursorRight",ns="exitEditing",Tr=(a,t)=>{const e=t.getRetinaScaling();a.setTransform(e,0,0,e,0,0);const s=t.viewportTransform;a.transform(s[0],s[1],s[2],s[3],s[4],s[5])},ph=m({selectionStart:0,selectionEnd:0,selectionColor:"rgba(17,119,255,0.3)",isEditing:!1,editable:!0,editingBorderColor:"rgba(102,153,255,0.25)",cursorWidth:2,cursorColor:"",cursorDelay:1e3,cursorDuration:600,caching:!0,hiddenTextareaContainer:null,keysMap:{9:ns,27:ns,33:es,34:ss,35:rs,36:is,37:is,38:es,39:rs,40:ss},keysMapRtl:{9:ns,27:ns,33:es,34:ss,35:is,36:rs,37:rs,38:es,39:is,40:ss},ctrlKeysMapDown:{65:"cmdAll"},ctrlKeysMapUp:{67:"copy",88:"cut"}},{_selectionDirection:null,_reSpace:/\s|\r?\n/,inCompositionMode:!1});class Gt extends fh{static getDefaults(){return m(m({},super.getDefaults()),Gt.ownDefaults)}get type(){const t=super.type;return t==="itext"?"i-text":t}constructor(t,e){super(t,m(m({},Gt.ownDefaults),e)),this.initBehavior()}_set(t,e){return this.isEditing&&this._savedProps&&t in this._savedProps?(this._savedProps[t]=e,this):(t==="canvas"&&(this.canvas instanceof _i&&this.canvas.textEditingManager.remove(this),e instanceof _i&&e.textEditingManager.add(this)),super._set(t,e))}setSelectionStart(t){t=Math.max(t,0),this._updateAndFire("selectionStart",t)}setSelectionEnd(t){t=Math.min(t,this.text.length),this._updateAndFire("selectionEnd",t)}_updateAndFire(t,e){this[t]!==e&&(this._fireSelectionChanged(),this[t]=e),this._updateTextarea()}_fireSelectionChanged(){this.fire("selection:changed"),this.canvas&&this.canvas.fire("text:selection:changed",{target:this})}initDimensions(){this.isEditing&&this.initDelayedCursor(),super.initDimensions()}getSelectionStyles(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.selectionStart||0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.selectionEnd,s=arguments.length>2?arguments[2]:void 0;return super.getSelectionStyles(t,e,s)}setSelectionStyles(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.selectionStart||0,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.selectionEnd;return super.setSelectionStyles(t,e,s)}get2DCursorLocation(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.selectionStart,e=arguments.length>1?arguments[1]:void 0;return super.get2DCursorLocation(t,e)}render(t){super.render(t),this.cursorOffsetCache={},this.renderCursorOrSelection()}toCanvasElement(t){const e=this.isEditing;this.isEditing=!1;const s=super.toCanvasElement(t);return this.isEditing=e,s}renderCursorOrSelection(){if(!this.isEditing||!this.canvas)return;const t=this.clearContextTop(!0);if(!t)return;const e=this._getCursorBoundaries(),s=this.findAncestorsWithClipPath(),i=s.length>0;let r,n=t;if(i){r=Tt(t.canvas),n=r.getContext("2d"),Tr(n,this.canvas);const o=this.calcTransformMatrix();n.transform(o[0],o[1],o[2],o[3],o[4],o[5])}if(this.selectionStart!==this.selectionEnd||this.inCompositionMode?this.renderSelection(n,e):this.renderCursor(n,e),i)for(const o of s){const h=o.clipPath,l=Tt(t.canvas),c=l.getContext("2d");if(Tr(c,this.canvas),!h.absolutePositioned){const g=o.calcTransformMatrix();c.transform(g[0],g[1],g[2],g[3],g[4],g[5])}h.transform(c),h.drawObject(c,!0,{}),this.drawClipPathOnCache(n,h,l)}i&&(t.setTransform(1,0,0,1,0,0),t.drawImage(r,0,0)),this.canvas.contextTopDirty=!0,t.restore()}findAncestorsWithClipPath(){const t=[];let e=this;for(;e;)e.clipPath&&t.push(e),e=e.parent;return t}_getCursorBoundaries(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.selectionStart,e=arguments.length>1?arguments[1]:void 0;const s=this._getLeftOffset(),i=this._getTopOffset(),r=this._getCursorBoundariesOffsets(t,e);return{left:s,top:i,leftOffset:r.left,topOffset:r.top}}_getCursorBoundariesOffsets(t,e){return e?this.__getCursorBoundariesOffsets(t):this.cursorOffsetCache&&"top"in this.cursorOffsetCache?this.cursorOffsetCache:this.cursorOffsetCache=this.__getCursorBoundariesOffsets(t)}__getCursorBoundariesOffsets(t){let e=0,s=0;const{charIndex:i,lineIndex:r}=this.get2DCursorLocation(t);for(let l=0;l<r;l++)e+=this.getHeightOfLine(l);const n=this._getLineLeftOffset(r),o=this.__charBounds[r][i];o&&(s=o.left),this.charSpacing!==0&&i===this._textLines[r].length&&(s-=this._getWidthOfCharSpacing());const h={top:e,left:n+(s>0?s:0)};return this.direction==="rtl"&&(this.textAlign===Z||this.textAlign===It||this.textAlign===Re?h.left*=-1:this.textAlign===X||this.textAlign===As?h.left=n-(s>0?s:0):this.textAlign!==F&&this.textAlign!==Be||(h.left=n-(s>0?s:0))),h}renderCursorAt(t){this._renderCursor(this.canvas.contextTop,this._getCursorBoundaries(t,!0),t)}renderCursor(t,e){this._renderCursor(t,e,this.selectionStart)}getCursorRenderingData(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.selectionStart,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this._getCursorBoundaries(t);const s=this.get2DCursorLocation(t),i=s.lineIndex,r=s.charIndex>0?s.charIndex-1:0,n=this.getValueOfPropertyAt(i,r,"fontSize"),o=this.getObjectScaling().x*this.canvas.getZoom(),h=this.cursorWidth/o,l=this.getValueOfPropertyAt(i,r,"deltaY"),c=e.topOffset+(1-this._fontSizeFraction)*this.getHeightOfLine(i)/this.lineHeight-n*(1-this._fontSizeFraction);return{color:this.cursorColor||this.getValueOfPropertyAt(i,r,"fill"),opacity:this._currentCursorOpacity,left:e.left+e.leftOffset-h/2,top:c+e.top+l,width:h,height:n}}_renderCursor(t,e,s){const{color:i,opacity:r,left:n,top:o,width:h,height:l}=this.getCursorRenderingData(s,e);t.fillStyle=i,t.globalAlpha=r,t.fillRect(n,o,h,l)}renderSelection(t,e){const s={selectionStart:this.inCompositionMode?this.hiddenTextarea.selectionStart:this.selectionStart,selectionEnd:this.inCompositionMode?this.hiddenTextarea.selectionEnd:this.selectionEnd};this._renderSelection(t,s,e)}renderDragSourceEffect(){const t=this.draggableTextDelegate.getDragStartSelection();this._renderSelection(this.canvas.contextTop,t,this._getCursorBoundaries(t.selectionStart,!0))}renderDropTargetEffect(t){const e=this.getSelectionStartFromPointer(t);this.renderCursorAt(e)}_renderSelection(t,e,s){const i=e.selectionStart,r=e.selectionEnd,n=this.textAlign.includes(It),o=this.get2DCursorLocation(i),h=this.get2DCursorLocation(r),l=o.lineIndex,c=h.lineIndex,g=o.charIndex<0?0:o.charIndex,u=h.charIndex<0?0:h.charIndex;for(let d=l;d<=c;d++){const f=this._getLineLeftOffset(d)||0;let v=this.getHeightOfLine(d),_=0,b=0,C=0;if(d===l&&(b=this.__charBounds[l][g].left),d>=l&&d<c)C=n&&!this.isEndOfWrapping(d)?this.width:this.getLineWidth(d)||5;else if(d===c)if(u===0)C=this.__charBounds[c][u].left;else{const L=this._getWidthOfCharSpacing();C=this.__charBounds[c][u-1].left+this.__charBounds[c][u-1].width-L}_=v,(this.lineHeight<1||d===c&&this.lineHeight>1)&&(v/=this.lineHeight);let O=s.left+f+b,w=v,S=0;const M=C-b;this.inCompositionMode?(t.fillStyle=this.compositionColor||"black",w=1,S=v):t.fillStyle=this.selectionColor,this.direction==="rtl"&&(this.textAlign===Z||this.textAlign===It||this.textAlign===Re?O=this.width-O-M:this.textAlign===X||this.textAlign===As?O=s.left+f-C:this.textAlign!==F&&this.textAlign!==Be||(O=s.left+f-C)),t.fillRect(O,s.top+s.topOffset+S,M,w),s.topOffset+=_}}getCurrentCharFontSize(){const t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,"fontSize")}getCurrentCharColor(){const t=this._getCurrentCharIndex();return this.getValueOfPropertyAt(t.l,t.c,nt)}_getCurrentCharIndex(){const t=this.get2DCursorLocation(this.selectionStart,!0),e=t.charIndex>0?t.charIndex-1:0;return{l:t.lineIndex,c:e}}dispose(){this.exitEditingImpl(),this.draggableTextDelegate.dispose(),super.dispose()}}p(Gt,"ownDefaults",ph),p(Gt,"type","IText"),D.setClass(Gt),D.setClass(Gt,"i-text");class le extends Gt{static getDefaults(){return m(m({},super.getDefaults()),le.ownDefaults)}constructor(t,e){super(t,m(m({},le.ownDefaults),e))}static createControls(){return{controls:Uo()}}initDimensions(){this.initialized&&(this.isEditing&&this.initDelayedCursor(),this._clearCache(),this.dynamicMinWidth=0,this._styleMap=this._generateStyleMap(this._splitText()),this.dynamicMinWidth>this.width&&this._set("width",this.dynamicMinWidth),this.textAlign.includes(It)&&this.enlargeSpaces(),this.height=this.calcTextHeight())}_generateStyleMap(t){let e=0,s=0,i=0;const r={};for(let n=0;n<t.graphemeLines.length;n++)t.graphemeText[i]===`
`&&n>0?(s=0,i++,e++):!this.splitByGrapheme&&this._reSpaceAndTab.test(t.graphemeText[i])&&n>0&&(s++,i++),r[n]={line:e,offset:s},i+=t.graphemeLines[n].length,s+=t.graphemeLines[n].length;return r}styleHas(t,e){if(this._styleMap&&!this.isWrapping){const s=this._styleMap[e];s&&(e=s.line)}return super.styleHas(t,e)}isEmptyStyles(t){if(!this.styles)return!0;let e,s=0,i=t+1,r=!1;const n=this._styleMap[t],o=this._styleMap[t+1];n&&(t=n.line,s=n.offset),o&&(i=o.line,r=i===t,e=o.offset);const h=t===void 0?this.styles:{line:this.styles[t]};for(const l in h)for(const c in h[l]){const g=parseInt(c,10);if(g>=s&&(!r||g<e))for(const u in h[l][c])return!1}return!0}_getStyleDeclaration(t,e){if(this._styleMap&&!this.isWrapping){const s=this._styleMap[t];if(!s)return{};t=s.line,e=s.offset+e}return super._getStyleDeclaration(t,e)}_setStyleDeclaration(t,e,s){const i=this._styleMap[t];super._setStyleDeclaration(i.line,i.offset+e,s)}_deleteStyleDeclaration(t,e){const s=this._styleMap[t];super._deleteStyleDeclaration(s.line,s.offset+e)}_getLineStyle(t){const e=this._styleMap[t];return!!this.styles[e.line]}_setLineStyle(t){const e=this._styleMap[t];super._setLineStyle(e.line)}_wrapText(t,e){this.isWrapping=!0;const s=this.getGraphemeDataForRender(t),i=[];for(let r=0;r<s.wordsData.length;r++)i.push(...this._wrapLine(r,e,s));return this.isWrapping=!1,i}getGraphemeDataForRender(t){const e=this.splitByGrapheme,s=e?"":" ";let i=0;return{wordsData:t.map(((r,n)=>{let o=0;const h=e?this.graphemeSplit(r):this.wordSplit(r);return h.length===0?[{word:[],width:0}]:h.map((l=>{const c=e?[l]:this.graphemeSplit(l),g=this._measureWord(c,n,o);return i=Math.max(g,i),o+=c.length+s.length,{word:c,width:g}}))})),largestWordWidth:i}}_measureWord(t,e){let s,i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,r=0;for(let n=0,o=t.length;n<o;n++)r+=this._getGraphemeBox(t[n],e,n+i,s,!0).kernedWidth,s=t[n];return r}wordSplit(t){return t.split(this._wordJoiners)}_wrapLine(t,e,s){let{largestWordWidth:i,wordsData:r}=s,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0;const o=this._getWidthOfCharSpacing(),h=this.splitByGrapheme,l=[],c=h?"":" ";let g=0,u=[],d=0,f=0,v=!0;e-=n;const _=Math.max(e,i,this.dynamicMinWidth),b=r[t];let C;for(d=0,C=0;C<b.length;C++){const{word:O,width:w}=b[C];d+=O.length,g+=f+w-o,g>_&&!v?(l.push(u),u=[],g=w,v=!0):g+=o,v||h||u.push(c),u=u.concat(O),f=h?0:this._measureWord([c],t,d),d++,v=!1}return C&&l.push(u),i+n>this.dynamicMinWidth&&(this.dynamicMinWidth=i-o+n),l}isEndOfWrapping(t){return!this._styleMap[t+1]||this._styleMap[t+1].line!==this._styleMap[t].line}missingNewlineOffset(t,e){return this.splitByGrapheme&&!e?this.isEndOfWrapping(t)?1:0:1}_splitTextIntoLines(t){const e=super._splitTextIntoLines(t),s=this._wrapText(e.lines,this.width),i=new Array(s.length);for(let r=0;r<s.length;r++)i[r]=s[r].join("");return e.lines=i,e.graphemeLines=s,e}getMinWidth(){return Math.max(this.minWidth,this.dynamicMinWidth)}_removeExtraneousStyles(){const t=new Map;for(const e in this._styleMap){const s=parseInt(e,10);if(this._textLines[s]){const i=this._styleMap[e].line;t.set("".concat(i),!0)}}for(const e in this.styles)t.has(e)||delete this.styles[e]}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return super.toObject(["minWidth","splitByGrapheme",...t])}}p(le,"type","Textbox"),p(le,"textLayoutProperties",[...Gt.textLayoutProperties,"width"]),p(le,"ownDefaults",{minWidth:20,dynamicMinWidth:2,lockScalingFlip:!0,noScaleCache:!1,_wordJoiners:/[ \t\r]/,splitByGrapheme:!1}),D.setClass(le);class Or extends Gs{shouldPerformLayout(t){return!!t.target.clipPath&&super.shouldPerformLayout(t)}shouldLayoutClipPath(){return!1}calcLayoutResult(t,e){const{target:s}=t,{clipPath:i,group:r}=s;if(!i||!this.shouldPerformLayout(t))return;const{width:n,height:o}=Ut(dn(s,i)),h=new y(n,o);if(i.absolutePositioned)return{center:Ce(i.getRelativeCenterPoint(),void 0,r?r.calcTransformMatrix():void 0),size:h};{const l=i.getRelativeCenterPoint().transform(s.calcOwnMatrix(),!0);if(this.shouldPerformLayout(t)){const{center:c=new y,correction:g=new y}=this.calcBoundingBox(e,t)||{};return{center:c.add(l),correction:g.subtract(l),size:h}}return{center:s.getRelativeCenterPoint().add(l),size:h}}}}p(Or,"type","clip-path"),D.setClass(Or);class Dr extends Gs{getInitialSize(t,e){let{target:s}=t,{size:i}=e;return new y(s.width||i.x,s.height||i.y)}}p(Dr,"type","fixed"),D.setClass(Dr);class mh extends Ge{subscribeTargets(t){const e=t.target;t.targets.reduce(((s,i)=>(i.parent&&s.add(i.parent),s)),new Set).forEach((s=>{s.layoutManager.subscribeTargets({target:s,targets:[e]})}))}unsubscribeTargets(t){const e=t.target,s=e.getObjects();t.targets.reduce(((i,r)=>(r.parent&&i.add(r.parent),i)),new Set).forEach((i=>{!s.some((r=>r.parent===i))&&i.layoutManager.unsubscribeTargets({target:i,targets:[e]})}))}}class ce extends ue{static getDefaults(){return m(m({},super.getDefaults()),ce.ownDefaults)}constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),Object.assign(this,ce.ownDefaults),this.setOptions(e);const{left:s,top:i,layoutManager:r}=e;this.groupInit(t,{left:s,top:i,layoutManager:r??new mh})}_shouldSetNestedCoords(){return!0}__objectSelectionMonitor(){}multiSelectAdd(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];this.multiSelectionStacking==="selection-order"?this.add(...e):e.forEach((i=>{const r=this._objects.findIndex((o=>o.isInFrontOf(i))),n=r===-1?this.size():r;this.insertAt(n,i)}))}canEnterGroup(t){return this.getObjects().some((e=>e.isDescendantOf(t)||t.isDescendantOf(e)))?(se("error","ActiveSelection: circular object trees are not supported, this call has no effect"),!1):super.canEnterGroup(t)}enterGroup(t,e){t.parent&&t.parent===t.group?t.parent._exitGroup(t):t.group&&t.parent!==t.group&&t.group.remove(t),this._enterGroup(t,e)}exitGroup(t,e){this._exitGroup(t,e),t.parent&&t.parent._enterGroup(t,!0)}_onAfterObjectsChange(t,e){super._onAfterObjectsChange(t,e);const s=new Set;e.forEach((i=>{const{parent:r}=i;r&&s.add(r)})),t===Bi?s.forEach((i=>{i._onAfterObjectsChange(Fs,e)})):s.forEach((i=>{i._set("dirty",!0)}))}onDeselect(){return this.removeAll(),!1}toString(){return"#<ActiveSelection: (".concat(this.complexity(),")>")}shouldCache(){return!1}isOnACache(){return!1}_renderControls(t,e,s){t.save(),t.globalAlpha=this.isMoving?this.borderOpacityWhenMoving:1;const i=m(m({hasControls:!1},s),{},{forActiveSelection:!0});for(let r=0;r<this._objects.length;r++)this._objects[r]._renderControls(t,i);super._renderControls(t,e),t.restore()}}p(ce,"type","ActiveSelection"),p(ce,"ownDefaults",{multiSelectionStacking:"canvas-stacking"}),D.setClass(ce),D.setClass(ce,"activeSelection");class vh{constructor(){p(this,"resources",{})}applyFilters(t,e,s,i,r){const n=r.getContext("2d");if(!n)return;n.drawImage(e,0,0,s,i);const o={sourceWidth:s,sourceHeight:i,imageData:n.getImageData(0,0,s,i),originalEl:e,originalImageData:n.getImageData(0,0,s,i),canvasEl:r,ctx:n,filterBackend:this};t.forEach((l=>{l.applyTo(o)}));const{imageData:h}=o;return h.width===s&&h.height===i||(r.width=h.width,r.height=h.height),n.putImageData(h,0,0),o}}class Mn{constructor(){let{tileSize:t=B.textureSize}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};p(this,"aPosition",new Float32Array([0,0,0,1,1,0,1,1])),p(this,"resources",{}),this.tileSize=t,this.setupGLContext(t,t),this.captureGPUInfo()}setupGLContext(t,e){this.dispose(),this.createWebGLCanvas(t,e)}createWebGLCanvas(t,e){const s=Tt({width:t,height:e}),i=s.getContext("webgl",{alpha:!0,premultipliedAlpha:!1,depth:!1,stencil:!1,antialias:!1});i&&(i.clearColor(0,0,0,0),this.canvas=s,this.gl=i)}applyFilters(t,e,s,i,r,n){const o=this.gl,h=r.getContext("2d");if(!o||!h)return;let l;n&&(l=this.getCachedTexture(n,e));const c={originalWidth:e.width||e.naturalWidth||0,originalHeight:e.height||e.naturalHeight||0,sourceWidth:s,sourceHeight:i,destinationWidth:s,destinationHeight:i,context:o,sourceTexture:this.createTexture(o,s,i,l?void 0:e),targetTexture:this.createTexture(o,s,i),originalTexture:l||this.createTexture(o,s,i,l?void 0:e),passes:t.length,webgl:!0,aPosition:this.aPosition,programCache:this.programCache,pass:0,filterBackend:this,targetCanvas:r},g=o.createFramebuffer();return o.bindFramebuffer(o.FRAMEBUFFER,g),t.forEach((u=>{u&&u.applyTo(c)})),(function(u){const d=u.targetCanvas,f=d.width,v=d.height,_=u.destinationWidth,b=u.destinationHeight;f===_&&v===b||(d.width=_,d.height=b)})(c),this.copyGLTo2D(o,c),o.bindTexture(o.TEXTURE_2D,null),o.deleteTexture(c.sourceTexture),o.deleteTexture(c.targetTexture),o.deleteFramebuffer(g),h.setTransform(1,0,0,1,0,0),c}dispose(){this.canvas&&(this.canvas=null,this.gl=null),this.clearWebGLCaches()}clearWebGLCaches(){this.programCache={},this.textureCache={}}createTexture(t,e,s,i,r){const{NEAREST:n,TEXTURE_2D:o,RGBA:h,UNSIGNED_BYTE:l,CLAMP_TO_EDGE:c,TEXTURE_MAG_FILTER:g,TEXTURE_MIN_FILTER:u,TEXTURE_WRAP_S:d,TEXTURE_WRAP_T:f}=t,v=t.createTexture();return t.bindTexture(o,v),t.texParameteri(o,g,r||n),t.texParameteri(o,u,r||n),t.texParameteri(o,d,c),t.texParameteri(o,f,c),i?t.texImage2D(o,0,h,h,l,i):t.texImage2D(o,0,h,e,s,0,h,l,null),v}getCachedTexture(t,e,s){const{textureCache:i}=this;if(i[t])return i[t];{const r=this.createTexture(this.gl,e.width,e.height,e,s);return r&&(i[t]=r),r}}evictCachesForKey(t){this.textureCache[t]&&(this.gl.deleteTexture(this.textureCache[t]),delete this.textureCache[t])}copyGLTo2D(t,e){const s=t.canvas,i=e.targetCanvas,r=i.getContext("2d");if(!r)return;r.translate(0,i.height),r.scale(1,-1);const n=s.height-i.height;r.drawImage(s,0,n,i.width,i.height,0,0,i.width,i.height)}copyGLTo2DPutImageData(t,e){const s=e.targetCanvas.getContext("2d"),i=e.destinationWidth,r=e.destinationHeight,n=i*r*4;if(!s)return;const o=new Uint8Array(this.imageBuffer,0,n),h=new Uint8ClampedArray(this.imageBuffer,0,n);t.readPixels(0,0,i,r,t.RGBA,t.UNSIGNED_BYTE,o);const l=new ImageData(h,i,r);s.putImageData(l,0,0)}captureGPUInfo(){if(this.gpuInfo)return this.gpuInfo;const t=this.gl,e={renderer:"",vendor:""};if(!t)return e;const s=t.getExtension("WEBGL_debug_renderer_info");if(s){const i=t.getParameter(s.UNMASKED_RENDERER_WEBGL),r=t.getParameter(s.UNMASKED_VENDOR_WEBGL);i&&(e.renderer=i.toLowerCase()),r&&(e.vendor=r.toLowerCase())}return this.gpuInfo=e,e}}let ni;function yh(){const{WebGLProbe:a}=Bt();return a.queryWebGL(Kt()),B.enableGLFiltering&&a.isSupported(B.textureSize)?new Mn({tileSize:B.textureSize}):new vh}function oi(){return!ni&&(!(arguments.length>0&&arguments[0]!==void 0)||arguments[0])&&(ni=yh()),ni}const _h=["filters","resizeFilter","src","crossOrigin","type"],En=["cropX","cropY"];class dt extends ut{static getDefaults(){return m(m({},super.getDefaults()),dt.ownDefaults)}constructor(t,e){super(),p(this,"_lastScaleX",1),p(this,"_lastScaleY",1),p(this,"_filterScalingX",1),p(this,"_filterScalingY",1),this.filters=[],Object.assign(this,dt.ownDefaults),this.setOptions(e),this.cacheKey="texture".concat(ie()),this.setElement(typeof t=="string"?(this.canvas&&Pt(this.canvas.getElement())||Oe()).getElementById(t):t,e)}getElement(){return this._element}setElement(t){var e;let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.removeTexture(this.cacheKey),this.removeTexture("".concat(this.cacheKey,"_filtered")),this._element=t,this._originalElement=t,this._setWidthHeight(s),(e=t.classList)===null||e===void 0||e.add(dt.CSS_CANVAS),this.filters.length!==0&&this.applyFilters(),this.resizeFilter&&this.applyResizeFilters()}removeTexture(t){const e=oi(!1);e instanceof Mn&&e.evictCachesForKey(t)}dispose(){super.dispose(),this.removeTexture(this.cacheKey),this.removeTexture("".concat(this.cacheKey,"_filtered")),this._cacheContext=null,["_originalElement","_element","_filteredEl","_cacheCanvas"].forEach((t=>{const e=this[t];e&&Bt().dispose(e),this[t]=void 0}))}getCrossOrigin(){return this._originalElement&&(this._originalElement.crossOrigin||null)}getOriginalSize(){const t=this.getElement();return t?{width:t.naturalWidth||t.width,height:t.naturalHeight||t.height}:{width:0,height:0}}_stroke(t){if(!this.stroke||this.strokeWidth===0)return;const e=this.width/2,s=this.height/2;t.beginPath(),t.moveTo(-e,-s),t.lineTo(e,-s),t.lineTo(e,s),t.lineTo(-e,s),t.lineTo(-e,-s),t.closePath()}toObject(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];const e=[];return this.filters.forEach((s=>{s&&e.push(s.toObject())})),m(m({},super.toObject([...En,...t])),{},{src:this.getSrc(),crossOrigin:this.getCrossOrigin(),filters:e},this.resizeFilter?{resizeFilter:this.resizeFilter.toObject()}:{})}hasCrop(){return!!this.cropX||!!this.cropY||this.width<this._element.width||this.height<this._element.height}_toSVG(){const t=[],e=this._element,s=-this.width/2,i=-this.height/2;let r=[],n=[],o="",h="";if(!e)return[];if(this.hasCrop()){const l=ie();r.push('<clipPath id="imageCrop_'+l+`">
`,'	<rect x="'+s+'" y="'+i+'" width="'+this.width+'" height="'+this.height+`" />
`,`</clipPath>
`),o=' clip-path="url(#imageCrop_'+l+')" '}if(this.imageSmoothing||(h=' image-rendering="optimizeSpeed"'),t.push("	<image ","COMMON_PARTS",'xlink:href="'.concat(this.getSvgSrc(!0),'" x="').concat(s-this.cropX,'" y="').concat(i-this.cropY,'" width="').concat(e.width||e.naturalWidth,'" height="').concat(e.height||e.naturalHeight,'"').concat(h).concat(o,`></image>
`)),this.stroke||this.strokeDashArray){const l=this.fill;this.fill=null,n=['	<rect x="'.concat(s,'" y="').concat(i,'" width="').concat(this.width,'" height="').concat(this.height,'" style="').concat(this.getSvgStyles(),`" />
`)],this.fill=l}return r=this.paintFirst!==nt?r.concat(n,t):r.concat(t,n),r}getSrc(t){const e=t?this._element:this._originalElement;return e?e.toDataURL?e.toDataURL():this.srcFromAttribute?e.getAttribute("src")||"":e.src:this.src||""}getSvgSrc(t){return this.getSrc(t)}setSrc(t){let{crossOrigin:e,signal:s}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return ws(t,{crossOrigin:e,signal:s}).then((i=>{e!==void 0&&this.set({crossOrigin:e}),this.setElement(i)}))}toString(){return'#<Image: { src: "'.concat(this.getSrc(),'" }>')}applyResizeFilters(){const t=this.resizeFilter,e=this.minimumScaleTrigger,s=this.getTotalObjectScaling(),i=s.x,r=s.y,n=this._filteredEl||this._originalElement;if(this.group&&this.set("dirty",!0),!t||i>e&&r>e)return this._element=n,this._filterScalingX=1,this._filterScalingY=1,this._lastScaleX=i,void(this._lastScaleY=r);const o=Tt(n),{width:h,height:l}=n;this._element=o,this._lastScaleX=t.scaleX=i,this._lastScaleY=t.scaleY=r,oi().applyFilters([t],n,h,l,this._element),this._filterScalingX=o.width/this._originalElement.width,this._filterScalingY=o.height/this._originalElement.height}applyFilters(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.filters||[];if(t=t.filter((r=>r&&!r.isNeutralState())),this.set("dirty",!0),this.removeTexture("".concat(this.cacheKey,"_filtered")),t.length===0)return this._element=this._originalElement,this._filteredEl=void 0,this._filterScalingX=1,void(this._filterScalingY=1);const e=this._originalElement,s=e.naturalWidth||e.width,i=e.naturalHeight||e.height;if(this._element===this._originalElement){const r=Tt({width:s,height:i});this._element=r,this._filteredEl=r}else this._filteredEl&&(this._element=this._filteredEl,this._filteredEl.getContext("2d").clearRect(0,0,s,i),this._lastScaleX=1,this._lastScaleY=1);oi().applyFilters(t,this._originalElement,s,i,this._element,this.cacheKey),this._originalElement.width===this._element.width&&this._originalElement.height===this._element.height||(this._filterScalingX=this._element.width/this._originalElement.width,this._filterScalingY=this._element.height/this._originalElement.height)}_render(t){t.imageSmoothingEnabled=this.imageSmoothing,this.isMoving!==!0&&this.resizeFilter&&this._needsResize()&&this.applyResizeFilters(),this._stroke(t),this._renderPaintInOrder(t)}drawCacheOnCanvas(t){t.imageSmoothingEnabled=this.imageSmoothing,super.drawCacheOnCanvas(t)}shouldCache(){return this.needsItsOwnCache()}_renderFill(t){const e=this._element;if(!e)return;const s=this._filterScalingX,i=this._filterScalingY,r=this.width,n=this.height,o=Math.max(this.cropX,0),h=Math.max(this.cropY,0),l=e.naturalWidth||e.width,c=e.naturalHeight||e.height,g=o*s,u=h*i,d=Math.min(r*s,l-g),f=Math.min(n*i,c-u),v=-r/2,_=-n/2,b=Math.min(r,l/s-o),C=Math.min(n,c/i-h);e&&t.drawImage(e,g,u,d,f,v,_,b,C)}_needsResize(){const t=this.getTotalObjectScaling();return t.x!==this._lastScaleX||t.y!==this._lastScaleY}_resetWidthHeight(){this.set(this.getOriginalSize())}_setWidthHeight(){let{width:t,height:e}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const s=this.getOriginalSize();this.width=t||s.width,this.height=e||s.height}parsePreserveAspectRatioAttribute(){const t=lo(this.preserveAspectRatio||""),e=this.width,s=this.height,i={width:e,height:s};let r,n=this._element.width,o=this._element.height,h=1,l=1,c=0,g=0,u=0,d=0;return!t||t.alignX===yt&&t.alignY===yt?(h=e/n,l=s/o):(t.meetOrSlice==="meet"&&(h=l=Oa(this._element,i),r=(e-n*h)/2,t.alignX==="Min"&&(c=-r),t.alignX==="Max"&&(c=r),r=(s-o*l)/2,t.alignY==="Min"&&(g=-r),t.alignY==="Max"&&(g=r)),t.meetOrSlice==="slice"&&(h=l=Da(this._element,i),r=n-e/h,t.alignX==="Mid"&&(u=r/2),t.alignX==="Max"&&(u=r),r=o-s/l,t.alignY==="Mid"&&(d=r/2),t.alignY==="Max"&&(d=r),n=e/h,o=s/l)),{width:n,height:o,scaleX:h,scaleY:l,offsetLeft:c,offsetTop:g,cropX:u,cropY:d}}static fromObject(t,e){let{filters:s,resizeFilter:i,src:r,crossOrigin:n,type:o}=t,h=N(t,_h);return Promise.all([ws(r,m(m({},e),{},{crossOrigin:n})),s&&Ye(s,e),i&&Ye([i],e),Ys(h,e)]).then((l=>{let[c,g=[],[u]=[],d={}]=l;return new this(c,m(m({},h),{},{src:r,filters:g,resizeFilter:u},d))}))}static fromURL(t){let{crossOrigin:e=null,signal:s}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2?arguments[2]:void 0;return ws(t,{crossOrigin:e,signal:s}).then((r=>new this(r,i)))}static async fromElement(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0;const i=Qt(t,this.ATTRIBUTE_NAMES,s);return this.fromURL(i["xlink:href"]||i.href,e,i).catch((r=>(se("log","Unable to parse Image",r),null)))}}p(dt,"type","Image"),p(dt,"cacheProperties",[...Zt,...En]),p(dt,"ownDefaults",{strokeWidth:0,srcFromAttribute:!1,minimumScaleTrigger:.5,cropX:0,cropY:0,imageSmoothing:!0}),p(dt,"CSS_CANVAS","canvas-img"),p(dt,"ATTRIBUTE_NAMES",[...re,"x","y","width","height","preserveAspectRatio","xlink:href","href","crossOrigin","image-rendering"]),D.setClass(dt),D.setSVGClass(dt);zs(["pattern","defs","symbol","metadata","clipPath","mask","desc"]);const Ns=a=>a.webgl!==void 0,Wi="precision highp float",xh=`
    `.concat(Wi,`;
    varying vec2 vTexCoord;
    uniform sampler2D uTexture;
    void main() {
      gl_FragColor = texture2D(uTexture, vTexCoord);
    }`),bh=["type"],Ch=["type"],Sh=new RegExp(Wi,"g");class ot{get type(){return this.constructor.type}constructor(){let t=N(arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},bh);Object.assign(this,this.constructor.defaults,t)}getFragmentSource(){return xh}getVertexSource(){return`
    attribute vec2 aPosition;
    varying vec2 vTexCoord;
    void main() {
      vTexCoord = aPosition;
      gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);
    }`}createProgram(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getFragmentSource(),s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:this.getVertexSource();const{WebGLProbe:{GLPrecision:i="highp"}}=Bt();i!=="highp"&&(e=e.replace(Sh,Wi.replace("highp",i)));const r=t.createShader(t.VERTEX_SHADER),n=t.createShader(t.FRAGMENT_SHADER),o=t.createProgram();if(!r||!n||!o)throw new Rt("Vertex, fragment shader or program creation error");if(t.shaderSource(r,s),t.compileShader(r),!t.getShaderParameter(r,t.COMPILE_STATUS))throw new Rt("Vertex shader compile error for ".concat(this.type,": ").concat(t.getShaderInfoLog(r)));if(t.shaderSource(n,e),t.compileShader(n),!t.getShaderParameter(n,t.COMPILE_STATUS))throw new Rt("Fragment shader compile error for ".concat(this.type,": ").concat(t.getShaderInfoLog(n)));if(t.attachShader(o,r),t.attachShader(o,n),t.linkProgram(o),!t.getProgramParameter(o,t.LINK_STATUS))throw new Rt('Shader link error for "'.concat(this.type,'" ').concat(t.getProgramInfoLog(o)));const h=this.getUniformLocations(t,o)||{};return h.uStepW=t.getUniformLocation(o,"uStepW"),h.uStepH=t.getUniformLocation(o,"uStepH"),{program:o,attributeLocations:this.getAttributeLocations(t,o),uniformLocations:h}}getAttributeLocations(t,e){return{aPosition:t.getAttribLocation(e,"aPosition")}}getUniformLocations(t,e){const s=this.constructor.uniformLocations,i={};for(let r=0;r<s.length;r++)i[s[r]]=t.getUniformLocation(e,s[r]);return i}sendAttributeData(t,e,s){const i=e.aPosition,r=t.createBuffer();t.bindBuffer(t.ARRAY_BUFFER,r),t.enableVertexAttribArray(i),t.vertexAttribPointer(i,2,t.FLOAT,!1,0,0),t.bufferData(t.ARRAY_BUFFER,s,t.STATIC_DRAW)}_setupFrameBuffer(t){const e=t.context;if(t.passes>1){const s=t.destinationWidth,i=t.destinationHeight;t.sourceWidth===s&&t.sourceHeight===i||(e.deleteTexture(t.targetTexture),t.targetTexture=t.filterBackend.createTexture(e,s,i)),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,t.targetTexture,0)}else e.bindFramebuffer(e.FRAMEBUFFER,null),e.finish()}_swapTextures(t){t.passes--,t.pass++;const e=t.targetTexture;t.targetTexture=t.sourceTexture,t.sourceTexture=e}isNeutralState(t){return!1}applyTo(t){Ns(t)?(this._setupFrameBuffer(t),this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)}applyTo2d(t){}getCacheKey(){return this.type}retrieveShader(t){const e=this.getCacheKey();return t.programCache[e]||(t.programCache[e]=this.createProgram(t.context)),t.programCache[e]}applyToWebGL(t){const e=t.context,s=this.retrieveShader(t);t.pass===0&&t.originalTexture?e.bindTexture(e.TEXTURE_2D,t.originalTexture):e.bindTexture(e.TEXTURE_2D,t.sourceTexture),e.useProgram(s.program),this.sendAttributeData(e,s.attributeLocations,t.aPosition),e.uniform1f(s.uniformLocations.uStepW,1/t.sourceWidth),e.uniform1f(s.uniformLocations.uStepH,1/t.sourceHeight),this.sendUniformData(e,s.uniformLocations),e.viewport(0,0,t.destinationWidth,t.destinationHeight),e.drawArrays(e.TRIANGLE_STRIP,0,4)}bindAdditionalTexture(t,e,s){t.activeTexture(s),t.bindTexture(t.TEXTURE_2D,e),t.activeTexture(t.TEXTURE0)}unbindAdditionalTexture(t,e){t.activeTexture(e),t.bindTexture(t.TEXTURE_2D,null),t.activeTexture(t.TEXTURE0)}sendUniformData(t,e){}createHelpLayer(t){if(!t.helpLayer){const{sourceWidth:e,sourceHeight:s}=t,i=Tt({width:e,height:s});t.helpLayer=i}}toObject(){const t=Object.keys(this.constructor.defaults||{});return m({type:this.type},t.reduce(((e,s)=>(e[s]=this[s],e)),{}))}toJSON(){return this.toObject()}static async fromObject(t,e){return new this(N(t,Ch))}}p(ot,"type","BaseFilter"),p(ot,"uniformLocations",[]);const wh={multiply:`gl_FragColor.rgb *= uColor.rgb;
`,screen:`gl_FragColor.rgb = 1.0 - (1.0 - gl_FragColor.rgb) * (1.0 - uColor.rgb);
`,add:`gl_FragColor.rgb += uColor.rgb;
`,difference:`gl_FragColor.rgb = abs(gl_FragColor.rgb - uColor.rgb);
`,subtract:`gl_FragColor.rgb -= uColor.rgb;
`,lighten:`gl_FragColor.rgb = max(gl_FragColor.rgb, uColor.rgb);
`,darken:`gl_FragColor.rgb = min(gl_FragColor.rgb, uColor.rgb);
`,exclusion:`gl_FragColor.rgb += uColor.rgb - 2.0 * (uColor.rgb * gl_FragColor.rgb);
`,overlay:`
    if (uColor.r < 0.5) {
      gl_FragColor.r *= 2.0 * uColor.r;
    } else {
      gl_FragColor.r = 1.0 - 2.0 * (1.0 - gl_FragColor.r) * (1.0 - uColor.r);
    }
    if (uColor.g < 0.5) {
      gl_FragColor.g *= 2.0 * uColor.g;
    } else {
      gl_FragColor.g = 1.0 - 2.0 * (1.0 - gl_FragColor.g) * (1.0 - uColor.g);
    }
    if (uColor.b < 0.5) {
      gl_FragColor.b *= 2.0 * uColor.b;
    } else {
      gl_FragColor.b = 1.0 - 2.0 * (1.0 - gl_FragColor.b) * (1.0 - uColor.b);
    }
    `,tint:`
    gl_FragColor.rgb *= (1.0 - uColor.a);
    gl_FragColor.rgb += uColor.rgb;
    `};class os extends ot{getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return`
      precision highp float;
      uniform sampler2D uTexture;
      uniform vec4 uColor;
      varying vec2 vTexCoord;
      void main() {
        vec4 color = texture2D(uTexture, vTexCoord);
        gl_FragColor = color;
        if (color.a > 0.0) {
          `.concat(wh[this.mode],`
        }
      }
      `)}applyTo2d(t){let{imageData:{data:e}}=t;const s=new z(this.color).getSource(),i=this.alpha,r=s[0]*i,n=s[1]*i,o=s[2]*i,h=1-i;for(let l=0;l<e.length;l+=4){const c=e[l],g=e[l+1],u=e[l+2];let d,f,v;switch(this.mode){case"multiply":d=c*r/255,f=g*n/255,v=u*o/255;break;case"screen":d=255-(255-c)*(255-r)/255,f=255-(255-g)*(255-n)/255,v=255-(255-u)*(255-o)/255;break;case"add":d=c+r,f=g+n,v=u+o;break;case"difference":d=Math.abs(c-r),f=Math.abs(g-n),v=Math.abs(u-o);break;case"subtract":d=c-r,f=g-n,v=u-o;break;case"darken":d=Math.min(c,r),f=Math.min(g,n),v=Math.min(u,o);break;case"lighten":d=Math.max(c,r),f=Math.max(g,n),v=Math.max(u,o);break;case"overlay":d=r<128?2*c*r/255:255-2*(255-c)*(255-r)/255,f=n<128?2*g*n/255:255-2*(255-g)*(255-n)/255,v=o<128?2*u*o/255:255-2*(255-u)*(255-o)/255;break;case"exclusion":d=r+c-2*r*c/255,f=n+g-2*n*g/255,v=o+u-2*o*u/255;break;case"tint":d=r+c*h,f=n+g*h,v=o+u*h}e[l]=d,e[l+1]=f,e[l+2]=v}}sendUniformData(t,e){const s=new z(this.color).getSource();s[0]=this.alpha*s[0]/255,s[1]=this.alpha*s[1]/255,s[2]=this.alpha*s[2]/255,s[3]=this.alpha,t.uniform4fv(e.uColor,s)}}p(os,"defaults",{color:"#F95C63",mode:"multiply",alpha:1}),p(os,"type","BlendColor"),p(os,"uniformLocations",["uColor"]),D.setClass(os);const Th={multiply:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform sampler2D uImage;
    uniform vec4 uColor;
    varying vec2 vTexCoord;
    varying vec2 vTexCoord2;
    void main() {
      vec4 color = texture2D(uTexture, vTexCoord);
      vec4 color2 = texture2D(uImage, vTexCoord2);
      color.rgba *= color2.rgba;
      gl_FragColor = color;
    }
    `,mask:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform sampler2D uImage;
    uniform vec4 uColor;
    varying vec2 vTexCoord;
    varying vec2 vTexCoord2;
    void main() {
      vec4 color = texture2D(uTexture, vTexCoord);
      vec4 color2 = texture2D(uImage, vTexCoord2);
      color.a = color2.a;
      gl_FragColor = color;
    }
    `},Oh=["type","image"];class as extends ot{getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return Th[this.mode]}getVertexSource(){return`
    attribute vec2 aPosition;
    varying vec2 vTexCoord;
    varying vec2 vTexCoord2;
    uniform mat3 uTransformMatrix;
    void main() {
      vTexCoord = aPosition;
      vTexCoord2 = (uTransformMatrix * vec3(aPosition, 1.0)).xy;
      gl_Position = vec4(aPosition * 2.0 - 1.0, 0.0, 1.0);
    }
    `}applyToWebGL(t){const e=t.context,s=this.createTexture(t.filterBackend,this.image);this.bindAdditionalTexture(e,s,e.TEXTURE1),super.applyToWebGL(t),this.unbindAdditionalTexture(e,e.TEXTURE1)}createTexture(t,e){return t.getCachedTexture(e.cacheKey,e.getElement())}calculateMatrix(){const t=this.image,{width:e,height:s}=t.getElement();return[1/t.scaleX,0,0,0,1/t.scaleY,0,-t.left/e,-t.top/s,1]}applyTo2d(t){let{imageData:{data:e,width:s,height:i},filterBackend:{resources:r}}=t;const n=this.image;r.blendImage||(r.blendImage=Kt());const o=r.blendImage,h=o.getContext("2d");o.width!==s||o.height!==i?(o.width=s,o.height=i):h.clearRect(0,0,s,i),h.setTransform(n.scaleX,0,0,n.scaleY,n.left,n.top),h.drawImage(n.getElement(),0,0,s,i);const l=h.getImageData(0,0,s,i).data;for(let c=0;c<e.length;c+=4){const g=e[c],u=e[c+1],d=e[c+2],f=e[c+3],v=l[c],_=l[c+1],b=l[c+2],C=l[c+3];switch(this.mode){case"multiply":e[c]=g*v/255,e[c+1]=u*_/255,e[c+2]=d*b/255,e[c+3]=f*C/255;break;case"mask":e[c+3]=C}}}sendUniformData(t,e){const s=this.calculateMatrix();t.uniform1i(e.uImage,1),t.uniformMatrix3fv(e.uTransformMatrix,!1,s)}toObject(){return m(m({},super.toObject()),{},{image:this.image&&this.image.toObject()})}static async fromObject(t,e){let{type:s,image:i}=t,r=N(t,Oh);return dt.fromObject(i,e).then((n=>new this(m(m({},r),{},{image:n}))))}}p(as,"type","BlendImage"),p(as,"defaults",{mode:"multiply",alpha:1}),p(as,"uniformLocations",["uTransformMatrix","uImage"]),D.setClass(as);class hs extends ot{getFragmentSource(){return`
    precision highp float;
    uniform sampler2D uTexture;
    uniform vec2 uDelta;
    varying vec2 vTexCoord;
    const float nSamples = 15.0;
    vec3 v3offset = vec3(12.9898, 78.233, 151.7182);
    float random(vec3 scale) {
      /* use the fragment position for a different seed per-pixel */
      return fract(sin(dot(gl_FragCoord.xyz, scale)) * 43758.5453);
    }
    void main() {
      vec4 color = vec4(0.0);
      float totalC = 0.0;
      float totalA = 0.0;
      float offset = random(v3offset);
      for (float t = -nSamples; t <= nSamples; t++) {
        float percent = (t + offset - 0.5) / nSamples;
        vec4 sample = texture2D(uTexture, vTexCoord + uDelta * percent);
        float weight = 1.0 - abs(percent);
        float alpha = weight * sample.a;
        color.rgb += sample.rgb * alpha;
        color.a += alpha;
        totalA += weight;
        totalC += alpha;
      }
      gl_FragColor.rgb = color.rgb / totalC;
      gl_FragColor.a = color.a / totalA;
    }
  `}applyTo(t){Ns(t)?(this.aspectRatio=t.sourceWidth/t.sourceHeight,t.passes++,this._setupFrameBuffer(t),this.horizontal=!0,this.applyToWebGL(t),this._swapTextures(t),this._setupFrameBuffer(t),this.horizontal=!1,this.applyToWebGL(t),this._swapTextures(t)):this.applyTo2d(t)}applyTo2d(t){let{imageData:{data:e,width:s,height:i}}=t;this.aspectRatio=s/i,this.horizontal=!0;let r=this.getBlurValue()*s;const n=new Uint8ClampedArray(e),o=15,h=4*s;for(let l=0;l<e.length;l+=4){let c=0,g=0,u=0,d=0,f=0;const v=l-l%h,_=v+h;for(let b=-14;b<o;b++){const C=b/o,O=4*Math.floor(r*C),w=1-Math.abs(C);let S=l+O;S<v?S=v:S>_&&(S=_);const M=e[S+3]*w;c+=e[S]*M,g+=e[S+1]*M,u+=e[S+2]*M,d+=M,f+=w}n[l]=c/d,n[l+1]=g/d,n[l+2]=u/d,n[l+3]=d/f}this.horizontal=!1,r=this.getBlurValue()*i;for(let l=0;l<n.length;l+=4){let c=0,g=0,u=0,d=0,f=0;const v=l%h,_=n.length-h+v;for(let b=-14;b<o;b++){const C=b/o,O=Math.floor(r*C)*h,w=1-Math.abs(C);let S=l+O;S<v?S=v:S>_&&(S=_);const M=n[S+3]*w;c+=n[S]*M,g+=n[S+1]*M,u+=n[S+2]*M,d+=M,f+=w}e[l]=c/d,e[l+1]=g/d,e[l+2]=u/d,e[l+3]=d/f}}sendUniformData(t,e){const s=this.chooseRightDelta();t.uniform2fv(e.uDelta,s)}isNeutralState(){return this.blur===0}getBlurValue(){let t=1;const{horizontal:e,aspectRatio:s}=this;return e?s>1&&(t=1/s):s<1&&(t=s),t*this.blur*.12}chooseRightDelta(){const t=this.getBlurValue();return this.horizontal?[t,0]:[0,t]}}p(hs,"type","Blur"),p(hs,"defaults",{blur:0}),p(hs,"uniformLocations",["uDelta"]),D.setClass(hs);class ls extends ot{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uBrightness;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    color.rgb += uBrightness;
    gl_FragColor = color;
  }
`}applyTo2d(t){let{imageData:{data:e}}=t;const s=Math.round(255*this.brightness);for(let i=0;i<e.length;i+=4)e[i]+=s,e[i+1]+=s,e[i+2]+=s}isNeutralState(){return this.brightness===0}sendUniformData(t,e){t.uniform1f(e.uBrightness,this.brightness)}}p(ls,"type","Brightness"),p(ls,"defaults",{brightness:0}),p(ls,"uniformLocations",["uBrightness"]),D.setClass(ls);const Pn={matrix:[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],colorsOnly:!0};class be extends ot{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  varying vec2 vTexCoord;
  uniform mat4 uColorMatrix;
  uniform vec4 uConstants;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    color *= uColorMatrix;
    color += uConstants;
    gl_FragColor = color;
  }`}applyTo2d(t){const e=t.imageData.data,s=this.matrix,i=this.colorsOnly;for(let r=0;r<e.length;r+=4){const n=e[r],o=e[r+1],h=e[r+2];if(e[r]=n*s[0]+o*s[1]+h*s[2]+255*s[4],e[r+1]=n*s[5]+o*s[6]+h*s[7]+255*s[9],e[r+2]=n*s[10]+o*s[11]+h*s[12]+255*s[14],!i){const l=e[r+3];e[r]+=l*s[3],e[r+1]+=l*s[8],e[r+2]+=l*s[13],e[r+3]=n*s[15]+o*s[16]+h*s[17]+l*s[18]+255*s[19]}}}sendUniformData(t,e){const s=this.matrix,i=[s[0],s[1],s[2],s[3],s[5],s[6],s[7],s[8],s[10],s[11],s[12],s[13],s[15],s[16],s[17],s[18]],r=[s[4],s[9],s[14],s[19]];t.uniformMatrix4fv(e.uColorMatrix,!1,i),t.uniform4fv(e.uConstants,r)}toObject(){return m(m({},super.toObject()),{},{matrix:[...this.matrix]})}}function de(a,t){var e;const s=(p(e=class extends be{toObject(){return{type:this.type,colorsOnly:this.colorsOnly}}},"type",a),p(e,"defaults",{colorsOnly:!1,matrix:t}),e);return D.setClass(s,a),s}p(be,"type","ColorMatrix"),p(be,"defaults",Pn),p(be,"uniformLocations",["uColorMatrix","uConstants"]),D.setClass(be);de("Brownie",[.5997,.34553,-.27082,0,.186,-.0377,.86095,.15059,0,-.1449,.24113,-.07441,.44972,0,-.02965,0,0,0,1,0]);de("Vintage",[.62793,.32021,-.03965,0,.03784,.02578,.64411,.03259,0,.02926,.0466,-.08512,.52416,0,.02023,0,0,0,1,0]);de("Kodachrome",[1.12855,-.39673,-.03992,0,.24991,-.16404,1.08352,-.05498,0,.09698,-.16786,-.56034,1.60148,0,.13972,0,0,0,1,0]);de("Technicolor",[1.91252,-.85453,-.09155,0,.04624,-.30878,1.76589,-.10601,0,-.27589,-.2311,-.75018,1.84759,0,.12137,0,0,0,1,0]);de("Polaroid",[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0]);de("Sepia",[.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0]);de("BlackWhite",[1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,1.5,1.5,1.5,0,-1,0,0,0,1,0]);class kr extends ot{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};super(t),this.subFilters=t.subFilters||[]}applyTo(t){Ns(t)&&(t.passes+=this.subFilters.length-1),this.subFilters.forEach((e=>{e.applyTo(t)}))}toObject(){return{type:this.type,subFilters:this.subFilters.map((t=>t.toObject()))}}isNeutralState(){return!this.subFilters.some((t=>!t.isNeutralState()))}static fromObject(t,e){return Promise.all((t.subFilters||[]).map((s=>D.getClass(s.type).fromObject(s,e)))).then((s=>new this({subFilters:s})))}}p(kr,"type","Composed"),D.setClass(kr);class cs extends ot{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uContrast;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    float contrastF = 1.015 * (uContrast + 1.0) / (1.0 * (1.015 - uContrast));
    color.rgb = contrastF * (color.rgb - 0.5) + 0.5;
    gl_FragColor = color;
  }`}isNeutralState(){return this.contrast===0}applyTo2d(t){let{imageData:{data:e}}=t;const s=Math.floor(255*this.contrast),i=259*(s+255)/(255*(259-s));for(let r=0;r<e.length;r+=4)e[r]=i*(e[r]-128)+128,e[r+1]=i*(e[r+1]-128)+128,e[r+2]=i*(e[r+2]-128)+128}sendUniformData(t,e){t.uniform1f(e.uContrast,this.contrast)}}p(cs,"type","Contrast"),p(cs,"defaults",{contrast:0}),p(cs,"uniformLocations",["uContrast"]),D.setClass(cs);const Dh={Convolute_3_1:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[9];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 0);
      for (float h = 0.0; h < 3.0; h+=1.0) {
        for (float w = 0.0; w < 3.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 1), uStepH * (h - 1));
          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 3.0 + w)];
        }
      }
      gl_FragColor = color;
    }
    `,Convolute_3_0:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[9];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 1);
      for (float h = 0.0; h < 3.0; h+=1.0) {
        for (float w = 0.0; w < 3.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 1.0), uStepH * (h - 1.0));
          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 3.0 + w)];
        }
      }
      float alpha = texture2D(uTexture, vTexCoord).a;
      gl_FragColor = color;
      gl_FragColor.a = alpha;
    }
    `,Convolute_5_1:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[25];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 0);
      for (float h = 0.0; h < 5.0; h+=1.0) {
        for (float w = 0.0; w < 5.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));
          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 5.0 + w)];
        }
      }
      gl_FragColor = color;
    }
    `,Convolute_5_0:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[25];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 1);
      for (float h = 0.0; h < 5.0; h+=1.0) {
        for (float w = 0.0; w < 5.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 2.0), uStepH * (h - 2.0));
          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 5.0 + w)];
        }
      }
      float alpha = texture2D(uTexture, vTexCoord).a;
      gl_FragColor = color;
      gl_FragColor.a = alpha;
    }
    `,Convolute_7_1:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[49];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 0);
      for (float h = 0.0; h < 7.0; h+=1.0) {
        for (float w = 0.0; w < 7.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));
          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 7.0 + w)];
        }
      }
      gl_FragColor = color;
    }
    `,Convolute_7_0:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[49];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 1);
      for (float h = 0.0; h < 7.0; h+=1.0) {
        for (float w = 0.0; w < 7.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 3.0), uStepH * (h - 3.0));
          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 7.0 + w)];
        }
      }
      float alpha = texture2D(uTexture, vTexCoord).a;
      gl_FragColor = color;
      gl_FragColor.a = alpha;
    }
    `,Convolute_9_1:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[81];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 0);
      for (float h = 0.0; h < 9.0; h+=1.0) {
        for (float w = 0.0; w < 9.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));
          color += texture2D(uTexture, vTexCoord + matrixPos) * uMatrix[int(h * 9.0 + w)];
        }
      }
      gl_FragColor = color;
    }
    `,Convolute_9_0:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform float uMatrix[81];
    uniform float uStepW;
    uniform float uStepH;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = vec4(0, 0, 0, 1);
      for (float h = 0.0; h < 9.0; h+=1.0) {
        for (float w = 0.0; w < 9.0; w+=1.0) {
          vec2 matrixPos = vec2(uStepW * (w - 4.0), uStepH * (h - 4.0));
          color.rgb += texture2D(uTexture, vTexCoord + matrixPos).rgb * uMatrix[int(h * 9.0 + w)];
        }
      }
      float alpha = texture2D(uTexture, vTexCoord).a;
      gl_FragColor = color;
      gl_FragColor.a = alpha;
    }
    `};class us extends ot{getCacheKey(){return"".concat(this.type,"_").concat(Math.sqrt(this.matrix.length),"_").concat(this.opaque?1:0)}getFragmentSource(){return Dh[this.getCacheKey()]}applyTo2d(t){const e=t.imageData,s=e.data,i=this.matrix,r=Math.round(Math.sqrt(i.length)),n=Math.floor(r/2),o=e.width,h=e.height,l=t.ctx.createImageData(o,h),c=l.data,g=this.opaque?1:0;let u,d,f,v,_,b,C,O,w,S,M,L,E;for(M=0;M<h;M++)for(S=0;S<o;S++){for(_=4*(M*o+S),u=0,d=0,f=0,v=0,E=0;E<r;E++)for(L=0;L<r;L++)C=M+E-n,b=S+L-n,C<0||C>=h||b<0||b>=o||(O=4*(C*o+b),w=i[E*r+L],u+=s[O]*w,d+=s[O+1]*w,f+=s[O+2]*w,g||(v+=s[O+3]*w));c[_]=u,c[_+1]=d,c[_+2]=f,c[_+3]=g?s[_+3]:v}t.imageData=l}sendUniformData(t,e){t.uniform1fv(e.uMatrix,this.matrix)}toObject(){return m(m({},super.toObject()),{},{opaque:this.opaque,matrix:[...this.matrix]})}}p(us,"type","Convolute"),p(us,"defaults",{opaque:!1,matrix:[0,0,0,0,1,0,0,0,0]}),p(us,"uniformLocations",["uMatrix","uOpaque","uHalfSize","uSize"]),D.setClass(us);const jn="Gamma";class gs extends ot{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform vec3 uGamma;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    vec3 correction = (1.0 / uGamma);
    color.r = pow(color.r, correction.r);
    color.g = pow(color.g, correction.g);
    color.b = pow(color.b, correction.b);
    gl_FragColor = color;
    gl_FragColor.rgb *= color.a;
  }
`}constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};super(t),this.gamma=t.gamma||this.constructor.defaults.gamma.concat()}applyTo2d(t){let{imageData:{data:e}}=t;const s=this.gamma,i=1/s[0],r=1/s[1],n=1/s[2];this.rgbValues||(this.rgbValues={r:new Uint8Array(256),g:new Uint8Array(256),b:new Uint8Array(256)});const o=this.rgbValues;for(let h=0;h<256;h++)o.r[h]=255*Math.pow(h/255,i),o.g[h]=255*Math.pow(h/255,r),o.b[h]=255*Math.pow(h/255,n);for(let h=0;h<e.length;h+=4)e[h]=o.r[e[h]],e[h+1]=o.g[e[h+1]],e[h+2]=o.b[e[h+2]]}sendUniformData(t,e){t.uniform3fv(e.uGamma,this.gamma)}isNeutralState(){const{gamma:t}=this;return t[0]===1&&t[1]===1&&t[2]===1}toObject(){return{type:jn,gamma:this.gamma.concat()}}}p(gs,"type",jn),p(gs,"defaults",{gamma:[1,1,1]}),p(gs,"uniformLocations",["uGamma"]),D.setClass(gs);const kh={average:`
    precision highp float;
    uniform sampler2D uTexture;
    varying vec2 vTexCoord;
    void main() {
      vec4 color = texture2D(uTexture, vTexCoord);
      float average = (color.r + color.b + color.g) / 3.0;
      gl_FragColor = vec4(average, average, average, color.a);
    }
    `,lightness:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform int uMode;
    varying vec2 vTexCoord;
    void main() {
      vec4 col = texture2D(uTexture, vTexCoord);
      float average = (max(max(col.r, col.g),col.b) + min(min(col.r, col.g),col.b)) / 2.0;
      gl_FragColor = vec4(average, average, average, col.a);
    }
    `,luminosity:`
    precision highp float;
    uniform sampler2D uTexture;
    uniform int uMode;
    varying vec2 vTexCoord;
    void main() {
      vec4 col = texture2D(uTexture, vTexCoord);
      float average = 0.21 * col.r + 0.72 * col.g + 0.07 * col.b;
      gl_FragColor = vec4(average, average, average, col.a);
    }
    `};class ds extends ot{applyTo2d(t){let{imageData:{data:e}}=t;for(let s,i=0;i<e.length;i+=4){const r=e[i],n=e[i+1],o=e[i+2];switch(this.mode){case"average":s=(r+n+o)/3;break;case"lightness":s=(Math.min(r,n,o)+Math.max(r,n,o))/2;break;case"luminosity":s=.21*r+.72*n+.07*o}e[i+2]=e[i+1]=e[i]=s}}getCacheKey(){return"".concat(this.type,"_").concat(this.mode)}getFragmentSource(){return kh[this.mode]}sendUniformData(t,e){t.uniform1i(e.uMode,1)}isNeutralState(){return!1}}p(ds,"type","Grayscale"),p(ds,"defaults",{mode:"average"}),p(ds,"uniformLocations",["uMode"]),D.setClass(ds);const Mh=m(m({},Pn),{},{rotation:0});class ai extends be{calculateMatrix(){const t=this.rotation*Math.PI,e=qt(t),s=$t(t),i=1/3,r=Math.sqrt(i)*s,n=1-e;this.matrix=[e+n/3,i*n-r,i*n+r,0,0,i*n+r,e+i*n,i*n-r,0,0,i*n-r,i*n+r,e+i*n,0,0,0,0,0,1,0]}isNeutralState(){return this.rotation===0}applyTo(t){this.calculateMatrix(),super.applyTo(t)}toObject(){return{type:this.type,rotation:this.rotation}}}p(ai,"type","HueRotation"),p(ai,"defaults",Mh),D.setClass(ai);class fs extends ot{applyTo2d(t){let{imageData:{data:e}}=t;for(let s=0;s<e.length;s+=4)e[s]=255-e[s],e[s+1]=255-e[s+1],e[s+2]=255-e[s+2],this.alpha&&(e[s+3]=255-e[s+3])}getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform int uInvert;
  uniform int uAlpha;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    if (uInvert == 1) {
      if (uAlpha == 1) {
        gl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,1.0 -color.a);
      } else {
        gl_FragColor = vec4(1.0 - color.r,1.0 -color.g,1.0 -color.b,color.a);
      }
    } else {
      gl_FragColor = color;
    }
  }
`}isNeutralState(){return!this.invert}sendUniformData(t,e){t.uniform1i(e.uInvert,Number(this.invert)),t.uniform1i(e.uAlpha,Number(this.alpha))}}p(fs,"type","Invert"),p(fs,"defaults",{alpha:!1,invert:!0}),p(fs,"uniformLocations",["uInvert","uAlpha"]),D.setClass(fs);class ps extends ot{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uStepH;
  uniform float uNoise;
  uniform float uSeed;
  varying vec2 vTexCoord;
  float rand(vec2 co, float seed, float vScale) {
    return fract(sin(dot(co.xy * vScale ,vec2(12.9898 , 78.233))) * 43758.5453 * (seed + 0.01) / 2.0);
  }
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    color.rgb += (0.5 - rand(vTexCoord, uSeed, 0.1 / uStepH)) * uNoise;
    gl_FragColor = color;
  }
`}applyTo2d(t){let{imageData:{data:e}}=t;const s=this.noise;for(let i=0;i<e.length;i+=4){const r=(.5-Math.random())*s;e[i]+=r,e[i+1]+=r,e[i+2]+=r}}sendUniformData(t,e){t.uniform1f(e.uNoise,this.noise/255),t.uniform1f(e.uSeed,Math.random())}isNeutralState(){return this.noise===0}}p(ps,"type","Noise"),p(ps,"defaults",{noise:0}),p(ps,"uniformLocations",["uNoise","uSeed"]),D.setClass(ps);class ms extends ot{applyTo2d(t){let{imageData:{data:e,width:s,height:i}}=t;for(let r=0;r<i;r+=this.blocksize)for(let n=0;n<s;n+=this.blocksize){const o=4*r*s+4*n,h=e[o],l=e[o+1],c=e[o+2],g=e[o+3];for(let u=r;u<Math.min(r+this.blocksize,i);u++)for(let d=n;d<Math.min(n+this.blocksize,s);d++){const f=4*u*s+4*d;e[f]=h,e[f+1]=l,e[f+2]=c,e[f+3]=g}}}isNeutralState(){return this.blocksize===1}getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uBlocksize;
  uniform float uStepW;
  uniform float uStepH;
  varying vec2 vTexCoord;
  void main() {
    float blockW = uBlocksize * uStepW;
    float blockH = uBlocksize * uStepH;
    int posX = int(vTexCoord.x / blockW);
    int posY = int(vTexCoord.y / blockH);
    float fposX = float(posX);
    float fposY = float(posY);
    vec2 squareCoords = vec2(fposX * blockW, fposY * blockH);
    vec4 color = texture2D(uTexture, squareCoords);
    gl_FragColor = color;
  }
`}sendUniformData(t,e){t.uniform1f(e.uBlocksize,this.blocksize)}}p(ms,"type","Pixelate"),p(ms,"defaults",{blocksize:4}),p(ms,"uniformLocations",["uBlocksize"]),D.setClass(ms);class vs extends ot{getFragmentSource(){return`
precision highp float;
uniform sampler2D uTexture;
uniform vec4 uLow;
uniform vec4 uHigh;
varying vec2 vTexCoord;
void main() {
  gl_FragColor = texture2D(uTexture, vTexCoord);
  if(all(greaterThan(gl_FragColor.rgb,uLow.rgb)) && all(greaterThan(uHigh.rgb,gl_FragColor.rgb))) {
    gl_FragColor.a = 0.0;
  }
}
`}applyTo2d(t){let{imageData:{data:e}}=t;const s=255*this.distance,i=new z(this.color).getSource(),r=[i[0]-s,i[1]-s,i[2]-s],n=[i[0]+s,i[1]+s,i[2]+s];for(let o=0;o<e.length;o+=4){const h=e[o],l=e[o+1],c=e[o+2];h>r[0]&&l>r[1]&&c>r[2]&&h<n[0]&&l<n[1]&&c<n[2]&&(e[o+3]=0)}}sendUniformData(t,e){const s=new z(this.color).getSource(),i=this.distance,r=[0+s[0]/255-i,0+s[1]/255-i,0+s[2]/255-i,1],n=[s[0]/255+i,s[1]/255+i,s[2]/255+i,1];t.uniform4fv(e.uLow,r),t.uniform4fv(e.uHigh,n)}}p(vs,"type","RemoveColor"),p(vs,"defaults",{color:"#FFFFFF",distance:.02,useAlpha:!1}),p(vs,"uniformLocations",["uLow","uHigh"]),D.setClass(vs);class ys extends ot{sendUniformData(t,e){t.uniform2fv(e.uDelta,this.horizontal?[1/this.width,0]:[0,1/this.height]),t.uniform1fv(e.uTaps,this.taps)}getFilterWindow(){const t=this.tempScale;return Math.ceil(this.lanczosLobes/t)}getCacheKey(){const t=this.getFilterWindow();return"".concat(this.type,"_").concat(t)}getFragmentSource(){const t=this.getFilterWindow();return this.generateShader(t)}getTaps(){const t=this.lanczosCreate(this.lanczosLobes),e=this.tempScale,s=this.getFilterWindow(),i=new Array(s);for(let r=1;r<=s;r++)i[r-1]=t(r*e);return i}generateShader(t){const e=new Array(t);for(let s=1;s<=t;s++)e[s-1]="".concat(s,".0 * uDelta");return`
      precision highp float;
      uniform sampler2D uTexture;
      uniform vec2 uDelta;
      varying vec2 vTexCoord;
      uniform float uTaps[`.concat(t,`];
      void main() {
        vec4 color = texture2D(uTexture, vTexCoord);
        float sum = 1.0;
        `).concat(e.map(((s,i)=>`
              color += texture2D(uTexture, vTexCoord + `.concat(s,") * uTaps[").concat(i,"] + texture2D(uTexture, vTexCoord - ").concat(s,") * uTaps[").concat(i,`];
              sum += 2.0 * uTaps[`).concat(i,`];
            `))).join(`
`),`
        gl_FragColor = color / sum;
      }
    `)}applyToForWebgl(t){t.passes++,this.width=t.sourceWidth,this.horizontal=!0,this.dW=Math.round(this.width*this.scaleX),this.dH=t.sourceHeight,this.tempScale=this.dW/this.width,this.taps=this.getTaps(),t.destinationWidth=this.dW,super.applyTo(t),t.sourceWidth=t.destinationWidth,this.height=t.sourceHeight,this.horizontal=!1,this.dH=Math.round(this.height*this.scaleY),this.tempScale=this.dH/this.height,this.taps=this.getTaps(),t.destinationHeight=this.dH,super.applyTo(t),t.sourceHeight=t.destinationHeight}applyTo(t){Ns(t)?this.applyToForWebgl(t):this.applyTo2d(t)}isNeutralState(){return this.scaleX===1&&this.scaleY===1}lanczosCreate(t){return e=>{if(e>=t||e<=-t)return 0;if(e<11920929e-14&&e>-11920929e-14)return 1;const s=(e*=Math.PI)/t;return Math.sin(e)/e*Math.sin(s)/s}}applyTo2d(t){const e=t.imageData,s=this.scaleX,i=this.scaleY;this.rcpScaleX=1/s,this.rcpScaleY=1/i;const r=e.width,n=e.height,o=Math.round(r*s),h=Math.round(n*i);let l;l=this.resizeType==="sliceHack"?this.sliceByTwo(t,r,n,o,h):this.resizeType==="hermite"?this.hermiteFastResize(t,r,n,o,h):this.resizeType==="bilinear"?this.bilinearFiltering(t,r,n,o,h):this.resizeType==="lanczos"?this.lanczosResize(t,r,n,o,h):new ImageData(o,h),t.imageData=l}sliceByTwo(t,e,s,i,r){const n=t.imageData,o=.5;let h=!1,l=!1,c=e*o,g=s*o;const u=t.filterBackend.resources;let d=0,f=0;const v=e;let _=0;u.sliceByTwo||(u.sliceByTwo=Kt());const b=u.sliceByTwo;(b.width<1.5*e||b.height<s)&&(b.width=1.5*e,b.height=s);const C=b.getContext("2d");for(C.clearRect(0,0,1.5*e,s),C.putImageData(n,0,0),i=Math.floor(i),r=Math.floor(r);!h||!l;)e=c,s=g,i<Math.floor(c*o)?c=Math.floor(c*o):(c=i,h=!0),r<Math.floor(g*o)?g=Math.floor(g*o):(g=r,l=!0),C.drawImage(b,d,f,e,s,v,_,c,g),d=v,f=_,_+=g;return C.getImageData(d,f,i,r)}lanczosResize(t,e,s,i,r){const n=t.imageData.data,o=t.ctx.createImageData(i,r),h=o.data,l=this.lanczosCreate(this.lanczosLobes),c=this.rcpScaleX,g=this.rcpScaleY,u=2/this.rcpScaleX,d=2/this.rcpScaleY,f=Math.ceil(c*this.lanczosLobes/2),v=Math.ceil(g*this.lanczosLobes/2),_={},b={x:0,y:0},C={x:0,y:0};return(function O(w){let S,M,L,E,W,$,tt,H,Y,R,it;for(b.x=(w+.5)*c,C.x=Math.floor(b.x),S=0;S<r;S++){for(b.y=(S+.5)*g,C.y=Math.floor(b.y),W=0,$=0,tt=0,H=0,Y=0,M=C.x-f;M<=C.x+f;M++)if(!(M<0||M>=e)){R=Math.floor(1e3*Math.abs(M-b.x)),_[R]||(_[R]={});for(let et=C.y-v;et<=C.y+v;et++)et<0||et>=s||(it=Math.floor(1e3*Math.abs(et-b.y)),_[R][it]||(_[R][it]=l(Math.sqrt(Math.pow(R*u,2)+Math.pow(it*d,2))/1e3)),L=_[R][it],L>0&&(E=4*(et*e+M),W+=L,$+=L*n[E],tt+=L*n[E+1],H+=L*n[E+2],Y+=L*n[E+3]))}E=4*(S*i+w),h[E]=$/W,h[E+1]=tt/W,h[E+2]=H/W,h[E+3]=Y/W}return++w<i?O(w):o})(0)}bilinearFiltering(t,e,s,i,r){let n,o,h,l,c,g,u,d,f,v,_,b,C,O=0;const w=this.rcpScaleX,S=this.rcpScaleY,M=4*(e-1),L=t.imageData.data,E=t.ctx.createImageData(i,r),W=E.data;for(u=0;u<r;u++)for(d=0;d<i;d++)for(c=Math.floor(w*d),g=Math.floor(S*u),f=w*d-c,v=S*u-g,C=4*(g*e+c),_=0;_<4;_++)n=L[C+_],o=L[C+4+_],h=L[C+M+_],l=L[C+M+4+_],b=n*(1-f)*(1-v)+o*f*(1-v)+h*v*(1-f)+l*f*v,W[O++]=b;return E}hermiteFastResize(t,e,s,i,r){const n=this.rcpScaleX,o=this.rcpScaleY,h=Math.ceil(n/2),l=Math.ceil(o/2),c=t.imageData.data,g=t.ctx.createImageData(i,r),u=g.data;for(let d=0;d<r;d++)for(let f=0;f<i;f++){const v=4*(f+d*i);let _=0,b=0,C=0,O=0,w=0,S=0,M=0;const L=(d+.5)*o;for(let E=Math.floor(d*o);E<(d+1)*o;E++){const W=Math.abs(L-(E+.5))/l,$=(f+.5)*n,tt=W*W;for(let H=Math.floor(f*n);H<(f+1)*n;H++){let Y=Math.abs($-(H+.5))/h;const R=Math.sqrt(tt+Y*Y);R>1&&R<-1||(_=2*R*R*R-3*R*R+1,_>0&&(Y=4*(H+E*e),M+=_*c[Y+3],C+=_,c[Y+3]<255&&(_=_*c[Y+3]/250),O+=_*c[Y],w+=_*c[Y+1],S+=_*c[Y+2],b+=_))}}u[v]=O/b,u[v+1]=w/b,u[v+2]=S/b,u[v+3]=M/C}return g}}p(ys,"type","Resize"),p(ys,"defaults",{resizeType:"hermite",scaleX:1,scaleY:1,lanczosLobes:3}),p(ys,"uniformLocations",["uDelta","uTaps"]),D.setClass(ys);class _s extends ot{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uSaturation;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    float rgMax = max(color.r, color.g);
    float rgbMax = max(rgMax, color.b);
    color.r += rgbMax != color.r ? (rgbMax - color.r) * uSaturation : 0.00;
    color.g += rgbMax != color.g ? (rgbMax - color.g) * uSaturation : 0.00;
    color.b += rgbMax != color.b ? (rgbMax - color.b) * uSaturation : 0.00;
    gl_FragColor = color;
  }
`}applyTo2d(t){let{imageData:{data:e}}=t;const s=-this.saturation;for(let i=0;i<e.length;i+=4){const r=e[i],n=e[i+1],o=e[i+2],h=Math.max(r,n,o);e[i]+=h!==r?(h-r)*s:0,e[i+1]+=h!==n?(h-n)*s:0,e[i+2]+=h!==o?(h-o)*s:0}}sendUniformData(t,e){t.uniform1f(e.uSaturation,-this.saturation)}isNeutralState(){return this.saturation===0}}p(_s,"type","Saturation"),p(_s,"defaults",{saturation:0}),p(_s,"uniformLocations",["uSaturation"]),D.setClass(_s);class xs extends ot{getFragmentSource(){return`
  precision highp float;
  uniform sampler2D uTexture;
  uniform float uVibrance;
  varying vec2 vTexCoord;
  void main() {
    vec4 color = texture2D(uTexture, vTexCoord);
    float max = max(color.r, max(color.g, color.b));
    float avg = (color.r + color.g + color.b) / 3.0;
    float amt = (abs(max - avg) * 2.0) * uVibrance;
    color.r += max != color.r ? (max - color.r) * amt : 0.00;
    color.g += max != color.g ? (max - color.g) * amt : 0.00;
    color.b += max != color.b ? (max - color.b) * amt : 0.00;
    gl_FragColor = color;
  }
`}applyTo2d(t){let{imageData:{data:e}}=t;const s=-this.vibrance;for(let i=0;i<e.length;i+=4){const r=e[i],n=e[i+1],o=e[i+2],h=Math.max(r,n,o),l=(r+n+o)/3,c=2*Math.abs(h-l)/255*s;e[i]+=h!==r?(h-r)*c:0,e[i+1]+=h!==n?(h-n)*c:0,e[i+2]+=h!==o?(h-o)*c:0}}sendUniformData(t,e){t.uniform1f(e.uVibrance,-this.vibrance)}isNeutralState(){return this.vibrance===0}}p(xs,"type","Vibrance"),p(xs,"defaults",{vibrance:0}),p(xs,"uniformLocations",["uVibrance"]),D.setClass(xs);const Eh={class:"min-h-screen bg-gray-50 p-6"},Ph={class:"max-w-6xl mx-auto space-y-6"},jh={class:"text-center mb-8"},Ah={class:"text-3xl font-bold text-gray-900 mb-2"},Fh={class:"text-gray-600"},Lh={class:"flex justify-center mb-6"},Ih={class:"inline-flex rounded-md shadow-sm",role:"group"},Rh={class:"grid md:grid-cols-3 gap-6 mb-8"},Bh={class:"bg-white p-6 rounded-lg shadow-sm border"},Wh={class:"text-2xl mb-3"},Xh={class:"text-lg font-semibold mb-2"},Yh={class:"text-gray-600 text-sm"},Vh={class:"bg-white p-6 rounded-lg shadow-sm border"},zh={class:"text-2xl mb-3"},Hh={class:"text-lg font-semibold mb-2"},Gh={class:"text-gray-600 text-sm"},Uh={class:"bg-white p-6 rounded-lg shadow-sm border"},Nh={class:"text-2xl mb-3"},qh={class:"text-lg font-semibold mb-2"},$h={class:"text-gray-600 text-sm"},Kh={class:"grid lg:grid-cols-2 gap-6"},Jh={class:"bg-white p-6 rounded-lg shadow-sm border"},Zh={class:"flex items-center justify-between mb-4"},Qh={class:"text-lg font-semibold text-gray-900"},tl={class:"flex justify-center"},el={key:0,class:"absolute inset-0 flex flex-col items-center justify-center text-gray-500"},sl={class:"text-4xl mb-2"},il={class:"mt-4 flex justify-center"},rl=["disabled"],nl=["disabled"],ol={class:"bg-white p-6 rounded-lg shadow-sm border"},al={class:"flex items-center justify-between mb-4"},hl={class:"text-lg font-semibold text-gray-900"},ll={key:0,class:"space-y-6"},cl={class:"space-y-2"},ul={class:"font-medium text-gray-900"},gl={class:"text-sm text-gray-600"},dl={class:"flex space-x-2"},fl=["disabled"],pl={key:0,class:"mt-2"},ml={class:"text-sm text-gray-600 mb-1"},vl={class:"border rounded p-2 bg-gray-50"},yl=["src"],_l={class:"space-y-2"},xl={class:"font-medium text-gray-900"},bl={class:"text-sm text-gray-600"},Cl=["disabled"],Sl={class:"space-y-2"},wl={class:"font-medium text-gray-900"},Tl={class:"text-sm text-gray-600"},Ol={class:"flex space-x-2"},Dl=["disabled"],kl={key:0,class:"mt-2"},Ml={class:"text-sm text-gray-600 mb-1"},El={class:"border rounded p-2 bg-gray-50"},Pl=["src"],jl={class:"space-y-2"},Al={class:"font-medium text-gray-900"},Fl={class:"text-sm text-gray-600"},Ll=["disabled"],Il=["disabled"],Rl={key:1,class:"space-y-6"},Bl={class:"space-y-2"},Wl={class:"font-medium text-gray-900"},Xl={class:"text-sm text-gray-600"},Yl={class:"flex space-x-2"},Vl=["disabled"],zl={class:"space-y-2"},Hl={class:"font-medium text-gray-900"},Gl={class:"text-sm text-gray-600"},Ul=["disabled"],Nl={key:0,class:"space-y-2"},ql={class:"font-medium text-gray-900"},$l={class:"border rounded p-2 bg-gray-50"},Kl=["src"],Jl=["disabled"],tc=Ln({__name:"ImageSteganography",setup(a){const{t}=In(),{success:e,error:s}=Xn(),i=Xt("encode"),r=Xt(null),n=Xt(null),o=Xt(!1),h=Xt(null),l=Xt(null),c=Xt(null),g=Xt(null),u=Rn({isLoading:!1,targetImageData:null,hiddenImageData:null,hiddenDataBinary:null,targetDataBinary:null}),d=$e(()=>u.hiddenImageData!==null),f=$e(()=>u.targetImageData!==null),v=$e(()=>u.hiddenDataBinary!==null),_=$e(()=>f.value&&v.value&&!u.isLoading),b=Xt(null);function C(){if(!r.value)return;const x=r.value.parentElement;x&&(r.value.width=x.clientWidth-20,r.value.height=Math.min(x.clientHeight-20,600)),n.value=new _i(r.value,{isDrawingMode:!1,selectable:!1,selection:!1,hoverCursor:"pointer",devicePixelRatio:!0}),o.value=!0}function O(){return new Promise(x=>{const k=document.createElement("input");k.type="file",k.accept="image/*",k.onchange=A=>{const G=A.target.files?.[0]||null;x(G)},k.click()})}async function w(){try{u.isLoading=!0;const x=await O();if(x){const k=URL.createObjectURL(x),A=new window.Image;A.onload=()=>{const G=document.createElement("canvas");G.width=A.width,G.height=A.height;const I=G.getContext("2d");I&&(I.drawImage(A,0,0),u.hiddenImageData=I.getImageData(0,0,G.width,G.height),h.value&&URL.revokeObjectURL(h.value),h.value=G.toDataURL(),e(t("tools.imageSteganography.messages.imageLoaded"))),URL.revokeObjectURL(k)},A.src=k}}catch(x){s(t("tools.imageSteganography.errors.imageLoadFailed")),console.error(x)}finally{u.isLoading=!1}}async function S(){try{u.isLoading=!0;const x=await O();if(x){const k=URL.createObjectURL(x);M(k)}}catch(x){s(t("tools.imageSteganography.errors.imageLoadFailed")),console.error(x)}finally{u.isLoading=!1}}function M(x){n.value&&(u.isLoading=!0,dt.fromURL(x,{crossOrigin:"anonymous"}).then(k=>{try{const A=n.value.width,G=n.value.height,I=k.width,K=k.height,j=Math.min(A/I,G/K);k.set({left:A/2,originX:"center",originY:"center",top:G/2,scaleX:j,scaleY:j,selectable:!1}),n.value.clear(),n.value.add(k),n.value.renderAll();const J=document.createElement("canvas");J.width=I,J.height=K;const ht=J.getContext("2d");ht&&(ht.drawImage(k.getElement(),0,0),u.targetImageData=ht.getImageData(0,0,J.width,J.height),l.value&&URL.revokeObjectURL(l.value),l.value=J.toDataURL()),e(t("tools.imageSteganography.messages.imageLoaded"))}catch(A){s(t("tools.imageSteganography.errors.imageLoadFailed")),console.error(A)}finally{u.isLoading=!1}}).catch(k=>{u.isLoading=!1,s(t("tools.imageSteganography.errors.imageLoadFailed")),console.error(k)}))}function L(){if(!u.hiddenImageData)return;const x=document.createElement("canvas");x.width=u.hiddenImageData.width,x.height=u.hiddenImageData.height;const k=x.getContext("2d");if(k){k.putImageData(u.hiddenImageData,0,0);const A=window.open("","_blank");A&&(A.document.write(`
        <html>
          <head>
            <title>${t("tools.imageSteganography.hiddenImagePreview")}</title>
          </head>
          <body style="margin: 0; display: flex; justify-content: center; align-items: center; height: 100vh; background: #f5f5f5;">
            <div>
              <h2 style="text-align: center; font-family: Arial, sans-serif;">${t("tools.imageSteganography.hiddenImagePreview")}</h2>
              <img src="${x.toDataURL()}" style="max-width: 90vw; max-height: 80vh;" />
            </div>
          </body>
        </html>
      `),A.document.close())}}function E(){if(!n.value)return;const x=n.value.toDataURL(),k=window.open("","_blank");k&&(k.document.write(`
      <html>
        <head>
          <title>${t("tools.imageSteganography.targetImagePreview")}</title>
        </head>
        <body style="margin: 0; display: flex; justify-content: center; align-items: center; height: 100vh; background: #f5f5f5;">
          <div>
            <h2 style="text-align: center; font-family: Arial, sans-serif;">${t("tools.imageSteganography.targetImagePreview")}</h2>
            <img src="${x}" style="max-width: 90vw; max-height: 80vh;" />
          </div>
        </body>
      </html>
    `),k.document.close())}function W(){if(u.hiddenImageData){u.isLoading=!0;try{const x=document.createElement("canvas"),k=x.getContext("2d");if(!k)throw new Error("Could not get canvas context");const A=u.hiddenImageData.width*u.hiddenImageData.height,G=Math.sqrt(A/8);x.width=Math.floor(G),x.height=Math.floor(G),k.putImageData(u.hiddenImageData,0,0);const I=document.createElement("canvas");I.width=x.width,I.height=x.height;const K=I.getContext("2d");if(K){u.hiddenImageData.width>x.width||u.hiddenImageData.height>x.height?K.drawImage(x,0,0,u.hiddenImageData.width,u.hiddenImageData.height,0,0,x.width,x.height):K.drawImage(x,0,0);const j=K.getImageData(0,0,I.width,I.height);u.hiddenDataBinary=Array.from(j.data,J=>J.toString(2).padStart(8,"0").split("")),e(t("tools.imageSteganography.messages.dataSaved"))}}catch(x){s(t("tools.imageSteganography.errors.dataSaveFailed")),console.error(x)}finally{u.isLoading=!1}}}function $(x){return x=x>254?x-1:x,x=x%2==1?x-1:x,x}function tt(){if(!(!u.hiddenDataBinary||!u.targetImageData||!n.value)){u.isLoading=!0;try{const x=[];for(let j=0;j<u.hiddenDataBinary.length;j++)x.push(...u.hiddenDataBinary[j]);const k=new Uint8ClampedArray(u.targetImageData.data);for(let j=0;j<k.length;j++)k[j]=$(k[j]);const A=Array.from(k,j=>j.toString(2).padStart(8,"0").split(""));for(let j=0;j<A.length&&j<x.length;j++)x[j]&&(A[j][7]=x[j]);for(let j=0;j<k.length;j++)k[j]=parseInt(A[j].join(""),2);const G=new ImageData(k,u.targetImageData.width,u.targetImageData.height),I=document.createElement("canvas");I.width=u.targetImageData.width,I.height=u.targetImageData.height;const K=I.getContext("2d");K&&(K.putImageData(G,0,0),dt.fromURL(I.toDataURL()).then(j=>{if(n.value){const J=n.value.width,ht=n.value.height,fe=j.width,at=j.height,ct=Math.min(J/fe,ht/at);j.set({left:J/2,originX:"center",originY:"center",top:ht/2,scaleX:ct,scaleY:ct,selectable:!1}),n.value.clear(),n.value.add(j),n.value.renderAll()}e(t("tools.imageSteganography.messages.encryptionComplete"))}).catch(j=>{s(t("tools.imageSteganography.errors.encryptionFailed")),console.error(j)}))}catch(x){s(t("tools.imageSteganography.errors.encryptionFailed")),console.error(x)}finally{u.isLoading=!1}}}function H(){if(g.value){u.isLoading=!0;try{const x=Array.from(g.value.data,at=>at.toString(2).padStart(8,"0").split("")),k=[],A=g.value.width*g.value.height,G=Math.sqrt(A/8),I=Math.floor(G),K=I*I*4;for(let at=0;at<x.length&&k.length<K;at++){const ct=parseInt(x[at][7],2);k.push(ct)}const j=new Uint8ClampedArray(k),J=new ImageData(j,I,I),ht=document.createElement("canvas");ht.width=I,ht.height=I;const fe=ht.getContext("2d");if(fe)fe.putImageData(J,0,0),c.value&&URL.revokeObjectURL(c.value),c.value=ht.toDataURL(),n.value?dt.fromURL(c.value).then(at=>{const ct=n.value.width,Xi=n.value.height,An=at.width,Fn=at.height,Yi=Math.min(ct/An,Xi/Fn);at.set({left:ct/2,originX:"center",originY:"center",top:Xi/2,scaleX:Yi,scaleY:Yi,selectable:!1}),n.value.clear(),n.value.add(at),n.value.renderAll(),e(t("tools.imageSteganography.messages.decryptionComplete"))}).catch(at=>{s(t("tools.imageSteganography.errors.decryptionFailed")),console.error(at)}):e(t("tools.imageSteganography.messages.decryptionComplete"));else throw new Error("Could not get decoding canvas context")}catch(x){s(t("tools.imageSteganography.errors.decryptionFailed")),console.error(x)}finally{u.isLoading=!1}}}function Y(){if(n.value)try{const x=n.value.toDataURL({format:"png",multiplier:1}),k=document.createElement("a");k.download="steganography-image.png",k.href=x,document.body.appendChild(k),k.click(),document.body.removeChild(k),e(t("tools.imageSteganography.messages.imageExported"))}catch(x){s(t("tools.imageSteganography.errors.exportFailed")),console.error(x)}}function R(){n.value&&n.value.clear(),u.targetImageData=null,u.hiddenImageData=null,u.hiddenDataBinary=null,u.targetDataBinary=null,h.value&&(URL.revokeObjectURL(h.value),h.value=null),l.value&&(URL.revokeObjectURL(l.value),l.value=null),e(t("tools.imageSteganography.messages.canvasCleared"))}async function it(){try{u.isLoading=!0;const x=await O();if(x){const k=URL.createObjectURL(x),A=new window.Image;A.onload=()=>{const G=document.createElement("canvas");G.width=A.width,G.height=A.height;const I=G.getContext("2d");I&&(I.drawImage(A,0,0),g.value=I.getImageData(0,0,G.width,G.height),e(t("tools.imageSteganography.messages.imageLoaded"))),URL.revokeObjectURL(k)},A.src=k}}catch(x){s(t("tools.imageSteganography.errors.imageLoadFailed")),console.error(x)}finally{u.isLoading=!1}}function et(){if(c.value)try{const x=document.createElement("a");x.download="decoded-image.png",x.href=c.value,document.body.appendChild(x),x.click(),document.body.removeChild(x),e(t("tools.imageSteganography.messages.imageExported"))}catch(x){s(t("tools.imageSteganography.errors.exportFailed")),console.error(x)}}function rt(){g.value=null,c.value&&(URL.revokeObjectURL(c.value),c.value=null),n.value&&n.value.clear(),e(t("tools.imageSteganography.messages.canvasCleared"))}Bn(()=>{C(),b.value=document.createElement("input"),b.value.type="file",b.value.accept="image/*",window.addEventListener("resize",V)}),Wn(()=>{window.removeEventListener("resize",V),h.value&&URL.revokeObjectURL(h.value),l.value&&URL.revokeObjectURL(l.value),c.value&&URL.revokeObjectURL(c.value)});function V(){if(r.value&&n.value){const x=r.value.parentElement;if(x){const k=x.clientWidth-20,A=Math.min(x.clientHeight-20,600);r.value.width=k,r.value.height=A,n.value.setWidth(k),n.value.setHeight(A),n.value.renderAll()}}}return(x,k)=>(Dt(),Ot("div",Eh,[T("div",Ph,[T("div",jh,[T("h1",Ah,P(x.$t("tools.imageSteganography.title")),1),T("p",Fh,P(x.$t("tools.imageSteganography.description")),1)]),T("div",Lh,[T("div",Ih,[T("button",{type:"button",onClick:k[0]||(k[0]=A=>i.value="encode"),class:qs(["px-4 py-2 text-sm font-medium rounded-l-lg",i.value==="encode"?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-100"])},P(x.$t("tools.imageSteganography.modeToggle.encode")),3),T("button",{type:"button",onClick:k[1]||(k[1]=A=>i.value="decode"),class:qs(["px-4 py-2 text-sm font-medium rounded-r-lg border-l border-gray-200",i.value==="decode"?"bg-blue-600 text-white":"bg-white text-gray-700 hover:bg-gray-100"])},P(x.$t("tools.imageSteganography.modeToggle.decode")),3)])]),T("div",Rh,[T("div",Bh,[T("div",Wh,P(i.value==="encode"?"🔒":"🔓"),1),T("h3",Xh,P(i.value==="encode"?x.$t("tools.imageSteganography.features.encryption.title"):x.$t("tools.imageSteganography.features.decryption.title")),1),T("p",Yh,P(i.value==="encode"?x.$t("tools.imageSteganography.features.encryption.description"):x.$t("tools.imageSteganography.features.decryption.description")),1)]),T("div",Vh,[T("div",zh,P(i.value==="encode"?"🖼️":"🔍"),1),T("h3",Hh,P(i.value==="encode"?x.$t("tools.imageSteganography.features.steganography.title"):x.$t("tools.imageSteganography.features.extraction.title")),1),T("p",Gh,P(i.value==="encode"?x.$t("tools.imageSteganography.features.steganography.description"):x.$t("tools.imageSteganography.features.extraction.description")),1)]),T("div",Uh,[T("div",Nh,P(i.value==="encode"?"📥":"📤"),1),T("h3",qh,P(i.value==="encode"?x.$t("tools.imageSteganography.features.export.title"):x.$t("tools.imageSteganography.features.result.title")),1),T("p",$h,P(i.value==="encode"?x.$t("tools.imageSteganography.features.export.description"):x.$t("tools.imageSteganography.features.result.description")),1)])]),T("div",Kh,[T("div",Jh,[T("div",Zh,[T("h3",Qh,P(i.value==="encode"?x.$t("tools.imageSteganography.canvasTitle"):x.$t("tools.imageSteganography.decodedImageTitle")),1)]),T("div",tl,[T("div",{class:qs(["relative border-2 border-dashed border-gray-300 rounded-lg bg-white flex items-center justify-center w-full",{"cursor-pointer hover:border-blue-500":!u.isLoading}]),style:{"min-height":"500px"}},[T("canvas",{ref_key:"canvasRef",ref:r,id:"steganography-canvas",class:"rounded-lg max-w-full max-h-full",style:{width:"100%",height:"100%","object-fit":"contain"}},null,512),!o.value||(i.value==="encode"?!u.targetImageData:!c.value)?(Dt(),Ot("div",el,[T("div",sl,P(i.value==="encode"?"🖼️":"🔍"),1),T("p",null,P(i.value==="encode"?x.$t("tools.imageSteganography.canvasPlaceholder"):x.$t("tools.imageSteganography.decodedImagePlaceholder")),1)])):pe("",!0)],2)]),T("div",il,[i.value==="encode"?(Dt(),Ot("button",{key:0,onClick:Y,disabled:!o.value||u.isLoading||!u.targetImageData,class:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},P(x.$t("tools.imageSteganography.exportImage")),9,rl)):(Dt(),Ot("button",{key:1,onClick:et,disabled:!c.value||u.isLoading,class:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},P(x.$t("tools.imageSteganography.exportDecodedImage")),9,nl))])]),T("div",ol,[T("div",al,[T("h3",hl,P(i.value==="encode"?x.$t("tools.imageSteganography.operationsTitle"):x.$t("tools.imageSteganography.decodingTitle")),1)]),i.value==="encode"?(Dt(),Ot("div",ll,[T("div",cl,[T("h4",ul,P(x.$t("tools.imageSteganography.step1")),1),T("p",gl,P(x.$t("tools.imageSteganography.step1Desc")),1),T("div",dl,[T("button",{onClick:w,disabled:u.isLoading,class:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},P(x.$t("tools.imageSteganography.selectHiddenImage")),9,fl),u.hiddenImageData?(Dt(),Ot("button",{key:0,onClick:L,class:"px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors font-medium"},P(x.$t("common.preview")),1)):pe("",!0)]),u.hiddenImageData?(Dt(),Ot("div",pl,[T("p",ml,P(x.$t("common.preview"))+":",1),T("div",vl,[T("img",{src:h.value||"",alt:"Hidden image preview",class:"max-w-full max-h-32 object-contain"},null,8,yl)])])):pe("",!0)]),T("div",_l,[T("h4",xl,P(x.$t("tools.imageSteganography.step2")),1),T("p",bl,P(x.$t("tools.imageSteganography.step2Desc")),1),T("button",{onClick:W,disabled:!d.value||u.isLoading,class:"w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},P(x.$t("tools.imageSteganography.saveHiddenData")),9,Cl)]),T("div",Sl,[T("h4",wl,P(x.$t("tools.imageSteganography.step3")),1),T("p",Tl,P(x.$t("tools.imageSteganography.step3Desc")),1),T("div",Ol,[T("button",{onClick:S,disabled:u.isLoading,class:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},P(x.$t("tools.imageSteganography.selectTargetImage")),9,Dl),u.targetImageData?(Dt(),Ot("button",{key:0,onClick:E,class:"px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors font-medium"},P(x.$t("common.preview")),1)):pe("",!0)]),l.value?(Dt(),Ot("div",kl,[T("p",Ml,P(x.$t("common.preview"))+":",1),T("div",El,[T("img",{src:l.value||"",alt:"Target image preview",class:"max-w-full max-h-32 object-contain"},null,8,Pl)])])):pe("",!0)]),T("div",jl,[T("h4",Al,P(x.$t("tools.imageSteganography.step4")),1),T("p",Fl,P(x.$t("tools.imageSteganography.step4Desc")),1),T("button",{onClick:tt,disabled:!_.value,class:"w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},P(x.$t("tools.imageSteganography.startEncryption")),9,Ll)]),T("button",{onClick:R,disabled:u.isLoading,class:"w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},P(x.$t("common.clear")),9,Il)])):(Dt(),Ot("div",Rl,[T("div",Bl,[T("h4",Wl,P(x.$t("tools.imageSteganography.decodeStep1")),1),T("p",Xl,P(x.$t("tools.imageSteganography.decodeStep1Desc")),1),T("div",Yl,[T("button",{onClick:it,disabled:u.isLoading,class:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},P(x.$t("tools.imageSteganography.selectImageToDecode")),9,Vl)])]),T("div",zl,[T("h4",Hl,P(x.$t("tools.imageSteganography.decodeStep2")),1),T("p",Gl,P(x.$t("tools.imageSteganography.decodeStep2Desc")),1),T("button",{onClick:H,disabled:!g.value||u.isLoading,class:"w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},P(x.$t("tools.imageSteganography.startDecoding")),9,Ul)]),c.value?(Dt(),Ot("div",Nl,[T("h4",ql,P(x.$t("tools.imageSteganography.decodedImagePreview")),1),T("div",$l,[T("img",{src:c.value||"",alt:"Decoded image preview",class:"max-w-full max-h-64 object-contain"},null,8,Kl)])])):pe("",!0),T("button",{onClick:rt,disabled:u.isLoading,class:"w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},P(x.$t("common.clear")),9,Jl)]))])])])]))}});export{tc as default};
