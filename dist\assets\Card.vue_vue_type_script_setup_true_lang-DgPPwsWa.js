import{d as v,i as n,c as a,o as t,f as l,P as m,X as d,a as r,Z as b,S as i,a0 as k,t as f}from"./index-CkZTMFXG.js";const $=["disabled"],C={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},S=v({__name:"Button",props:{variant:{default:"primary"},size:{default:"md"},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},iconLeft:{},iconRight:{}},setup(c){const p=c,o=n(()=>({sm:"px-3 py-1.5 text-sm rounded-lg",md:"px-4 py-2 text-sm rounded-xl",lg:"px-6 py-3 text-base rounded-xl"})[p.size]),g=n(()=>({primary:"bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 shadow-lg hover:shadow-glow",secondary:"bg-slate-700 hover:bg-slate-600 text-slate-100 focus:ring-slate-500 border border-slate-600",success:"bg-success-600 hover:bg-success-700 text-white focus:ring-success-500 shadow-lg",warning:"bg-warning-600 hover:bg-warning-700 text-white focus:ring-warning-500 shadow-lg",error:"bg-error-600 hover:bg-error-700 text-white focus:ring-error-500 shadow-lg",ghost:"bg-transparent hover:bg-slate-800/50 text-slate-300 hover:text-white focus:ring-slate-500 border border-slate-700/50"})[p.variant]);return(e,u)=>(t(),a("button",k({class:["inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-dark-900 disabled:opacity-50 disabled:cursor-not-allowed",o.value,g.value,"hover-lift"],disabled:e.disabled||e.loading},e.$attrs),[e.loading?(t(),a("svg",C,[...u[0]||(u[0]=[r("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),r("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)])])):l("",!0),e.iconLeft&&!e.loading?(t(),m(b(e.iconLeft),{key:1,class:i(["w-4 h-4",e.$slots.default?"mr-2":""])},null,8,["class"])):l("",!0),d(e.$slots,"default"),e.iconRight?(t(),m(b(e.iconRight),{key:2,class:i(["w-4 h-4",e.$slots.default?"ml-2":""])},null,8,["class"])):l("",!0)],16,$))}}),B={class:"flex items-center justify-between"},z={class:"flex items-center space-x-3"},x={key:0,class:"text-2xl"},P={class:"text-lg font-semibold text-slate-100"},L={key:0,class:"text-sm text-slate-400"},V=v({__name:"Card",props:{title:{},subtitle:{},icon:{},hover:{type:Boolean,default:!1},padding:{type:Boolean,default:!0},shadow:{type:Boolean,default:!1}},emits:["click"],setup(c,{emit:p}){const o=c,g=n(()=>o.padding?"p-6":""),e=n(()=>o.padding?"p-6 pb-4":"p-4"),u=n(()=>o.padding?o.$slots?.header||o.title?"px-6 pb-6":"p-6":""),w=n(()=>o.padding?"p-6 pt-4":"p-4");return(s,h)=>(t(),a("div",{class:i(["glass rounded-2xl border border-slate-700/30 transition-all duration-200",s.hover?"hover:border-slate-600/50 hover:shadow-glow hover-lift cursor-pointer":"",s.padding?g.value:"",s.shadow?"shadow-dark-lg":""]),onClick:h[0]||(h[0]=y=>s.$emit("click",y))},[s.$slots.header||s.title?(t(),a("div",{key:0,class:i(["border-b border-slate-700/30",e.value])},[d(s.$slots,"header",{},()=>[r("div",B,[r("div",z,[s.icon?(t(),a("div",x,f(s.icon),1)):l("",!0),r("div",null,[r("h3",P,f(s.title),1),s.subtitle?(t(),a("p",L,f(s.subtitle),1)):l("",!0)])]),d(s.$slots,"header-actions")])])],2)):l("",!0),r("div",{class:i(u.value)},[d(s.$slots,"default")],2),s.$slots.footer?(t(),a("div",{key:1,class:i(["border-t border-slate-700/30",w.value])},[d(s.$slots,"footer")],2)):l("",!0)],2))}});export{S as _,V as a};
