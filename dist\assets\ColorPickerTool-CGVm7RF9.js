import{d as Qt,u as Zt,r as y,a4 as T,w as te,a1 as ee,a2 as se,c as v,a as e,t as i,e as w,a3 as M,g as E,S as $,f as O,R as at,$ as lt,h as it,F as q,k as J,o as x,W as oe}from"./index-CkZTMFXG.js";import{u as ne}from"./useToast-virEbLJw.js";const re={class:"color-picker-tool"},ae={class:"bg-white rounded-lg shadow-md p-6 mb-8"},le={class:"text-2xl font-bold text-gray-800 mb-2"},ie={class:"text-gray-600 mb-6"},ce={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},de={class:"space-y-6"},ue={class:"bg-gray-50 p-6 rounded-lg"},ge={class:"text-lg font-semibold text-gray-800 mb-4"},me={class:"mb-6"},he={class:"mb-4"},pe={class:"flex space-x-2 mb-2"},be={class:"space-y-4"},ve={key:0},xe={class:"flex justify-between mb-1"},fe={class:"text-sm text-gray-500"},ye=["value"],ke={class:"flex justify-between mb-1"},Fe={class:"text-sm text-gray-500"},Ce=["value"],we={class:"flex justify-between mb-1"},_e={class:"text-sm text-gray-500"},Me=["value"],Ee={class:"flex justify-between mb-1"},$e={class:"text-sm text-gray-500"},Ie=["value"],Be={key:1},De={class:"flex justify-between mb-1"},Ae={class:"text-sm text-gray-500"},Pe=["value"],Re={class:"flex justify-between mb-1"},He={class:"text-sm text-gray-500"},je=["value"],Se={class:"flex justify-between mb-1"},Te={class:"text-sm text-gray-500"},Le=["value"],Ve={class:"flex justify-between mb-1"},Ue={class:"text-sm text-gray-500"},Ye=["value"],We={key:2},Ge={class:"flex justify-between mb-1"},ze={class:"text-sm text-gray-500"},Xe=["value"],Ke={class:"flex justify-between mb-1"},Ne={class:"text-sm text-gray-500"},Oe=["value"],qe={class:"flex justify-between mb-1"},Je={class:"text-sm text-gray-500"},Qe=["value"],Ze={class:"flex justify-between mb-1"},ts={class:"text-sm text-gray-500"},es=["value"],ss={key:3},os={class:"flex justify-between mb-1"},ns={class:"text-sm text-gray-500"},rs=["value"],as={class:"flex justify-between mb-1"},ls={class:"text-sm text-gray-500"},is=["value"],cs={class:"flex justify-between mb-1"},ds={class:"text-sm text-gray-500"},us=["value"],gs={class:"flex justify-between mb-1"},ms={class:"text-sm text-gray-500"},hs=["value"],ps={class:"flex justify-between mb-1"},bs={class:"text-sm text-gray-500"},vs=["value"],xs={class:"bg-gray-50 p-6 rounded-lg"},fs={class:"text-lg font-semibold text-gray-800 mb-4"},ys={class:"space-y-2"},ks={class:"text-gray-600"},Fs={class:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"},Cs={key:1},ws={class:"flex justify-between items-center mb-2"},_s={class:"text-sm text-gray-600"},Ms={class:"flex space-x-2"},Es={class:"relative"},$s=["src"],Is={key:0,class:"text-sm text-gray-500 mt-2"},Bs={class:"mt-4 flex space-x-2"},Ds={class:"space-y-6"},As={class:"bg-gray-50 p-6 rounded-lg"},Ps={class:"flex justify-between items-center mb-4"},Rs={class:"text-lg font-semibold text-gray-800"},Hs={class:"grid grid-cols-6 gap-2"},js=["onClick"],Ss={class:"p-4 pb-2 flex justify-between items-center sticky top-0 bg-white border-b"},Ts={class:"text-lg text-slate-800 font-semibold"},Ls={class:"p-4"},Vs={class:"space-y-6"},Us={class:"text-md font-medium text-slate-800"},Ys={class:"grid grid-cols-10 gap-2"},Ws=["onClick","title"],Gs={class:"p-4 flex justify-end gap-2 sticky bottom-0 bg-white border-t"},zs={class:"bg-gray-50 p-6 rounded-lg"},Xs={class:"text-lg font-semibold text-gray-800 mb-4"},Ks={class:"space-y-4"},Ns={class:"flex items-center justify-between p-3 bg-white rounded border"},Os={class:"flex items-center space-x-2"},qs={class:"flex items-center justify-between p-3 bg-white rounded border"},Js={class:"flex items-center space-x-2"},Qs={class:"flex items-center justify-between p-3 bg-white rounded border"},Zs={class:"flex items-center space-x-2"},to={class:"flex items-center justify-between p-3 bg-white rounded border"},eo={class:"flex items-center space-x-2"},so={class:"flex items-center justify-between p-3 bg-white rounded border"},oo={class:"flex items-center space-x-2"},no={class:"flex items-center justify-between p-3 bg-white rounded border"},ro={class:"flex items-center space-x-2"},ao={class:"bg-gray-50 p-6 rounded-lg"},lo={class:"text-lg font-semibold text-gray-800 mb-4"},io={class:"grid grid-cols-2 gap-4"},co={class:"text-sm text-gray-600 mb-2"},uo={class:"h-20 rounded border flex items-center justify-center",style:{"background-color":"white"}},go={class:"text-sm text-gray-600 mb-2"},mo={class:"h-20 rounded border flex items-center justify-center",style:{"background-color":"#333"}},ho={class:"bg-white rounded-lg shadow-md p-6"},po={class:"text-xl font-bold text-gray-800 mb-4"},bo={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},vo={class:"bg-blue-50 p-5 rounded-lg border-l-4 border-blue-500"},xo={class:"text-lg font-semibold text-gray-900 mb-2"},fo={class:"text-gray-600 text-sm"},yo={class:"bg-green-50 p-5 rounded-lg border-l-4 border-green-500"},ko={class:"text-lg font-semibold text-gray-900 mb-2"},Fo={class:"text-gray-600 text-sm"},Co={class:"bg-purple-50 p-5 rounded-lg border-l-4 border-purple-500"},wo={class:"text-lg font-semibold text-gray-900 mb-2"},_o={class:"text-gray-600 text-sm"},Mo=Qt({__name:"ColorPickerTool",setup(Eo){const{t:I}=Zt(),{success:Q,error:D}=ne(),L=y(null),V=y(null),_=y(null),U=y(null),F=y(null),Z=y(!1),p=y(!1),A=y(!1),P=T({x:0,y:0}),R=T({x:0,y:0}),Y=y(150),ct=y(5),s=T({hex:"#3b82f6",rgb:{r:59,g:130,b:246,a:1},hsl:{h:217,s:91,l:60},hsv:{h:217,s:76,v:96},cmyk:{c:76,m:47,y:0,k:4}}),d=T({hex:"#3b82f6",rgb:"rgb(59, 130, 246)",rgba:"rgba(59, 130, 246, 1.00)",hsl:"hsl(217, 91%, 60%)",hsv:"hsv(217, 76%, 96%)",cmyk:"cmyk(76%, 47%, 0%, 4%)"}),f=y("rgba"),dt=["#ef4444","#f97316","#eab308","#22c55e","#3b82f6","#6366f1","#8b5cf6","#ec4899","#64748b","#000000","#ffffff","#f4f4f5"],W=y(!1),ut=[{name:"Red",colors:["#FFEBEE","#FFCDD2","#EF9A9A","#E57373","#EF5350","#F44336","#E53935","#D32F2F","#C62828","#B71C1C"]},{name:"Pink",colors:["#FCE4EC","#F8BBD0","#F48FB1","#F06292","#EC407A","#E91E63","#D81B60","#C2185B","#AD1457","#880E4F"]},{name:"Purple",colors:["#F3E5F5","#E1BEE7","#CE93D8","#BA68C8","#AB47BC","#9C27B0","#8E24AA","#7B1FA2","#6A1B9A","#4A148C"]},{name:"Deep Purple",colors:["#EDE7F6","#D1C4E9","#B39DDB","#9575CD","#7E57C2","#673AB7","#5E35B1","#512DA8","#4527A0","#311B92"]},{name:"Indigo",colors:["#E8EAF6","#C5CAE9","#9FA8DA","#7986CB","#5C6BC0","#3F51B5","#3949AB","#303F9F","#283593","#1A237E"]},{name:"Blue",colors:["#E3F2FD","#BBDEFB","#90CAF9","#64B5F6","#42A5F5","#2196F3","#1E88E5","#1976D2","#1565C0","#0D47A1"]},{name:"Light Blue",colors:["#E1F5FE","#B3E5FC","#81D4FA","#4FC3F7","#29B6F6","#03A9F4","#039BE5","#0288D1","#0277BD","#01579B"]},{name:"Cyan",colors:["#E0F7FA","#B2EBF2","#80DEEA","#4DD0E1","#26C6DA","#00BCD4","#00ACC1","#0097A7","#00838F","#006064"]},{name:"Teal",colors:["#E0F2F1","#B2DFDB","#80CBC4","#4DB6AC","#26A69A","#009688","#00897B","#00796B","#00695C","#004D40"]},{name:"Green",colors:["#E8F5E9","#C8E6C9","#A5D6A7","#81C784","#66BB6A","#4CAF50","#43A047","#388E3C","#2E7D32","#1B5E20"]},{name:"Light Green",colors:["#F1F8E9","#DCEDC8","#C5E1A5","#AED581","#9CCC65","#8BC34A","#7CB342","#689F38","#558B2F","#33691E"]},{name:"Lime",colors:["#F9FBE7","#FFF9C4","#FFF59D","#FFF176","#FFEE58","#FFEB3B","#FDD835","#FBC02D","#F9A825","#F57F17"]},{name:"Yellow",colors:["#FFFDE7","#FFF9C4","#FFF59D","#FFF176","#FFEE58","#FFEB3B","#FDD835","#FBC02D","#F9A825","#F57F17"]},{name:"Amber",colors:["#FFF8E1","#FFECB3","#FFE082","#FFD54F","#FFCA28","#FFC107","#FFB300","#FFA000","#FF8F00","#FF6F00"]},{name:"Orange",colors:["#FFF3E0","#FFE0B2","#FFCC80","#FFB74D","#FFA726","#FF9800","#FB8C00","#F57C00","#EF6C00","#E65100"]},{name:"Deep Orange",colors:["#FBE9E7","#FFCCBC","#FFAB91","#FF8A65","#FF7043","#FF5722","#F4511E","#E64A19","#D84315","#BF360C"]},{name:"Brown",colors:["#EFEBE9","#D7CCC8","#BCAAA4","#A1887F","#8D6E63","#795548","#6D4C41","#5D4037","#4E342E","#3E2723"]},{name:"Grey",colors:["#FAFAFA","#F5F5F5","#EEEEEE","#E0E0E0","#BDBDBD","#9E9E9E","#757575","#616161","#424242","#212121"]},{name:"Blue Grey",colors:["#ECEFF1","#CFD8DC","#B0BEC5","#90A4AE","#78909C","#607D8B","#546E7A","#455A64","#37474F","#263238"]}];te(s,()=>{z()});const G=o=>{const t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(o);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16),a:1}:{r:0,g:0,b:0,a:1}},gt=(o,t,n)=>"#"+[o,t,n].map(r=>{const a=r.toString(16);return a.length===1?"0"+a:a}).join(""),mt=(o,t,n)=>{o/=255,t/=255,n/=255;const r=Math.max(o,t,n),a=Math.min(o,t,n);let l=0;const c=(r+a)/2;let h;if(r===a)l=h=0;else{const u=r-a;switch(h=c>.5?u/(2-r-a):u/(r+a),r){case o:l=(t-n)/u+(t<n?6:0);break;case t:l=(n-o)/u+2;break;case n:l=(o-t)/u+4;break}l/=6}return{h:Math.round(l*360),s:Math.round(h*100),l:Math.round(c*100)}},tt=(o,t,n)=>{o/=360,t/=100,n/=100;let r,a,l;if(t===0)r=a=l=n;else{const c=(g,k,m)=>(m<0&&(m+=1),m>1&&(m-=1),m<.16666666666666666?g+(k-g)*6*m:m<.5?k:m<.6666666666666666?g+(k-g)*(.6666666666666666-m)*6:g),h=n<.5?n*(1+t):n+t-n*t,u=2*n-h;r=c(u,h,o+1/3),a=c(u,h,o),l=c(u,h,o-1/3)}return{r:Math.round(r*255),g:Math.round(a*255),b:Math.round(l*255),a:1}},ht=(o,t,n)=>{o/=255,t/=255,n/=255;const r=Math.max(o,t,n),a=Math.min(o,t,n);let l=0;const c=r,h=r===0?0:(r-a)/r;if(r===a)l=0;else{switch(r){case o:l=(t-n)/(r-a)+(t<n?6:0);break;case t:l=(n-o)/(r-a)+2;break;case n:l=(o-t)/(r-a)+4;break}l/=6}return{h:Math.round(l*360),s:Math.round(h*100),v:Math.round(c*100)}},et=(o,t,n)=>{o/=360,t/=100,n/=100;let r=0,a=0,l=0;const c=Math.floor(o*6),h=o*6-c,u=n*(1-t),g=n*(1-h*t),k=n*(1-(1-h)*t);switch(c%6){case 0:r=n,a=k,l=u;break;case 1:r=g,a=n,l=u;break;case 2:r=u,a=n,l=k;break;case 3:r=u,a=g,l=n;break;case 4:r=k,a=u,l=n;break;case 5:r=n,a=u,l=g;break}return{r:Math.round(r*255),g:Math.round(a*255),b:Math.round(l*255),a:1}},pt=(o,t,n)=>{let r=1-o/255,a=1-t/255,l=1-n/255;const c=Math.min(r,a,l);return c===1?{c:0,m:0,y:0,k:100}:(r=(r-c)/(1-c),a=(a-c)/(1-c),l=(l-c)/(1-c),{c:Math.round(r*100),m:Math.round(a*100),y:Math.round(l*100),k:Math.round(c*100)})},st=(o,t,n,r)=>{o/=100,t/=100,n/=100,r/=100;const a=255*(1-o)*(1-r),l=255*(1-t)*(1-r),c=255*(1-n)*(1-r);return{r:Math.round(a),g:Math.round(l),b:Math.round(c),a:1}},b=()=>{const o=mt(s.rgb.r,s.rgb.g,s.rgb.b);s.hsl=o;const t=ht(s.rgb.r,s.rgb.g,s.rgb.b);s.hsv=t;const n=pt(s.rgb.r,s.rgb.g,s.rgb.b);s.cmyk=n,s.hex=gt(s.rgb.r,s.rgb.g,s.rgb.b),z()},z=()=>{d.hex=s.hex,d.rgb=`rgb(${s.rgb.r}, ${s.rgb.g}, ${s.rgb.b})`,d.rgba=`rgba(${s.rgb.r}, ${s.rgb.g}, ${s.rgb.b}, ${parseFloat(String(s.rgb.a)).toFixed(2)})`,d.hsl=`hsl(${Math.round(s.hsl.h)}, ${Math.round(s.hsl.s)}%, ${Math.round(s.hsl.l)}%)`,d.hsv=`hsv(${Math.round(s.hsv.h)}, ${Math.round(s.hsv.s)}%, ${Math.round(s.hsv.v)}%)`,d.cmyk=`cmyk(${Math.round(s.cmyk.c)}%, ${Math.round(s.cmyk.m)}%, ${Math.round(s.cmyk.y)}%, ${Math.round(s.cmyk.k)}%)`},bt=()=>{L.value&&L.value.click()},vt=()=>{const o=G(s.hex);s.rgb={...o,a:s.rgb.a},b()},X=()=>{b()},xt=()=>{s.rgb.a=parseFloat(String(s.rgb.a))||0,b()},ft=o=>{const t=o.target;s.rgb.r=parseInt(t.value),X()},yt=o=>{const t=o.target;s.rgb.g=parseInt(t.value),X()},kt=o=>{const t=o.target;s.rgb.b=parseInt(t.value),X()},H=o=>{const t=o.target;s.rgb.a=parseFloat(t.value),xt()},K=()=>{s.hsl.h=Math.max(0,Math.min(360,s.hsl.h)),s.hsl.s=Math.max(0,Math.min(100,s.hsl.s)),s.hsl.l=Math.max(0,Math.min(100,s.hsl.l));const o=tt(s.hsl.h,s.hsl.s,s.hsl.l);s.rgb={...o,a:s.rgb.a},b()},Ft=o=>{const t=o.target,n=parseInt(t.value),r={...s.hsl,h:Math.min(360,Math.max(0,n))};s.hsl=r,K()},Ct=o=>{const t=o.target,n=parseInt(t.value),r={...s.hsl,s:Math.min(100,Math.max(0,n))};s.hsl=r,K()},wt=o=>{const t=o.target,n=parseInt(t.value),r={...s.hsl,l:Math.min(100,Math.max(0,n))};s.hsl=r,K()},N=()=>{s.hsv.h=Math.max(0,Math.min(360,s.hsv.h)),s.hsv.s=Math.max(0,Math.min(100,s.hsv.s)),s.hsv.v=Math.max(0,Math.min(100,s.hsv.v));const o=et(s.hsv.h,s.hsv.s,s.hsv.v);s.rgb={...o,a:s.rgb.a},b()},_t=o=>{const t=o.target,n=parseInt(t.value),r={...s.hsv,h:Math.min(360,Math.max(0,n))};s.hsv=r,N()},Mt=o=>{const t=o.target,n=parseInt(t.value),r={...s.hsv,s:Math.min(100,Math.max(0,n))};s.hsv=r,N()},Et=o=>{const t=o.target,n=parseInt(t.value),r={...s.hsv,v:Math.min(100,Math.max(0,n))};s.hsv=r,N()},j=()=>{s.cmyk.c=Math.max(0,Math.min(100,s.cmyk.c)),s.cmyk.m=Math.max(0,Math.min(100,s.cmyk.m)),s.cmyk.y=Math.max(0,Math.min(100,s.cmyk.y)),s.cmyk.k=Math.max(0,Math.min(100,s.cmyk.k));const o=st(s.cmyk.c,s.cmyk.m,s.cmyk.y,s.cmyk.k);s.rgb={...o,a:s.rgb.a},b()},$t=o=>{const t=o.target,n=parseInt(t.value),r={...s.cmyk,c:Math.min(100,Math.max(0,n))};s.cmyk=r,j()},It=o=>{const t=o.target,n=parseInt(t.value),r={...s.cmyk,m:Math.min(100,Math.max(0,n))};s.cmyk=r,j()},Bt=o=>{const t=o.target,n=parseInt(t.value),r={...s.cmyk,y:Math.min(100,Math.max(0,n))};s.cmyk=r,j()},Dt=o=>{const t=o.target,n=parseInt(t.value),r={...s.cmyk,k:Math.min(100,Math.max(0,n))};s.cmyk=r,j()},ot=o=>{s.hex=o;const t=G(o);s.rgb={...t,a:s.rgb.a},b()},At=()=>{W.value=!0},S=()=>{W.value=!1},Pt=o=>{ot(o),S()},nt=()=>{V.value&&V.value.click()},Rt=o=>{const t=o.target;if(t.files&&t.files[0]){const n=t.files[0],r=new FileReader;r.onload=a=>{F.value=a.target?.result,p.value=!1},r.readAsDataURL(n),t.value=""}},Ht=o=>{if(o.preventDefault(),Z.value=!1,o.dataTransfer?.files&&o.dataTransfer.files[0]){const t=o.dataTransfer.files[0];if(t.type.match("image.*")){const n=new FileReader;n.onload=r=>{F.value=r.target?.result,p.value=!1},n.readAsDataURL(t)}}},jt=async()=>{try{const o=await navigator.clipboard.read();for(const t of o)for(const n of t.types)if(n.startsWith("image/")){const r=await t.getType(n),a=new FileReader;a.onload=l=>{F.value=l.target?.result,p.value=!1},a.readAsDataURL(r);return}D(I("tools.colorPicker.noImageInClipboard"))}catch{D(I("tools.colorPicker.pasteFailed"))}},St=()=>{F.value=null,p.value=!1},Tt=()=>{p.value=!p.value},Lt=o=>{if(!p.value||!_.value||!F.value)return;const n=_.value.getBoundingClientRect();R.x=o.clientX-n.left,R.y=o.clientY-n.top,P.x=o.clientX-n.left+80,P.y=o.clientY-n.top-80,A.value=!0,Ut(o)},Vt=()=>{A.value=!1},Ut=o=>{if(!_.value||!U.value)return;const t=_.value,n=U.value,r=n.getContext("2d");if(!r)return;const a=t.getBoundingClientRect(),l=t.naturalWidth/a.width,c=t.naturalHeight/a.height,h=Math.floor((o.clientX-a.left)*l),u=Math.floor((o.clientY-a.top)*c),g=Y.value;n.width=g,n.height=g;const k=ct.value,m=g/k;r.drawImage(t,h-m/2,u-m/2,m,m,0,0,g,g);const C=g/2;r.strokeStyle="#ffffff",r.lineWidth=2,r.beginPath(),r.moveTo(C,C-10),r.lineTo(C,C+10),r.moveTo(C-10,C),r.lineTo(C+10,C),r.stroke(),r.strokeStyle="#000000",r.lineWidth=2,r.strokeRect(0,0,g,g)},Yt=o=>{if(!p.value||!_.value||!F.value)return;const t=_.value,n=document.createElement("canvas"),r=n.getContext("2d");if(!r)return;n.width=t.naturalWidth,n.height=t.naturalHeight,r.drawImage(t,0,0,t.naturalWidth,t.naturalHeight);const a=t.getBoundingClientRect(),l=t.naturalWidth/a.width,c=t.naturalHeight/a.height,h=Math.floor((o.clientX-a.left)*l),u=Math.floor((o.clientY-a.top)*c),g=Math.max(0,Math.min(t.naturalWidth-1,h)),k=Math.max(0,Math.min(t.naturalHeight-1,u));try{const m=r.getImageData(g,k,1,1).data,C=m[0],Ot=m[1],qt=m[2],Jt=parseFloat(String(s.rgb.a))||1;s.rgb={r:C,g:Ot,b:qt,a:Jt},b(),Q(I("tools.colorPicker.colorPicked"))}catch(m){console.error("Color picking error:",m),D(I("tools.colorPicker.colorPickError"))}},Wt=()=>{if(!/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(d.hex))return;s.hex=d.hex;const t=G(s.hex);s.rgb={...t,a:s.rgb.a},b()},Gt=()=>{const o=/^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/,t=d.rgb.match(o);if(!t||t.length!==4)return;const n=parseInt(t[1]),r=parseInt(t[2]),a=parseInt(t[3]);n<0||n>255||r<0||r>255||a<0||a>255||(s.rgb={...s.rgb,r:n,g:r,b:a},b())},zt=()=>{const o=/^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(0|1|0\.\d+|1\.0+)\s*\)$/,t=d.rgba.match(o);if(!t||t.length!==5)return;const n=parseInt(t[1]),r=parseInt(t[2]),a=parseInt(t[3]),l=parseFloat(t[4]);n<0||n>255||r<0||r>255||a<0||a>255||l<0||l>1||(s.rgb={r:n,g:r,b:a,a:l},b())},Xt=()=>{const o=/^hsl\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*\)$/,t=d.hsl.match(o);if(!t||t.length!==4)return;const n=parseInt(t[1]),r=parseInt(t[2]),a=parseInt(t[3]);if(n<0||n>360||r<0||r>100||a<0||a>100)return;const l=tt(n,r,a);s.rgb={...l,a:s.rgb.a},b()},Kt=()=>{const o=/^hsv\(\s*(\d{1,3})\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*\)$/,t=d.hsv.match(o);if(!t||t.length!==4)return;const n=parseInt(t[1]),r=parseInt(t[2]),a=parseInt(t[3]);if(n<0||n>360||r<0||r>100||a<0||a>100)return;const l=et(n,r,a);s.rgb={...l,a:s.rgb.a},b()},Nt=()=>{const o=/^cmyk\(\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*,\s*(\d{1,3})%\s*\)$/,t=d.cmyk.match(o);if(!t||t.length!==5)return;const n=parseInt(t[1]),r=parseInt(t[2]),a=parseInt(t[3]),l=parseInt(t[4]);if(n<0||n>100||r<0||r>100||a<0||a>100||l<0||l>100)return;const c=st(n,r,a,l);s.rgb={...c,a:s.rgb.a},b()},B=o=>{navigator.clipboard.writeText(o).then(()=>{Q(I("toast.copied"))}).catch(()=>{D(I("toast.copyFailed"))})};ee(()=>{window.addEventListener("dragover",o=>{o.dataTransfer&&(o.dataTransfer.dropEffect="copy")}),window.addEventListener("drop",o=>{o.preventDefault()}),window.addEventListener("paste",rt),z()}),se(()=>{window.removeEventListener("dragover",()=>{}),window.removeEventListener("drop",()=>{}),window.removeEventListener("paste",rt)});const rt=o=>{if(o.preventDefault(),!!o.clipboardData){if(o.clipboardData.files&&o.clipboardData.files.length>0){const t=o.clipboardData.files[0];if(t.type.match("image.*")){const n=new FileReader;n.onload=r=>{F.value=r.target?.result,p.value=!1},n.readAsDataURL(t);return}}if(o.clipboardData.items)for(let t=0;t<o.clipboardData.items.length;t++){const n=o.clipboardData.items[t];if(n.type.indexOf("image")!==-1){const r=n.getAsFile();if(r){const a=new FileReader;a.onload=l=>{F.value=l.target?.result,p.value=!1},a.readAsDataURL(r);return}}}}};return(o,t)=>(x(),v("div",re,[e("div",ae,[e("h2",le,i(o.$t("tools.colorPicker.title")),1),e("p",ie,i(o.$t("tools.colorPicker.description")),1),e("div",ce,[e("div",de,[e("div",ue,[e("h3",ge,i(o.$t("tools.colorPicker.colorPicker")),1),e("div",me,[e("div",{class:"w-full h-32 rounded-lg mb-4 cursor-pointer border-2 border-gray-300",style:M({backgroundColor:s.hex}),onClick:bt},null,4),w(e("input",{ref_key:"colorPickerInput",ref:L,type:"color","onUpdate:modelValue":t[0]||(t[0]=n=>s.hex=n),class:"hidden",onInput:vt},null,544),[[E,s.hex]])]),e("div",he,[e("div",pe,[e("button",{onClick:t[1]||(t[1]=n=>f.value="rgba"),class:$(["px-3 py-1 text-sm rounded transition-colors",f.value==="rgba"?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"])}," RGBA ",2),e("button",{onClick:t[2]||(t[2]=n=>f.value="hsl"),class:$(["px-3 py-1 text-sm rounded transition-colors",f.value==="hsl"?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"])}," HSL ",2),e("button",{onClick:t[3]||(t[3]=n=>f.value="hsv"),class:$(["px-3 py-1 text-sm rounded transition-colors",f.value==="hsv"?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"])}," HSV ",2),e("button",{onClick:t[4]||(t[4]=n=>f.value="cmyk"),class:$(["px-3 py-1 text-sm rounded transition-colors",f.value==="cmyk"?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"])}," CMYK ",2)])]),e("div",be,[f.value==="rgba"?(x(),v("div",ve,[e("div",null,[e("div",xe,[t[19]||(t[19]=e("span",{class:"text-sm font-medium text-gray-700"},"R",-1)),e("span",fe,i(s.rgb.r),1)]),e("input",{type:"range",min:"0",max:"255",value:s.rgb.r,onInput:ft,class:"w-full h-2 bg-red-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,ye)]),e("div",null,[e("div",ke,[t[20]||(t[20]=e("span",{class:"text-sm font-medium text-gray-700"},"G",-1)),e("span",Fe,i(s.rgb.g),1)]),e("input",{type:"range",min:"0",max:"255",value:s.rgb.g,onInput:yt,class:"w-full h-2 bg-green-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,Ce)]),e("div",null,[e("div",we,[t[21]||(t[21]=e("span",{class:"text-sm font-medium text-gray-700"},"B",-1)),e("span",_e,i(s.rgb.b),1)]),e("input",{type:"range",min:"0",max:"255",value:s.rgb.b,onInput:kt,class:"w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,Me)]),e("div",null,[e("div",Ee,[t[22]||(t[22]=e("span",{class:"text-sm font-medium text-gray-700"},"A",-1)),e("span",$e,i(parseFloat(String(s.rgb.a)).toFixed(2)),1)]),e("input",{type:"range",min:"0",max:"1",step:"0.01",value:s.rgb.a,onInput:H,class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,Ie)])])):f.value==="hsl"?(x(),v("div",Be,[e("div",null,[e("div",De,[t[23]||(t[23]=e("span",{class:"text-sm font-medium text-gray-700"},"H",-1)),e("span",Ae,i(s.hsl.h),1)]),e("input",{type:"range",min:"0",max:"360",value:s.hsl.h,onInput:Ft,class:"w-full h-2 bg-red-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,Pe)]),e("div",null,[e("div",Re,[t[24]||(t[24]=e("span",{class:"text-sm font-medium text-gray-700"},"S",-1)),e("span",He,i(s.hsl.s),1)]),e("input",{type:"range",min:"0",max:"100",value:s.hsl.s,onInput:Ct,class:"w-full h-2 bg-green-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,je)]),e("div",null,[e("div",Se,[t[25]||(t[25]=e("span",{class:"text-sm font-medium text-gray-700"},"L",-1)),e("span",Te,i(s.hsl.l),1)]),e("input",{type:"range",min:"0",max:"100",value:s.hsl.l,onInput:wt,class:"w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,Le)]),e("div",null,[e("div",Ve,[t[26]||(t[26]=e("span",{class:"text-sm font-medium text-gray-700"},"A",-1)),e("span",Ue,i(parseFloat(String(s.rgb.a)).toFixed(2)),1)]),e("input",{type:"range",min:"0",max:"1",step:"0.01",value:s.rgb.a,onInput:H,class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,Ye)])])):f.value==="hsv"?(x(),v("div",We,[e("div",null,[e("div",Ge,[t[27]||(t[27]=e("span",{class:"text-sm font-medium text-gray-700"},"H",-1)),e("span",ze,i(s.hsv.h),1)]),e("input",{type:"range",min:"0",max:"360",value:s.hsv.h,onInput:_t,class:"w-full h-2 bg-red-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,Xe)]),e("div",null,[e("div",Ke,[t[28]||(t[28]=e("span",{class:"text-sm font-medium text-gray-700"},"S",-1)),e("span",Ne,i(s.hsv.s),1)]),e("input",{type:"range",min:"0",max:"100",value:s.hsv.s,onInput:Mt,class:"w-full h-2 bg-green-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,Oe)]),e("div",null,[e("div",qe,[t[29]||(t[29]=e("span",{class:"text-sm font-medium text-gray-700"},"V",-1)),e("span",Je,i(s.hsv.v),1)]),e("input",{type:"range",min:"0",max:"100",value:s.hsv.v,onInput:Et,class:"w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,Qe)]),e("div",null,[e("div",Ze,[t[30]||(t[30]=e("span",{class:"text-sm font-medium text-gray-700"},"A",-1)),e("span",ts,i(parseFloat(String(s.rgb.a)).toFixed(2)),1)]),e("input",{type:"range",min:"0",max:"1",step:"0.01",value:s.rgb.a,onInput:H,class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,es)])])):f.value==="cmyk"?(x(),v("div",ss,[e("div",null,[e("div",os,[t[31]||(t[31]=e("span",{class:"text-sm font-medium text-gray-700"},"C",-1)),e("span",ns,i(s.cmyk.c),1)]),e("input",{type:"range",min:"0",max:"100",value:s.cmyk.c,onInput:$t,class:"w-full h-2 bg-red-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,rs)]),e("div",null,[e("div",as,[t[32]||(t[32]=e("span",{class:"text-sm font-medium text-gray-700"},"M",-1)),e("span",ls,i(s.cmyk.m),1)]),e("input",{type:"range",min:"0",max:"100",value:s.cmyk.m,onInput:It,class:"w-full h-2 bg-green-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,is)]),e("div",null,[e("div",cs,[t[33]||(t[33]=e("span",{class:"text-sm font-medium text-gray-700"},"Y",-1)),e("span",ds,i(s.cmyk.y),1)]),e("input",{type:"range",min:"0",max:"100",value:s.cmyk.y,onInput:Bt,class:"w-full h-2 bg-blue-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,us)]),e("div",null,[e("div",gs,[t[34]||(t[34]=e("span",{class:"text-sm font-medium text-gray-700"},"K",-1)),e("span",ms,i(s.cmyk.k),1)]),e("input",{type:"range",min:"0",max:"100",value:s.cmyk.k,onInput:Dt,class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,hs)]),e("div",null,[e("div",ps,[t[35]||(t[35]=e("span",{class:"text-sm font-medium text-gray-700"},"A",-1)),e("span",bs,i(parseFloat(String(s.rgb.a)).toFixed(2)),1)]),e("input",{type:"range",min:"0",max:"1",step:"0.01",value:s.rgb.a,onInput:H,class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-fix"},null,40,vs)])])):O("",!0)])]),e("div",xs,[e("h3",fs,i(o.$t("tools.colorPicker.imagePicker")),1),F.value?(x(),v("div",Cs,[e("div",ws,[e("p",_s,i(o.$t("tools.colorPicker.imagePreview")),1),e("div",Ms,[e("button",{onClick:Tt,class:$(["px-3 py-1 text-sm rounded transition-colors",p.value?"bg-red-600 text-white hover:bg-red-700":"bg-blue-600 text-white hover:bg-blue-700"])},i(p.value?o.$t("tools.colorPicker.cancelPick"):o.$t("tools.colorPicker.pickColor")),3),e("button",{onClick:St,class:"px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"},i(o.$t("common.clear")),1)])]),e("div",Es,[e("img",{src:F.value,alt:"Preview",class:$(["max-w-full h-auto rounded-lg",{"cursor-crosshair":p.value}]),onClick:Yt,onMousemove:Lt,onMouseleave:Vt,ref_key:"previewImage",ref:_},null,42,$s),w(e("canvas",{ref_key:"magnifierCanvas",ref:U,class:"absolute border-2 border-white shadow-lg rounded-lg pointer-events-none",style:M({width:Y.value+"px",height:Y.value+"px",left:P.x+"px",top:P.y+"px",transform:"translate(-50%, -50%)"})},null,4),[[lt,A.value&&p.value]]),w(e("div",{class:"absolute w-4 h-4 border-2 border-white pointer-events-none",style:M({left:R.x+"px",top:R.y+"px",transform:"translate(-50%, -50%)"})},null,4),[[lt,A.value&&p.value]])]),p.value?(x(),v("p",Is,[it(i(o.$t("tools.colorPicker.clickToPick"))+" ",1),t[37]||(t[37]=e("br",null,null,-1)),it(" "+i(o.$t("tools.colorPicker.keepPickingUntilCancel")),1)])):O("",!0),e("div",Bs,[e("button",{onClick:nt,class:"px-3 py-1 text-sm bg-gray-200 rounded hover:bg-gray-300 transition-colors"},i(o.$t("tools.colorPicker.selectImage")),1),e("button",{onClick:jt,class:"px-3 py-1 text-sm bg-gray-200 rounded hover:bg-gray-300 transition-colors"},i(o.$t("common.paste")),1)])])):(x(),v("div",{key:0,class:$(["border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer mb-4",{"border-blue-500 bg-blue-50":Z.value}]),onDrop:Ht,onDragover:t[5]||(t[5]=at(()=>{},["prevent"])),onClick:nt},[e("input",{ref_key:"imageInput",ref:V,type:"file",accept:"image/*",class:"hidden",onChange:Rt},null,544),e("div",ys,[t[36]||(t[36]=e("div",{class:"text-2xl"},"🎨",-1)),e("p",ks,i(o.$t("tools.colorPicker.dropImage")),1),e("button",Fs,i(o.$t("tools.colorPicker.selectImage")),1)])],34))])]),e("div",Ds,[e("div",As,[e("div",Ps,[e("h3",Rs,i(o.$t("tools.colorPicker.commonColors")),1),e("button",{onClick:At,class:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"},i(o.$t("common.more")),1)]),e("div",Hs,[(x(),v(q,null,J(dt,n=>e("div",{key:n,class:"w-8 h-8 rounded cursor-pointer border border-gray-300 hover:scale-110 transition-transform",style:M({backgroundColor:n}),onClick:r=>ot(n)},null,12,js)),64))])]),W.value?(x(),v("div",{key:0,class:"fixed inset-0 bg-slate-950/50 flex justify-center items-center z-[9999]",onClick:S,"data-testid":"color-modal"},[e("div",{class:"bg-white rounded-xl shadow-2xl shadow-slate-950/5 border border-slate-200 w-11/12 max-w-4xl max-h-[90vh] overflow-y-auto",onClick:t[6]||(t[6]=at(()=>{},["stop"]))},[e("div",Ss,[e("h1",Ts,i(o.$t("tools.colorPicker.commonColors")),1),e("button",{type:"button",onClick:S,class:"inline-grid place-items-center border align-middle select-none font-sans font-medium text-center transition-all duration-300 ease-in disabled:opacity-50 disabled:shadow-none disabled:pointer-events-none data-[shape=circular]:rounded-full text-sm min-w-[34px] min-h-[34px] rounded-md bg-transparent border-transparent text-slate-200-foreground hover:bg-slate-200/10 hover:border-slate-200/10 shadow-none hover:shadow-none outline-none absolute right-2 top-2","aria-label":"Close"},[...t[38]||(t[38]=[e("svg",{width:"1.5em",height:"1.5em","stroke-width":"1.5",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",color:"currentColor",class:"h-5 w-5"},[e("path",{d:"M6.75827 17.2426L12.0009 12M17.2435 6.75736L12.0009 12M12.0009 12L6.75827 6.75736M12.0009 12L17.2435 17.2426",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round"})],-1)])])]),e("div",Ls,[e("div",Vs,[(x(),v(q,null,J(ut,n=>e("div",{key:n.name,class:"space-y-2","data-testid":"color-group"},[e("h2",Us,i(n.name),1),e("div",Ys,[(x(!0),v(q,null,J(n.colors,r=>(x(),v("div",{key:r,class:"w-8 h-8 rounded cursor-pointer border border-gray-300 hover:scale-110 transition-transform",style:M({backgroundColor:r}),onClick:a=>Pt(r),title:r},null,12,Ws))),128))])])),64))])]),e("div",Gs,[e("button",{type:"button",onClick:S,class:"inline-flex items-center justify-center border align-middle select-none font-sans font-medium text-center transition-all duration-300 ease-in disabled:opacity-50 disabled:shadow-none disabled:cursor-not-allowed data-[shape=pill]:rounded-full data-[width=full]:w-full focus:shadow-none text-sm rounded-md py-2 px-4 bg-transparent border-transparent text-red-500 hover:bg-red-500/10 hover:border-red-500/10 shadow-none hover:shadow-none outline-none"},i(o.$t("common.close")),1)])])])):O("",!0),e("div",zs,[e("h3",Xs,i(o.$t("tools.colorPicker.conversions")),1),e("div",Ks,[e("div",Ns,[t[39]||(t[39]=e("span",{class:"font-medium"},"HEX",-1)),e("div",Os,[w(e("input",{"onUpdate:modelValue":t[7]||(t[7]=n=>d.hex=n),type:"text",class:"w-40 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:Wt},null,544),[[E,d.hex]]),e("button",{onClick:t[8]||(t[8]=n=>B(s.hex)),class:"px-2 py-1 text-xs bg-gray-200 rounded hover:bg-gray-300"},i(o.$t("common.copy")),1)])]),e("div",qs,[t[40]||(t[40]=e("span",{class:"font-medium"},"RGB",-1)),e("div",Js,[w(e("input",{"onUpdate:modelValue":t[9]||(t[9]=n=>d.rgb=n),type:"text",class:"w-48 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:Gt},null,544),[[E,d.rgb]]),e("button",{onClick:t[10]||(t[10]=n=>B(`rgb(${s.rgb.r}, ${s.rgb.g}, ${s.rgb.b})`)),class:"px-2 py-1 text-xs bg-gray-200 rounded hover:bg-gray-300"},i(o.$t("common.copy")),1)])]),e("div",Qs,[t[41]||(t[41]=e("span",{class:"font-medium"},"RGBA",-1)),e("div",Zs,[w(e("input",{"onUpdate:modelValue":t[11]||(t[11]=n=>d.rgba=n),type:"text",class:"w-52 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:zt},null,544),[[E,d.rgba]]),e("button",{onClick:t[12]||(t[12]=n=>B(`rgba(${s.rgb.r}, ${s.rgb.g}, ${s.rgb.b}, ${parseFloat(String(s.rgb.a)).toFixed(2)})`)),class:"px-2 py-1 text-xs bg-gray-200 rounded hover:bg-gray-300"},i(o.$t("common.copy")),1)])]),e("div",to,[t[42]||(t[42]=e("span",{class:"font-medium"},"HSL",-1)),e("div",eo,[w(e("input",{"onUpdate:modelValue":t[13]||(t[13]=n=>d.hsl=n),type:"text",class:"w-52 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:Xt},null,544),[[E,d.hsl]]),e("button",{onClick:t[14]||(t[14]=n=>B(`hsl(${Math.round(s.hsl.h)}, ${Math.round(s.hsl.s)}%, ${Math.round(s.hsl.l)}%)`)),class:"px-2 py-1 text-xs bg-gray-200 rounded hover:bg-gray-300"},i(o.$t("common.copy")),1)])]),e("div",so,[t[43]||(t[43]=e("span",{class:"font-medium"},"HSV",-1)),e("div",oo,[w(e("input",{"onUpdate:modelValue":t[15]||(t[15]=n=>d.hsv=n),type:"text",class:"w-52 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:Kt},null,544),[[E,d.hsv]]),e("button",{onClick:t[16]||(t[16]=n=>B(`hsv(${Math.round(s.hsv.h)}, ${Math.round(s.hsv.s)}%, ${Math.round(s.hsv.v)}%)`)),class:"px-2 py-1 text-xs bg-gray-200 rounded hover:bg-gray-300"},i(o.$t("common.copy")),1)])]),e("div",no,[t[44]||(t[44]=e("span",{class:"font-medium"},"CMYK",-1)),e("div",ro,[w(e("input",{"onUpdate:modelValue":t[17]||(t[17]=n=>d.cmyk=n),type:"text",class:"w-56 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-transparent",onInput:Nt},null,544),[[E,d.cmyk]]),e("button",{onClick:t[18]||(t[18]=n=>B(`cmyk(${Math.round(s.cmyk.c)}%, ${Math.round(s.cmyk.m)}%, ${Math.round(s.cmyk.y)}%, ${Math.round(s.cmyk.k)}%)`)),class:"px-2 py-1 text-xs bg-gray-200 rounded hover:bg-gray-300"},i(o.$t("common.copy")),1)])])])]),e("div",ao,[e("h3",lo,i(o.$t("tools.colorPicker.preview")),1),e("div",io,[e("div",null,[e("p",co,i(o.$t("tools.colorPicker.onLight")),1),e("div",uo,[e("div",{class:"w-16 h-16 rounded",style:M({backgroundColor:s.hex})},null,4)])]),e("div",null,[e("p",go,i(o.$t("tools.colorPicker.onDark")),1),e("div",mo,[e("div",{class:"w-16 h-16 rounded",style:M({backgroundColor:s.hex})},null,4)])])])])])])]),e("div",ho,[e("h2",po,i(o.$t("tools.colorPicker.features.title")),1),e("div",bo,[e("div",vo,[e("h3",xo," 🎨 "+i(o.$t("tools.colorPicker.features.conversions.title")),1),e("p",fo,i(o.$t("tools.colorPicker.features.conversions.description")),1)]),e("div",yo,[e("h3",ko," 📸 "+i(o.$t("tools.colorPicker.features.imagePicker.title")),1),e("p",Fo,i(o.$t("tools.colorPicker.features.imagePicker.description")),1)]),e("div",Co,[e("h3",wo," 📋 "+i(o.$t("tools.colorPicker.features.commonColors.title")),1),e("p",_o,i(o.$t("tools.colorPicker.features.commonColors.description")),1)])])])]))}}),Bo=oe(Mo,[["__scopeId","data-v-1fb34a60"]]);export{Bo as default};
