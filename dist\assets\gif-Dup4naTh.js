import{a as R}from"./_commonjsHelpers-DsqdWQfm.js";import{c as y}from"./_commonjs-dynamic-modules-TDtrdbi3.js";var W={exports:{}},P;function A(){return P||(P=1,(function(N,M){(function(w){N.exports=w()})(function(){return(function w(v,x,o){function l(u,t){if(!x[u]){if(!v[u]){var s=typeof y=="function"&&y;if(!t&&s)return s(u,!0);if(m)return m(u,!0);var n=new Error("Cannot find module '"+u+"'");throw n.code="MODULE_NOT_FOUND",n}var f=x[u]={exports:{}};v[u][0].call(f.exports,function(p){var a=v[u][1][p];return l(a||p)},f,f.exports,w,v,x,o)}return x[u].exports}for(var m=typeof y=="function"&&y,c=0;c<o.length;c++)l(o[c]);return l})({1:[function(w,v,x){function o(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}v.exports=o,o.EventEmitter=o,o.prototype._events=void 0,o.prototype._maxListeners=void 0,o.defaultMaxListeners=10,o.prototype.setMaxListeners=function(t){if(!m(t)||t<0||isNaN(t))throw TypeError("n must be a positive number");return this._maxListeners=t,this},o.prototype.emit=function(t){var s,n,f,p,a,e;if(this._events||(this._events={}),t==="error"&&(!this._events.error||c(this._events.error)&&!this._events.error.length)){if(s=arguments[1],s instanceof Error)throw s;var r=new Error('Uncaught, unspecified "error" event. ('+s+")");throw r.context=s,r}if(n=this._events[t],u(n))return!1;if(l(n))switch(arguments.length){case 1:n.call(this);break;case 2:n.call(this,arguments[1]);break;case 3:n.call(this,arguments[1],arguments[2]);break;default:p=Array.prototype.slice.call(arguments,1),n.apply(this,p)}else if(c(n))for(p=Array.prototype.slice.call(arguments,1),e=n.slice(),f=e.length,a=0;a<f;a++)e[a].apply(this,p);return!0},o.prototype.addListener=function(t,s){var n;if(!l(s))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",t,l(s.listener)?s.listener:s),this._events[t]?c(this._events[t])?this._events[t].push(s):this._events[t]=[this._events[t],s]:this._events[t]=s,c(this._events[t])&&!this._events[t].warned&&(u(this._maxListeners)?n=o.defaultMaxListeners:n=this._maxListeners,n&&n>0&&this._events[t].length>n&&(this._events[t].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[t].length),typeof console.trace=="function"&&console.trace())),this},o.prototype.on=o.prototype.addListener,o.prototype.once=function(t,s){if(!l(s))throw TypeError("listener must be a function");var n=!1;function f(){this.removeListener(t,f),n||(n=!0,s.apply(this,arguments))}return f.listener=s,this.on(t,f),this},o.prototype.removeListener=function(t,s){var n,f,p,a;if(!l(s))throw TypeError("listener must be a function");if(!this._events||!this._events[t])return this;if(n=this._events[t],p=n.length,f=-1,n===s||l(n.listener)&&n.listener===s)delete this._events[t],this._events.removeListener&&this.emit("removeListener",t,s);else if(c(n)){for(a=p;a-- >0;)if(n[a]===s||n[a].listener&&n[a].listener===s){f=a;break}if(f<0)return this;n.length===1?(n.length=0,delete this._events[t]):n.splice(f,1),this._events.removeListener&&this.emit("removeListener",t,s)}return this},o.prototype.removeAllListeners=function(t){var s,n;if(!this._events)return this;if(!this._events.removeListener)return arguments.length===0?this._events={}:this._events[t]&&delete this._events[t],this;if(arguments.length===0){for(s in this._events)s!=="removeListener"&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events={},this}if(n=this._events[t],l(n))this.removeListener(t,n);else if(n)for(;n.length;)this.removeListener(t,n[n.length-1]);return delete this._events[t],this},o.prototype.listeners=function(t){var s;return!this._events||!this._events[t]?s=[]:l(this._events[t])?s=[this._events[t]]:s=this._events[t].slice(),s},o.prototype.listenerCount=function(t){if(this._events){var s=this._events[t];if(l(s))return 1;if(s)return s.length}return 0},o.listenerCount=function(t,s){return t.listenerCount(s)};function l(t){return typeof t=="function"}function m(t){return typeof t=="number"}function c(t){return typeof t=="object"&&t!==null}function u(t){return t===void 0}},{}],2:[function(w,v,x){var o,l,m,c,u;u=navigator.userAgent.toLowerCase(),c=navigator.platform.toLowerCase(),o=u.match(/(opera|ie|firefox|chrome|version)[\s\/:]([\w\d\.]+)?.*?(safari|version[\s\/:]([\w\d\.]+)|$)/)||[null,"unknown",0],m=o[1]==="ie"&&document.documentMode,l={name:o[1]==="version"?o[3]:o[1],version:m||parseFloat(o[1]==="opera"&&o[4]?o[4]:o[2]),platform:{name:u.match(/ip(?:ad|od|hone)/)?"ios":(u.match(/(?:webos|android)/)||c.match(/mac|win|linux/)||["other"])[0]}},l[l.name]=!0,l[l.name+parseInt(l.version,10)]=!0,l.platform[l.platform.name]=!0,v.exports=l},{}],3:[function(w,v,x){var o,l,m,c=function(n,f){for(var p in f)u.call(f,p)&&(n[p]=f[p]);function a(){this.constructor=n}return a.prototype=f.prototype,n.prototype=new a,n.__super__=f.prototype,n},u={}.hasOwnProperty,t=[].indexOf||function(n){for(var f=0,p=this.length;f<p;f++)if(f in this&&this[f]===n)return f;return-1},s=[].slice;o=w("events").EventEmitter,m=w("./browser.coffee"),l=(function(n){var f,p;c(a,n),f={workerScript:"gif.worker.js",workers:2,repeat:0,background:"#fff",quality:10,width:null,height:null,transparent:null,debug:!1,dither:!1},p={delay:500,copy:!1};function a(e){var r,i,h;this.running=!1,this.options={},this.frames=[],this.freeWorkers=[],this.activeWorkers=[],this.setOptions(e);for(i in f)h=f[i],(r=this.options)[i]==null&&(r[i]=h)}return a.prototype.setOption=function(e,r){if(this.options[e]=r,this._canvas!=null&&(e==="width"||e==="height"))return this._canvas[e]=r},a.prototype.setOptions=function(e){var r,i,h;i=[];for(r in e)u.call(e,r)&&(h=e[r],i.push(this.setOption(r,h)));return i},a.prototype.addFrame=function(e,r){var i,h;r==null&&(r={}),i={},i.transparent=this.options.transparent;for(h in p)i[h]=r[h]||p[h];if(this.options.width==null&&this.setOption("width",e.width),this.options.height==null&&this.setOption("height",e.height),typeof ImageData<"u"&&ImageData!==null&&e instanceof ImageData)i.data=e.data;else if(typeof CanvasRenderingContext2D<"u"&&CanvasRenderingContext2D!==null&&e instanceof CanvasRenderingContext2D||typeof WebGLRenderingContext<"u"&&WebGLRenderingContext!==null&&e instanceof WebGLRenderingContext)r.copy?i.data=this.getContextData(e):i.context=e;else if(e.childNodes!=null)r.copy?i.data=this.getImageData(e):i.image=e;else throw new Error("Invalid image");return this.frames.push(i)},a.prototype.render=function(){var e,r,i;if(this.running)throw new Error("Already running");if(this.options.width==null||this.options.height==null)throw new Error("Width and height must be set prior to rendering");if(this.running=!0,this.nextFrame=0,this.finishedFrames=0,this.imageParts=(function(){var h,d,g;for(g=[],h=0,d=this.frames.length;0<=d?h<d:h>d;0<=d?++h:--h)g.push(null);return g}).call(this),r=this.spawnWorkers(),this.options.globalPalette===!0)this.renderNextFrame();else for(e=0,i=r;0<=i?e<i:e>i;0<=i?++e:--e)this.renderNextFrame();return this.emit("start"),this.emit("progress",0)},a.prototype.abort=function(){for(var e;e=this.activeWorkers.shift(),e!=null;)this.log("killing active worker"),e.terminate();return this.running=!1,this.emit("abort")},a.prototype.spawnWorkers=function(){var e,r,i;return e=Math.min(this.options.workers,this.frames.length),(function(){i=[];for(var h=r=this.freeWorkers.length;r<=e?h<e:h>e;r<=e?h++:h--)i.push(h);return i}).apply(this).forEach((function(h){return function(d){var g;return h.log("spawning worker "+d),g=new Worker(h.options.workerScript),g.onmessage=function(_){return h.activeWorkers.splice(h.activeWorkers.indexOf(g),1),h.freeWorkers.push(g),h.frameFinished(_.data)},h.freeWorkers.push(g)}})(this)),e},a.prototype.frameFinished=function(e){var r,i;if(this.log("frame "+e.index+" finished - "+this.activeWorkers.length+" active"),this.finishedFrames++,this.emit("progress",this.finishedFrames/this.frames.length),this.imageParts[e.index]=e,this.options.globalPalette===!0&&(this.options.globalPalette=e.globalPalette,this.log("global palette analyzed"),this.frames.length>2))for(r=1,i=this.freeWorkers.length;1<=i?r<i:r>i;1<=i?++r:--r)this.renderNextFrame();return t.call(this.imageParts,null)>=0?this.renderNextFrame():this.finishRendering()},a.prototype.finishRendering=function(){var e,r,i,h,d,g,_,k,C,D,O,b,I,L,E,F;for(k=0,L=this.imageParts,d=0,C=L.length;d<C;d++)r=L[d],k+=(r.data.length-1)*r.pageSize+r.cursor;for(k+=r.pageSize-r.cursor,this.log("rendering finished - filesize "+Math.round(k/1e3)+"kb"),e=new Uint8Array(k),b=0,E=this.imageParts,g=0,D=E.length;g<D;g++)for(r=E[g],F=r.data,i=_=0,O=F.length;_<O;i=++_)I=F[i],e.set(I,b),i===r.data.length-1?b+=r.cursor:b+=r.pageSize;return h=new Blob([e],{type:"image/gif"}),this.emit("finished",h,e)},a.prototype.renderNextFrame=function(){var e,r,i;if(this.freeWorkers.length===0)throw new Error("No free workers");if(!(this.nextFrame>=this.frames.length))return e=this.frames[this.nextFrame++],i=this.freeWorkers.shift(),r=this.getTask(e),this.log("starting frame "+(r.index+1)+" of "+this.frames.length),this.activeWorkers.push(i),i.postMessage(r)},a.prototype.getContextData=function(e){return e.getImageData(0,0,this.options.width,this.options.height).data},a.prototype.getImageData=function(e){var r;return this._canvas==null&&(this._canvas=document.createElement("canvas"),this._canvas.width=this.options.width,this._canvas.height=this.options.height),r=this._canvas.getContext("2d"),r.setFill=this.options.background,r.fillRect(0,0,this.options.width,this.options.height),r.drawImage(e,0,0),this.getContextData(r)},a.prototype.getTask=function(e){var r,i;if(r=this.frames.indexOf(e),i={index:r,last:r===this.frames.length-1,delay:e.delay,transparent:e.transparent,width:this.options.width,height:this.options.height,quality:this.options.quality,dither:this.options.dither,globalPalette:this.options.globalPalette,repeat:this.options.repeat,canTransfer:m.name==="chrome"},e.data!=null)i.data=e.data;else if(e.context!=null)i.data=this.getContextData(e.context);else if(e.image!=null)i.data=this.getImageData(e.image);else throw new Error("Invalid frame");return i},a.prototype.log=function(){var e;if(e=1<=arguments.length?s.call(arguments,0):[],!!this.options.debug)return console.log.apply(console,e)},a})(o),v.exports=l},{"./browser.coffee":2,events:1}]},{},[3])(3)})})(W)),W.exports}var G=A();const U=R(G);export{U as G};
