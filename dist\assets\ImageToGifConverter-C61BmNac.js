import{d as K,u as Y,a1 as J,a2 as Q,r as b,a4 as X,c as g,a as e,f as G,t as s,S as Z,R as L,e as w,g as T,v as ee,F as te,k as oe,a3 as se,o as v,W as re}from"./index-CkZTMFXG.js";import{u as le}from"./useToast-virEbLJw.js";import{G as ie}from"./gif-Dup4naTh.js";import"./_commonjsHelpers-DsqdWQfm.js";import"./_commonjs-dynamic-modules-TDtrdbi3.js";const ae={class:"min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-800 py-8"},ne={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},de={class:"glass rounded-2xl p-6 mb-8 border border-slate-700/30 shadow-dark-lg"},ue={class:"text-3xl font-bold text-slate-100 mb-4 text-gradient"},ce={class:"text-slate-300 text-lg"},me={class:"mt-4 p-4 bg-primary-500/10 rounded-xl border border-primary-500/20"},ge={class:"font-semibold text-primary-400 mb-2"},ve={class:"list-decimal list-inside space-y-1 text-slate-300"},pe={class:"glass rounded-2xl p-6 mb-8 border border-slate-700/30 shadow-dark-lg"},fe={class:"text-lg font-semibold text-slate-100 mb-4 border-b border-slate-700/30 pb-3"},he={class:"mb-6"},be={class:"text-slate-300 mb-4"},ye={class:"px-6 py-2 bg-primary-600 text-white rounded-xl hover:bg-primary-700 transition-colors hover-lift"},xe={class:"text-xs text-slate-400 mt-2"},we={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"},_e={class:"block text-sm font-medium text-slate-300 mb-2"},Ce={class:"block text-sm font-medium text-slate-300 mb-2"},Ge={value:"high"},Te={value:"medium"},ke={value:"low"},$e={class:"block text-sm font-medium text-slate-300 mb-2"},Ie={key:0,class:"bg-white rounded-lg shadow-md p-6 mb-8"},Ue={class:"text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2"},Fe={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Le={class:"text-md font-semibold text-gray-800 mb-3"},Me={class:"space-y-3 max-h-96 overflow-y-auto"},Re={class:"flex-shrink-0 w-16 h-16 bg-gray-100 rounded-md overflow-hidden"},De=["src","alt"],Ee={class:"ml-3 flex-grow min-w-0"},ze={class:"text-sm font-medium text-gray-900 truncate"},Ve={class:"text-xs text-gray-500"},qe={class:"flex items-center"},Oe=["onUpdate:modelValue","onInput"],Be=["onClick"],je={class:"mt-4 flex flex-wrap gap-2"},Se=["disabled"],Pe=["disabled"],Ae=["disabled"],Ne=["disabled"],He={class:"space-y-4"},We={class:"flex gap-2"},Ke=["disabled"],Ye={class:"space-y-2"},Je={class:"block text-sm font-medium text-gray-700"},Qe={class:"flex items-center gap-2"},Xe={class:"text-sm text-gray-500"},Ze={key:1,class:"glass rounded-2xl p-6 mb-8 border border-slate-700/30 shadow-dark-lg"},et={class:"text-center"},tt={class:"text-lg font-semibold text-slate-100 mb-2"},ot={class:"text-slate-300"},st={class:"mt-4 bg-slate-700/50 rounded-full h-2"},rt={class:"text-sm text-slate-400 mt-2"},lt={key:0,class:"mt-6"},it={class:"text-md font-medium text-slate-200 mb-2"},at=["src"],nt={key:2,class:"glass rounded-2xl p-6 mb-8 border border-slate-700/30 shadow-dark-lg"},dt={class:"text-lg font-semibold text-slate-100 mb-4 border-b border-slate-700/30 pb-3"},ut={class:"text-center"},ct=["src"],mt={class:"flex justify-center gap-4"},gt={class:"bg-warning-500/10 border-l-4 border-warning-500 p-4 mb-8 rounded-xl"},vt={class:"flex"},pt={class:"ml-3"},ft={class:"text-sm font-medium text-warning-400"},ht={class:"mt-2 text-sm text-slate-300"},bt={class:"list-disc list-inside space-y-1"},yt={class:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-12"},xt={class:"glass p-6 rounded-2xl shadow-dark-lg border-l-4 border-primary-500"},wt={class:"text-lg font-semibold text-slate-100 mb-3"},_t={class:"text-slate-300 text-sm"},Ct={class:"glass p-6 rounded-2xl shadow-dark-lg border-l-4 border-success-500"},Gt={class:"text-lg font-semibold text-slate-100 mb-3"},Tt={class:"text-slate-300 text-sm"},kt={class:"glass p-6 rounded-2xl shadow-dark-lg border-l-4 border-purple-500"},$t={class:"text-lg font-semibold text-slate-100 mb-3"},It={class:"text-slate-300 text-sm"},Ut=K({__name:"ImageToGifConverter",setup(Ft){const{t:c}=Y(),{success:_,error:y}=le();J(()=>{document.addEventListener("paste",I)}),Q(()=>{document.removeEventListener("paste",I),l.value.forEach(t=>{URL.revokeObjectURL(t.url)}),n.value&&URL.revokeObjectURL(n.value)});async function I(t){const o=t.clipboardData?.items;if(!o)return;const r=[];for(let i=0;i<o.length;i++){const u=o[i];if(u.type.indexOf("image")!==-1){const f=u.getAsFile();if(f){const m=new File([f],`pasted-image-${Date.now()}.png`,{type:f.type});r.push(m)}}}r.length>0&&(await k(r),_(c("tools.imageToGifConverter.messages.filesPasted",{count:r.length})))}const C=b(),U=b(!1),x=b(!1),p=b(0),n=b(""),l=b([]),a=X({width:300,quality:"medium",fps:2,loopCount:0});function M(t){t.preventDefault(),U.value=!1;const o=t.dataTransfer?.files;if(o&&o.length>0){const r=Array.from(o).filter(i=>i.type.startsWith("image/"));r.length>0?k(r):y(c("tools.imageToGifConverter.errors.noImages"))}}function R(t){const o=t.target,r=o.files;if(r&&r.length>0){const i=Array.from(r).filter(u=>u.type.startsWith("image/"));i.length>0?k(i):y(c("tools.imageToGifConverter.errors.noImages"))}o&&(o.value="")}async function k(t){try{for(const o of t){const r=URL.createObjectURL(o);l.value.push({file:o,url:r,name:o.name,delay:1})}_(c("tools.imageToGifConverter.messages.filesAdded",{count:t.length}))}catch(o){console.error("Error adding image files:",o),y(c("tools.imageToGifConverter.errors.fileProcessing"))}}function D(t){const o=l.value[t];o&&(URL.revokeObjectURL(o.url),l.value.splice(t,1))}function F(){l.value.forEach(t=>{URL.revokeObjectURL(t.url)}),l.value=[],n.value&&(URL.revokeObjectURL(n.value),n.value=""),_(c("tools.imageToGifConverter.messages.cleared"))}function E(t,o){const r=o.target,i=parseFloat(r.value);!isNaN(i)&&i>0&&(l.value[t].delay=i)}function z(){if(l.value.length<=1)return;const t=l.value[0];l.value.shift(),l.value.push(t)}function V(){if(l.value.length<=1)return;const t=l.value.pop();t&&l.value.unshift(t)}function q(){l.value.length<=1||l.value.reverse()}function O(){if(!(l.value.length<=1))for(let t=l.value.length-1;t>0;t--){const o=Math.floor(Math.random()*(t+1));[l.value[t],l.value[o]]=[l.value[o],l.value[t]]}}function B(t){if(t===0)return"0 Bytes";const o=1024,r=["Bytes","KB","MB","GB"],i=Math.floor(Math.log(t)/Math.log(o));return parseFloat((t/Math.pow(o,i)).toFixed(2))+" "+r[i]}async function j(){if(l.value.length===0){y(c("tools.imageToGifConverter.errors.noImagesSelected"));return}x.value=!0,p.value=0;try{await S(),_(c("tools.imageToGifConverter.messages.gifGenerated"))}catch(t){console.error("Error generating GIF:",t),y(c("tools.imageToGifConverter.errors.processingFailed")+": "+t.message)}finally{x.value=!1}}async function S(){const t=document.createElement("canvas"),o=t.getContext("2d");if(!o)throw new Error("Unable to get canvas context. Your browser may not support this feature.");t.width=a.width,t.height=a.width;const r={high:1,medium:10,low:20},i=new ie({workers:2,quality:r[a.quality],width:t.width,height:t.height,workerScript:"/gif.worker.js"}),u=l.value.length;let f=0;for(const m of l.value)try{const d=new Image;await new Promise((H,W)=>{d.onload=()=>H(),d.onerror=()=>W(new Error("Failed to load image")),d.src=m.url});const $=d.height/d.width;t.height=Math.round(t.width*$),o.drawImage(d,0,0,t.width,t.height);const N=Math.round(m.delay*1e3);i.addFrame(t,{copy:!0,delay:N}),f++,p.value=Math.round(f/u*100)}catch(d){console.error("Error processing image:",m.name,d)}return new Promise((m,d)=>{i.on("finished",h=>{const $=URL.createObjectURL(h);n.value=$,p.value=100,m()}),i.on("abort",()=>{d(new Error("GIF generation was aborted"))}),i.on("error",h=>{d(new Error("GIF generation error: "+h.message))});try{i.render()}catch(h){d(new Error("Failed to start GIF rendering: "+h.message))}})}function P(){if(!n.value)return;const t=document.createElement("a");t.href=n.value,t.download=`image-to-gif-${Date.now()}.gif`,document.body.appendChild(t),t.click(),document.body.removeChild(t)}function A(){F(),p.value=0,a.width=300,a.quality="medium",a.fps=2,a.loopCount=0,C.value&&(C.value.value="")}return(t,o)=>(v(),g("div",ae,[e("div",ne,[e("div",de,[e("h1",ue," 🖼️ "+s(t.$t("tools.imageToGifConverter.title")),1),e("p",ce,s(t.$t("tools.imageToGifConverter.description")),1),e("div",me,[e("h3",ge,s(t.$t("tools.imageToGifConverter.howToUse.title")),1),e("ol",ve,[e("li",null,s(t.$t("tools.imageToGifConverter.howToUse.step1")),1),e("li",null,s(t.$t("tools.imageToGifConverter.howToUse.step2")),1),e("li",null,s(t.$t("tools.imageToGifConverter.howToUse.step3")),1),e("li",null,s(t.$t("tools.imageToGifConverter.howToUse.step4")),1)])])]),e("div",pe,[e("h3",fe,s(t.$t("tools.imageToGifConverter.upload.title")),1),e("div",he,[e("div",{onDrop:M,onDragover:o[0]||(o[0]=L(()=>{},["prevent"])),onDragenter:o[1]||(o[1]=L(()=>{},["prevent"])),class:Z(["border-2 border-dashed border-slate-600/50 rounded-2xl p-8 text-center hover:border-primary-500 transition-colors cursor-pointer hover-lift",{"border-primary-500 bg-primary-500/10":U.value}]),onClick:o[2]||(o[2]=r=>C.value.click())},[o[7]||(o[7]=e("div",{class:"text-slate-400 text-4xl mb-4"},"🖼️",-1)),e("p",be,s(t.$t("tools.imageToGifConverter.upload.dragDrop")),1),e("input",{type:"file",ref_key:"fileInput",ref:C,onChange:R,accept:"image/*",multiple:"",class:"hidden"},null,544),e("button",ye,s(t.$t("tools.imageToGifConverter.upload.selectFile")),1),e("p",xe,s(t.$t("tools.imageToGifConverter.upload.supportedFormats")),1)],34)]),e("div",we,[e("div",null,[e("label",_e,s(t.$t("tools.imageToGifConverter.settings.width")),1),w(e("input",{"onUpdate:modelValue":o[3]||(o[3]=r=>a.width=r),type:"number",min:"100",max:"800",class:"w-full px-3 py-2 bg-slate-800/50 border border-slate-600/50 rounded-xl text-slate-100 placeholder-slate-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"},null,512),[[T,a.width,void 0,{number:!0}]])]),e("div",null,[e("label",Ce,s(t.$t("tools.imageToGifConverter.settings.quality")),1),w(e("select",{"onUpdate:modelValue":o[4]||(o[4]=r=>a.quality=r),class:"w-full px-3 py-2 bg-slate-800/50 border border-slate-600/50 rounded-xl text-slate-100 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"},[e("option",Ge,s(t.$t("tools.imageToGifConverter.settings.qualityOptions.high")),1),e("option",Te,s(t.$t("tools.imageToGifConverter.settings.qualityOptions.medium")),1),e("option",ke,s(t.$t("tools.imageToGifConverter.settings.qualityOptions.low")),1)],512),[[ee,a.quality]])]),e("div",null,[e("label",$e,s(t.$t("tools.imageToGifConverter.settings.fps")),1),w(e("input",{"onUpdate:modelValue":o[5]||(o[5]=r=>a.fps=r),type:"number",min:"1",max:"30",class:"w-full px-3 py-2 bg-slate-800/50 border border-slate-600/50 rounded-xl text-slate-100 placeholder-slate-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"},null,512),[[T,a.fps,void 0,{number:!0}]])])])]),l.value.length>0?(v(),g("div",Ie,[e("h3",Ue,s(t.$t("tools.imageToGifConverter.preview.title")),1),e("div",Fe,[e("div",null,[e("h4",Le,s(t.$t("tools.imageToGifConverter.preview.selectedImages"))+" ("+s(l.value.length)+") ",1),e("div",Me,[(v(!0),g(te,null,oe(l.value,(r,i)=>(v(),g("div",{key:i,class:"flex items-center p-3 border border-gray-200 rounded-lg"},[e("div",Re,[e("img",{src:r.url,alt:r.name,class:"w-full h-full object-cover"},null,8,De)]),e("div",Ee,[e("p",ze,s(r.name),1),e("p",Ve,s(B(r.file.size)),1)]),e("div",qe,[w(e("input",{"onUpdate:modelValue":u=>r.delay=u,type:"number",min:"0.1",max:"10",step:"0.1",class:"w-16 px-2 py-1 border border-gray-300 rounded text-xs",onInput:u=>E(i,u)},null,40,Oe),[[T,r.delay,void 0,{number:!0}]]),o[9]||(o[9]=e("span",{class:"ml-1 text-xs text-gray-500"},"s",-1)),e("button",{onClick:u=>D(i),class:"ml-2 text-red-500 hover:text-red-700"},[...o[8]||(o[8]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z","clip-rule":"evenodd"})],-1)])],8,Be)])]))),128))]),e("div",je,[e("button",{onClick:z,disabled:l.value.length<=1,class:"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 disabled:opacity-50"},s(t.$t("tools.imageToGifConverter.preview.moveUp")),9,Se),e("button",{onClick:V,disabled:l.value.length<=1,class:"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 disabled:opacity-50"},s(t.$t("tools.imageToGifConverter.preview.moveDown")),9,Pe),e("button",{onClick:q,disabled:l.value.length<=1,class:"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 disabled:opacity-50"},s(t.$t("tools.imageToGifConverter.preview.reverse")),9,Ae),e("button",{onClick:O,disabled:l.value.length<=1,class:"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 disabled:opacity-50"},s(t.$t("tools.imageToGifConverter.preview.shuffle")),9,Ne)])]),e("div",null,[e("div",He,[e("div",We,[e("button",{onClick:j,disabled:x.value,class:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"},s(x.value?t.$t("common.loading"):t.$t("tools.imageToGifConverter.actions.generateGif")),9,Ke),e("button",{onClick:F,class:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"},s(t.$t("common.clear")),1)]),e("div",Ye,[e("label",Je,s(t.$t("tools.imageToGifConverter.settings.loopCount")),1),e("div",Qe,[w(e("input",{"onUpdate:modelValue":o[6]||(o[6]=r=>a.loopCount=r),type:"number",min:"0",max:"100",class:"w-20 px-2 py-1 border border-gray-300 rounded text-sm"},null,512),[[T,a.loopCount,void 0,{number:!0}]]),e("span",Xe," (0 = "+s(t.$t("tools.imageToGifConverter.settings.infinite"))+") ",1)])])])])])])):G("",!0),x.value?(v(),g("div",Ze,[e("div",et,[o[10]||(o[10]=e("div",{class:"relative mx-auto mb-4"},[e("div",{class:"w-16 h-16 border-4 border-slate-700 border-t-primary-500 rounded-full animate-spin"}),e("div",{class:"absolute inset-0 w-16 h-16 border-4 border-transparent border-r-primary-400 rounded-full animate-spin",style:{"animation-direction":"reverse","animation-duration":"1s"}})],-1)),e("h3",tt,s(t.$t("tools.imageToGifConverter.processing.title")),1),e("p",ot,s(t.$t("tools.imageToGifConverter.processing.description")),1),e("div",st,[e("div",{class:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:se({width:p.value+"%"})},null,4)]),e("p",rt,s(p.value)+"%",1),n.value?(v(),g("div",lt,[e("h4",it,s(t.$t("tools.imageToGifConverter.processing.preview")),1),e("img",{src:n.value,alt:"GIF Preview",class:"max-w-full h-auto mx-auto rounded-xl shadow-dark-lg",style:{"max-height":"200px"}},null,8,at)])):G("",!0)])])):G("",!0),n.value?(v(),g("div",nt,[e("h3",dt,s(t.$t("tools.imageToGifConverter.result.title")),1),e("div",ut,[e("img",{src:n.value,alt:"Generated GIF",class:"max-w-full h-auto mx-auto rounded-xl shadow-dark-lg mb-4"},null,8,ct),e("div",mt,[e("button",{onClick:P,class:"px-6 py-2 bg-success-600 text-white rounded-xl hover:bg-success-700 transition-colors hover-lift"},s(t.$t("tools.imageToGifConverter.result.download")),1),e("button",{onClick:A,class:"px-6 py-2 bg-slate-600 text-white rounded-xl hover:bg-slate-700 transition-colors hover-lift"},s(t.$t("tools.imageToGifConverter.result.createNew")),1)])])])):G("",!0),e("div",gt,[e("div",vt,[o[11]||(o[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-warning-500",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])],-1)),e("div",pt,[e("h3",ft,s(t.$t("tools.imageToGifConverter.tips.title")),1),e("div",ht,[e("ul",bt,[e("li",null,s(t.$t("tools.imageToGifConverter.tips.tip1")),1),e("li",null,s(t.$t("tools.imageToGifConverter.tips.tip2")),1),e("li",null,s(t.$t("tools.imageToGifConverter.tips.tip3")),1),e("li",null,s(t.$t("tools.imageToGifConverter.tips.tip4")),1)])])])])]),e("div",yt,[e("div",xt,[e("h3",wt," 🖼️ "+s(t.$t("tools.imageToGifConverter.features.conversion.title")),1),e("p",_t,s(t.$t("tools.imageToGifConverter.features.conversion.description")),1)]),e("div",Ct,[e("h3",Gt," ⚙️ "+s(t.$t("tools.imageToGifConverter.features.customization.title")),1),e("p",Tt,s(t.$t("tools.imageToGifConverter.features.customization.description")),1)]),e("div",kt,[e("h3",$t," 🔄 "+s(t.$t("tools.imageToGifConverter.features.animation.title")),1),e("p",It,s(t.$t("tools.imageToGifConverter.features.animation.description")),1)])])])]))}}),zt=re(Ut,[["__scopeId","data-v-cfe43313"]]);export{zt as default};
