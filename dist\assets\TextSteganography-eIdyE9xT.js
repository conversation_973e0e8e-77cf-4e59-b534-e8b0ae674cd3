import{d as $,i as S,c as p,o as d,f as u,a as s,h as c,t as r,a0 as V,u as C,a4 as B,P as x,Q as i,U as m}from"./index-CkZTMFXG.js";import{u as j}from"./useToast-virEbLJw.js";import{_}from"./ToolLayout.vue_vue_type_script_setup_true_lang-BsMmX7pX.js";import{a as T,_ as g}from"./Card.vue_vue_type_script_setup_true_lang-DgPPwsWa.js";const z={class:"space-y-2"},M=["for"],F={key:0,class:"text-error-400 ml-1"},P={class:"relative"},E=["id","value","placeholder","disabled","readonly","rows"],I={key:0,class:"absolute bottom-2 right-2 text-xs text-slate-400 bg-slate-900/80 px-2 py-1 rounded"},U={key:1,class:"text-sm text-slate-400"},L={key:2,class:"text-sm text-error-400"},N={key:3,class:"text-sm text-success-400"},h=$({__name:"Textarea",props:{modelValue:{},label:{},placeholder:{},helpText:{},errorMessage:{},successMessage:{},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},required:{type:Boolean,default:!1},clearable:{type:Boolean,default:!1},showCount:{type:Boolean,default:!1},maxLength:{},rows:{default:4},error:{type:Boolean,default:!1},success:{type:Boolean,default:!1}},emits:["update:modelValue","blur","focus","keydown","keyup"],setup(v,{emit:y}){const f=S(()=>`textarea-${Math.random().toString(36).substr(2,9)}`);return(t,e)=>(d(),p("div",z,[t.label?(d(),p("label",{key:0,for:f.value,class:"block text-sm font-medium text-slate-200"},[c(r(t.label)+" ",1),t.required?(d(),p("span",F,"*")):u("",!0)],8,M)):u("",!0),s("div",P,[s("textarea",V({id:f.value,value:t.modelValue,placeholder:t.placeholder,disabled:t.disabled,readonly:t.readonly,rows:t.rows,class:["block w-full transition-all duration-200 bg-slate-800/50 border border-slate-600/50 text-slate-100 placeholder-slate-400","focus:ring-2 focus:ring-primary-500 focus:border-primary-500","disabled:opacity-50 disabled:cursor-not-allowed","rounded-xl px-4 py-3 resize-none",t.error?"border-error-500 focus:border-error-500 focus:ring-error-500":"",t.success?"border-success-500 focus:border-success-500 focus:ring-success-500":""],onInput:e[0]||(e[0]=n=>t.$emit("update:modelValue",n.target.value)),onBlur:e[1]||(e[1]=n=>t.$emit("blur",n)),onFocus:e[2]||(e[2]=n=>t.$emit("focus",n)),onKeydown:e[3]||(e[3]=n=>t.$emit("keydown",n)),onKeyup:e[4]||(e[4]=n=>t.$emit("keyup",n))},t.$attrs),null,16,E),t.showCount?(d(),p("div",I,r(t.modelValue?.length||0)+r(t.maxLength?`/${t.maxLength}`:""),1)):u("",!0),t.clearable&&t.modelValue?(d(),p("button",{key:1,onClick:e[5]||(e[5]=n=>t.$emit("update:modelValue","")),class:"absolute top-2 right-2 text-slate-400 hover:text-slate-200 transition-colors duration-200 p-1 rounded-lg hover:bg-slate-700/50",type:"button"},[...e[6]||(e[6]=[s("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])])):u("",!0)]),t.helpText?(d(),p("p",U,r(t.helpText),1)):u("",!0),t.error&&t.errorMessage?(d(),p("p",L,r(t.errorMessage),1)):u("",!0),t.success&&t.successMessage?(d(),p("p",N,r(t.successMessage),1)):u("",!0)]))}}),q={class:"grid lg:grid-cols-2 gap-6"},A={class:"flex items-center justify-between"},D={class:"text-lg font-semibold text-slate-100"},K={class:"space-y-4"},Q={class:"block text-sm font-medium text-slate-200 mb-2"},R={class:"block text-sm font-medium text-slate-200 mb-2"},G={class:"flex items-center justify-between mb-2"},H={class:"block text-sm font-medium text-slate-200"},J={class:"flex items-center justify-between"},O={class:"text-lg font-semibold text-slate-100"},W={class:"space-y-4"},X={class:"block text-sm font-medium text-slate-200 mb-2"},Y={class:"flex items-center justify-between mb-2"},Z={class:"block text-sm font-medium text-slate-200"},ee={class:"w-full p-3 border border-slate-600/50 rounded-xl bg-slate-800/30 overflow-auto min-h-[96px]"},te={class:"text-slate-100 font-mono text-sm whitespace-pre-wrap break-words"},ae=$({__name:"TextSteganography",setup(v){const{t:y}=C(),{copySuccess:f,copyError:t}=j(),e=B({text:"",hiddenText:"",cipherText:"",tempText:"",decodeText:""});function n(){e.text="",e.hiddenText="",e.cipherText="",e.tempText="",e.decodeText=""}function w(){try{let o=[];o=e.text.split(""),o.splice(1,0,e.hiddenText.split("").map(l=>l.codePointAt(0).toString(2)).join(" ").split("").map(l=>l==="1"?"​":l==="0"?"‌":"‍").join("‎")),e.cipherText=o.join("")}catch(o){console.error("Encoding error:",o)}}function k(){if(!e.tempText){e.decodeText="";return}try{const o=e.tempText.replace(/[\u200b-\u200f\uFEFF\u202a-\u202e]/g,""),l=e.tempText.replace(/[^\u200b-\u200f\uFEFF\u202a-\u202e]/g,"");e.decodeText=l.split("‎").map(a=>a==="​"?"1":a==="‌"?"0":" ").join("").split(" ").map(a=>String.fromCharCode(parseInt(a,2))).join("")}catch(o){console.error("Decoding error:",o),e.decodeText=y("tools.textSteganography.errors.decodingFailed")}}function b(o){navigator.clipboard.writeText(o).then(()=>{f()}).catch(()=>{t()})}return(o,l)=>(d(),x(_,{title:o.$t("tools.textSteganography.title"),description:o.$t("tools.textSteganography.description"),icon:"🔒",features:["零宽字符","文本隐写","安全加密"]},{default:i(()=>[s("div",q,[m(T,{class:"glass border-slate-700/50"},{header:i(()=>[s("div",A,[s("h3",D,r(o.$t("tools.textSteganography.encryptionTitle")),1)])]),default:i(()=>[s("div",K,[s("div",null,[s("label",Q,r(o.$t("tools.textSteganography.visibleText")),1),m(h,{modelValue:e.text,"onUpdate:modelValue":l[0]||(l[0]=a=>e.text=a),placeholder:o.$t("tools.textSteganography.visibleTextPlaceholder"),class:"w-full font-mono text-sm resize-none",rows:"4"},null,8,["modelValue","placeholder"])]),s("div",null,[s("label",R,r(o.$t("tools.textSteganography.hiddenText")),1),m(h,{modelValue:e.hiddenText,"onUpdate:modelValue":l[1]||(l[1]=a=>e.hiddenText=a),placeholder:o.$t("tools.textSteganography.hiddenTextPlaceholder"),class:"w-full font-mono text-sm resize-none",rows:"4"},null,8,["modelValue","placeholder"])]),m(g,{onClick:w,disabled:!e.text||!e.hiddenText,class:"w-full",variant:"primary",size:"lg"},{default:i(()=>[c(r(o.$t("tools.textSteganography.generateSteganography")),1)]),_:1},8,["disabled"]),s("div",null,[s("div",G,[s("label",H,r(o.$t("tools.textSteganography.steganographyResult")),1),e.cipherText?(d(),x(g,{key:0,onClick:l[2]||(l[2]=a=>b(e.cipherText)),variant:"ghost",size:"sm"},{default:i(()=>[c(r(o.$t("common.copy")),1)]),_:1})):u("",!0)]),m(h,{modelValue:e.cipherText,"onUpdate:modelValue":l[3]||(l[3]=a=>e.cipherText=a),readonly:"",class:"w-full font-mono text-sm resize-none bg-slate-800/30",rows:"4"},null,8,["modelValue"])])])]),_:1}),m(T,{class:"glass border-slate-700/50"},{header:i(()=>[s("div",J,[s("h3",O,r(o.$t("tools.textSteganography.decryptionTitle")),1)])]),default:i(()=>[s("div",W,[s("div",null,[s("label",X,r(o.$t("tools.textSteganography.steganographyText")),1),m(h,{modelValue:e.tempText,"onUpdate:modelValue":l[4]||(l[4]=a=>e.tempText=a),placeholder:o.$t("tools.textSteganography.steganographyTextPlaceholder"),class:"w-full font-mono text-sm resize-none",rows:"4",onInput:k},null,8,["modelValue","placeholder"])]),s("div",null,[s("div",Y,[s("label",Z,r(o.$t("tools.textSteganography.decodedText")),1),e.decodeText?(d(),x(g,{key:0,onClick:l[5]||(l[5]=a=>b(e.decodeText)),variant:"ghost",size:"sm"},{default:i(()=>[c(r(o.$t("common.copy")),1)]),_:1})):u("",!0)]),s("div",ee,[s("p",te,r(e.decodeText),1)])]),m(g,{onClick:n,class:"w-full",variant:"secondary",size:"lg"},{default:i(()=>[c(r(o.$t("common.clear")),1)]),_:1})])]),_:1})])]),_:1},8,["title","description"]))}});export{ae as default};
