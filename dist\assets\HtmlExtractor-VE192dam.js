import{d as z,r as k,i as U,c as m,a as t,t as a,e as b,g as C,j as g,F as A,k as $,o as h,h as H,f as E}from"./index-CkZTMFXG.js";import{u as Q}from"./useToast-virEbLJw.js";const Y={class:"min-h-screen bg-dark-950 text-slate-100"},X={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},G={class:"text-center mb-12"},J={class:"text-4xl font-bold text-slate-100 mb-4 text-gradient"},K={class:"text-xl text-slate-400 max-w-3xl mx-auto"},Z={class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},tt={class:"text-xl font-semibold text-slate-100 mb-4 border-b border-slate-700/30 pb-2"},et={class:"mb-4"},st={for:"base-url",class:"block text-sm font-medium text-slate-300 mb-2"},ot=["placeholder"],rt={class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},lt={class:"flex items-center justify-between mb-4 border-b border-slate-700/30 pb-2"},at={class:"text-lg font-semibold text-slate-100"},nt={class:"flex gap-2"},it={class:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4"},ut={class:"flex items-center space-x-3 p-3 bg-slate-800/30 rounded-xl hover:bg-slate-700/50 transition-colors cursor-pointer hover-lift group border border-slate-700/30"},ct={class:"text-sm font-medium text-slate-300 group-hover:text-primary-400 transition-colors duration-200"},dt={class:"flex items-center space-x-3 p-3 bg-slate-800/30 rounded-xl hover:bg-slate-700/50 transition-colors cursor-pointer hover-lift group border border-slate-700/30"},pt={class:"text-sm font-medium text-slate-300 group-hover:text-primary-400 transition-colors duration-200"},mt={class:"flex items-center space-x-3 p-3 bg-slate-800/30 rounded-xl hover:bg-slate-700/50 transition-colors cursor-pointer hover-lift group border border-slate-700/30"},bt={class:"text-sm font-medium text-slate-300 group-hover:text-primary-400 transition-colors duration-200"},ht={class:"flex items-center space-x-3 p-3 bg-slate-800/30 rounded-xl hover:bg-slate-700/50 transition-colors cursor-pointer hover-lift group border border-slate-700/30"},xt={class:"text-sm font-medium text-slate-300 group-hover:text-primary-400 transition-colors duration-200"},ft={class:"flex items-center space-x-3 p-3 bg-slate-800/30 rounded-xl hover:bg-slate-700/50 transition-colors cursor-pointer hover-lift group border border-slate-700/30"},gt={class:"text-sm font-medium text-slate-300 group-hover:text-primary-400 transition-colors duration-200"},vt={class:"flex items-center space-x-3 p-3 bg-slate-800/30 rounded-xl hover:bg-slate-700/50 transition-colors cursor-pointer hover-lift group border border-slate-700/30"},yt={class:"text-sm font-medium text-slate-300 group-hover:text-primary-400 transition-colors duration-200"},_t={class:"flex items-center space-x-3 p-3 bg-slate-800/30 rounded-xl hover:bg-slate-700/50 transition-colors cursor-pointer hover-lift group border border-slate-700/30"},kt={class:"text-sm font-medium text-slate-300 group-hover:text-primary-400 transition-colors duration-200"},Et={class:"flex items-center space-x-3 p-3 bg-slate-800/30 rounded-xl hover:bg-slate-700/50 transition-colors cursor-pointer hover-lift group border border-slate-700/30"},wt={class:"text-sm font-medium text-slate-300 group-hover:text-primary-400 transition-colors duration-200"},At={class:"flex items-center space-x-3 p-3 bg-slate-800/30 rounded-xl hover:bg-slate-700/50 transition-colors cursor-pointer hover-lift group border border-slate-700/30"},$t={class:"text-sm font-medium text-slate-300 group-hover:text-primary-400 transition-colors duration-200"},Ut={class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},Ct={class:"text-lg font-semibold text-slate-100 mb-4 border-b border-slate-700/30 pb-2"},St={class:"flex flex-wrap items-center gap-6"},jt={class:"flex items-center space-x-3"},qt={class:"text-sm font-medium text-slate-300"},Vt={class:"flex items-center space-x-3"},Tt={class:"text-sm font-medium text-slate-300"},Lt={class:"flex gap-3 ml-auto"},Pt=["disabled"],Rt={class:"glass rounded-xl p-6 border border-slate-700/50"},Bt={class:"text-xl font-semibold text-slate-100 mb-4 border-b border-slate-700/30 pb-2"},Mt={class:"mb-3 px-3 py-2 bg-primary-500/20 rounded-md border-l-4 border-primary-500"},Ot={class:"text-primary-400 font-medium text-sm"},It={key:0,class:"text-center py-12"},Wt={class:"text-slate-400"},Dt={key:1,class:"space-y-4"},Ft={class:"bg-slate-800 text-slate-100 px-3 py-2 font-medium flex items-center text-sm"},Nt={class:"mr-2"},zt={class:"divide-y divide-slate-700/30"},Ht={class:"mb-1"},Qt=["href"],Yt={key:0,class:"text-slate-400 text-xs mb-1"},Xt={key:1,class:"flex flex-wrap gap-1 mb-2"},Gt={key:2,class:"mt-2 p-2 bg-slate-800/30 rounded border border-slate-700/50"},Jt=["src","alt"],Kt={key:3,class:"mt-2 p-2 bg-slate-800/30 rounded border border-slate-700/50"},Zt=["src"],te={class:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-12"},ee={class:"glass p-6 rounded-xl border-l-4 border-primary-500 border border-slate-700/50"},se={class:"text-lg font-semibold text-slate-100 mb-3"},oe={class:"text-slate-400 text-sm"},re={class:"glass p-6 rounded-xl border-l-4 border-success-500 border border-slate-700/50"},le={class:"text-lg font-semibold text-slate-100 mb-3"},ae={class:"text-slate-400 text-sm"},ne={class:"glass p-6 rounded-xl border-l-4 border-purple-500 border border-slate-700/50"},ie={class:"text-lg font-semibold text-slate-100 mb-3"},ue={class:"text-slate-400 text-sm"},me=z({__name:"HtmlExtractor",setup(ce){const v=k(""),p=k(""),o=k({images:!0,videos:!0,audios:!0,links:!0,css:!0,js:!0,iframes:!0,metadata:!0,forms:!0,uniqueOnly:!0,absoluteUrls:!1}),{copySuccess:S,copyError:j}=Q(),y=k([]);function x(s,e){if(!s||!e||s.startsWith("http")||s.startsWith("//"))return s;try{const r=new URL(e);return new URL(s,r).href}catch{return s}}function q(s){const e=[];return s.querySelectorAll("img").forEach(l=>{const n=l.getAttribute("src");if(console.log(n),n){const c=o.value.absoluteUrls?x(n,p.value):n;e.push({type:"image",url:c,text:l.getAttribute("alt")||"",attributes:{alt:l.getAttribute("alt")||"",title:l.getAttribute("title")||""}})}}),s.querySelectorAll("*").forEach(l=>{const n=l.style.backgroundImage;n&&n.match(/url\(["']?([^"')]+)["']?\)/g)?.forEach(d=>{const f=d.match(/url\(["']?([^"')]+)["']?\)/);if(f?.[1]){const _=o.value.absoluteUrls?x(f[1],p.value):f[1];e.push({type:"css-background",url:_,text:"CSS Background Image"})}})}),e}function V(s){const e=[];return s.querySelectorAll("a[href]").forEach(i=>{const l=i.getAttribute("href");if(l){const n=o.value.absoluteUrls?x(l,p.value):l;e.push({type:"link",url:n,text:i.textContent?.trim()||"",attributes:{target:i.getAttribute("target")||"",rel:i.getAttribute("rel")||""}})}}),e}function T(s){const e=[];return s.querySelectorAll("video").forEach(l=>{const n=l.getAttribute("src");if(n){const d=o.value.absoluteUrls?x(n,p.value):n;e.push({type:"video",url:d,attributes:{controls:l.getAttribute("controls")||"",autoplay:l.getAttribute("autoplay")||""}})}l.querySelectorAll("source").forEach(d=>{const f=d.getAttribute("src");if(f){const _=o.value.absoluteUrls?x(f,p.value):f;e.push({type:"video",url:_,attributes:{type:d.getAttribute("type")||""}})}})}),s.querySelectorAll("audio").forEach(l=>{const n=l.getAttribute("src");if(n){const d=o.value.absoluteUrls?x(n,p.value):n;e.push({type:"audio",url:d})}l.querySelectorAll("source").forEach(d=>{const f=d.getAttribute("src");if(f){const _=o.value.absoluteUrls?x(f,p.value):f;e.push({type:"audio",url:_,attributes:{type:d.getAttribute("type")||""}})}})}),e}function L(s){const e=[];return s.querySelectorAll('link[rel="stylesheet"]').forEach(l=>{const n=l.getAttribute("href");if(n){const c=o.value.absoluteUrls?x(n,p.value):n;e.push({type:"css",url:c})}}),s.querySelectorAll("script[src]").forEach(l=>{const n=l.getAttribute("src");if(n){const c=o.value.absoluteUrls?x(n,p.value):n;e.push({type:"js",url:c})}}),e}function P(s){const e=[];return s.querySelectorAll("iframe").forEach(i=>{const l=i.getAttribute("src");if(l){const n=o.value.absoluteUrls?x(l,p.value):l;e.push({type:"iframe",url:n,attributes:{title:i.getAttribute("title")||"",width:i.getAttribute("width")||"",height:i.getAttribute("height")||""}})}}),e}function R(s){const e=[];return s.querySelectorAll("meta").forEach(i=>{const l=i.getAttribute("content"),n=i.getAttribute("name")||i.getAttribute("property");l&&n&&e.push({type:"metadata",url:l,text:n})}),e}function B(s){const e=[];return s.querySelectorAll("form").forEach(i=>{const l=i.getAttribute("action");if(l){const n=o.value.absoluteUrls?x(l,p.value):l;e.push({type:"form",url:n,attributes:{method:i.getAttribute("method")||"get",enctype:i.getAttribute("enctype")||""}})}}),e}function u(){if(!v.value.trim()){y.value=[];return}const e=new DOMParser().parseFromString(v.value,"text/html");let r=[];if(o.value.images&&(r=[...r,...q(e)]),o.value.links&&(r=[...r,...V(e)]),(o.value.videos||o.value.audios)&&(r=[...r,...T(e)]),(o.value.css||o.value.js)&&(r=[...r,...L(e)]),o.value.iframes&&(r=[...r,...P(e)]),o.value.metadata&&(r=[...r,...R(e)]),o.value.forms&&(r=[...r,...B(e)]),o.value.uniqueOnly){const i=new Set;r=r.filter(l=>{const n=`${l.type}-${l.url}`;return i.has(n)?!1:(i.add(n),!0)})}y.value=r}const M=U(()=>{const s={};return y.value.forEach(e=>{s[e.type]||(s[e.type]=[]),s[e.type].push(e)}),s}),w=U(()=>y.value.length);function O(){v.value=`<!DOCTYPE html>
<html>
<head>
    <title>Example Page</title>
    <link rel="stylesheet" href="/css/style.css">
    <meta name="description" content="This is an example page">
</head>
<body>
    <h1>Welcome to Example Page</h1>
    <img src="/images/logo.png" alt="Logo">
    <a href="https://www.example.com">External Link</a>
    <video src="/videos/demo.mp4" controls></video>
    <audio src="/audio/music.mp3"></audio>
    <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ"></iframe>
    <form action="/submit" method="post">
        <input type="text" name="username">
        <button type="submit">Submit</button>
    </form>
    <script src="/js/app.js"><\/script>
</body>
</html>`,u()}function I(){v.value="",y.value=[]}function W(){const s=y.value.map(e=>`${e.type}: ${e.url}`).join(`
`);navigator.clipboard.writeText(s).then(()=>{S()}).catch(()=>{j()})}function D(){o.value.images=!0,o.value.videos=!0,o.value.audios=!0,o.value.links=!0,o.value.css=!0,o.value.js=!0,o.value.iframes=!0,o.value.metadata=!0,o.value.forms=!0,u()}function F(){o.value.images=!1,o.value.videos=!1,o.value.audios=!1,o.value.links=!1,o.value.css=!1,o.value.js=!1,o.value.iframes=!1,o.value.metadata=!1,o.value.forms=!1,u()}function N(s){return{image:"🖼️","css-background":"🎨",video:"📹",audio:"🎵",link:"🔗",css:"🎨",js:"📜",iframe:"🖼️",metadata:"🔍",form:"📝"}[s]||"📄"}return(s,e)=>(h(),m("div",Y,[t("div",X,[t("div",G,[t("h1",J,a(s.$t("tools.htmlExtractor.title")),1),t("p",K,a(s.$t("tools.htmlExtractor.description")),1)]),t("div",Z,[t("h2",tt,a(s.$t("common.input"))+" HTML Code ",1),t("div",et,[t("label",st,a(s.$t("tools.htmlExtractor.baseUrl"))+": ",1),b(t("input",{id:"base-url",type:"url","onUpdate:modelValue":e[0]||(e[0]=r=>p.value=r),placeholder:"https://example.com",class:"w-full px-4 py-3 bg-slate-800/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-slate-100 placeholder-slate-400 transition-all duration-200"},null,512),[[C,p.value]])]),b(t("textarea",{"onUpdate:modelValue":e[1]||(e[1]=r=>v.value=r),onInput:u,placeholder:s.$t("tools.htmlExtractor.inputPlaceholder"),class:"w-full h-64 px-4 py-3 bg-slate-800/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-mono text-sm text-slate-100 placeholder-slate-400 resize-none transition-all duration-200"},null,40,ot),[[C,v.value]])]),t("div",rt,[t("div",lt,[t("h3",at,a(s.$t("tools.htmlExtractor.contentTypes")),1),t("div",nt,[t("button",{onClick:D,class:"px-3 py-1 text-sm bg-primary-500/20 text-primary-400 rounded-md hover:bg-primary-500/30 transition-colors cursor-pointer hover-lift"},a(s.$t("common.selectAll")),1),t("button",{onClick:F,class:"px-3 py-1 text-sm bg-slate-700/50 text-slate-300 rounded-md hover:bg-slate-600/50 transition-colors cursor-pointer hover-lift"},a(s.$t("common.clearSelection")),1)])]),t("div",it,[t("label",ut,[b(t("input",{type:"checkbox","onUpdate:modelValue":e[2]||(e[2]=r=>o.value.images=r),onChange:u,class:"h-4 w-4 text-primary-500 focus:ring-primary-500 border-slate-600 rounded"},null,544),[[g,o.value.images]]),t("span",ct,"🖼️ "+a(s.$t("tools.htmlExtractor.types.images")),1)]),t("label",dt,[b(t("input",{type:"checkbox","onUpdate:modelValue":e[3]||(e[3]=r=>o.value.videos=r),onChange:u,class:"h-4 w-4 text-primary-500 focus:ring-primary-500 border-slate-600 rounded"},null,544),[[g,o.value.videos]]),t("span",pt,"📹 "+a(s.$t("tools.htmlExtractor.types.videos")),1)]),t("label",mt,[b(t("input",{type:"checkbox","onUpdate:modelValue":e[4]||(e[4]=r=>o.value.audios=r),onChange:u,class:"h-4 w-4 text-primary-500 focus:ring-primary-500 border-slate-600 rounded"},null,544),[[g,o.value.audios]]),t("span",bt,"🎵 "+a(s.$t("tools.htmlExtractor.types.audio")),1)]),t("label",ht,[b(t("input",{type:"checkbox","onUpdate:modelValue":e[5]||(e[5]=r=>o.value.links=r),onChange:u,class:"h-4 w-4 text-primary-500 focus:ring-primary-500 border-slate-600 rounded"},null,544),[[g,o.value.links]]),t("span",xt,"🔗 "+a(s.$t("tools.htmlExtractor.types.links")),1)]),t("label",ft,[b(t("input",{type:"checkbox","onUpdate:modelValue":e[6]||(e[6]=r=>o.value.css=r),onChange:u,class:"h-4 w-4 text-primary-500 focus:ring-primary-500 border-slate-600 rounded"},null,544),[[g,o.value.css]]),t("span",gt,"🎨 "+a(s.$t("tools.htmlExtractor.types.css")),1)]),t("label",vt,[b(t("input",{type:"checkbox","onUpdate:modelValue":e[7]||(e[7]=r=>o.value.js=r),onChange:u,class:"h-4 w-4 text-primary-500 focus:ring-primary-500 border-slate-600 rounded"},null,544),[[g,o.value.js]]),t("span",yt,"📜 "+a(s.$t("tools.htmlExtractor.types.javascript")),1)]),t("label",_t,[b(t("input",{type:"checkbox","onUpdate:modelValue":e[8]||(e[8]=r=>o.value.iframes=r),onChange:u,class:"h-4 w-4 text-primary-500 focus:ring-primary-500 border-slate-600 rounded"},null,544),[[g,o.value.iframes]]),t("span",kt,"🖼️ "+a(s.$t("tools.htmlExtractor.types.iframes")),1)]),t("label",Et,[b(t("input",{type:"checkbox","onUpdate:modelValue":e[9]||(e[9]=r=>o.value.metadata=r),onChange:u,class:"h-4 w-4 text-primary-500 focus:ring-primary-500 border-slate-600 rounded"},null,544),[[g,o.value.metadata]]),t("span",wt,"🔍 "+a(s.$t("tools.htmlExtractor.types.metadata")),1)]),t("label",At,[b(t("input",{type:"checkbox","onUpdate:modelValue":e[10]||(e[10]=r=>o.value.forms=r),onChange:u,class:"h-4 w-4 text-primary-500 focus:ring-primary-500 border-slate-600 rounded"},null,544),[[g,o.value.forms]]),t("span",$t,"📝 "+a(s.$t("tools.htmlExtractor.types.forms")),1)])])]),t("div",Ut,[t("h3",Ct,a(s.$t("common.options")),1),t("div",St,[t("label",jt,[b(t("input",{type:"checkbox","onUpdate:modelValue":e[11]||(e[11]=r=>o.value.uniqueOnly=r),onChange:u,class:"h-4 w-4 text-primary-500 focus:ring-primary-500 border-slate-600 rounded"},null,544),[[g,o.value.uniqueOnly]]),t("span",qt,"✨ "+a(s.$t("tools.htmlExtractor.options.uniqueOnly")),1)]),t("label",Vt,[b(t("input",{type:"checkbox","onUpdate:modelValue":e[12]||(e[12]=r=>o.value.absoluteUrls=r),onChange:u,class:"h-4 w-4 text-primary-500 focus:ring-primary-500 border-slate-600 rounded"},null,544),[[g,o.value.absoluteUrls]]),t("span",Tt,"🔗 "+a(s.$t("tools.htmlExtractor.options.absoluteUrls")),1)]),t("div",Lt,[t("button",{onClick:u,class:"px-4 py-2 bg-primary-600 text-white rounded-xl hover:bg-primary-700 transition-colors font-medium cursor-pointer hover-lift"},a(s.$t("common.extract"))+" "+a(s.$t("common.results")),1),t("button",{onClick:W,disabled:w.value===0,class:"px-4 py-2 bg-success-600 text-white rounded-xl hover:bg-success-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer hover-lift"},a(s.$t("common.copy"))+" "+a(s.$t("common.results")),9,Pt),t("button",{onClick:O,class:"px-4 py-2 bg-slate-600 text-white rounded-xl hover:bg-slate-700 transition-colors font-medium cursor-pointer hover-lift"},a(s.$t("common.loadExample")),1),t("button",{onClick:I,class:"px-4 py-2 bg-danger-600 text-white rounded-xl hover:bg-danger-700 transition-colors font-medium cursor-pointer hover-lift"},a(s.$t("common.clear")),1)])])]),t("div",Rt,[t("h2",Bt,a(s.$t("tools.htmlExtractor.extractionResults")),1),t("div",Mt,[t("span",Ot,a(w.value)+" "+a(s.$t("common.items"))+" "+a(s.$t("common.found")),1)]),w.value===0?(h(),m("div",It,[e[15]||(e[15]=t("div",{class:"text-slate-400 text-5xl mb-4 animate-bounce-subtle"},"📋",-1)),t("p",Wt,a(s.$t("tools.htmlExtractor.noResults")),1)])):(h(),m("div",Dt,[(h(!0),m(A,null,$(M.value,(r,i)=>(h(),m("div",{key:i,class:"border border-slate-700/50 rounded-xl overflow-hidden"},[t("h3",Ft,[t("span",Nt,a(N(i)),1),H(" "+a(i.toUpperCase())+" ("+a(r.length)+") ",1)]),t("div",zt,[(h(!0),m(A,null,$(r,(l,n)=>(h(),m("div",{key:n,class:"px-3 py-2 hover:bg-slate-800/30"},[t("div",Ht,[t("a",{href:l.url,target:"_blank",rel:"noopener",class:"text-primary-400 hover:text-primary-300 hover:underline break-all font-medium text-sm"},a(l.url),9,Qt)]),l.text?(h(),m("div",Yt,a(l.text),1)):E("",!0),l.attributes?(h(),m("div",Xt,[(h(!0),m(A,null,$(l.attributes,(c,d)=>(h(),m("span",{key:d,class:"inline-block bg-slate-700/50 text-slate-300 px-1.5 py-0.5 rounded text-xs"},a(d)+": "+a(c),1))),128))])):E("",!0),l.type==="image"||l.type==="css-background"?(h(),m("div",Gt,[t("img",{referrerpolicy:"no-referrer",src:l.url,alt:l.text||"Preview image",class:"max-w-full max-h-48 rounded shadow-sm hover:scale-105 transition-transform duration-200",onError:e[13]||(e[13]=c=>c.target.style.display="none"),loading:"lazy"},null,40,Jt)])):E("",!0),l.type==="video"?(h(),m("div",Kt,[t("video",{src:l.url,class:"max-w-full max-h-48 rounded shadow-sm",controls:"",preload:"metadata",onError:e[14]||(e[14]=c=>c.target.style.display="none")}," Your browser does not support the video tag. ",40,Zt)])):E("",!0)]))),128))])]))),128))]))]),t("div",te,[t("div",ee,[t("h3",se," 🖼️ "+a(s.$t("tools.htmlExtractor.features.imageExtraction.title")),1),t("p",oe,a(s.$t("tools.htmlExtractor.features.imageExtraction.description")),1)]),t("div",re,[t("h3",le," 🎬 "+a(s.$t("tools.htmlExtractor.features.mediaProcessing.title")),1),t("p",ae,a(s.$t("tools.htmlExtractor.features.mediaProcessing.description")),1)]),t("div",ne,[t("h3",ie," 🔗 "+a(s.$t("tools.htmlExtractor.features.linkAnalysis.title")),1),t("p",ue,a(s.$t("tools.htmlExtractor.features.linkAnalysis.description")),1)])])])]))}});export{me as default};
