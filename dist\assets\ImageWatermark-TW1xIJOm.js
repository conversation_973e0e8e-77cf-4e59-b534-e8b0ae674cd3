const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/jszip.min-BZakjvyN.js","assets/_commonjsHelpers-DsqdWQfm.js","assets/_commonjs-dynamic-modules-TDtrdbi3.js"])))=>i.map(i=>d[i]);
import{d as we,u as ke,r as y,i as te,a1 as ye,a2 as xe,c as h,a as t,f as R,t as a,S as P,R as E,e as L,g as S,v as oe,b as _e,h as F,F as We,k as $e,_ as Ue,o as f,a3 as Re,W as Le}from"./index-CkZTMFXG.js";import{u as Ie}from"./useToast-virEbLJw.js";import{G as Ce}from"./gif-Dup4naTh.js";import{l as se}from"./index-CNw5tbJV.js";import"./_commonjsHelpers-DsqdWQfm.js";import"./_commonjs-dynamic-modules-TDtrdbi3.js";const Se={class:"min-h-screen bg-gray-50"},ze={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},Pe={class:"text-center mb-12"},Be={class:"text-4xl font-bold text-gray-900 mb-4"},je={class:"text-xl text-gray-600 max-w-3xl mx-auto"},Oe={class:"flex flex-col lg:flex-row gap-6"},Fe={class:"bg-white rounded-lg shadow-md p-6 mb-8"},De={class:"space-y-4"},Ee={class:"text-lg font-medium text-gray-900 mb-2"},Ae={class:"text-gray-600"},Te={class:"text-sm text-gray-500 mt-2"},Me={class:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"},Ve={class:"bg-white rounded-lg shadow-md p-6 mb-8"},Ge={class:"text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2"},Ne={class:"mb-6"},He={class:"block text-sm font-medium text-gray-700 mb-2"},Je={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ze={class:"font-medium"},qe={class:"text-sm text-gray-500 mt-1"},Ke={class:"font-medium"},Qe={class:"text-sm text-gray-500 mt-1"},Xe={class:"font-medium"},Ye={class:"text-sm text-gray-500 mt-1"},et={key:0,class:"mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200"},tt={class:"text-md font-medium text-gray-900 mb-3"},ot={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},st={class:"block text-sm font-medium text-gray-700 mb-1"},at=["placeholder"],rt={class:"block text-sm font-medium text-gray-700 mb-1"},lt={class:"block text-sm font-medium text-gray-700 mb-1"},it={class:"block text-sm font-medium text-gray-700 mb-1"},nt={key:1,class:"mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200"},dt={class:"text-md font-medium text-gray-900 mb-3"},ct={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},mt={class:"block text-sm font-medium text-gray-700 mb-1"},ut={key:0,class:"flex flex-col items-center"},gt=["src","alt"],pt={key:1},bt={class:"text-gray-600"},vt={class:"mb-3"},ht={class:"block text-sm font-medium text-gray-700 mb-1"},ft=["max"],wt={class:"mb-3"},kt={class:"block text-sm font-medium text-gray-700 mb-1"},yt={class:"mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200"},xt={class:"text-md font-medium text-gray-900 mb-3"},_t={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Wt={class:"block text-sm font-medium text-gray-700 mb-1"},$t={value:"top-left"},Ut={value:"top-center"},Rt={value:"top-right"},Lt={value:"center-left"},It={value:"center"},Ct={value:"center-right"},St={value:"bottom-left"},zt={value:"bottom-center"},Pt={value:"bottom-right"},Bt={class:"block text-sm font-medium text-gray-700 mb-1"},jt={class:"mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200"},Ot={class:"text-md font-medium text-gray-900 mb-3"},Ft={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Dt={class:"block text-sm font-medium text-gray-700 mb-1"},Et={class:"block text-sm font-medium text-gray-700 mb-1"},At={class:"block text-sm font-medium text-gray-700 mb-1"},Tt={key:0,class:"lg:w-1/2"},Mt={class:"bg-white rounded-lg shadow-md p-6 mb-8 h-full flex flex-col"},Vt={class:"flex justify-between items-center mb-6"},Gt={class:"text-lg font-semibold text-gray-900"},Nt={class:"flex gap-3"},Ht=["disabled"],Jt={key:0,class:"flex items-center"},Zt={key:1},qt=["disabled"],Kt={key:0,class:"mb-6 p-4 bg-green-50 rounded-lg border border-green-200"},Qt={class:"grid grid-cols-1 md:grid-cols-3 gap-4 text-center"},Xt={class:"text-2xl font-bold text-green-700"},Yt={class:"text-sm text-green-600"},eo={class:"text-2xl font-bold text-green-700"},to={class:"text-sm text-green-600"},oo={class:"text-2xl font-bold text-green-700"},so={class:"text-sm text-green-600"},ao={class:"flex-1 overflow-y-auto"},ro={class:"grid grid-cols-1 gap-4"},lo={class:"flex items-start space-x-4"},io={class:"flex-shrink-0"},no=["src","alt"],co={class:"flex-1 grid grid-cols-1 md:grid-cols-3 gap-4"},mo={class:"md:col-span-2"},uo={class:"text-sm font-medium text-gray-900 truncate"},go={class:"text-sm text-gray-500"},po={key:0,class:"mt-2 p-2 bg-blue-100 rounded text-blue-800 text-xs"},bo={class:"flex flex-wrap gap-4 mt-2"},vo={class:"text-sm text-gray-600"},ho={key:0,class:"text-sm text-green-600"},fo={class:"flex flex-col items-end space-y-2"},wo={class:"flex items-center space-x-2"},ko={class:"text-xs text-gray-600"},yo={key:0,class:"w-full max-w-[100px]"},xo={class:"bg-gray-200 rounded-full h-2"},_o={class:"text-xs text-gray-500 text-center mt-1"},Wo={class:"flex justify-end gap-2"},$o=["onClick"],Uo=["onClick"],Ro=["onClick","disabled"],Lo=["onClick"],Io={key:0,class:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"},Co={class:"relative bg-white rounded-lg shadow-lg w-full max-w-4xl mx-4"},So={class:"flex items-center justify-between p-4 border-b border-gray-200 rounded-t"},zo={class:"text-xl font-semibold text-gray-800"},Po={class:"p-6"},Bo={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},jo={class:"text-lg font-medium text-gray-900 mb-2"},Oo={class:"border border-gray-200 rounded-lg p-2"},Fo=["src","alt"],Do={class:"mt-2 text-sm text-gray-600"},Eo={key:0},Ao={class:"text-lg font-medium text-gray-900 mb-2"},To={class:"border border-gray-200 rounded-lg p-2"},Mo=["src","alt"],Vo={class:"mt-2 text-sm text-gray-600"},Go={class:"flex items-center justify-end p-6 border-t border-gray-200 rounded-b"},No={class:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-12"},Ho={class:"bg-white p-6 rounded-lg shadow-md border-l-4 border-blue-500"},Jo={class:"text-lg font-semibold text-gray-900 mb-3"},Zo={class:"text-gray-600 text-sm"},qo={class:"bg-white p-6 rounded-lg shadow-md border-l-4 border-green-500"},Ko={class:"text-lg font-semibold text-gray-900 mb-3"},Qo={class:"text-gray-600 text-sm"},Xo={class:"bg-white p-6 rounded-lg shadow-md border-l-4 border-purple-500"},Yo={class:"text-lg font-semibold text-gray-900 mb-3"},es={class:"text-gray-600 text-sm"},ts=we({__name:"ImageWatermark",setup(os){const{t:I}=ke(),{success:G,error:B}=Ie(),j=y(),A=y(),n=y([]),T=y(!1),Z=y(!1),D=y(!1),M=y(!1),_=y(null),b=y("text"),U=y({text:"Watermark",fontSize:24,color:"#ffffff",fontFamily:"Arial"}),k=y({file:null,preview:""}),W=y({width:100,maxWidth:300,opacity:80}),z=y({position:"bottom-right",margin:20}),x=y({opacity:80,rotation:0,scale:100}),ae=te(()=>n.value.some(e=>e.status==="completed")),V=te(()=>{const e=n.value.filter(i=>i.status==="completed"),o=e.reduce((i,l)=>i+l.originalSize,0),s=e.reduce((i,l)=>i+(l.processedSize||0),0),r=e.length;return{totalOriginalSize:o,totalProcessedSize:s,processedCount:r}});function re(){j.value?.click()}function le(e){const o=e.target.files;o&&N(Array.from(o)),j.value&&(j.value.value="")}function ie(e){e.preventDefault(),T.value=!1;const o=e.dataTransfer?.files;o&&N(Array.from(o))}async function N(e){const o=e.filter(s=>s.type.startsWith("image/"));if(o.length===0){B(I("tools.imageWatermark.errors.noValidImages"));return}for(const s of o){const r=n.value.findIndex(l=>l.name===s.name&&l.originalSize===s.size),i=s.type==="image/gif";if(r!==-1)try{const l=await H(s),d=await J(s);n.value[r]={name:s.name,file:s,preview:d,originalSize:s.size,dimensions:l,status:"pending",progress:0,isGif:i}}catch(l){console.error("Error processing file:",s.name,l)}else try{const l=await H(s),d=await J(s),c={name:s.name,file:s,preview:d,originalSize:s.size,dimensions:l,status:"pending",progress:0,isGif:i};n.value.push(c)}catch(l){console.error("Error processing file:",s.name,l)}}}function ne(){A.value?.click()}function de(e){const o=e.target.files;o&&o.length>0&&q(o[0]),A.value&&(A.value.value="")}function ce(e){e.preventDefault(),Z.value=!1;const o=e.dataTransfer?.files;o&&o.length>0&&q(o[0])}async function q(e){if(!e.type.startsWith("image/")){B(I("tools.imageWatermark.errors.invalidWatermark"));return}try{k.value.preview&&URL.revokeObjectURL(k.value.preview);const o=await J(e),s=await H(e);k.value={file:e,preview:o},W.value.maxWidth=Math.min(s.width,500),W.value.width=Math.min(W.value.width,W.value.maxWidth)}catch(o){console.error("Error processing watermark image:",o),B(I("tools.imageWatermark.errors.watermarkProcessingFailed"))}}function me(){k.value.preview&&URL.revokeObjectURL(k.value.preview),k.value={file:null,preview:""}}function H(e){return new Promise((o,s)=>{const r=new Image;r.onload=()=>{o({width:r.width,height:r.height})},r.onerror=s,r.src=URL.createObjectURL(e)})}function J(e){return new Promise((o,s)=>{const r=new FileReader;r.onload=i=>o(i.target?.result),r.onerror=s,r.readAsDataURL(e)})}async function K(e){const o=n.value[e];if(o){if((b.value==="image"||b.value==="combined")&&!k.value.file){B(I("tools.imageWatermark.errors.noWatermarkImage"));return}if((b.value==="text"||b.value==="combined")&&!U.value.text.trim()){B(I("tools.imageWatermark.errors.noWatermarkText"));return}o.status="processing",o.progress=0;try{o.isGif?await ue(o,e):await Q(o,e)}catch(s){console.error("Processing error:",s),n.value[e].status="error",B(I("tools.imageWatermark.errors.processingFailed",{filename:o.name}))}}}async function Q(e,o){const s=document.createElement("canvas"),r=s.getContext("2d");if(!r)throw new Error("Could not get canvas context");const i=new Image;await new Promise((d,c)=>{i.onload=d,i.onerror=c,i.src=e.preview}),s.width=i.width,s.height=i.height,r.drawImage(i,0,0,s.width,s.height),r.globalAlpha=x.value.opacity/100,(b.value==="text"||b.value==="combined")&&await X(r,s),(b.value==="image"||b.value==="combined")&&await Y(r,s),r.globalAlpha=1;for(let d=20;d<=80;d+=20)n.value[o].progress=d,await new Promise(c=>setTimeout(c,100));const l=await new Promise(d=>{s.toBlob(c=>{d(c)},"image/png",.9)});n.value[o].progress=100,n.value[o].processedBlob=l,n.value[o].processedSize=l.size,n.value[o].status="completed",n.value[o].processedBlob&&(n.value[o].processedPreviewUrl=URL.createObjectURL(n.value[o].processedBlob))}async function ue(e,o){try{const s=await e.file.arrayBuffer(),r=se.parseGIF(s),i=se.decompressFrames(r,!0);if(i.length===0)throw new Error("No frames found in GIF");const l=r.lsd.width,d=r.lsd.height,c=document.createElement("canvas");c.width=l,c.height=d;const v=c.getContext("2d");if(!v)throw new Error("Could not get canvas context");const C=new Ce({workers:2,quality:10,width:l,height:d,workerScript:"/gif.worker.js"}),w=document.createElement("canvas");w.width=l,w.height=d;const u=w.getContext("2d");if(!u)throw new Error("Could not get offscreen canvas context");for(let m=0;m<i.length;m++){const g=i[m];if(m===0)u.clearRect(0,0,l,d);else{const $=i[m-1];$.disposalType===2?u.clearRect(0,0,l,d):$.disposalType}if(g.patch){const $=new ImageData(new Uint8ClampedArray(g.patch),g.dims.width,g.dims.height);u.putImageData($,g.dims.left,g.dims.top)}v.clearRect(0,0,l,d),v.drawImage(w,0,0),v.globalAlpha=x.value.opacity/100,(b.value==="text"||b.value==="combined")&&await X(v,c),(b.value==="image"||b.value==="combined")&&await Y(v,c),v.globalAlpha=1,C.addFrame(v,{copy:!0,delay:g.delay||100}),n.value[o].progress=Math.round((m+1)/i.length*80)}const p=await new Promise((m,g)=>{C.on("finished",$=>{m($)}),C.on("error",$=>{g(new Error("GIF processing error: "+$.message))});try{C.render()}catch($){g(new Error("Failed to start GIF rendering: "+$.message))}});n.value[o].progress=100,n.value[o].processedBlob=p,n.value[o].processedSize=p.size,n.value[o].status="completed",n.value[o].processedBlob&&(n.value[o].processedPreviewUrl=URL.createObjectURL(n.value[o].processedBlob))}catch(s){console.error("GIF processing error:",s),await Q(e,o)}}async function X(e,o){const{text:s,fontSize:r,color:i,fontFamily:l}=U.value,{position:d,margin:c}=z.value,{rotation:v,scale:C}=x.value;e.font=`${r*C/100}px ${l}`,e.fillStyle=i,e.textAlign="left",e.textBaseline="top";const u=e.measureText(s).width,p=r*C/100;let m=0,g=0;switch(d){case"top-left":m=c,g=c;break;case"top-center":m=(o.width-u)/2,g=c;break;case"top-right":m=o.width-u-c,g=c;break;case"center-left":m=c,g=(o.height-p)/2;break;case"center":m=(o.width-u)/2,g=(o.height-p)/2;break;case"center-right":m=o.width-u-c,g=(o.height-p)/2;break;case"bottom-left":m=c,g=o.height-p-c;break;case"bottom-center":m=(o.width-u)/2,g=o.height-p-c;break;case"bottom-right":m=o.width-u-c,g=o.height-p-c;break}v!==0?(e.save(),e.translate(m+u/2,g+p/2),e.rotate(v*Math.PI/180),e.fillText(s,-u/2,-p/2),e.restore()):e.fillText(s,m,g)}async function Y(e,o){if(!k.value.file||!k.value.preview)return;const{width:s,opacity:r}=W.value,{position:i,margin:l}=z.value,{rotation:d,scale:c}=x.value,v=new Image;await new Promise((g,$)=>{v.onload=g,v.onerror=$,v.src=k.value.preview});const C=v.height/v.width,w=s*c/100,u=w*C;let p=0,m=0;switch(i){case"top-left":p=l,m=l;break;case"top-center":p=(o.width-w)/2,m=l;break;case"top-right":p=o.width-w-l,m=l;break;case"center-left":p=l,m=(o.height-u)/2;break;case"center":p=(o.width-w)/2,m=(o.height-u)/2;break;case"center-right":p=o.width-w-l,m=(o.height-u)/2;break;case"bottom-left":p=l,m=o.height-u-l;break;case"bottom-center":p=(o.width-w)/2,m=o.height-u-l;break;case"bottom-right":p=o.width-w-l,m=o.height-u-l;break}e.globalAlpha=r/100,d!==0?(e.save(),e.translate(p+w/2,m+u/2),e.rotate(d*Math.PI/180),e.drawImage(v,-w/2,-u/2,w,u),e.restore()):e.drawImage(v,p,m,w,u),e.globalAlpha=1}async function ge(){D.value=!0;const e=n.value.map((s,r)=>({img:s,index:r})),o=3;for(let s=0;s<e.length;s+=o){const r=e.slice(s,s+o);await Promise.all(r.map(({index:i})=>K(i)))}D.value=!1,G(I("tools.imageWatermark.success.processingComplete"))}function pe(e){const o=n.value[e];if(!o.processedBlob)return;const s=URL.createObjectURL(o.processedBlob),r=document.createElement("a");r.href=s,r.download=`${o.name.replace(/\.[^/.]+$/,"")}_watermark.${o.isGif?"gif":"png"}`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(s)}async function be(){const{default:e}=await Ue(async()=>{const{default:d}=await import("./jszip.min-BZakjvyN.js").then(c=>c.j);return{default:d}},__vite__mapDeps([0,1,2])),o=new e,s=n.value.filter(d=>d.status==="completed"&&d.processedBlob);for(const d of s)if(d.processedBlob){const c=d.isGif?"gif":"png",v=`${d.name.replace(/\.[^/.]+$/,"")}_watermark.${c}`;o.file(v,d.processedBlob)}const r=await o.generateAsync({type:"blob"}),i=URL.createObjectURL(r),l=document.createElement("a");l.href=i,l.download=`watermarked_images_${new Date().toISOString().split("T")[0]}.zip`,document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(i),G(I("tools.imageWatermark.success.downloadComplete"))}function O(e){if(e===0)return"0 B";const o=1024,s=["B","KB","MB","GB"],r=Math.floor(Math.log(e)/Math.log(o));return parseFloat((e/Math.pow(o,r)).toFixed(1))+" "+s[r]}function ve(e){const o=n.value[e];URL.revokeObjectURL(o.preview),o.processedBlob&&URL.revokeObjectURL(URL.createObjectURL(o.processedBlob)),o.processedPreviewUrl&&URL.revokeObjectURL(o.processedPreviewUrl),n.value.splice(e,1)}function he(){n.value.forEach(e=>{URL.revokeObjectURL(e.preview),e.processedBlob&&URL.revokeObjectURL(URL.createObjectURL(e.processedBlob)),e.processedPreviewUrl&&URL.revokeObjectURL(e.processedPreviewUrl)}),n.value=[],j.value&&(j.value.value="")}function fe(e){_.value=n.value[e],M.value=!0}function ee(e){const o=e.clipboardData?.items;if(!o)return;const s=[];for(const r of o)if(r.type.startsWith("image/")){const i=r.getAsFile();i&&s.push(i)}s.length>0&&(N(s),G(I("tools.imageWatermark.success.pasteSuccess")))}return ye(()=>{document.addEventListener("paste",ee),document.addEventListener("dragenter",e=>{e.preventDefault(),T.value=!0}),document.addEventListener("dragleave",e=>{e.relatedTarget||(T.value=!1)})}),xe(()=>{document.removeEventListener("paste",ee),n.value.forEach(e=>{URL.revokeObjectURL(e.preview),e.processedBlob&&URL.revokeObjectURL(URL.createObjectURL(e.processedBlob)),e.processedPreviewUrl&&URL.revokeObjectURL(e.processedPreviewUrl)}),k.value.preview&&URL.revokeObjectURL(k.value.preview)}),(e,o)=>(f(),h("div",Se,[t("div",ze,[t("div",Pe,[t("h1",Be,a(e.$t("tools.imageWatermark.title")),1),t("p",je,a(e.$t("tools.imageWatermark.description")),1)]),t("div",Oe,[t("div",{class:P(n.value.length>0?"lg:w-1/2":"w-full")},[t("div",Fe,[t("div",{onDrop:ie,onDragover:o[0]||(o[0]=E(()=>{},["prevent"])),onDragenter:o[1]||(o[1]=E(()=>{},["prevent"])),onClick:re,class:P(["border-2 border-dashed rounded-lg p-12 text-center cursor-pointer transition-colors",T.value?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-gray-400"])},[t("input",{ref_key:"fileInput",ref:j,type:"file",multiple:"",accept:"image/*",onChange:le,class:"hidden"},null,544),t("div",De,[o[20]||(o[20]=t("div",{class:"text-6xl text-gray-400"},"💧",-1)),t("div",null,[t("h3",Ee,a(e.$t("tools.imageWatermark.uploadTitle")),1),t("p",Ae,a(e.$t("tools.imageWatermark.uploadDescription")),1),t("p",Te,a(e.$t("tools.imageWatermark.supportedFormats"))+": JPG, PNG, WebP, GIF ",1)]),t("button",Me,a(e.$t("tools.imageWatermark.selectFiles")),1)])],34)]),t("div",Ve,[t("h3",Ge,a(e.$t("tools.imageWatermark.settings")),1),t("div",Ne,[t("label",He,a(e.$t("tools.imageWatermark.watermarkType")),1),t("div",Je,[t("button",{onClick:o[2]||(o[2]=s=>b.value="text"),class:P(["px-4 py-3 rounded-lg border transition-colors text-center",b.value==="text"?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 hover:border-gray-400"])},[t("div",Ze,a(e.$t("tools.imageWatermark.textWatermark")),1),t("div",qe,a(e.$t("tools.imageWatermark.textWatermarkDesc")),1)],2),t("button",{onClick:o[3]||(o[3]=s=>b.value="image"),class:P(["px-4 py-3 rounded-lg border transition-colors text-center",b.value==="image"?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 hover:border-gray-400"])},[t("div",Ke,a(e.$t("tools.imageWatermark.imageWatermark")),1),t("div",Qe,a(e.$t("tools.imageWatermark.imageWatermarkDesc")),1)],2),t("button",{onClick:o[4]||(o[4]=s=>b.value="combined"),class:P(["px-4 py-3 rounded-lg border transition-colors text-center",b.value==="combined"?"border-blue-500 bg-blue-50 text-blue-700":"border-gray-300 hover:border-gray-400"])},[t("div",Xe,a(e.$t("tools.imageWatermark.combinedWatermark")),1),t("div",Ye,a(e.$t("tools.imageWatermark.combinedWatermarkDesc")),1)],2)])]),b.value==="text"||b.value==="combined"?(f(),h("div",et,[t("h4",tt,a(e.$t("tools.imageWatermark.textSettings")),1),t("div",ot,[t("div",null,[t("label",st,a(e.$t("tools.imageWatermark.watermarkText")),1),L(t("input",{"onUpdate:modelValue":o[5]||(o[5]=s=>U.value.text=s),type:"text",placeholder:e.$t("tools.imageWatermark.textPlaceholder"),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,8,at),[[S,U.value.text]])]),t("div",null,[t("label",rt,a(e.$t("tools.imageWatermark.fontSize"))+": "+a(U.value.fontSize)+"px ",1),L(t("input",{"onUpdate:modelValue":o[6]||(o[6]=s=>U.value.fontSize=s),type:"range",min:"12",max:"100",class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"},null,512),[[S,U.value.fontSize]])]),t("div",null,[t("label",lt,a(e.$t("tools.imageWatermark.fontColor")),1),L(t("input",{"onUpdate:modelValue":o[7]||(o[7]=s=>U.value.color=s),type:"color",class:"w-full h-10 border border-gray-300 rounded-lg cursor-pointer"},null,512),[[S,U.value.color]])]),t("div",null,[t("label",it,a(e.$t("tools.imageWatermark.fontFamily")),1),L(t("select",{"onUpdate:modelValue":o[8]||(o[8]=s=>U.value.fontFamily=s),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},[...o[21]||(o[21]=[_e('<option value="Arial" data-v-1ab28b4e>Arial</option><option value="Verdana" data-v-1ab28b4e>Verdana</option><option value="Helvetica" data-v-1ab28b4e>Helvetica</option><option value="Times New Roman" data-v-1ab28b4e>Times New Roman</option><option value="Courier New" data-v-1ab28b4e>Courier New</option>',5)])],512),[[oe,U.value.fontFamily]])])])])):R("",!0),b.value==="image"||b.value==="combined"?(f(),h("div",nt,[t("h4",dt,a(e.$t("tools.imageWatermark.imageSettings")),1),t("div",ct,[t("div",null,[t("label",mt,a(e.$t("tools.imageWatermark.watermarkImage")),1),t("div",{onDrop:ce,onDragover:o[9]||(o[9]=E(()=>{},["prevent"])),onDragenter:o[10]||(o[10]=E(()=>{},["prevent"])),onClick:ne,class:P(["border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors",Z.value?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-gray-400"])},[t("input",{ref_key:"watermarkInput",ref:A,type:"file",accept:"image/*",onChange:de,class:"hidden"},null,544),k.value.preview?(f(),h("div",ut,[t("img",{src:k.value.preview,alt:e.$t("tools.imageWatermark.watermarkPreview"),class:"max-h-24 max-w-full mb-2"},null,8,gt),t("button",{onClick:E(me,["stop"]),class:"text-sm text-red-600 hover:text-red-800"},a(e.$t("tools.imageWatermark.removeWatermark")),1)])):(f(),h("div",pt,[o[22]||(o[22]=t("div",{class:"text-4xl text-gray-400 mb-2"},"🖼️",-1)),t("p",bt,a(e.$t("tools.imageWatermark.uploadWatermark")),1)]))],34)]),t("div",null,[t("div",vt,[t("label",ht,a(e.$t("tools.imageWatermark.imageWidth"))+": "+a(W.value.width)+"px ",1),L(t("input",{"onUpdate:modelValue":o[11]||(o[11]=s=>W.value.width=s),type:"range",min:"20",max:W.value.maxWidth,class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"},null,8,ft),[[S,W.value.width]])]),t("div",wt,[t("label",kt,a(e.$t("tools.imageWatermark.imageOpacity"))+": "+a(W.value.opacity)+"% ",1),L(t("input",{"onUpdate:modelValue":o[12]||(o[12]=s=>W.value.opacity=s),type:"range",min:"0",max:"100",class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"},null,512),[[S,W.value.opacity]])])])])])):R("",!0),t("div",yt,[t("h4",xt,a(e.$t("tools.imageWatermark.positionSettings")),1),t("div",_t,[t("div",null,[t("label",Wt,a(e.$t("tools.imageWatermark.position")),1),L(t("select",{"onUpdate:modelValue":o[13]||(o[13]=s=>z.value.position=s),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},[t("option",$t,a(e.$t("tools.imageWatermark.topLeft")),1),t("option",Ut,a(e.$t("tools.imageWatermark.topCenter")),1),t("option",Rt,a(e.$t("tools.imageWatermark.topRight")),1),t("option",Lt,a(e.$t("tools.imageWatermark.centerLeft")),1),t("option",It,a(e.$t("tools.imageWatermark.center")),1),t("option",Ct,a(e.$t("tools.imageWatermark.centerRight")),1),t("option",St,a(e.$t("tools.imageWatermark.bottomLeft")),1),t("option",zt,a(e.$t("tools.imageWatermark.bottomCenter")),1),t("option",Pt,a(e.$t("tools.imageWatermark.bottomRight")),1)],512),[[oe,z.value.position]])]),t("div",null,[t("label",Bt,a(e.$t("tools.imageWatermark.margin"))+": "+a(z.value.margin)+"px ",1),L(t("input",{"onUpdate:modelValue":o[14]||(o[14]=s=>z.value.margin=s),type:"range",min:"0",max:"100",class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"},null,512),[[S,z.value.margin]])])])]),t("div",jt,[t("h4",Ot,a(e.$t("tools.imageWatermark.advancedSettings")),1),t("div",Ft,[t("div",null,[t("label",Dt,a(e.$t("tools.imageWatermark.opacity"))+": "+a(x.value.opacity)+"% ",1),L(t("input",{"onUpdate:modelValue":o[15]||(o[15]=s=>x.value.opacity=s),type:"range",min:"0",max:"100",class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"},null,512),[[S,x.value.opacity]])]),t("div",null,[t("label",Et,a(e.$t("tools.imageWatermark.rotation"))+": "+a(x.value.rotation)+"° ",1),L(t("input",{"onUpdate:modelValue":o[16]||(o[16]=s=>x.value.rotation=s),type:"range",min:"0",max:"360",class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"},null,512),[[S,x.value.rotation]])]),t("div",null,[t("label",At,a(e.$t("tools.imageWatermark.scale"))+": "+a(x.value.scale)+"% ",1),L(t("input",{"onUpdate:modelValue":o[17]||(o[17]=s=>x.value.scale=s),type:"range",min:"10",max:"200",class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"},null,512),[[S,x.value.scale]])])])])])],2),n.value.length>0?(f(),h("div",Tt,[t("div",Mt,[t("div",Vt,[t("h3",Gt,a(e.$t("tools.imageWatermark.imageList"))+" ("+a(n.value.length)+") ",1),t("div",Nt,[t("button",{onClick:ge,disabled:D.value,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"},[D.value?(f(),h("span",Jt,[o[23]||(o[23]=t("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},[t("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),t("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)),F(" "+a(e.$t("tools.imageWatermark.processing")),1)])):(f(),h("span",Zt,a(e.$t("tools.imageWatermark.processAll")),1))],8,Ht),t("button",{onClick:be,disabled:!ae.value,class:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"},a(e.$t("tools.imageWatermark.downloadAll")),9,qt),t("button",{onClick:he,class:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"},a(e.$t("common.clear")),1)])]),V.value.totalOriginalSize>0?(f(),h("div",Kt,[t("div",Qt,[t("div",null,[t("div",Xt,a(O(V.value.totalOriginalSize)),1),t("div",Yt,a(e.$t("tools.imageWatermark.originalSize")),1)]),t("div",null,[t("div",eo,a(O(V.value.totalProcessedSize)),1),t("div",to,a(e.$t("tools.imageWatermark.processedSize")),1)]),t("div",null,[t("div",oo,a(V.value.processedCount)+"/"+a(n.value.length),1),t("div",so,a(e.$t("tools.imageWatermark.processed")),1)])])])):R("",!0),t("div",ao,[t("div",ro,[(f(!0),h(We,null,$e(n.value,(s,r)=>(f(),h("div",{key:r,class:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow flex flex-col gap-4"},[t("div",lo,[t("div",io,[t("img",{src:s.preview,alt:s.name,class:"w-24 h-24 object-cover rounded-lg border border-gray-200"},null,8,no)]),t("div",co,[t("div",mo,[t("h4",uo,a(s.name),1),t("p",go,a(s.dimensions.width)+" × "+a(s.dimensions.height)+" px ",1),s.isGif?(f(),h("div",po," ℹ️ "+a(e.$t("tools.imageWatermark.gifWarning")),1)):R("",!0),t("div",bo,[t("div",vo,a(e.$t("tools.imageWatermark.original"))+": "+a(O(s.originalSize)),1),s.processedSize?(f(),h("div",ho,a(e.$t("tools.imageWatermark.processed"))+": "+a(O(s.processedSize)),1)):R("",!0)])]),t("div",fo,[t("div",wo,[t("div",{class:P(["w-3 h-3 rounded-full",s.status==="pending"?"bg-gray-400":s.status==="processing"?"bg-yellow-400":s.status==="completed"?"bg-green-400":"bg-red-400"])},null,2),t("span",ko,a(e.$t(`tools.imageWatermark.status.${s.status}`)),1)]),s.status==="processing"?(f(),h("div",yo,[t("div",xo,[t("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:Re({width:`${s.progress}%`})},null,4)]),t("div",_o,a(s.progress)+"% ",1)])):R("",!0)])])]),t("div",Wo,[s.status==="completed"&&s.processedBlob?(f(),h("button",{key:0,onClick:i=>fe(r),class:"px-3 py-1 text-sm bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors"},a(e.$t("common.preview")),9,$o)):R("",!0),s.status==="completed"&&s.processedBlob?(f(),h("button",{key:1,onClick:i=>pe(r),class:"px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors"},a(e.$t("common.download")),9,Uo)):R("",!0),t("button",{onClick:i=>K(r),disabled:D.value,class:"px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:opacity-50"},a(e.$t("tools.imageWatermark.process")),9,Ro),t("button",{onClick:i=>ve(r),class:"px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700 transition-colors"},a(e.$t("tools.imageWatermark.remove")),9,Lo)])]))),128))])])])])):R("",!0)]),M.value?(f(),h("div",Io,[t("div",Co,[t("div",So,[t("h3",zo,a(e.$t("tools.imageWatermark.imagePreview")),1),t("button",{onClick:o[18]||(o[18]=s=>M.value=!1),class:"text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center"},[...o[24]||(o[24]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])])]),t("div",Po,[t("div",Bo,[t("div",null,[t("h4",jo,a(e.$t("tools.imageWatermark.originalImage")),1),t("div",Oo,[t("img",{src:_.value?.preview,alt:_.value?.name,class:"w-full h-auto max-h-96 object-contain"},null,8,Fo)]),t("div",Do,[F(a(_.value?.name),1),o[25]||(o[25]=t("br",null,null,-1)),F(" "+a(e.$t("tools.imageWatermark.size"))+": "+a(O(_.value?.originalSize||0)),1),o[26]||(o[26]=t("br",null,null,-1)),F(" "+a(e.$t("tools.imageWatermark.dimensions"))+": "+a(_.value?.dimensions.width)+" × "+a(_.value?.dimensions.height)+" px ",1)])]),_.value?.processedBlob?(f(),h("div",Eo,[t("h4",Ao,a(e.$t("tools.imageWatermark.processedImage")),1),t("div",To,[t("img",{src:_.value?.processedPreviewUrl,alt:_.value?.name,class:"w-full h-auto max-h-96 object-contain"},null,8,Mo)]),t("div",Vo,[F(a(_.value?.name),1),o[27]||(o[27]=t("br",null,null,-1)),F(" "+a(e.$t("tools.imageWatermark.size"))+": "+a(O(_.value?.processedSize||0)),1)])])):R("",!0)])]),t("div",Go,[t("button",{onClick:o[19]||(o[19]=s=>M.value=!1),class:"text-gray-600 bg-gray-200 hover:bg-gray-300 focus:ring-4 focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5"},a(e.$t("common.close")),1)])])])):R("",!0),t("div",No,[t("div",Ho,[t("h3",Jo," 💧 "+a(e.$t("tools.imageWatermark.features.watermark.title")),1),t("p",Zo,a(e.$t("tools.imageWatermark.features.watermark.description")),1)]),t("div",qo,[t("h3",Ko," 🖼️ "+a(e.$t("tools.imageWatermark.features.batch.title")),1),t("p",Qo,a(e.$t("tools.imageWatermark.features.batch.description")),1)]),t("div",Xo,[t("h3",Yo," ⚙️ "+a(e.$t("tools.imageWatermark.features.customization.title")),1),t("p",es,a(e.$t("tools.imageWatermark.features.customization.description")),1)])])])]))}}),ds=Le(ts,[["__scopeId","data-v-1ab28b4e"]]);export{ds as default};
