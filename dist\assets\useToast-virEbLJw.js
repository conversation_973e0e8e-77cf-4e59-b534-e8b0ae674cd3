import{d as i,r as _,i as h,a1 as B,P as b,o as d,U as L,Q as M,c as C,f as I,a as c,Z as T,t as z,S as k,a8 as S,a9 as E,aa as n,u as V,ab as N}from"./index-CkZTMFXG.js";const D={class:"flex-shrink-0"},H={class:"flex-1 min-w-0"},W={key:0,class:"text-sm font-semibold mb-1"},A={class:"text-sm"},F=i({__name:"Toast",props:{type:{default:"info"},title:{},message:{},duration:{default:5e3},onClose:{}},setup(s){const o=s,a=_(!1);let r=null;const u=h(()=>({success:"bg-green-50 border-green-200 text-green-800",error:"bg-red-50 border-red-200 text-red-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800",info:"bg-blue-50 border-blue-200 text-blue-800"})[o.type]),p=i({name:"IconSuccess",render(){return n("svg",{class:"w-6 h-6 text-green-500",fill:"currentColor",viewBox:"0 0 20 20"},[n("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"})])}}),v=i({name:"IconError",render(){return n("svg",{class:"w-6 h-6 text-red-500",fill:"currentColor",viewBox:"0 0 20 20"},[n("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})])}}),m=i({name:"IconWarning",render(){return n("svg",{class:"w-6 h-6 text-yellow-500",fill:"currentColor",viewBox:"0 0 20 20"},[n("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])}}),x=i({name:"IconInfo",render(){return n("svg",{class:"w-6 h-6 text-blue-500",fill:"currentColor",viewBox:"0 0 20 20"},[n("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z","clip-rule":"evenodd"})])}}),g=h(()=>({success:p,error:v,warning:m,info:x})[o.type]);function t(){a.value=!1,r&&clearTimeout(r),setTimeout(()=>{o.onClose?.()},200)}return B(()=>{a.value=!0,o.duration>0&&(r=setTimeout(t,o.duration))}),(e,l)=>(d(),b(E,{to:"body"},[L(S,{"enter-active-class":"duration-300 ease-out","enter-from-class":"transform translate-x-full opacity-0","enter-to-class":"translate-x-0 opacity-100","leave-active-class":"duration-200 ease-in","leave-from-class":"translate-x-0 opacity-100","leave-to-class":"transform translate-x-full opacity-0"},{default:M(()=>[a.value?(d(),C("div",{key:0,class:k(["fixed top-4 right-4 z-50 max-w-sm w-full shadow-lg rounded-lg border",u.value,"flex items-start p-4 space-x-3"])},[c("div",D,[(d(),b(T(g.value),{class:"w-6 h-6"}))]),c("div",H,[e.title?(d(),C("div",W,z(e.title),1)):I("",!0),c("div",A,z(e.message),1)]),c("button",{onClick:t,class:"flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors cursor-pointer"},[...l[0]||(l[0]=[c("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[c("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)])])],2)):I("",!0)]),_:1})]))}}),f=_([]);let P=0;function U(){const{t:s}=V();function o(t){const e=`toast-${++P}`,l={...t,id:e,onClose:()=>a(e)};f.value.push(l);const w=N(F,{...l,onClose:()=>{a(e),w.unmount()}}),y=document.createElement("div");return document.body.appendChild(y),w.mount(y),e}function a(t){const e=f.value.findIndex(l=>l.id===t);e>-1&&f.value.splice(e,1)}function r(t,e){return o({type:"success",title:e||s("toast.success"),message:t})}function u(t,e){return o({type:"error",title:e||s("toast.error"),message:t})}function p(t,e){return o({type:"warning",title:e||s("toast.warning"),message:t})}function v(t,e){return o({type:"info",title:e||s("toast.info"),message:t})}function m(){r(s("toast.copied"))}function x(){u(s("toast.copyFailed"))}function g(){r(s("toast.downloadSuccess"))}return{toasts:f,showToast:o,success:r,error:u,warning:p,info:v,copySuccess:m,copyError:x,downloadSuccess:g,removeToast:a}}export{U as u};
