import{d as ee,u as te,r as $,w as re,c as _,a as o,t as u,e as k,f as D,v as P,g as R,h as oe,o as T}from"./index-CkZTMFXG.js";import{u as se}from"./useToast-virEbLJw.js";const ne={class:"min-h-screen bg-gray-50 p-6"},le={class:"max-w-6xl mx-auto space-y-6"},ae={class:"text-center mb-8"},ie={class:"text-3xl font-bold text-gray-900 mb-2"},ue={class:"text-gray-600"},de={class:"grid md:grid-cols-3 gap-6 mb-8"},ce={class:"bg-white p-6 rounded-lg shadow-sm border"},ve={class:"text-lg font-semibold mb-2"},fe={class:"text-gray-600 text-sm"},me={class:"bg-white p-6 rounded-lg shadow-sm border"},pe={class:"text-lg font-semibold mb-2"},ge={class:"text-gray-600 text-sm"},he={class:"bg-white p-6 rounded-lg shadow-sm border"},ye={class:"text-lg font-semibold mb-2"},be={class:"text-gray-600 text-sm"},xe={class:"grid lg:grid-cols-2 gap-6"},we={class:"bg-white p-6 rounded-lg shadow-sm border"},$e={class:"flex items-center justify-between mb-4"},Ce={class:"text-lg font-semibold text-gray-900"},Ee={class:"flex space-x-2"},_e=["disabled"],ke={class:"mb-4"},Te={class:"text-sm font-medium text-gray-700 block mb-2"},Ne=["placeholder"],Se={key:0,class:"mt-2 text-red-600 text-sm"},Oe={class:"bg-white p-6 rounded-lg shadow-sm border"},Ie={class:"flex items-center justify-between mb-4"},De={class:"text-lg font-semibold text-gray-900"},Ae={class:"flex space-x-2"},Ue=["disabled"],Ve={class:"mb-4"},Xe={class:"text-sm font-medium text-gray-700 block mb-2"},qe=["placeholder"],Fe={key:0,class:"mt-2 text-red-600 text-sm"},Je={class:"bg-white p-6 rounded-lg shadow-sm border"},Pe={class:"flex items-center justify-between"},Re={class:"text-lg font-semibold text-gray-900 mb-2"},Le={class:"text-gray-600 text-sm"},Me={class:"flex items-center space-x-4"},ze=["disabled"],Be=["disabled"],je=ee({__name:"UniversalConverter",setup(Ke){const{t:h}=te(),{error:N,copySuccess:L,copyError:M}=se(),m=$(""),c=$(""),x=$("json"),w=$("xml"),y=$(""),b=$("");let S=null;function A(e){S&&clearTimeout(S),S=window.setTimeout(()=>{e==="left"?C():E()},500)}function C(){if(!m.value.trim()){c.value="",b.value="";return}try{y.value="",b.value="";const e=U(m.value,x.value,w.value);c.value=e}catch(e){b.value=e.message||h("tools.universalConverter.errors.conversionFailed"),N(b.value)}}function E(){if(!c.value.trim()){m.value="",y.value="";return}try{y.value="",b.value="";const e=U(c.value,w.value,x.value);m.value=e}catch(e){y.value=e.message||h("tools.universalConverter.errors.conversionFailed"),N(y.value)}}function O(e,t){try{switch(t){case"json":const r=JSON.parse(e);return JSON.stringify(r,null,2);case"xml":return z(e);case"query":return e.trim();default:return e}}catch{return e}}function z(e){try{const r=new DOMParser().parseFromString(e,"text/xml");if(r.querySelector("parsererror"))return e;let a=new XMLSerializer().serializeToString(r);a=a.replace(/></g,`>
<`);const n=a.split(`
`);let i=0;return n.map(d=>{const f=d.trim();if(!f)return"";f.startsWith("</")&&i--;const p="  ".repeat(Math.max(0,i))+f;return f.startsWith("<")&&!f.startsWith("</")&&!f.endsWith("/>")&&i++,p}).join(`
`)}catch{return e}}function U(e,t,r){if(t===r)return O(e,t);let s;switch(t){case"json":s=B(e);break;case"xml":s=K(e);break;case"query":s=Q(e);break;default:throw new Error(h("tools.universalConverter.errors.unsupportedFormat"))}switch(r){case"json":return I(s);case"xml":return V(s);case"query":return G(s);default:throw new Error(h("tools.universalConverter.errors.unsupportedFormat"))}}function B(e){try{return JSON.parse(e)}catch{throw new Error(h("tools.universalConverter.errors.invalidJson"))}}function K(e){try{let t=function(n){const i={};if(n.attributes&&n.attributes.length>0)for(let p=0;p<n.attributes.length;p++){const g=n.attributes[p];i[`@${g.name}`]=g.value}const v=Array.from(n.children);let d="";for(const p of n.childNodes)if(p.nodeType===Node.TEXT_NODE){const g=p.textContent?.trim();g&&(d+=g)}if(v.length===0)return d||"";const f={};return v.forEach(p=>{const g=p.tagName,Z=t(p);f[g]||(f[g]=[]),f[g].push(Z)}),Object.keys(f).forEach(p=>{const g=f[p];g.length===1?i[p]=g[0]:i[p]=g}),d&&Object.keys(i).length>0&&(i["#text"]=d),V(i)};const s=new DOMParser().parseFromString(e,"text/xml");if(s.querySelector("parsererror"))throw new Error(h("tools.universalConverter.errors.invalidXml"));const a=s.documentElement;if(!a)throw new Error(h("tools.universalConverter.errors.invalidXml"));return{[a.tagName]:t(a)}}catch{throw new Error(h("tools.universalConverter.errors.invalidXml"))}}function Q(e){try{const t={},s=(e.startsWith("?")?e.slice(1):e).split("&");for(const l of s){if(!l.trim())continue;const a=l.indexOf("=");let n,i;if(a===-1?(n=l,i=""):(n=l.slice(0,a),i=l.slice(a+1)),!n)continue;const v=decodeURIComponent(n),d=decodeURIComponent(i),f=W(d);j(t,v,f)}return t}catch{throw new Error(h("tools.universalConverter.errors.invalidQuery"))}}function W(e){if(e==="")return"";if(e==="true")return!0;if(e==="false")return!1;if(e==="null")return null;if(e!=="undefined"){if(/^-?\d+$/.test(e)){const t=parseInt(e,10);if(!isNaN(t))return t}if(/^-?\d*\.\d+$/.test(e)){const t=parseFloat(e);if(!isNaN(t))return t}return e}}function j(e,t,r){const s=[];let l="",a=!1;for(let v=0;v<t.length;v++){const d=t[v];d==="["?(l&&(s.push(l),l=""),a=!0):d==="]"?a&&(l===""?s.push("[]"):/^\d+$/.test(l)?s.push(parseInt(l)):s.push(l),l="",a=!1):l+=d}if(l&&s.push(l),s.length===0){e[t]=r;return}let n=e;for(let v=0;v<s.length-1;v++){const d=s[v],f=s[v+1];if(d==="[]")throw new Error("Array append notation [] can only be used as the last key");n[d]||(typeof f=="number"||f==="[]"?n[d]=[]:n[d]={}),n=n[d]}const i=s[s.length-1];if(i==="[]"){if(!Array.isArray(n))throw new Error("Cannot append to non-array");n.push(r)}else if(typeof i=="number"){if(!Array.isArray(n))throw new Error("Cannot set array index on non-array");n[i]=r}else n[i]=r}function I(e){return JSON.stringify(e,null,2)}function V(e){try{let t=function(r,s=0){const l="  ".repeat(s);let a="";return typeof r=="string"||typeof r=="number"||typeof r=="boolean"?String(r):r==null?"":Array.isArray(r)?(r.forEach(n=>{a+=`${l}<item>
`,a+=`${l}  ${t(n,s+1)}
`,a+=`${l}</item>
`}),a.trim()):(typeof r=="object"&&Object.keys(r).forEach(n=>{const i=r[n];Array.isArray(i)?(a+=`${l}<${n}>
`,i.forEach(v=>{a+=`${l}  <item>
`;const d=t(v,s+2);d&&(a+=`${d}
`),a+=`${l}  </item>
`}),a+=`${l}</${n}>
`):typeof i=="object"&&i!==null?(a+=`${l}<${n}>
`,a+=t(i,s+1),a+=`${l}</${n}>
`):a+=`${l}<${n}>${String(i)}</${n}>
`}),a)};return t(e,1)}catch{throw new Error(h("tools.universalConverter.errors.xmlGenerationFailed"))}}function G(e){try{let t=function(s,l=""){if(s==null){r.push(`${encodeURIComponent(l)}=`);return}if(typeof s=="object")if(Array.isArray(s))s.length===0?r.push(`${encodeURIComponent(l)}=`):s.forEach((a,n)=>{const i=l?`${l}[${n}]`:`[${n}]`;t(a,i)});else{const a=Object.keys(s);a.length===0?r.push(`${encodeURIComponent(l)}=`):a.forEach(n=>{const i=s[n],v=l?`${l}[${n}]`:n;t(i,v)})}else{const a=s===!0?"true":s===!1?"false":String(s);r.push(`${encodeURIComponent(l)}=${encodeURIComponent(a)}`)}};const r=[];return t(e),r.join("&")}catch{throw new Error(h("tools.universalConverter.errors.queryGenerationFailed"))}}function H(){const e=m.value;m.value=c.value,c.value=e;const t=x.value;x.value=w.value,w.value=t;const r=y.value;y.value=b.value,b.value=r}function X(e){try{if(e==="left"){if(!m.value.trim())return;y.value="";const t=O(m.value,x.value);m.value=t}else{if(!c.value.trim())return;b.value="";const t=O(c.value,w.value);c.value=t}}catch(t){const r=t.message||h("tools.universalConverter.errors.formatFailed");e==="left"?y.value=r:b.value=r,N(r)}}function q(e){e==="left"?(m.value="",y.value=""):(c.value="",b.value="")}function F(e){const t={user:{id:1,name:"John Doe",email:"<EMAIL>",active:!0,roles:["user","admin"],profile:{age:30,city:"New York"}}};e==="left"?(m.value=I(t),x.value="json",C()):(c.value=I(t),w.value="json",E())}function J(e){switch(e){case"json":return`{
  "key": "value"
}`;case"xml":return`<key>value</key>
`;case"query":return"key1=value1&key2=value2";default:return""}}function Y(e){navigator.clipboard.writeText(e).then(()=>{L()}).catch(()=>{M()})}return re([x,w],()=>{m.value&&C(),c.value&&E()}),(e,t)=>(T(),_("div",ne,[o("div",le,[o("div",ae,[o("h1",ie,u(e.$t("tools.universalConverter.title")),1),o("p",ue,u(e.$t("tools.universalConverter.description")),1)]),o("div",de,[o("div",ce,[t[13]||(t[13]=o("div",{class:"text-2xl mb-3"},"🔄",-1)),o("h3",ve,u(e.$t("tools.universalConverter.features.bidirectional.title")),1),o("p",fe,u(e.$t("tools.universalConverter.features.bidirectional.description")),1)]),o("div",me,[t[14]||(t[14]=o("div",{class:"text-2xl mb-3"},"⚡",-1)),o("h3",pe,u(e.$t("tools.universalConverter.features.realtime.title")),1),o("p",ge,u(e.$t("tools.universalConverter.features.realtime.description")),1)]),o("div",he,[t[15]||(t[15]=o("div",{class:"text-2xl mb-3"},"🛡️",-1)),o("h3",ye,u(e.$t("tools.universalConverter.features.validation.title")),1),o("p",be,u(e.$t("tools.universalConverter.features.validation.description")),1)])]),o("div",xe,[o("div",we,[o("div",$e,[o("h3",Ce,u(e.$t("tools.universalConverter.inputTitle")),1),o("div",Ee,[o("button",{onClick:t[0]||(t[0]=r=>X("left")),disabled:!m.value.trim(),class:"px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"},u(e.$t("tools.universalConverter.formatButton")),9,_e),o("button",{onClick:t[1]||(t[1]=r=>F("left")),class:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"},u(e.$t("common.loadExample")),1),o("button",{onClick:t[2]||(t[2]=r=>q("left")),class:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"},u(e.$t("common.clear")),1)])]),o("div",ke,[o("label",Te,u(e.$t("tools.universalConverter.format"))+": ",1),k(o("select",{"onUpdate:modelValue":t[3]||(t[3]=r=>x.value=r),onChange:C,class:"w-full px-3 py-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[...t[16]||(t[16]=[o("option",{value:"json"},"JSON",-1),o("option",{value:"xml"},"XML",-1),o("option",{value:"query"},"HTTP Query Parameters",-1)])],544),[[P,x.value]])]),k(o("textarea",{"onUpdate:modelValue":t[4]||(t[4]=r=>m.value=r),onInput:t[5]||(t[5]=r=>A("left")),placeholder:J(x.value),class:"w-full h-80 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,40,Ne),[[R,m.value]]),y.value?(T(),_("div",Se,u(y.value),1)):D("",!0)]),o("div",Oe,[o("div",Ie,[o("h3",De,u(e.$t("tools.universalConverter.outputTitle")),1),o("div",Ae,[o("button",{onClick:t[6]||(t[6]=r=>X("right")),disabled:!c.value.trim(),class:"px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 disabled:bg-gray-100 disabled:text-gray-400 disabled:cursor-not-allowed transition-colors"},u(e.$t("tools.universalConverter.formatButton")),9,Ue),o("button",{onClick:t[7]||(t[7]=r=>F("right")),class:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"},u(e.$t("common.loadExample")),1),o("button",{onClick:t[8]||(t[8]=r=>q("right")),class:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"},u(e.$t("common.clear")),1),c.value?(T(),_("button",{key:0,onClick:t[9]||(t[9]=r=>Y(c.value)),class:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"},u(e.$t("common.copy")),1)):D("",!0)])]),o("div",Ve,[o("label",Xe,u(e.$t("tools.universalConverter.format"))+": ",1),k(o("select",{"onUpdate:modelValue":t[10]||(t[10]=r=>w.value=r),onChange:E,class:"w-full px-3 py-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[...t[17]||(t[17]=[o("option",{value:"json"},"JSON",-1),o("option",{value:"xml"},"XML",-1),o("option",{value:"query"},"HTTP Query Parameters",-1)])],544),[[P,w.value]])]),k(o("textarea",{"onUpdate:modelValue":t[11]||(t[11]=r=>c.value=r),onInput:t[12]||(t[12]=r=>A("right")),placeholder:J(w.value),class:"w-full h-80 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,40,qe),[[R,c.value]]),b.value?(T(),_("div",Fe,u(b.value),1)):D("",!0)])]),o("div",Je,[o("div",Pe,[o("div",null,[o("h3",Re,u(e.$t("tools.universalConverter.conversionDirection")),1),o("p",Le,u(e.$t("tools.universalConverter.conversionDirectionDescription")),1)]),o("div",Me,[o("button",{onClick:H,class:"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium flex items-center"},[t[18]||(t[18]=o("span",{class:"mr-2"},"🔄",-1)),oe(" "+u(e.$t("tools.universalConverter.swap")),1)]),o("button",{onClick:C,disabled:!m.value.trim(),class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},u(e.$t("tools.universalConverter.convertRight")),9,ze),o("button",{onClick:E,disabled:!c.value.trim(),class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},u(e.$t("tools.universalConverter.convertLeft")),9,Be)])])])])]))}});export{je as default};
