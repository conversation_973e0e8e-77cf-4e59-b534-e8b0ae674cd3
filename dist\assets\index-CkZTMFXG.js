const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/UniversalConverter-u29ZJqGD.js","assets/useToast-virEbLJw.js","assets/HtmlExtractor-VE192dam.js","assets/FileRenamer-8iwcmLug.js","assets/_commonjsHelpers-DsqdWQfm.js","assets/ToolLayout.vue_vue_type_script_setup_true_lang-BsMmX7pX.js","assets/FileRenamer-Chv_p_oW.css","assets/FaviconGenerator-BzYPCUOq.js","assets/Card.vue_vue_type_script_setup_true_lang-DgPPwsWa.js","assets/FaviconGenerator-xvZ8_Gia.css","assets/ImageCompressor-tv9weCbE.js","assets/gif-Dup4naTh.js","assets/_commonjs-dynamic-modules-TDtrdbi3.js","assets/index-CNw5tbJV.js","assets/ImageCompressor-BhZrYnBI.css","assets/ImageListProcessor-CS2UUMkS.js","assets/ImageListProcessor-BflY_BqN.css","assets/VideoToGifConverter-tQni_wuY.js","assets/VideoToGifConverter-D8JlcJvT.css","assets/ImageToGifConverter-C61BmNac.js","assets/ImageToGifConverter-CMe2v8pJ.css","assets/GifEditor-DZ7MNy3Z.js","assets/GifEditor-BxAd2HNn.css","assets/BackgroundRemover-Dd2_rEwa.js","assets/BackgroundRemover-D1394zvB.css","assets/ImageWatermark-TW1xIJOm.js","assets/ImageWatermark-CLlRJ2R6.css","assets/JsonMissingKeyFinder-F07T3fHk.js","assets/JsonArraySlicer-DPwaXy1_.js","assets/JsonPathExtractor-DHW-5XPk.js","assets/JsonPathExtractor-BwZlr8Cp.css","assets/JsonArrayDeduplicator-DjIMs4z-.js","assets/QrCodeTool-D0VkbNjs.js","assets/QrCodeTool-BWIRJ236.css","assets/TextSteganography-eIdyE9xT.js","assets/ImageSteganography-V62__44H.js","assets/TextProcessor-BfAS-qOI.js","assets/ColorPickerTool-CGVm7RF9.js","assets/ColorPickerTool-Cd_OfuL_.css","assets/HeartCollage-DZj6PT0P.js","assets/HeartCollage-_R4kCIJE.css","assets/PdfViewer-DS84vcUy.js","assets/PdfViewer-eBc-FCAx.css","assets/SvgEditor-DWvxc48g.js","assets/SvgEditor-Ccsl8VLM.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))o(r);new MutationObserver(r=>{for(const s of r)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&o(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const s={};return r.integrity&&(s.integrity=r.integrity),r.referrerPolicy&&(s.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?s.credentials="include":r.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function o(r){if(r.ep)return;r.ep=!0;const s=n(r);fetch(r.href,s)}})();/**
* @vue/shared v3.5.19
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function pt(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const fe={},Fn=[],gt=()=>{},yi=()=>!1,vo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Wr=e=>e.startsWith("onUpdate:"),Oe=Object.assign,zr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},ef=Object.prototype.hasOwnProperty,ve=(e,t)=>ef.call(e,t),te=Array.isArray,Rn=e=>Hn(e)==="[object Map]",xn=e=>Hn(e)==="[object Set]",Js=e=>Hn(e)==="[object Date]",Xl=e=>Hn(e)==="[object RegExp]",ae=e=>typeof e=="function",Ee=e=>typeof e=="string",wt=e=>typeof e=="symbol",xe=e=>e!==null&&typeof e=="object",Kr=e=>(xe(e)||ae(e))&&ae(e.then)&&ae(e.catch),_i=Object.prototype.toString,Hn=e=>_i.call(e),Zl=e=>Hn(e).slice(8,-1),rr=e=>Hn(e)==="[object Object]",qr=e=>Ee(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,kn=pt(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),tf=pt("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),Yr=e=>{const t=Object.create(null);return(n=>t[n]||(t[n]=e(n)))},nf=/-(\w)/g,je=Yr(e=>e.replace(nf,(t,n)=>n?n.toUpperCase():"")),of=/\B([A-Z])/g,et=Yr(e=>e.replace(of,"-$1").toLowerCase()),yo=Yr(e=>e.charAt(0).toUpperCase()+e.slice(1)),no=Yr(e=>e?`on${yo(e)}`:""),Xe=(e,t)=>!Object.is(e,t),An=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},bi=(e,t,n,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:n})},Vo=e=>{const t=parseFloat(e);return isNaN(t)?e:t},$o=e=>{const t=Ee(e)?Number(e):NaN;return isNaN(t)?e:t};let ia;const sr=()=>ia||(ia=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),rf=/^[_$a-zA-Z\xA0-\uFFFF][_$a-zA-Z0-9\xA0-\uFFFF]*$/;function sf(e){return rf.test(e)?`__props.${e}`:`__props[${JSON.stringify(e)}]`}function af(e,t){return e+JSON.stringify(t,(n,o)=>typeof o=="function"?o.toString():o)}const lf={TEXT:1,1:"TEXT",CLASS:2,2:"CLASS",STYLE:4,4:"STYLE",PROPS:8,8:"PROPS",FULL_PROPS:16,16:"FULL_PROPS",NEED_HYDRATION:32,32:"NEED_HYDRATION",STABLE_FRAGMENT:64,64:"STABLE_FRAGMENT",KEYED_FRAGMENT:128,128:"KEYED_FRAGMENT",UNKEYED_FRAGMENT:256,256:"UNKEYED_FRAGMENT",NEED_PATCH:512,512:"NEED_PATCH",DYNAMIC_SLOTS:1024,1024:"DYNAMIC_SLOTS",DEV_ROOT_FRAGMENT:2048,2048:"DEV_ROOT_FRAGMENT",CACHED:-1,"-1":"CACHED",BAIL:-2,"-2":"BAIL"},cf={1:"TEXT",2:"CLASS",4:"STYLE",8:"PROPS",16:"FULL_PROPS",32:"NEED_HYDRATION",64:"STABLE_FRAGMENT",128:"KEYED_FRAGMENT",256:"UNKEYED_FRAGMENT",512:"NEED_PATCH",1024:"DYNAMIC_SLOTS",2048:"DEV_ROOT_FRAGMENT",[-1]:"CACHED",[-2]:"BAIL"},uf={ELEMENT:1,1:"ELEMENT",FUNCTIONAL_COMPONENT:2,2:"FUNCTIONAL_COMPONENT",STATEFUL_COMPONENT:4,4:"STATEFUL_COMPONENT",TEXT_CHILDREN:8,8:"TEXT_CHILDREN",ARRAY_CHILDREN:16,16:"ARRAY_CHILDREN",SLOTS_CHILDREN:32,32:"SLOTS_CHILDREN",TELEPORT:64,64:"TELEPORT",SUSPENSE:128,128:"SUSPENSE",COMPONENT_SHOULD_KEEP_ALIVE:256,256:"COMPONENT_SHOULD_KEEP_ALIVE",COMPONENT_KEPT_ALIVE:512,512:"COMPONENT_KEPT_ALIVE",COMPONENT:6,6:"COMPONENT"},df={STABLE:1,1:"STABLE",DYNAMIC:2,2:"DYNAMIC",FORWARDED:3,3:"FORWARDED"},ff={1:"STABLE",2:"DYNAMIC",3:"FORWARDED"},pf="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console,Error,Symbol",Si=pt(pf),mf=Si,aa=2;function gf(e,t=0,n=e.length){if(t=Math.max(0,Math.min(t,e.length)),n=Math.max(0,Math.min(n,e.length)),t>n)return"";let o=e.split(/(\r?\n)/);const r=o.filter((a,l)=>l%2===1);o=o.filter((a,l)=>l%2===0);let s=0;const i=[];for(let a=0;a<o.length;a++)if(s+=o[a].length+(r[a]&&r[a].length||0),s>=t){for(let l=a-aa;l<=a+aa||n>s;l++){if(l<0||l>=o.length)continue;const c=l+1;i.push(`${c}${" ".repeat(Math.max(3-String(c).length,0))}|  ${o[l]}`);const u=o[l].length,d=r[l]&&r[l].length||0;if(l===a){const f=t-(s-(u+d)),y=Math.max(1,n>s?u-f:n-t);i.push("   |  "+" ".repeat(f)+"^".repeat(y))}else if(l>a){if(n>s){const f=Math.max(Math.min(n-s,u),1);i.push("   |  "+"^".repeat(f))}s+=u+d}}break}return i.join(`
`)}function bn(e){if(te(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=Ee(o)?ec(o):bn(o);if(r)for(const s in r)t[s]=r[s]}return t}else if(Ee(e)||xe(e))return e}const hf=/;(?![^(]*\))/g,vf=/:([^]+)/,yf=/\/\*[^]*?\*\//g;function ec(e){const t={};return e.replace(yf,"").split(hf).forEach(n=>{if(n){const o=n.split(vf);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function _f(e){if(!e)return"";if(Ee(e))return e;let t="";for(const n in e){const o=e[n];if(Ee(o)||typeof o=="number"){const r=n.startsWith("--")?n:et(n);t+=`${r}:${o};`}}return t}function St(e){let t="";if(Ee(e))t=e;else if(te(e))for(let n=0;n<e.length;n++){const o=St(e[n]);o&&(t+=o+" ")}else if(xe(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function tc(e){if(!e)return null;let{class:t,style:n}=e;return t&&!Ee(t)&&(e.class=St(t)),n&&(e.style=bn(n)),e}const bf="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",Sf="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",Ef="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",xf="area,base,br,col,embed,hr,img,input,link,meta,param,source,track,wbr",wf=pt(bf),Cf=pt(Sf),Tf=pt(Ef),Of=pt(xf),nc="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",oc=pt(nc),Nf=pt(nc+",async,autofocus,autoplay,controls,default,defer,disabled,hidden,inert,loop,open,required,reversed,scoped,seamless,checked,muted,multiple,selected");function Ei(e){return!!e||e===""}const Pf=/[>/="'\u0009\u000a\u000c\u0020]/,vs={};function If(e){if(vs.hasOwnProperty(e))return vs[e];const t=Pf.test(e);return t&&console.error(`unsafe attribute name: ${e}`),vs[e]=!t}const Ff={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv"},Rf=pt("accept,accept-charset,accesskey,action,align,allow,alt,async,autocapitalize,autocomplete,autofocus,autoplay,background,bgcolor,border,buffered,capture,challenge,charset,checked,cite,class,code,codebase,color,cols,colspan,content,contenteditable,contextmenu,controls,coords,crossorigin,csp,data,datetime,decoding,default,defer,dir,dirname,disabled,download,draggable,dropzone,enctype,enterkeyhint,for,form,formaction,formenctype,formmethod,formnovalidate,formtarget,headers,height,hidden,high,href,hreflang,http-equiv,icon,id,importance,inert,integrity,ismap,itemprop,keytype,kind,label,lang,language,loading,list,loop,low,manifest,max,maxlength,minlength,media,min,multiple,muted,name,novalidate,open,optimum,pattern,ping,placeholder,poster,preload,radiogroup,readonly,referrerpolicy,rel,required,reversed,rows,rowspan,sandbox,scope,scoped,selected,shape,size,sizes,slot,span,spellcheck,src,srcdoc,srclang,srcset,start,step,style,summary,tabindex,target,title,translate,type,usemap,value,width,wrap"),kf=pt("xmlns,accent-height,accumulate,additive,alignment-baseline,alphabetic,amplitude,arabic-form,ascent,attributeName,attributeType,azimuth,baseFrequency,baseline-shift,baseProfile,bbox,begin,bias,by,calcMode,cap-height,class,clip,clipPathUnits,clip-path,clip-rule,color,color-interpolation,color-interpolation-filters,color-profile,color-rendering,contentScriptType,contentStyleType,crossorigin,cursor,cx,cy,d,decelerate,descent,diffuseConstant,direction,display,divisor,dominant-baseline,dur,dx,dy,edgeMode,elevation,enable-background,end,exponent,fill,fill-opacity,fill-rule,filter,filterRes,filterUnits,flood-color,flood-opacity,font-family,font-size,font-size-adjust,font-stretch,font-style,font-variant,font-weight,format,from,fr,fx,fy,g1,g2,glyph-name,glyph-orientation-horizontal,glyph-orientation-vertical,glyphRef,gradientTransform,gradientUnits,hanging,height,href,hreflang,horiz-adv-x,horiz-origin-x,id,ideographic,image-rendering,in,in2,intercept,k,k1,k2,k3,k4,kernelMatrix,kernelUnitLength,kerning,keyPoints,keySplines,keyTimes,lang,lengthAdjust,letter-spacing,lighting-color,limitingConeAngle,local,marker-end,marker-mid,marker-start,markerHeight,markerUnits,markerWidth,mask,maskContentUnits,maskUnits,mathematical,max,media,method,min,mode,name,numOctaves,offset,opacity,operator,order,orient,orientation,origin,overflow,overline-position,overline-thickness,panose-1,paint-order,path,pathLength,patternContentUnits,patternTransform,patternUnits,ping,pointer-events,points,pointsAtX,pointsAtY,pointsAtZ,preserveAlpha,preserveAspectRatio,primitiveUnits,r,radius,referrerPolicy,refX,refY,rel,rendering-intent,repeatCount,repeatDur,requiredExtensions,requiredFeatures,restart,result,rotate,rx,ry,scale,seed,shape-rendering,slope,spacing,specularConstant,specularExponent,speed,spreadMethod,startOffset,stdDeviation,stemh,stemv,stitchTiles,stop-color,stop-opacity,strikethrough-position,strikethrough-thickness,string,stroke,stroke-dasharray,stroke-dashoffset,stroke-linecap,stroke-linejoin,stroke-miterlimit,stroke-opacity,stroke-width,style,surfaceScale,systemLanguage,tabindex,tableValues,target,targetX,targetY,text-anchor,text-decoration,text-rendering,textLength,to,transform,transform-origin,type,u1,u2,underline-position,underline-thickness,unicode,unicode-bidi,unicode-range,units-per-em,v-alphabetic,v-hanging,v-ideographic,v-mathematical,values,vector-effect,version,vert-adv-y,vert-origin-x,vert-origin-y,viewBox,viewTarget,visibility,width,widths,word-spacing,writing-mode,x,x-height,x1,x2,xChannelSelector,xlink:actuate,xlink:arcrole,xlink:href,xlink:role,xlink:show,xlink:title,xlink:type,xmlns:xlink,xml:base,xml:lang,xml:space,y,y1,y2,yChannelSelector,z,zoomAndPan"),Af=pt("accent,accentunder,actiontype,align,alignmentscope,altimg,altimg-height,altimg-valign,altimg-width,alttext,bevelled,close,columnsalign,columnlines,columnspan,denomalign,depth,dir,display,displaystyle,encoding,equalcolumns,equalrows,fence,fontstyle,fontweight,form,frame,framespacing,groupalign,height,href,id,indentalign,indentalignfirst,indentalignlast,indentshift,indentshiftfirst,indentshiftlast,indextype,justify,largetop,largeop,lquote,lspace,mathbackground,mathcolor,mathsize,mathvariant,maxsize,minlabelspacing,mode,other,overflow,position,rowalign,rowlines,rowspan,rquote,rspace,scriptlevel,scriptminsize,scriptsizemultiplier,selection,separator,separators,shift,side,src,stackalign,stretchy,subscriptshift,superscriptshift,symmetric,voffset,width,widths,xlink:href,xlink:show,xlink:type,xmlns");function Lf(e){if(e==null)return!1;const t=typeof e;return t==="string"||t==="number"||t==="boolean"}const Df=/["'&<>]/;function Mf(e){const t=""+e,n=Df.exec(t);if(!n)return t;let o="",r,s,i=0;for(s=n.index;s<t.length;s++){switch(t.charCodeAt(s)){case 34:r="&quot;";break;case 38:r="&amp;";break;case 39:r="&#39;";break;case 60:r="&lt;";break;case 62:r="&gt;";break;default:continue}i!==s&&(o+=t.slice(i,s)),i=s+1,o+=r}return i!==s?o+t.slice(i,s):o}const Gf=/^-?>|<!--|-->|--!>|<!-$/g;function Jf(e){return e.replace(Gf,"")}const rc=/[ !"#$%&'()*+,./:;<=>?@[\\\]^`{|}~]/g;function Uf(e,t){return e.replace(rc,n=>t?n==='"'?'\\\\\\"':`\\\\${n}`:`\\${n}`)}function Vf(e,t){if(e.length!==t.length)return!1;let n=!0;for(let o=0;n&&o<e.length;o++)n=tn(e[o],t[o]);return n}function tn(e,t){if(e===t)return!0;let n=Js(e),o=Js(t);if(n||o)return n&&o?e.getTime()===t.getTime():!1;if(n=wt(e),o=wt(t),n||o)return e===t;if(n=te(e),o=te(t),n||o)return n&&o?Vf(e,t):!1;if(n=xe(e),o=xe(t),n||o){if(!n||!o)return!1;const r=Object.keys(e).length,s=Object.keys(t).length;if(r!==s)return!1;for(const i in e){const a=e.hasOwnProperty(i),l=t.hasOwnProperty(i);if(a&&!l||!a&&l||!tn(e[i],t[i]))return!1}}return String(e)===String(t)}function ir(e,t){return e.findIndex(n=>tn(n,t))}const sc=e=>!!(e&&e.__v_isRef===!0),re=e=>Ee(e)?e:e==null?"":te(e)||xe(e)&&(e.toString===_i||!ae(e.toString))?sc(e)?re(e.value):JSON.stringify(e,ic,2):String(e),ic=(e,t)=>sc(t)?ic(e,t.value):Rn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[o,r],s)=>(n[ys(o,s)+" =>"]=r,n),{})}:xn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>ys(n))}:wt(t)?ys(t):xe(t)&&!te(t)&&!rr(t)?String(t):t,ys=(e,t="")=>{var n;return wt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};function ac(e){return e==null?"initial":typeof e=="string"?e===""?" ":e:String(e)}const m1=Object.freeze(Object.defineProperty({__proto__:null,EMPTY_ARR:Fn,EMPTY_OBJ:fe,NO:yi,NOOP:gt,PatchFlagNames:cf,PatchFlags:lf,ShapeFlags:uf,SlotFlags:df,camelize:je,capitalize:yo,cssVarNameEscapeSymbolsRE:rc,def:bi,escapeHtml:Mf,escapeHtmlComment:Jf,extend:Oe,genCacheKey:af,genPropsAccessExp:sf,generateCodeFrame:gf,getEscapedCssVarName:Uf,getGlobalThis:sr,hasChanged:Xe,hasOwn:ve,hyphenate:et,includeBooleanAttr:Ei,invokeArrayFns:An,isArray:te,isBooleanAttr:Nf,isBuiltInDirective:tf,isDate:Js,isFunction:ae,isGloballyAllowed:Si,isGloballyWhitelisted:mf,isHTMLTag:wf,isIntegerKey:qr,isKnownHtmlAttr:Rf,isKnownMathMLAttr:Af,isKnownSvgAttr:kf,isMap:Rn,isMathMLTag:Tf,isModelListener:Wr,isObject:xe,isOn:vo,isPlainObject:rr,isPromise:Kr,isRegExp:Xl,isRenderableAttrValue:Lf,isReservedProp:kn,isSSRSafeAttrName:If,isSVGTag:Cf,isSet:xn,isSpecialBooleanAttr:oc,isString:Ee,isSymbol:wt,isVoidTag:Of,looseEqual:tn,looseIndexOf:ir,looseToNumber:Vo,makeMap:pt,normalizeClass:St,normalizeCssVarValue:ac,normalizeProps:tc,normalizeStyle:bn,objectToString:_i,parseStringStyle:ec,propsToAttrMap:Ff,remove:zr,slotFlagsText:ff,stringifyStyle:_f,toDisplayString:re,toHandlerKey:no,toNumber:$o,toRawType:Zl,toTypeString:Hn},Symbol.toStringTag,{value:"Module"}));/**
* @vue/reactivity v3.5.19
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Qe;class xi{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Qe,!t&&Qe&&(this.index=(Qe.scopes||(Qe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Qe;try{return Qe=this,t()}finally{Qe=n}}}on(){++this._on===1&&(this.prevScope=Qe,Qe=this)}off(){this._on>0&&--this._on===0&&(Qe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,o;for(n=0,o=this.effects.length;n<o;n++)this.effects[n].stop();for(this.effects.length=0,n=0,o=this.cleanups.length;n<o;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,o=this.scopes.length;n<o;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function lc(e){return new xi(e)}function cc(){return Qe}function $f(e,t=!1){Qe&&Qe.cleanups.push(e)}let Ne;const _s=new WeakSet;class Ho{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Qe&&Qe.active&&Qe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,_s.has(this)&&(_s.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||dc(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,la(this),fc(this);const t=Ne,n=Ft;Ne=this,Ft=!0;try{return this.fn()}finally{pc(this),Ne=t,Ft=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ti(t);this.deps=this.depsTail=void 0,la(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?_s.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Us(this)&&this.run()}get dirty(){return Us(this)}}let uc=0,Fo,Ro;function dc(e,t=!1){if(e.flags|=8,t){e.next=Ro,Ro=e;return}e.next=Fo,Fo=e}function wi(){uc++}function Ci(){if(--uc>0)return;if(Ro){let t=Ro;for(Ro=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Fo;){let t=Fo;for(Fo=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(o){e||(e=o)}t=n}}if(e)throw e}function fc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function pc(e){let t,n=e.depsTail,o=n;for(;o;){const r=o.prevDep;o.version===-1?(o===n&&(n=r),Ti(o),Hf(o)):t=o,o.dep.activeLink=o.prevActiveLink,o.prevActiveLink=void 0,o=r}e.deps=t,e.depsTail=n}function Us(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(mc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function mc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===jo)||(e.globalVersion=jo,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Us(e))))return;e.flags|=2;const t=e.dep,n=Ne,o=Ft;Ne=e,Ft=!0;try{fc(e);const r=e.fn(e._value);(t.version===0||Xe(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{Ne=n,Ft=o,pc(e),e.flags&=-3}}function Ti(e,t=!1){const{dep:n,prevSub:o,nextSub:r}=e;if(o&&(o.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=o,e.nextSub=void 0),n.subs===e&&(n.subs=o,!o&&n.computed)){n.computed.flags&=-5;for(let s=n.computed.deps;s;s=s.nextDep)Ti(s,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Hf(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}function jf(e,t){e.effect instanceof Ho&&(e=e.effect.fn);const n=new Ho(e);t&&Oe(n,t);try{n.run()}catch(r){throw n.stop(),r}const o=n.run.bind(n);return o.effect=n,o}function Bf(e){e.effect.stop()}let Ft=!0;const gc=[];function nn(){gc.push(Ft),Ft=!1}function on(){const e=gc.pop();Ft=e===void 0?!0:e}function la(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=Ne;Ne=void 0;try{t()}finally{Ne=n}}}let jo=0;class Wf{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Qr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!Ne||!Ft||Ne===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==Ne)n=this.activeLink=new Wf(Ne,this),Ne.deps?(n.prevDep=Ne.depsTail,Ne.depsTail.nextDep=n,Ne.depsTail=n):Ne.deps=Ne.depsTail=n,hc(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const o=n.nextDep;o.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=o),n.prevDep=Ne.depsTail,n.nextDep=void 0,Ne.depsTail.nextDep=n,Ne.depsTail=n,Ne.deps===n&&(Ne.deps=o)}return n}trigger(t){this.version++,jo++,this.notify(t)}notify(t){wi();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ci()}}}function hc(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let o=t.deps;o;o=o.nextDep)hc(o)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Ir=new WeakMap,Ln=Symbol(""),Vs=Symbol(""),Bo=Symbol("");function Ze(e,t,n){if(Ft&&Ne){let o=Ir.get(e);o||Ir.set(e,o=new Map);let r=o.get(n);r||(o.set(n,r=new Qr),r.map=o,r.key=n),r.track()}}function Yt(e,t,n,o,r,s){const i=Ir.get(e);if(!i){jo++;return}const a=l=>{l&&l.trigger()};if(wi(),t==="clear")i.forEach(a);else{const l=te(e),c=l&&qr(n);if(l&&n==="length"){const u=Number(o);i.forEach((d,f)=>{(f==="length"||f===Bo||!wt(f)&&f>=u)&&a(d)})}else switch((n!==void 0||i.has(void 0))&&a(i.get(n)),c&&a(i.get(Bo)),t){case"add":l?c&&a(i.get("length")):(a(i.get(Ln)),Rn(e)&&a(i.get(Vs)));break;case"delete":l||(a(i.get(Ln)),Rn(e)&&a(i.get(Vs)));break;case"set":Rn(e)&&a(i.get(Ln));break}}Ci()}function zf(e,t){const n=Ir.get(e);return n&&n.get(t)}function zn(e){const t=ge(e);return t===e?t:(Ze(t,"iterate",Bo),ht(e)?t:t.map(We))}function Xr(e){return Ze(e=ge(e),"iterate",Bo),e}const Kf={__proto__:null,[Symbol.iterator](){return bs(this,Symbol.iterator,We)},concat(...e){return zn(this).concat(...e.map(t=>te(t)?zn(t):t))},entries(){return bs(this,"entries",e=>(e[1]=We(e[1]),e))},every(e,t){return jt(this,"every",e,t,void 0,arguments)},filter(e,t){return jt(this,"filter",e,t,n=>n.map(We),arguments)},find(e,t){return jt(this,"find",e,t,We,arguments)},findIndex(e,t){return jt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return jt(this,"findLast",e,t,We,arguments)},findLastIndex(e,t){return jt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return jt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ss(this,"includes",e)},indexOf(...e){return Ss(this,"indexOf",e)},join(e){return zn(this).join(e)},lastIndexOf(...e){return Ss(this,"lastIndexOf",e)},map(e,t){return jt(this,"map",e,t,void 0,arguments)},pop(){return wo(this,"pop")},push(...e){return wo(this,"push",e)},reduce(e,...t){return ca(this,"reduce",e,t)},reduceRight(e,...t){return ca(this,"reduceRight",e,t)},shift(){return wo(this,"shift")},some(e,t){return jt(this,"some",e,t,void 0,arguments)},splice(...e){return wo(this,"splice",e)},toReversed(){return zn(this).toReversed()},toSorted(e){return zn(this).toSorted(e)},toSpliced(...e){return zn(this).toSpliced(...e)},unshift(...e){return wo(this,"unshift",e)},values(){return bs(this,"values",We)}};function bs(e,t,n){const o=Xr(e),r=o[t]();return o!==e&&!ht(e)&&(r._next=r.next,r.next=()=>{const s=r._next();return s.value&&(s.value=n(s.value)),s}),r}const qf=Array.prototype;function jt(e,t,n,o,r,s){const i=Xr(e),a=i!==e&&!ht(e),l=i[t];if(l!==qf[t]){const d=l.apply(e,s);return a?We(d):d}let c=n;i!==e&&(a?c=function(d,f){return n.call(this,We(d),f,e)}:n.length>2&&(c=function(d,f){return n.call(this,d,f,e)}));const u=l.call(i,c,o);return a&&r?r(u):u}function ca(e,t,n,o){const r=Xr(e);let s=n;return r!==e&&(ht(e)?n.length>3&&(s=function(i,a,l){return n.call(this,i,a,l,e)}):s=function(i,a,l){return n.call(this,i,We(a),l,e)}),r[t](s,...o)}function Ss(e,t,n){const o=ge(e);Ze(o,"iterate",Bo);const r=o[t](...n);return(r===-1||r===!1)&&ts(n[0])?(n[0]=ge(n[0]),o[t](...n)):r}function wo(e,t,n=[]){nn(),wi();const o=ge(e)[t].apply(e,n);return Ci(),on(),o}const Yf=pt("__proto__,__v_isRef,__isVue"),vc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(wt));function Qf(e){wt(e)||(e=String(e));const t=ge(this);return Ze(t,"has",e),t.hasOwnProperty(e)}class yc{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,o){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,s=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return s;if(n==="__v_raw")return o===(r?s?wc:xc:s?Ec:Sc).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(o)?t:void 0;const i=te(t);if(!r){let l;if(i&&(l=Kf[n]))return l;if(n==="hasOwnProperty")return Qf}const a=Reflect.get(t,n,Je(t)?t:o);return(wt(n)?vc.has(n):Yf(n))||(r||Ze(t,"get",n),s)?a:Je(a)?i&&qr(n)?a:a.value:xe(a)?r?Ni(a):vn(a):a}}class _c extends yc{constructor(t=!1){super(!1,t)}set(t,n,o,r){let s=t[n];if(!this._isShallow){const l=rn(s);if(!ht(o)&&!rn(o)&&(s=ge(s),o=ge(o)),!te(t)&&Je(s)&&!Je(o))return l||(s.value=o),!0}const i=te(t)&&qr(n)?Number(n)<t.length:ve(t,n),a=Reflect.set(t,n,o,Je(t)?t:r);return t===ge(r)&&(i?Xe(o,s)&&Yt(t,"set",n,o):Yt(t,"add",n,o)),a}deleteProperty(t,n){const o=ve(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&o&&Yt(t,"delete",n,void 0),r}has(t,n){const o=Reflect.has(t,n);return(!wt(n)||!vc.has(n))&&Ze(t,"has",n),o}ownKeys(t){return Ze(t,"iterate",te(t)?"length":Ln),Reflect.ownKeys(t)}}class bc extends yc{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Xf=new _c,Zf=new bc,ep=new _c(!0),tp=new bc(!0),$s=e=>e,fr=e=>Reflect.getPrototypeOf(e);function np(e,t,n){return function(...o){const r=this.__v_raw,s=ge(r),i=Rn(s),a=e==="entries"||e===Symbol.iterator&&i,l=e==="keys"&&i,c=r[e](...o),u=n?$s:t?Fr:We;return!t&&Ze(s,"iterate",l?Vs:Ln),{next(){const{value:d,done:f}=c.next();return f?{value:d,done:f}:{value:a?[u(d[0]),u(d[1])]:u(d),done:f}},[Symbol.iterator](){return this}}}}function pr(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function op(e,t){const n={get(r){const s=this.__v_raw,i=ge(s),a=ge(r);e||(Xe(r,a)&&Ze(i,"get",r),Ze(i,"get",a));const{has:l}=fr(i),c=t?$s:e?Fr:We;if(l.call(i,r))return c(s.get(r));if(l.call(i,a))return c(s.get(a));s!==i&&s.get(r)},get size(){const r=this.__v_raw;return!e&&Ze(ge(r),"iterate",Ln),Reflect.get(r,"size",r)},has(r){const s=this.__v_raw,i=ge(s),a=ge(r);return e||(Xe(r,a)&&Ze(i,"has",r),Ze(i,"has",a)),r===a?s.has(r):s.has(r)||s.has(a)},forEach(r,s){const i=this,a=i.__v_raw,l=ge(a),c=t?$s:e?Fr:We;return!e&&Ze(l,"iterate",Ln),a.forEach((u,d)=>r.call(s,c(u),c(d),i))}};return Oe(n,e?{add:pr("add"),set:pr("set"),delete:pr("delete"),clear:pr("clear")}:{add(r){!t&&!ht(r)&&!rn(r)&&(r=ge(r));const s=ge(this);return fr(s).has.call(s,r)||(s.add(r),Yt(s,"add",r,r)),this},set(r,s){!t&&!ht(s)&&!rn(s)&&(s=ge(s));const i=ge(this),{has:a,get:l}=fr(i);let c=a.call(i,r);c||(r=ge(r),c=a.call(i,r));const u=l.call(i,r);return i.set(r,s),c?Xe(s,u)&&Yt(i,"set",r,s):Yt(i,"add",r,s),this},delete(r){const s=ge(this),{has:i,get:a}=fr(s);let l=i.call(s,r);l||(r=ge(r),l=i.call(s,r)),a&&a.call(s,r);const c=s.delete(r);return l&&Yt(s,"delete",r,void 0),c},clear(){const r=ge(this),s=r.size!==0,i=r.clear();return s&&Yt(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=np(r,e,t)}),n}function Zr(e,t){const n=op(e,t);return(o,r,s)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?o:Reflect.get(ve(n,r)&&r in o?n:o,r,s)}const rp={get:Zr(!1,!1)},sp={get:Zr(!1,!0)},ip={get:Zr(!0,!1)},ap={get:Zr(!0,!0)},Sc=new WeakMap,Ec=new WeakMap,xc=new WeakMap,wc=new WeakMap;function lp(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function cp(e){return e.__v_skip||!Object.isExtensible(e)?0:lp(Zl(e))}function vn(e){return rn(e)?e:es(e,!1,Xf,rp,Sc)}function Oi(e){return es(e,!1,ep,sp,Ec)}function Ni(e){return es(e,!0,Zf,ip,xc)}function up(e){return es(e,!0,tp,ap,wc)}function es(e,t,n,o,r){if(!xe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=cp(e);if(s===0)return e;const i=r.get(e);if(i)return i;const a=new Proxy(e,s===2?o:n);return r.set(e,a),a}function yn(e){return rn(e)?yn(e.__v_raw):!!(e&&e.__v_isReactive)}function rn(e){return!!(e&&e.__v_isReadonly)}function ht(e){return!!(e&&e.__v_isShallow)}function ts(e){return e?!!e.__v_raw:!1}function ge(e){const t=e&&e.__v_raw;return t?ge(t):e}function Cc(e){return!ve(e,"__v_skip")&&Object.isExtensible(e)&&bi(e,"__v_skip",!0),e}const We=e=>xe(e)?vn(e):e,Fr=e=>xe(e)?Ni(e):e;function Je(e){return e?e.__v_isRef===!0:!1}function Fe(e){return Tc(e,!1)}function ar(e){return Tc(e,!0)}function Tc(e,t){return Je(e)?e:new dp(e,t)}class dp{constructor(t,n){this.dep=new Qr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:ge(t),this._value=n?t:We(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,o=this.__v_isShallow||ht(t)||rn(t);t=o?t:ge(t),Xe(t,n)&&(this._rawValue=t,this._value=o?t:We(t),this.dep.trigger())}}function fp(e){e.dep&&e.dep.trigger()}function Ut(e){return Je(e)?e.value:e}function pp(e){return ae(e)?e():Ut(e)}const mp={get:(e,t,n)=>t==="__v_raw"?e:Ut(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return Je(r)&&!Je(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function Pi(e){return yn(e)?e:new Proxy(e,mp)}class gp{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new Qr,{get:o,set:r}=t(n.track.bind(n),n.trigger.bind(n));this._get=o,this._set=r}get value(){return this._value=this._get()}set value(t){this._set(t)}}function Oc(e){return new gp(e)}function hp(e){const t=te(e)?new Array(e.length):{};for(const n in e)t[n]=Nc(e,n);return t}class vp{constructor(t,n,o){this._object=t,this._key=n,this._defaultValue=o,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return zf(ge(this._object),this._key)}}class yp{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function _p(e,t,n){return Je(e)?e:ae(e)?new yp(e):xe(e)&&arguments.length>1?Nc(e,t,n):Fe(e)}function Nc(e,t,n){const o=e[t];return Je(o)?o:new vp(e,t,n)}class bp{constructor(t,n,o){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Qr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=jo-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=o}notify(){if(this.flags|=16,!(this.flags&8)&&Ne!==this)return dc(this,!0),!0}get value(){const t=this.dep.track();return mc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Sp(e,t,n=!1){let o,r;return ae(e)?o=e:(o=e.get,r=e.set),new bp(o,r,n)}const Ep={GET:"get",HAS:"has",ITERATE:"iterate"},xp={SET:"set",ADD:"add",DELETE:"delete",CLEAR:"clear"},mr={},Rr=new WeakMap;let pn;function wp(){return pn}function Pc(e,t=!1,n=pn){if(n){let o=Rr.get(n);o||Rr.set(n,o=[]),o.push(e)}}function Cp(e,t,n=fe){const{immediate:o,deep:r,once:s,scheduler:i,augmentJob:a,call:l}=n,c=h=>r?h:ht(h)||r===!1||r===0?Qt(h,1):Qt(h);let u,d,f,y,C=!1,w=!1;if(Je(e)?(d=()=>e.value,C=ht(e)):yn(e)?(d=()=>c(e),C=!0):te(e)?(w=!0,C=e.some(h=>yn(h)||ht(h)),d=()=>e.map(h=>{if(Je(h))return h.value;if(yn(h))return c(h);if(ae(h))return l?l(h,2):h()})):ae(e)?t?d=l?()=>l(e,2):e:d=()=>{if(f){nn();try{f()}finally{on()}}const h=pn;pn=u;try{return l?l(e,3,[y]):e(y)}finally{pn=h}}:d=gt,t&&r){const h=d,S=r===!0?1/0:r;d=()=>Qt(h(),S)}const L=cc(),A=()=>{u.stop(),L&&L.active&&zr(L.effects,u)};if(s&&t){const h=t;t=(...S)=>{h(...S),A()}}let I=w?new Array(e.length).fill(mr):mr;const p=h=>{if(!(!(u.flags&1)||!u.dirty&&!h))if(t){const S=u.run();if(r||C||(w?S.some((E,F)=>Xe(E,I[F])):Xe(S,I))){f&&f();const E=pn;pn=u;try{const F=[S,I===mr?void 0:w&&I[0]===mr?[]:I,y];I=S,l?l(t,3,F):t(...F)}finally{pn=E}}}else u.run()};return a&&a(p),u=new Ho(d),u.scheduler=i?()=>i(p,!1):p,y=h=>Pc(h,!1,u),f=u.onStop=()=>{const h=Rr.get(u);if(h){if(l)l(h,4);else for(const S of h)S();Rr.delete(u)}},t?o?p(!0):I=u.run():i?i(p.bind(null,!0),!0):u.run(),A.pause=u.pause.bind(u),A.resume=u.resume.bind(u),A.stop=A,A}function Qt(e,t=1/0,n){if(t<=0||!xe(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Je(e))Qt(e.value,t,n);else if(te(e))for(let o=0;o<e.length;o++)Qt(e[o],t,n);else if(xn(e)||Rn(e))e.forEach(o=>{Qt(o,t,n)});else if(rr(e)){for(const o in e)Qt(e[o],t,n);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&Qt(e[o],t,n)}return e}/**
* @vue/runtime-core v3.5.19
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Ic=[];function Tp(e){Ic.push(e)}function Op(){Ic.pop()}function Np(e,t){}const Pp={SETUP_FUNCTION:0,0:"SETUP_FUNCTION",RENDER_FUNCTION:1,1:"RENDER_FUNCTION",NATIVE_EVENT_HANDLER:5,5:"NATIVE_EVENT_HANDLER",COMPONENT_EVENT_HANDLER:6,6:"COMPONENT_EVENT_HANDLER",VNODE_HOOK:7,7:"VNODE_HOOK",DIRECTIVE_HOOK:8,8:"DIRECTIVE_HOOK",TRANSITION_HOOK:9,9:"TRANSITION_HOOK",APP_ERROR_HANDLER:10,10:"APP_ERROR_HANDLER",APP_WARN_HANDLER:11,11:"APP_WARN_HANDLER",FUNCTION_REF:12,12:"FUNCTION_REF",ASYNC_COMPONENT_LOADER:13,13:"ASYNC_COMPONENT_LOADER",SCHEDULER:14,14:"SCHEDULER",COMPONENT_UPDATE:15,15:"COMPONENT_UPDATE",APP_UNMOUNT_CLEANUP:16,16:"APP_UNMOUNT_CLEANUP"},Ip={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function _o(e,t,n,o){try{return o?e(...o):e()}catch(r){jn(r,t,n)}}function Ct(e,t,n,o){if(ae(e)){const r=_o(e,t,n,o);return r&&Kr(r)&&r.catch(s=>{jn(s,t,n)}),r}if(te(e)){const r=[];for(let s=0;s<e.length;s++)r.push(Ct(e[s],t,n,o));return r}}function jn(e,t,n,o=!0){const r=t?t.vnode:null,{errorHandler:s,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||fe;if(t){let a=t.parent;const l=t.proxy,c=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const u=a.ec;if(u){for(let d=0;d<u.length;d++)if(u[d](e,l,c)===!1)return}a=a.parent}if(s){nn(),_o(s,null,10,[e,l,c]),on();return}}Fp(e,n,r,o,i)}function Fp(e,t,n,o=!0,r=!1){if(r)throw e;console.error(e)}const at=[];let Lt=-1;const oo=[];let mn=null,qn=0;const Fc=Promise.resolve();let kr=null;function Jn(e){const t=kr||Fc;return e?t.then(this?e.bind(this):e):t}function Rp(e){let t=Lt+1,n=at.length;for(;t<n;){const o=t+n>>>1,r=at[o],s=zo(r);s<e||s===e&&r.flags&2?t=o+1:n=o}return t}function Ii(e){if(!(e.flags&1)){const t=zo(e),n=at[at.length-1];!n||!(e.flags&2)&&t>=zo(n)?at.push(e):at.splice(Rp(t),0,e),e.flags|=1,Rc()}}function Rc(){kr||(kr=Fc.then(kc))}function Wo(e){te(e)?oo.push(...e):mn&&e.id===-1?mn.splice(qn+1,0,e):e.flags&1||(oo.push(e),e.flags|=1),Rc()}function ua(e,t,n=Lt+1){for(;n<at.length;n++){const o=at[n];if(o&&o.flags&2){if(e&&o.id!==e.uid)continue;at.splice(n,1),n--,o.flags&4&&(o.flags&=-2),o(),o.flags&4||(o.flags&=-2)}}}function Ar(e){if(oo.length){const t=[...new Set(oo)].sort((n,o)=>zo(n)-zo(o));if(oo.length=0,mn){mn.push(...t);return}for(mn=t,qn=0;qn<mn.length;qn++){const n=mn[qn];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}mn=null,qn=0}}const zo=e=>e.id==null?e.flags&2?-1:1/0:e.id;function kc(e){try{for(Lt=0;Lt<at.length;Lt++){const t=at[Lt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),_o(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Lt<at.length;Lt++){const t=at[Lt];t&&(t.flags&=-2)}Lt=-1,at.length=0,Ar(),kr=null,(at.length||oo.length)&&kc()}}let Yn,gr=[];function Ac(e,t){var n,o;Yn=e,Yn?(Yn.enabled=!0,gr.forEach(({event:r,args:s})=>Yn.emit(r,...s)),gr=[]):typeof window<"u"&&window.HTMLElement&&!((o=(n=window.navigator)==null?void 0:n.userAgent)!=null&&o.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(s=>{Ac(s,t)}),setTimeout(()=>{Yn||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,gr=[])},3e3)):gr=[]}let Ke=null,ns=null;function Ko(e){const t=Ke;return Ke=e,ns=e&&e.type.__scopeId||null,t}function kp(e){ns=e}function Ap(){ns=null}const Lp=e=>Gt;function Gt(e,t=Ke,n){if(!t||e._n)return e;const o=(...r)=>{o._d&&Ys(-1);const s=Ko(t);let i;try{i=e(...r)}finally{Ko(s),o._d&&Ys(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function Lc(e,t){if(Ke===null)return e;const n=dr(Ke),o=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[s,i,a,l=fe]=t[r];s&&(ae(s)&&(s={mounted:s,updated:s}),s.deep&&Qt(i),o.push({dir:s,instance:n,value:i,oldValue:void 0,arg:a,modifiers:l}))}return e}function Mt(e,t,n,o){const r=e.dirs,s=t&&t.dirs;for(let i=0;i<r.length;i++){const a=r[i];s&&(a.oldValue=s[i].value);let l=a.dir[o];l&&(nn(),Ct(l,n,8,[e.el,a,e,t]),on())}}const Dc=Symbol("_vte"),Mc=e=>e.__isTeleport,ko=e=>e&&(e.disabled||e.disabled===""),da=e=>e&&(e.defer||e.defer===""),fa=e=>typeof SVGElement<"u"&&e instanceof SVGElement,pa=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,Hs=(e,t)=>{const n=e&&e.to;return Ee(n)?t?t(n):null:n},Gc={name:"Teleport",__isTeleport:!0,process(e,t,n,o,r,s,i,a,l,c){const{mc:u,pc:d,pbc:f,o:{insert:y,querySelector:C,createText:w,createComment:L}}=c,A=ko(t.props);let{shapeFlag:I,children:p,dynamicChildren:h}=t;if(e==null){const S=t.el=w(""),E=t.anchor=w("");y(S,n,o),y(E,n,o);const F=(N,G)=>{I&16&&(r&&r.isCE&&(r.ce._teleportTarget=N),u(p,N,G,r,s,i,a,l))},M=()=>{const N=t.target=Hs(t.props,C),G=Uc(N,t,w,y);N&&(i!=="svg"&&fa(N)?i="svg":i!=="mathml"&&pa(N)&&(i="mathml"),A||(F(N,G),Cr(t,!1)))};A&&(F(n,E),Cr(t,!0)),da(t.props)?(t.el.__isMounted=!1,Ue(()=>{M(),delete t.el.__isMounted},s)):M()}else{if(da(t.props)&&e.el.__isMounted===!1){Ue(()=>{Gc.process(e,t,n,o,r,s,i,a,l,c)},s);return}t.el=e.el,t.targetStart=e.targetStart;const S=t.anchor=e.anchor,E=t.target=e.target,F=t.targetAnchor=e.targetAnchor,M=ko(e.props),N=M?n:E,G=M?S:F;if(i==="svg"||fa(E)?i="svg":(i==="mathml"||pa(E))&&(i="mathml"),h?(f(e.dynamicChildren,h,N,r,s,i,a),$i(e,t,!0)):l||d(e,t,N,G,r,s,i,a,!1),A)M?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):hr(t,n,S,c,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const $=t.target=Hs(t.props,C);$&&hr(t,$,null,c,0)}else M&&hr(t,E,F,c,1);Cr(t,A)}},remove(e,t,n,{um:o,o:{remove:r}},s){const{shapeFlag:i,children:a,anchor:l,targetStart:c,targetAnchor:u,target:d,props:f}=e;if(d&&(r(c),r(u)),s&&r(l),i&16){const y=s||!ko(f);for(let C=0;C<a.length;C++){const w=a[C];o(w,t,n,y,!!w.dynamicChildren)}}},move:hr,hydrate:Dp};function hr(e,t,n,{o:{insert:o},m:r},s=2){s===0&&o(e.targetAnchor,t,n);const{el:i,anchor:a,shapeFlag:l,children:c,props:u}=e,d=s===2;if(d&&o(i,t,n),(!d||ko(u))&&l&16)for(let f=0;f<c.length;f++)r(c[f],t,n,2);d&&o(a,t,n)}function Dp(e,t,n,o,r,s,{o:{nextSibling:i,parentNode:a,querySelector:l,insert:c,createText:u}},d){const f=t.target=Hs(t.props,l);if(f){const y=ko(t.props),C=f._lpa||f.firstChild;if(t.shapeFlag&16)if(y)t.anchor=d(i(e),t,a(e),n,o,r,s),t.targetStart=C,t.targetAnchor=C&&i(C);else{t.anchor=i(e);let w=C;for(;w;){if(w&&w.nodeType===8){if(w.data==="teleport start anchor")t.targetStart=w;else if(w.data==="teleport anchor"){t.targetAnchor=w,f._lpa=t.targetAnchor&&i(t.targetAnchor);break}}w=i(w)}t.targetAnchor||Uc(f,t,u,c),d(C&&i(C),t,f,n,o,r,s)}Cr(t,y)}return t.anchor&&i(t.anchor)}const Jc=Gc;function Cr(e,t){const n=e.ctx;if(n&&n.ut){let o,r;for(t?(o=e.el,r=e.anchor):(o=e.targetStart,r=e.targetAnchor);o&&o!==r;)o.nodeType===1&&o.setAttribute("data-v-owner",n.uid),o=o.nextSibling;n.ut()}}function Uc(e,t,n,o){const r=t.targetStart=n(""),s=t.targetAnchor=n("");return r[Dc]=s,e&&(o(r,e),o(s,e)),s}const qt=Symbol("_leaveCb"),vr=Symbol("_enterCb");function Fi(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return ln(()=>{e.isMounted=!0}),cr(()=>{e.isUnmounting=!0}),e}const _t=[Function,Array],Ri={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:_t,onEnter:_t,onAfterEnter:_t,onEnterCancelled:_t,onBeforeLeave:_t,onLeave:_t,onAfterLeave:_t,onLeaveCancelled:_t,onBeforeAppear:_t,onAppear:_t,onAfterAppear:_t,onAppearCancelled:_t},Vc=e=>{const t=e.subTree;return t.component?Vc(t.component):t},Mp={name:"BaseTransition",props:Ri,setup(e,{slots:t}){const n=$e(),o=Fi();return()=>{const r=t.default&&os(t.default(),!0);if(!r||!r.length)return;const s=$c(r),i=ge(e),{mode:a}=i;if(o.isLeaving)return Es(s);const l=ma(s);if(!l)return Es(s);let c=ao(l,i,o,n,d=>c=d);l.type!==De&&sn(l,c);let u=n.subTree&&ma(n.subTree);if(u&&u.type!==De&&!Ot(l,u)&&Vc(n).type!==De){let d=ao(u,i,o,n);if(sn(u,d),a==="out-in"&&l.type!==De)return o.isLeaving=!0,d.afterLeave=()=>{o.isLeaving=!1,n.job.flags&8||n.update(),delete d.afterLeave,u=void 0},Es(s);a==="in-out"&&l.type!==De?d.delayLeave=(f,y,C)=>{const w=jc(o,u);w[String(u.key)]=u,f[qt]=()=>{y(),f[qt]=void 0,delete c.delayedLeave,u=void 0},c.delayedLeave=()=>{C(),delete c.delayedLeave,u=void 0}}:u=void 0}else u&&(u=void 0);return s}}};function $c(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==De){t=n;break}}return t}const Hc=Mp;function jc(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function ao(e,t,n,o,r){const{appear:s,mode:i,persisted:a=!1,onBeforeEnter:l,onEnter:c,onAfterEnter:u,onEnterCancelled:d,onBeforeLeave:f,onLeave:y,onAfterLeave:C,onLeaveCancelled:w,onBeforeAppear:L,onAppear:A,onAfterAppear:I,onAppearCancelled:p}=t,h=String(e.key),S=jc(n,e),E=(N,G)=>{N&&Ct(N,o,9,G)},F=(N,G)=>{const $=G[1];E(N,G),te(N)?N.every(T=>T.length<=1)&&$():N.length<=1&&$()},M={mode:i,persisted:a,beforeEnter(N){let G=l;if(!n.isMounted)if(s)G=L||l;else return;N[qt]&&N[qt](!0);const $=S[h];$&&Ot(e,$)&&$.el[qt]&&$.el[qt](),E(G,[N])},enter(N){let G=c,$=u,T=d;if(!n.isMounted)if(s)G=A||c,$=I||u,T=p||d;else return;let W=!1;const se=N[vr]=ee=>{W||(W=!0,ee?E(T,[N]):E($,[N]),M.delayedLeave&&M.delayedLeave(),N[vr]=void 0)};G?F(G,[N,se]):se()},leave(N,G){const $=String(e.key);if(N[vr]&&N[vr](!0),n.isUnmounting)return G();E(f,[N]);let T=!1;const W=N[qt]=se=>{T||(T=!0,G(),se?E(w,[N]):E(C,[N]),N[qt]=void 0,S[$]===e&&delete S[$])};S[$]=e,y?F(y,[N,W]):W()},clone(N){const G=ao(N,t,n,o,r);return r&&r(G),G}};return M}function Es(e){if(lr(e))return e=Ht(e),e.children=null,e}function ma(e){if(!lr(e))return Mc(e.type)&&e.children?$c(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&ae(n.default))return n.default()}}function sn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,sn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function os(e,t=!1,n){let o=[],r=0;for(let s=0;s<e.length;s++){let i=e[s];const a=n==null?i.key:String(n)+String(i.key!=null?i.key:s);i.type===Ce?(i.patchFlag&128&&r++,o=o.concat(os(i.children,t,a))):(t||i.type!==De)&&o.push(a!=null?Ht(i,{key:a}):i)}if(r>1)for(let s=0;s<o.length;s++)o[s].patchFlag=-2;return o}/*! #__NO_SIDE_EFFECTS__ */function nt(e,t){return ae(e)?Oe({name:e.name},t,{setup:e}):e}function Gp(){const e=$e();return e?(e.appContext.config.idPrefix||"v")+"-"+e.ids[0]+e.ids[1]++:""}function ki(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Jp(e){const t=$e(),n=ar(null);if(t){const r=t.refs===fe?t.refs={}:t.refs;Object.defineProperty(r,e,{enumerable:!0,get:()=>n.value,set:s=>n.value=s})}return n}function ro(e,t,n,o,r=!1){if(te(e)){e.forEach((C,w)=>ro(C,t&&(te(t)?t[w]:t),n,o,r));return}if(_n(o)&&!r){o.shapeFlag&512&&o.type.__asyncResolved&&o.component.subTree.component&&ro(e,t,n,o.component.subTree);return}const s=o.shapeFlag&4?dr(o.component):o.el,i=r?null:s,{i:a,r:l}=e,c=t&&t.r,u=a.refs===fe?a.refs={}:a.refs,d=a.setupState,f=ge(d),y=d===fe?yi:C=>ve(f,C);if(c!=null&&c!==l){if(Ee(c))u[c]=null,y(c)&&(d[c]=null);else if(Je(c)){c.value=null;const C=t;C.k&&(u[C.k]=null)}}if(ae(l))_o(l,a,12,[i,u]);else{const C=Ee(l),w=Je(l);if(C||w){const L=()=>{if(e.f){const A=C?y(l)?d[l]:u[l]:l.value;if(r)te(A)&&zr(A,s);else if(te(A))A.includes(s)||A.push(s);else if(C)u[l]=[s],y(l)&&(d[l]=u[l]);else{const I=[s];l.value=I,e.k&&(u[e.k]=I)}}else C?(u[l]=i,y(l)&&(d[l]=i)):w&&(l.value=i,e.k&&(u[e.k]=i))};i?(L.id=-1,Ue(L,n)):L()}}}let ga=!1;const Kn=()=>{ga||(console.error("Hydration completed but contains mismatches."),ga=!0)},Up=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",Vp=e=>e.namespaceURI.includes("MathML"),yr=e=>{if(e.nodeType===1){if(Up(e))return"svg";if(Vp(e))return"mathml"}},eo=e=>e.nodeType===8;function $p(e){const{mt:t,p:n,o:{patchProp:o,createText:r,nextSibling:s,parentNode:i,remove:a,insert:l,createComment:c}}=e,u=(p,h)=>{if(!h.hasChildNodes()){n(null,p,h),Ar(),h._vnode=p;return}d(h.firstChild,p,null,null,null),Ar(),h._vnode=p},d=(p,h,S,E,F,M=!1)=>{M=M||!!h.dynamicChildren;const N=eo(p)&&p.data==="[",G=()=>w(p,h,S,E,F,N),{type:$,ref:T,shapeFlag:W,patchFlag:se}=h;let ee=p.nodeType;h.el=p,se===-2&&(M=!1,h.dynamicChildren=null);let q=null;switch($){case en:ee!==3?h.children===""?(l(h.el=r(""),i(p),p),q=p):q=G():(p.data!==h.children&&(Kn(),p.data=h.children),q=s(p));break;case De:I(p)?(q=s(p),A(h.el=p.content.firstChild,p,S)):ee!==8||N?q=G():q=s(p);break;case Gn:if(N&&(p=s(p),ee=p.nodeType),ee===1||ee===3){q=p;const ie=!h.children.length;for(let X=0;X<h.staticCount;X++)ie&&(h.children+=q.nodeType===1?q.outerHTML:q.data),X===h.staticCount-1&&(h.anchor=q),q=s(q);return N?s(q):q}else G();break;case Ce:N?q=C(p,h,S,E,F,M):q=G();break;default:if(W&1)(ee!==1||h.type.toLowerCase()!==p.tagName.toLowerCase())&&!I(p)?q=G():q=f(p,h,S,E,F,M);else if(W&6){h.slotScopeIds=F;const ie=i(p);if(N?q=L(p):eo(p)&&p.data==="teleport start"?q=L(p,p.data,"teleport end"):q=s(p),t(h,ie,null,S,E,yr(ie),M),_n(h)&&!h.type.__asyncResolved){let X;N?(X=B(Ce),X.anchor=q?q.previousSibling:ie.lastChild):X=p.nodeType===3?Vn(""):B("div"),X.el=p,h.component.subTree=X}}else W&64?ee!==8?q=G():q=h.type.hydrate(p,h,S,E,F,M,e,y):W&128&&(q=h.type.hydrate(p,h,S,E,yr(i(p)),F,M,e,d))}return T!=null&&ro(T,null,E,h),q},f=(p,h,S,E,F,M)=>{M=M||!!h.dynamicChildren;const{type:N,props:G,patchFlag:$,shapeFlag:T,dirs:W,transition:se}=h,ee=N==="input"||N==="option";if(ee||$!==-1){W&&Mt(h,null,S,"created");let q=!1;if(I(p)){q=pu(null,se)&&S&&S.vnode.props&&S.vnode.props.appear;const X=p.content.firstChild;if(q){const be=X.getAttribute("class");be&&(X.$cls=be),se.beforeEnter(X)}A(X,p,S),h.el=p=X}if(T&16&&!(G&&(G.innerHTML||G.textContent))){let X=y(p.firstChild,h,p,S,E,F,M);for(;X;){_r(p,1)||Kn();const be=X;X=X.nextSibling,a(be)}}else if(T&8){let X=h.children;X[0]===`
`&&(p.tagName==="PRE"||p.tagName==="TEXTAREA")&&(X=X.slice(1)),p.textContent!==X&&(_r(p,0)||Kn(),p.textContent=h.children)}if(G){if(ee||!M||$&48){const X=p.tagName.includes("-");for(const be in G)(ee&&(be.endsWith("value")||be==="indeterminate")||vo(be)&&!kn(be)||be[0]==="."||X)&&o(p,be,null,G[be],void 0,S)}else if(G.onClick)o(p,"onClick",null,G.onClick,void 0,S);else if($&4&&yn(G.style))for(const X in G.style)G.style[X]}let ie;(ie=G&&G.onVnodeBeforeMount)&&ut(ie,S,h),W&&Mt(h,null,S,"beforeMount"),((ie=G&&G.onVnodeMounted)||W||q)&&Eu(()=>{ie&&ut(ie,S,h),q&&se.enter(p),W&&Mt(h,null,S,"mounted")},E)}return p.nextSibling},y=(p,h,S,E,F,M,N)=>{N=N||!!h.dynamicChildren;const G=h.children,$=G.length;for(let T=0;T<$;T++){const W=N?G[T]:G[T]=dt(G[T]),se=W.type===en;p?(se&&!N&&T+1<$&&dt(G[T+1]).type===en&&(l(r(p.data.slice(W.children.length)),S,s(p)),p.data=W.children),p=d(p,W,E,F,M,N)):se&&!W.children?l(W.el=r(""),S):(_r(S,1)||Kn(),n(null,W,S,null,E,F,yr(S),M))}return p},C=(p,h,S,E,F,M)=>{const{slotScopeIds:N}=h;N&&(F=F?F.concat(N):N);const G=i(p),$=y(s(p),h,G,S,E,F,M);return $&&eo($)&&$.data==="]"?s(h.anchor=$):(Kn(),l(h.anchor=c("]"),G,$),$)},w=(p,h,S,E,F,M)=>{if(_r(p.parentElement,1)||Kn(),h.el=null,M){const $=L(p);for(;;){const T=s(p);if(T&&T!==$)a(T);else break}}const N=s(p),G=i(p);return a(p),n(null,h,G,N,S,E,yr(G),F),S&&(S.vnode.el=h.el,as(S,h.el)),N},L=(p,h="[",S="]")=>{let E=0;for(;p;)if(p=s(p),p&&eo(p)&&(p.data===h&&E++,p.data===S)){if(E===0)return s(p);E--}return p},A=(p,h,S)=>{const E=h.parentNode;E&&E.replaceChild(p,h);let F=S;for(;F;)F.vnode.el===h&&(F.vnode.el=F.subTree.el=p),F=F.parent},I=p=>p.nodeType===1&&p.tagName==="TEMPLATE";return[u,d]}const ha="data-allow-mismatch",Hp={0:"text",1:"children",2:"class",3:"style",4:"attribute"};function _r(e,t){if(t===0||t===1)for(;e&&!e.hasAttribute(ha);)e=e.parentElement;const n=e&&e.getAttribute(ha);if(n==null)return!1;if(n==="")return!0;{const o=n.split(",");return t===0&&o.includes("children")?!0:o.includes(Hp[t])}}const jp=sr().requestIdleCallback||(e=>setTimeout(e,1)),Bp=sr().cancelIdleCallback||(e=>clearTimeout(e)),Wp=(e=1e4)=>t=>{const n=jp(t,{timeout:e});return()=>Bp(n)};function zp(e){const{top:t,left:n,bottom:o,right:r}=e.getBoundingClientRect(),{innerHeight:s,innerWidth:i}=window;return(t>0&&t<s||o>0&&o<s)&&(n>0&&n<i||r>0&&r<i)}const Kp=e=>(t,n)=>{const o=new IntersectionObserver(r=>{for(const s of r)if(s.isIntersecting){o.disconnect(),t();break}},e);return n(r=>{if(r instanceof Element){if(zp(r))return t(),o.disconnect(),!1;o.observe(r)}}),()=>o.disconnect()},qp=e=>t=>{if(e){const n=matchMedia(e);if(n.matches)t();else return n.addEventListener("change",t,{once:!0}),()=>n.removeEventListener("change",t)}},Yp=(e=[])=>(t,n)=>{Ee(e)&&(e=[e]);let o=!1;const r=i=>{o||(o=!0,s(),t(),i.target.dispatchEvent(new i.constructor(i.type,i)))},s=()=>{n(i=>{for(const a of e)i.removeEventListener(a,r)})};return n(i=>{for(const a of e)i.addEventListener(a,r,{once:!0})}),s};function Qp(e,t){if(eo(e)&&e.data==="["){let n=1,o=e.nextSibling;for(;o;){if(o.nodeType===1){if(t(o)===!1)break}else if(eo(o))if(o.data==="]"){if(--n===0)break}else o.data==="["&&n++;o=o.nextSibling}}else t(e)}const _n=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function Xp(e){ae(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,hydrate:s,timeout:i,suspensible:a=!0,onError:l}=e;let c=null,u,d=0;const f=()=>(d++,c=null,y()),y=()=>{let C;return c||(C=c=t().catch(w=>{if(w=w instanceof Error?w:new Error(String(w)),l)return new Promise((L,A)=>{l(w,()=>L(f()),()=>A(w),d+1)});throw w}).then(w=>C!==c&&c?c:(w&&(w.__esModule||w[Symbol.toStringTag]==="Module")&&(w=w.default),u=w,w)))};return nt({name:"AsyncComponentWrapper",__asyncLoader:y,__asyncHydrate(C,w,L){let A=!1;(w.bu||(w.bu=[])).push(()=>A=!0);const I=()=>{A||L()},p=s?()=>{const h=s(I,S=>Qp(C,S));h&&(w.bum||(w.bum=[])).push(h)}:I;u?p():y().then(()=>!w.isUnmounted&&p())},get __asyncResolved(){return u},setup(){const C=ze;if(ki(C),u)return()=>xs(u,C);const w=p=>{c=null,jn(p,C,13,!o)};if(a&&C.suspense||co)return y().then(p=>()=>xs(p,C)).catch(p=>(w(p),()=>o?B(o,{error:p}):null));const L=Fe(!1),A=Fe(),I=Fe(!!r);return r&&setTimeout(()=>{I.value=!1},r),i!=null&&setTimeout(()=>{if(!L.value&&!A.value){const p=new Error(`Async component timed out after ${i}ms.`);w(p),A.value=p}},i),y().then(()=>{L.value=!0,C.parent&&lr(C.parent.vnode)&&C.parent.update()}).catch(p=>{w(p),A.value=p}),()=>{if(L.value&&u)return xs(u,C);if(A.value&&o)return B(o,{error:A.value});if(n&&!I.value)return B(n)}}})}function xs(e,t){const{ref:n,props:o,children:r,ce:s}=t.vnode,i=B(e,o,r);return i.ref=n,i.ce=s,delete t.vnode.ce,i}const lr=e=>e.type.__isKeepAlive,Zp={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=$e(),o=n.ctx;if(!o.renderer)return()=>{const I=t.default&&t.default();return I&&I.length===1?I[0]:I};const r=new Map,s=new Set;let i=null;const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:d}}}=o,f=d("div");o.activate=(I,p,h,S,E)=>{const F=I.component;c(I,p,h,0,a),l(F.vnode,I,p,h,F,a,S,I.slotScopeIds,E),Ue(()=>{F.isDeactivated=!1,F.a&&An(F.a);const M=I.props&&I.props.onVnodeMounted;M&&ut(M,F.parent,I)},a)},o.deactivate=I=>{const p=I.component;Dr(p.m),Dr(p.a),c(I,f,null,1,a),Ue(()=>{p.da&&An(p.da);const h=I.props&&I.props.onVnodeUnmounted;h&&ut(h,p.parent,I),p.isDeactivated=!0},a)};function y(I){ws(I),u(I,n,a,!0)}function C(I){r.forEach((p,h)=>{const S=ti(p.type);S&&!I(S)&&w(h)})}function w(I){const p=r.get(I);p&&(!i||!Ot(p,i))?y(p):i&&ws(i),r.delete(I),s.delete(I)}ft(()=>[e.include,e.exclude],([I,p])=>{I&&C(h=>Po(I,h)),p&&C(h=>!Po(p,h))},{flush:"post",deep:!0});let L=null;const A=()=>{L!=null&&(Mr(n.subTree.type)?Ue(()=>{r.set(L,br(n.subTree))},n.subTree.suspense):r.set(L,br(n.subTree)))};return ln(A),ss(A),cr(()=>{r.forEach(I=>{const{subTree:p,suspense:h}=n,S=br(p);if(I.type===S.type&&I.key===S.key){ws(S);const E=S.component.da;E&&Ue(E,h);return}y(I)})}),()=>{if(L=null,!t.default)return i=null;const I=t.default(),p=I[0];if(I.length>1)return i=null,I;if(!$t(p)||!(p.shapeFlag&4)&&!(p.shapeFlag&128))return i=null,p;let h=br(p);if(h.type===De)return i=null,h;const S=h.type,E=ti(_n(h)?h.type.__asyncResolved||{}:S),{include:F,exclude:M,max:N}=e;if(F&&(!E||!Po(F,E))||M&&E&&Po(M,E))return h.shapeFlag&=-257,i=h,p;const G=h.key==null?S:h.key,$=r.get(G);return h.el&&(h=Ht(h),p.shapeFlag&128&&(p.ssContent=h)),L=G,$?(h.el=$.el,h.component=$.component,h.transition&&sn(h,h.transition),h.shapeFlag|=512,s.delete(G),s.add(G)):(s.add(G),N&&s.size>parseInt(N,10)&&w(s.values().next().value)),h.shapeFlag|=256,i=h,Mr(p.type)?p:h}}},em=Zp;function Po(e,t){return te(e)?e.some(n=>Po(n,t)):Ee(e)?e.split(",").includes(t):Xl(e)?(e.lastIndex=0,e.test(t)):!1}function Bc(e,t){zc(e,"a",t)}function Wc(e,t){zc(e,"da",t)}function zc(e,t,n=ze){const o=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(rs(t,o,n),n){let r=n.parent;for(;r&&r.parent;)lr(r.parent.vnode)&&tm(o,t,n,r),r=r.parent}}function tm(e,t,n,o){const r=rs(t,e,o,!0);bo(()=>{zr(o[t],r)},n)}function ws(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function br(e){return e.shapeFlag&128?e.ssContent:e}function rs(e,t,n=ze,o=!1){if(n){const r=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...i)=>{nn();const a=$n(n),l=Ct(t,n,e,i);return a(),on(),l});return o?r.unshift(s):r.push(s),s}}const an=e=>(t,n=ze)=>{(!co||e==="sp")&&rs(e,(...o)=>t(...o),n)},Ai=an("bm"),ln=an("m"),Li=an("bu"),ss=an("u"),cr=an("bum"),bo=an("um"),Kc=an("sp"),qc=an("rtg"),Yc=an("rtc");function Qc(e,t=ze){rs("ec",e,t)}const Di="components",nm="directives";function qo(e,t){return Mi(Di,e,!0,t)||e}const Xc=Symbol.for("v-ndc");function om(e){return Ee(e)?Mi(Di,e,!1)||e:e||Xc}function rm(e){return Mi(nm,e)}function Mi(e,t,n=!0,o=!1){const r=Ke||ze;if(r){const s=r.type;if(e===Di){const a=ti(s,!1);if(a&&(a===t||a===je(t)||a===yo(je(t))))return s}const i=va(r[e]||s[e],t)||va(r.appContext[e],t);return!i&&o?s:i}}function va(e,t){return e&&(e[t]||e[je(t)]||e[yo(je(t))])}function Dn(e,t,n,o){let r;const s=n&&n[o],i=te(e);if(i||Ee(e)){const a=i&&yn(e);let l=!1,c=!1;a&&(l=!ht(e),c=rn(e),e=Xr(e)),r=new Array(e.length);for(let u=0,d=e.length;u<d;u++)r[u]=t(l?c?Fr(We(e[u])):We(e[u]):e[u],u,void 0,s&&s[u])}else if(typeof e=="number"){r=new Array(e);for(let a=0;a<e;a++)r[a]=t(a+1,a,void 0,s&&s[a])}else if(xe(e))if(e[Symbol.iterator])r=Array.from(e,(a,l)=>t(a,l,void 0,s&&s[l]));else{const a=Object.keys(e);r=new Array(a.length);for(let l=0,c=a.length;l<c;l++){const u=a[l];r[l]=t(e[u],u,l,s&&s[l])}}else r=[];return n&&(n[o]=r),r}function sm(e,t){for(let n=0;n<t.length;n++){const o=t[n];if(te(o))for(let r=0;r<o.length;r++)e[o[r].name]=o[r].fn;else o&&(e[o.name]=o.key?(...r)=>{const s=o.fn(...r);return s&&(s.key=o.key),s}:o.fn)}return e}function im(e,t,n={},o,r){if(Ke.ce||Ke.parent&&_n(Ke.parent)&&Ke.parent.ce)return t!=="default"&&(n.name=t),me(),lo(Ce,null,[B("slot",n,o&&o())],64);let s=e[t];s&&s._c&&(s._d=!1),me();const i=s&&Gi(s(n)),a=n.key||i&&i.key,l=lo(Ce,{key:(a&&!wt(a)?a:`_${t}`)+(!i&&o?"_fb":"")},i||(o?o():[]),i&&e._===1?64:-2);return!r&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),s&&s._c&&(s._d=!0),l}function Gi(e){return e.some(t=>$t(t)?!(t.type===De||t.type===Ce&&!Gi(t.children)):!0)?e:null}function am(e,t){const n={};for(const o in e)n[t&&/[A-Z]/.test(o)?`on:${o}`:no(o)]=e[o];return n}const js=e=>e?Pu(e)?dr(e):js(e.parent):null,Ao=Oe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>js(e.parent),$root:e=>js(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ji(e),$forceUpdate:e=>e.f||(e.f=()=>{Ii(e.update)}),$nextTick:e=>e.n||(e.n=Jn.bind(e.proxy)),$watch:e=>Jm.bind(e)}),Cs=(e,t)=>e!==fe&&!e.__isScriptSetup&&ve(e,t),Bs={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:o,data:r,props:s,accessCache:i,type:a,appContext:l}=e;let c;if(t[0]!=="$"){const y=i[t];if(y!==void 0)switch(y){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return s[t]}else{if(Cs(o,t))return i[t]=1,o[t];if(r!==fe&&ve(r,t))return i[t]=2,r[t];if((c=e.propsOptions[0])&&ve(c,t))return i[t]=3,s[t];if(n!==fe&&ve(n,t))return i[t]=4,n[t];Ws&&(i[t]=0)}}const u=Ao[t];let d,f;if(u)return t==="$attrs"&&Ze(e.attrs,"get",""),u(e);if((d=a.__cssModules)&&(d=d[t]))return d;if(n!==fe&&ve(n,t))return i[t]=4,n[t];if(f=l.config.globalProperties,ve(f,t))return f[t]},set({_:e},t,n){const{data:o,setupState:r,ctx:s}=e;return Cs(r,t)?(r[t]=n,!0):o!==fe&&ve(o,t)?(o[t]=n,!0):ve(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:s,type:i}},a){let l,c;return!!(n[a]||e!==fe&&a[0]!=="$"&&ve(e,a)||Cs(t,a)||(l=s[0])&&ve(l,a)||ve(o,a)||ve(Ao,a)||ve(r.config.globalProperties,a)||(c=i.__cssModules)&&c[a])},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ve(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}},lm=Oe({},Bs,{get(e,t){if(t!==Symbol.unscopables)return Bs.get(e,t,e)},has(e,t){return t[0]!=="_"&&!Si(t)}});function cm(){return null}function um(){return null}function dm(e){}function fm(e){}function pm(){return null}function mm(){}function gm(e,t){return null}function hm(){return Zc().slots}function vm(){return Zc().attrs}function Zc(e){const t=$e();return t.setupContext||(t.setupContext=Ru(t))}function Yo(e){return te(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}function ym(e,t){const n=Yo(e);for(const o in t){if(o.startsWith("__skip"))continue;let r=n[o];r?te(r)||ae(r)?r=n[o]={type:r,default:t[o]}:r.default=t[o]:r===null&&(r=n[o]={default:t[o]}),r&&t[`__skip_${o}`]&&(r.skipFactory=!0)}return n}function _m(e,t){return!e||!t?e||t:te(e)&&te(t)?e.concat(t):Oe({},Yo(e),Yo(t))}function bm(e,t){const n={};for(const o in e)t.includes(o)||Object.defineProperty(n,o,{enumerable:!0,get:()=>e[o]});return n}function Sm(e){const t=$e();let n=e();return Xs(),Kr(n)&&(n=n.catch(o=>{throw $n(t),o})),[n,()=>$n(t)]}let Ws=!0;function Em(e){const t=Ji(e),n=e.proxy,o=e.ctx;Ws=!1,t.beforeCreate&&ya(t.beforeCreate,e,"bc");const{data:r,computed:s,methods:i,watch:a,provide:l,inject:c,created:u,beforeMount:d,mounted:f,beforeUpdate:y,updated:C,activated:w,deactivated:L,beforeDestroy:A,beforeUnmount:I,destroyed:p,unmounted:h,render:S,renderTracked:E,renderTriggered:F,errorCaptured:M,serverPrefetch:N,expose:G,inheritAttrs:$,components:T,directives:W,filters:se}=t;if(c&&xm(c,o,null),i)for(const ie in i){const X=i[ie];ae(X)&&(o[ie]=X.bind(n))}if(r){const ie=r.call(n,n);xe(ie)&&(e.data=vn(ie))}if(Ws=!0,s)for(const ie in s){const X=s[ie],be=ae(X)?X.bind(n,n):ae(X.get)?X.get.bind(n,n):gt,Ye=!ae(X)&&ae(X.set)?X.set.bind(n):gt,Ae=pe({get:be,set:Ye});Object.defineProperty(o,ie,{enumerable:!0,configurable:!0,get:()=>Ae.value,set:Ie=>Ae.value=Ie})}if(a)for(const ie in a)eu(a[ie],o,n,ie);if(l){const ie=ae(l)?l.call(n):l;Reflect.ownKeys(ie).forEach(X=>{Lo(X,ie[X])})}u&&ya(u,e,"c");function q(ie,X){te(X)?X.forEach(be=>ie(be.bind(n))):X&&ie(X.bind(n))}if(q(Ai,d),q(ln,f),q(Li,y),q(ss,C),q(Bc,w),q(Wc,L),q(Qc,M),q(Yc,E),q(qc,F),q(cr,I),q(bo,h),q(Kc,N),te(G))if(G.length){const ie=e.exposed||(e.exposed={});G.forEach(X=>{Object.defineProperty(ie,X,{get:()=>n[X],set:be=>n[X]=be,enumerable:!0})})}else e.exposed||(e.exposed={});S&&e.render===gt&&(e.render=S),$!=null&&(e.inheritAttrs=$),T&&(e.components=T),W&&(e.directives=W),N&&ki(e)}function xm(e,t,n=gt){te(e)&&(e=zs(e));for(const o in e){const r=e[o];let s;xe(r)?"default"in r?s=vt(r.from||o,r.default,!0):s=vt(r.from||o):s=vt(r),Je(s)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>s.value,set:i=>s.value=i}):t[o]=s}}function ya(e,t,n){Ct(te(e)?e.map(o=>o.bind(t.proxy)):e.bind(t.proxy),t,n)}function eu(e,t,n,o){let r=o.includes(".")?yu(n,o):()=>n[o];if(Ee(e)){const s=t[e];ae(s)&&ft(r,s)}else if(ae(e))ft(r,e.bind(n));else if(xe(e))if(te(e))e.forEach(s=>eu(s,t,n,o));else{const s=ae(e.handler)?e.handler.bind(n):t[e.handler];ae(s)&&ft(r,s,e)}}function Ji(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:s,config:{optionMergeStrategies:i}}=e.appContext,a=s.get(t);let l;return a?l=a:!r.length&&!n&&!o?l=t:(l={},r.length&&r.forEach(c=>Lr(l,c,i,!0)),Lr(l,t,i)),xe(t)&&s.set(t,l),l}function Lr(e,t,n,o=!1){const{mixins:r,extends:s}=t;s&&Lr(e,s,n,!0),r&&r.forEach(i=>Lr(e,i,n,!0));for(const i in t)if(!(o&&i==="expose")){const a=wm[i]||n&&n[i];e[i]=a?a(e[i],t[i]):t[i]}return e}const wm={data:_a,props:ba,emits:ba,methods:Io,computed:Io,beforeCreate:st,created:st,beforeMount:st,mounted:st,beforeUpdate:st,updated:st,beforeDestroy:st,beforeUnmount:st,destroyed:st,unmounted:st,activated:st,deactivated:st,errorCaptured:st,serverPrefetch:st,components:Io,directives:Io,watch:Tm,provide:_a,inject:Cm};function _a(e,t){return t?e?function(){return Oe(ae(e)?e.call(this,this):e,ae(t)?t.call(this,this):t)}:t:e}function Cm(e,t){return Io(zs(e),zs(t))}function zs(e){if(te(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function st(e,t){return e?[...new Set([].concat(e,t))]:t}function Io(e,t){return e?Oe(Object.create(null),e,t):t}function ba(e,t){return e?te(e)&&te(t)?[...new Set([...e,...t])]:Oe(Object.create(null),Yo(e),Yo(t??{})):t}function Tm(e,t){if(!e)return t;if(!t)return e;const n=Oe(Object.create(null),e);for(const o in t)n[o]=st(e[o],t[o]);return n}function tu(){return{app:null,config:{isNativeTag:yi,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Om=0;function Nm(e,t){return function(o,r=null){ae(o)||(o=Oe({},o)),r!=null&&!xe(r)&&(r=null);const s=tu(),i=new WeakSet,a=[];let l=!1;const c=s.app={_uid:Om++,_component:o,_props:r,_container:null,_context:s,_instance:null,version:Au,get config(){return s.config},set config(u){},use(u,...d){return i.has(u)||(u&&ae(u.install)?(i.add(u),u.install(c,...d)):ae(u)&&(i.add(u),u(c,...d))),c},mixin(u){return s.mixins.includes(u)||s.mixins.push(u),c},component(u,d){return d?(s.components[u]=d,c):s.components[u]},directive(u,d){return d?(s.directives[u]=d,c):s.directives[u]},mount(u,d,f){if(!l){const y=c._ceVNode||B(o,r);return y.appContext=s,f===!0?f="svg":f===!1&&(f=void 0),d&&t?t(y,u):e(y,u,f),l=!0,c._container=u,u.__vue_app__=c,dr(y.component)}},onUnmount(u){a.push(u)},unmount(){l&&(Ct(a,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide(u,d){return s.provides[u]=d,c},runWithContext(u){const d=Mn;Mn=c;try{return u()}finally{Mn=d}}};return c}}let Mn=null;function Lo(e,t){if(ze){let n=ze.provides;const o=ze.parent&&ze.parent.provides;o===n&&(n=ze.provides=Object.create(o)),n[e]=t}}function vt(e,t,n=!1){const o=$e();if(o||Mn){let r=Mn?Mn._context.provides:o?o.parent==null||o.ce?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&ae(t)?t.call(o&&o.proxy):t}}function Pm(){return!!($e()||Mn)}const nu={},ou=()=>Object.create(nu),ru=e=>Object.getPrototypeOf(e)===nu;function Im(e,t,n,o=!1){const r={},s=ou();e.propsDefaults=Object.create(null),su(e,t,r,s);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=o?r:Oi(r):e.type.props?e.props=r:e.props=s,e.attrs=s}function Fm(e,t,n,o){const{props:r,attrs:s,vnode:{patchFlag:i}}=e,a=ge(r),[l]=e.propsOptions;let c=!1;if((o||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let d=0;d<u.length;d++){let f=u[d];if(is(e.emitsOptions,f))continue;const y=t[f];if(l)if(ve(s,f))y!==s[f]&&(s[f]=y,c=!0);else{const C=je(f);r[C]=Ks(l,a,C,y,e,!1)}else y!==s[f]&&(s[f]=y,c=!0)}}}else{su(e,t,r,s)&&(c=!0);let u;for(const d in a)(!t||!ve(t,d)&&((u=et(d))===d||!ve(t,u)))&&(l?n&&(n[d]!==void 0||n[u]!==void 0)&&(r[d]=Ks(l,a,d,void 0,e,!0)):delete r[d]);if(s!==a)for(const d in s)(!t||!ve(t,d))&&(delete s[d],c=!0)}c&&Yt(e.attrs,"set","")}function su(e,t,n,o){const[r,s]=e.propsOptions;let i=!1,a;if(t)for(let l in t){if(kn(l))continue;const c=t[l];let u;r&&ve(r,u=je(l))?!s||!s.includes(u)?n[u]=c:(a||(a={}))[u]=c:is(e.emitsOptions,l)||(!(l in o)||c!==o[l])&&(o[l]=c,i=!0)}if(s){const l=ge(n),c=a||fe;for(let u=0;u<s.length;u++){const d=s[u];n[d]=Ks(r,l,d,c[d],e,!ve(c,d))}}return i}function Ks(e,t,n,o,r,s){const i=e[n];if(i!=null){const a=ve(i,"default");if(a&&o===void 0){const l=i.default;if(i.type!==Function&&!i.skipFactory&&ae(l)){const{propsDefaults:c}=r;if(n in c)o=c[n];else{const u=$n(r);o=c[n]=l.call(null,t),u()}}else o=l;r.ce&&r.ce._setProp(n,o)}i[0]&&(s&&!a?o=!1:i[1]&&(o===""||o===et(n))&&(o=!0))}return o}const Rm=new WeakMap;function iu(e,t,n=!1){const o=n?Rm:t.propsCache,r=o.get(e);if(r)return r;const s=e.props,i={},a=[];let l=!1;if(!ae(e)){const u=d=>{l=!0;const[f,y]=iu(d,t,!0);Oe(i,f),y&&a.push(...y)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!s&&!l)return xe(e)&&o.set(e,Fn),Fn;if(te(s))for(let u=0;u<s.length;u++){const d=je(s[u]);Sa(d)&&(i[d]=fe)}else if(s)for(const u in s){const d=je(u);if(Sa(d)){const f=s[u],y=i[d]=te(f)||ae(f)?{type:f}:Oe({},f),C=y.type;let w=!1,L=!0;if(te(C))for(let A=0;A<C.length;++A){const I=C[A],p=ae(I)&&I.name;if(p==="Boolean"){w=!0;break}else p==="String"&&(L=!1)}else w=ae(C)&&C.name==="Boolean";y[0]=w,y[1]=L,(w||ve(y,"default"))&&a.push(d)}}const c=[i,a];return xe(e)&&o.set(e,c),c}function Sa(e){return e[0]!=="$"&&!kn(e)}const Ui=e=>e==="_"||e==="_ctx"||e==="$stable",Vi=e=>te(e)?e.map(dt):[dt(e)],km=(e,t,n)=>{if(t._n)return t;const o=Gt((...r)=>Vi(t(...r)),n);return o._c=!1,o},au=(e,t,n)=>{const o=e._ctx;for(const r in e){if(Ui(r))continue;const s=e[r];if(ae(s))t[r]=km(r,s,o);else if(s!=null){const i=Vi(s);t[r]=()=>i}}},lu=(e,t)=>{const n=Vi(t);e.slots.default=()=>n},cu=(e,t,n)=>{for(const o in t)(n||!Ui(o))&&(e[o]=t[o])},Am=(e,t,n)=>{const o=e.slots=ou();if(e.vnode.shapeFlag&32){const r=t._;r?(cu(o,t,n),n&&bi(o,"_",r,!0)):au(t,o)}else t&&lu(e,t)},Lm=(e,t,n)=>{const{vnode:o,slots:r}=e;let s=!0,i=fe;if(o.shapeFlag&32){const a=t._;a?n&&a===1?s=!1:cu(r,t,n):(s=!t.$stable,au(t,r)),i=t}else t&&(lu(e,t),i={default:1});if(s)for(const a in r)!Ui(a)&&i[a]==null&&delete r[a]},Ue=Eu;function uu(e){return fu(e)}function du(e){return fu(e,$p)}function fu(e,t){const n=sr();n.__VUE__=!0;const{insert:o,remove:r,patchProp:s,createElement:i,createText:a,createComment:l,setText:c,setElementText:u,parentNode:d,nextSibling:f,setScopeId:y=gt,insertStaticContent:C}=e,w=(_,g,b,R=null,k=null,J=null,z=void 0,m=null,v=!!g.dynamicChildren)=>{if(_===g)return;_&&!Ot(_,g)&&(R=V(_),Ie(_,k,J,!0),_=null),g.patchFlag===-2&&(v=!1,g.dynamicChildren=null);const{type:x,ref:U,shapeFlag:j}=g;switch(x){case en:L(_,g,b,R);break;case De:A(_,g,b,R);break;case Gn:_==null&&I(g,b,R,z);break;case Ce:T(_,g,b,R,k,J,z,m,v);break;default:j&1?S(_,g,b,R,k,J,z,m,v):j&6?W(_,g,b,R,k,J,z,m,v):(j&64||j&128)&&x.process(_,g,b,R,k,J,z,m,v,Z)}U!=null&&k?ro(U,_&&_.ref,J,g||_,!g):U==null&&_&&_.ref!=null&&ro(_.ref,null,J,_,!0)},L=(_,g,b,R)=>{if(_==null)o(g.el=a(g.children),b,R);else{const k=g.el=_.el;g.children!==_.children&&c(k,g.children)}},A=(_,g,b,R)=>{_==null?o(g.el=l(g.children||""),b,R):g.el=_.el},I=(_,g,b,R)=>{[_.el,_.anchor]=C(_.children,g,b,R,_.el,_.anchor)},p=({el:_,anchor:g},b,R)=>{let k;for(;_&&_!==g;)k=f(_),o(_,b,R),_=k;o(g,b,R)},h=({el:_,anchor:g})=>{let b;for(;_&&_!==g;)b=f(_),r(_),_=b;r(g)},S=(_,g,b,R,k,J,z,m,v)=>{g.type==="svg"?z="svg":g.type==="math"&&(z="mathml"),_==null?E(g,b,R,k,J,z,m,v):N(_,g,k,J,z,m,v)},E=(_,g,b,R,k,J,z,m)=>{let v,x;const{props:U,shapeFlag:j,transition:H,dirs:O}=_;if(v=_.el=i(_.type,J,U&&U.is,U),j&8?u(v,_.children):j&16&&M(_.children,v,null,R,k,Ts(_,J),z,m),O&&Mt(_,null,R,"created"),F(v,_,_.scopeId,z,R),U){for(const oe in U)oe!=="value"&&!kn(oe)&&s(v,oe,null,U[oe],J,R);"value"in U&&s(v,"value",null,U.value,J),(x=U.onVnodeBeforeMount)&&ut(x,R,_)}O&&Mt(_,null,R,"beforeMount");const D=pu(k,H);D&&H.beforeEnter(v),o(v,g,b),((x=U&&U.onVnodeMounted)||D||O)&&Ue(()=>{x&&ut(x,R,_),D&&H.enter(v),O&&Mt(_,null,R,"mounted")},k)},F=(_,g,b,R,k)=>{if(b&&y(_,b),R)for(let J=0;J<R.length;J++)y(_,R[J]);if(k){let J=k.subTree;if(g===J||Mr(J.type)&&(J.ssContent===g||J.ssFallback===g)){const z=k.vnode;F(_,z,z.scopeId,z.slotScopeIds,k.parent)}}},M=(_,g,b,R,k,J,z,m,v=0)=>{for(let x=v;x<_.length;x++){const U=_[x]=m?gn(_[x]):dt(_[x]);w(null,U,g,b,R,k,J,z,m)}},N=(_,g,b,R,k,J,z)=>{const m=g.el=_.el;let{patchFlag:v,dynamicChildren:x,dirs:U}=g;v|=_.patchFlag&16;const j=_.props||fe,H=g.props||fe;let O;if(b&&On(b,!1),(O=H.onVnodeBeforeUpdate)&&ut(O,b,g,_),U&&Mt(g,_,b,"beforeUpdate"),b&&On(b,!0),(j.innerHTML&&H.innerHTML==null||j.textContent&&H.textContent==null)&&u(m,""),x?G(_.dynamicChildren,x,m,b,R,Ts(g,k),J):z||X(_,g,m,null,b,R,Ts(g,k),J,!1),v>0){if(v&16)$(m,j,H,b,k);else if(v&2&&j.class!==H.class&&s(m,"class",null,H.class,k),v&4&&s(m,"style",j.style,H.style,k),v&8){const D=g.dynamicProps;for(let oe=0;oe<D.length;oe++){const ne=D[oe],_e=j[ne],Le=H[ne];(Le!==_e||ne==="value")&&s(m,ne,_e,Le,k,b)}}v&1&&_.children!==g.children&&u(m,g.children)}else!z&&x==null&&$(m,j,H,b,k);((O=H.onVnodeUpdated)||U)&&Ue(()=>{O&&ut(O,b,g,_),U&&Mt(g,_,b,"updated")},R)},G=(_,g,b,R,k,J,z)=>{for(let m=0;m<g.length;m++){const v=_[m],x=g[m],U=v.el&&(v.type===Ce||!Ot(v,x)||v.shapeFlag&198)?d(v.el):b;w(v,x,U,null,R,k,J,z,!0)}},$=(_,g,b,R,k)=>{if(g!==b){if(g!==fe)for(const J in g)!kn(J)&&!(J in b)&&s(_,J,g[J],null,k,R);for(const J in b){if(kn(J))continue;const z=b[J],m=g[J];z!==m&&J!=="value"&&s(_,J,m,z,k,R)}"value"in b&&s(_,"value",g.value,b.value,k)}},T=(_,g,b,R,k,J,z,m,v)=>{const x=g.el=_?_.el:a(""),U=g.anchor=_?_.anchor:a("");let{patchFlag:j,dynamicChildren:H,slotScopeIds:O}=g;O&&(m=m?m.concat(O):O),_==null?(o(x,b,R),o(U,b,R),M(g.children||[],b,U,k,J,z,m,v)):j>0&&j&64&&H&&_.dynamicChildren?(G(_.dynamicChildren,H,b,k,J,z,m),(g.key!=null||k&&g===k.subTree)&&$i(_,g,!0)):X(_,g,b,U,k,J,z,m,v)},W=(_,g,b,R,k,J,z,m,v)=>{g.slotScopeIds=m,_==null?g.shapeFlag&512?k.ctx.activate(g,b,R,z,v):se(g,b,R,k,J,z,v):ee(_,g,v)},se=(_,g,b,R,k,J,z)=>{const m=_.component=Nu(_,R,k);if(lr(_)&&(m.ctx.renderer=Z),Iu(m,!1,z),m.asyncDep){if(k&&k.registerDep(m,q,z),!_.el){const v=m.subTree=B(De);A(null,v,g,b),_.placeholder=v.el}}else q(m,_,g,b,k,J,z)},ee=(_,g,b)=>{const R=g.component=_.component;if(Bm(_,g,b))if(R.asyncDep&&!R.asyncResolved){ie(R,g,b);return}else R.next=g,R.update();else g.el=_.el,R.vnode=g},q=(_,g,b,R,k,J,z)=>{const m=()=>{if(_.isMounted){let{next:j,bu:H,u:O,parent:D,vnode:oe}=_;{const rt=mu(_);if(rt){j&&(j.el=oe.el,ie(_,j,z)),rt.asyncDep.then(()=>{_.isUnmounted||m()});return}}let ne=j,_e;On(_,!1),j?(j.el=oe.el,ie(_,j,z)):j=oe,H&&An(H),(_e=j.props&&j.props.onVnodeBeforeUpdate)&&ut(_e,D,j,oe),On(_,!0);const Le=Tr(_),He=_.subTree;_.subTree=Le,w(He,Le,d(He.el),V(He),_,k,J),j.el=Le.el,ne===null&&as(_,Le.el),O&&Ue(O,k),(_e=j.props&&j.props.onVnodeUpdated)&&Ue(()=>ut(_e,D,j,oe),k)}else{let j;const{el:H,props:O}=g,{bm:D,m:oe,parent:ne,root:_e,type:Le}=_,He=_n(g);if(On(_,!1),D&&An(D),!He&&(j=O&&O.onVnodeBeforeMount)&&ut(j,ne,g),On(_,!0),H&&he){const rt=()=>{_.subTree=Tr(_),he(H,_.subTree,_,k,null)};He&&Le.__asyncHydrate?Le.__asyncHydrate(H,_,rt):rt()}else{_e.ce&&_e.ce._def.shadowRoot!==!1&&_e.ce._injectChildStyle(Le);const rt=_.subTree=Tr(_);w(null,rt,b,R,_,k,J),g.el=rt.el}if(oe&&Ue(oe,k),!He&&(j=O&&O.onVnodeMounted)){const rt=g;Ue(()=>ut(j,ne,rt),k)}(g.shapeFlag&256||ne&&_n(ne.vnode)&&ne.vnode.shapeFlag&256)&&_.a&&Ue(_.a,k),_.isMounted=!0,g=b=R=null}};_.scope.on();const v=_.effect=new Ho(m);_.scope.off();const x=_.update=v.run.bind(v),U=_.job=v.runIfDirty.bind(v);U.i=_,U.id=_.uid,v.scheduler=()=>Ii(U),On(_,!0),x()},ie=(_,g,b)=>{g.component=_;const R=_.vnode.props;_.vnode=g,_.next=null,Fm(_,g.props,R,b),Lm(_,g.children,b),nn(),ua(_),on()},X=(_,g,b,R,k,J,z,m,v=!1)=>{const x=_&&_.children,U=_?_.shapeFlag:0,j=g.children,{patchFlag:H,shapeFlag:O}=g;if(H>0){if(H&128){Ye(x,j,b,R,k,J,z,m,v);return}else if(H&256){be(x,j,b,R,k,J,z,m,v);return}}O&8?(U&16&&Ge(x,k,J),j!==x&&u(b,j)):U&16?O&16?Ye(x,j,b,R,k,J,z,m,v):Ge(x,k,J,!0):(U&8&&u(b,""),O&16&&M(j,b,R,k,J,z,m,v))},be=(_,g,b,R,k,J,z,m,v)=>{_=_||Fn,g=g||Fn;const x=_.length,U=g.length,j=Math.min(x,U);let H;for(H=0;H<j;H++){const O=g[H]=v?gn(g[H]):dt(g[H]);w(_[H],O,b,null,k,J,z,m,v)}x>U?Ge(_,k,J,!0,!1,j):M(g,b,R,k,J,z,m,v,j)},Ye=(_,g,b,R,k,J,z,m,v)=>{let x=0;const U=g.length;let j=_.length-1,H=U-1;for(;x<=j&&x<=H;){const O=_[x],D=g[x]=v?gn(g[x]):dt(g[x]);if(Ot(O,D))w(O,D,b,null,k,J,z,m,v);else break;x++}for(;x<=j&&x<=H;){const O=_[j],D=g[H]=v?gn(g[H]):dt(g[H]);if(Ot(O,D))w(O,D,b,null,k,J,z,m,v);else break;j--,H--}if(x>j){if(x<=H){const O=H+1,D=O<U?g[O].el:R;for(;x<=H;)w(null,g[x]=v?gn(g[x]):dt(g[x]),b,D,k,J,z,m,v),x++}}else if(x>H)for(;x<=j;)Ie(_[x],k,J,!0),x++;else{const O=x,D=x,oe=new Map;for(x=D;x<=H;x++){const mt=g[x]=v?gn(g[x]):dt(g[x]);mt.key!=null&&oe.set(mt.key,x)}let ne,_e=0;const Le=H-D+1;let He=!1,rt=0;const Wn=new Array(Le);for(x=0;x<Le;x++)Wn[x]=0;for(x=O;x<=j;x++){const mt=_[x];if(_e>=Le){Ie(mt,k,J,!0);continue}let kt;if(mt.key!=null)kt=oe.get(mt.key);else for(ne=D;ne<=H;ne++)if(Wn[ne-D]===0&&Ot(mt,g[ne])){kt=ne;break}kt===void 0?Ie(mt,k,J,!0):(Wn[kt-D]=x+1,kt>=rt?rt=kt:He=!0,w(mt,g[kt],b,null,k,J,z,m,v),_e++)}const oa=He?Dm(Wn):Fn;for(ne=oa.length-1,x=Le-1;x>=0;x--){const mt=D+x,kt=g[mt],ra=g[mt+1],sa=mt+1<U?ra.el||ra.placeholder:R;Wn[x]===0?w(null,kt,b,sa,k,J,z,m,v):He&&(ne<0||x!==oa[ne]?Ae(kt,b,sa,2):ne--)}}},Ae=(_,g,b,R,k=null)=>{const{el:J,type:z,transition:m,children:v,shapeFlag:x}=_;if(x&6){Ae(_.component.subTree,g,b,R);return}if(x&128){_.suspense.move(g,b,R);return}if(x&64){z.move(_,g,b,Z);return}if(z===Ce){o(J,g,b);for(let j=0;j<v.length;j++)Ae(v[j],g,b,R);o(_.anchor,g,b);return}if(z===Gn){p(_,g,b);return}if(R!==2&&x&1&&m)if(R===0)m.beforeEnter(J),o(J,g,b),Ue(()=>m.enter(J),k);else{const{leave:j,delayLeave:H,afterLeave:O}=m,D=()=>{_.ctx.isUnmounted?r(J):o(J,g,b)},oe=()=>{J._isLeaving&&J[qt](!0),j(J,()=>{D(),O&&O()})};H?H(J,D,oe):oe()}else o(J,g,b)},Ie=(_,g,b,R=!1,k=!1)=>{const{type:J,props:z,ref:m,children:v,dynamicChildren:x,shapeFlag:U,patchFlag:j,dirs:H,cacheIndex:O}=_;if(j===-2&&(k=!1),m!=null&&(nn(),ro(m,null,b,_,!0),on()),O!=null&&(g.renderCache[O]=void 0),U&256){g.ctx.deactivate(_);return}const D=U&1&&H,oe=!_n(_);let ne;if(oe&&(ne=z&&z.onVnodeBeforeUnmount)&&ut(ne,g,_),U&6)yt(_.component,b,R);else{if(U&128){_.suspense.unmount(b,R);return}D&&Mt(_,null,g,"beforeUnmount"),U&64?_.type.remove(_,g,b,Z,R):x&&!x.hasOnce&&(J!==Ce||j>0&&j&64)?Ge(x,g,b,!1,!0):(J===Ce&&j&384||!k&&U&16)&&Ge(v,g,b),R&&lt(_)}(oe&&(ne=z&&z.onVnodeUnmounted)||D)&&Ue(()=>{ne&&ut(ne,g,_),D&&Mt(_,null,g,"unmounted")},b)},lt=_=>{const{type:g,el:b,anchor:R,transition:k}=_;if(g===Ce){ot(b,R);return}if(g===Gn){h(_);return}const J=()=>{r(b),k&&!k.persisted&&k.afterLeave&&k.afterLeave()};if(_.shapeFlag&1&&k&&!k.persisted){const{leave:z,delayLeave:m}=k,v=()=>z(b,J);m?m(_.el,J,v):v()}else J()},ot=(_,g)=>{let b;for(;_!==g;)b=f(_),r(_),_=b;r(g)},yt=(_,g,b)=>{const{bum:R,scope:k,job:J,subTree:z,um:m,m:v,a:x}=_;Dr(v),Dr(x),R&&An(R),k.stop(),J&&(J.flags|=8,Ie(z,_,g,b)),m&&Ue(m,g),Ue(()=>{_.isUnmounted=!0},g)},Ge=(_,g,b,R=!1,k=!1,J=0)=>{for(let z=J;z<_.length;z++)Ie(_[z],g,b,R,k)},V=_=>{if(_.shapeFlag&6)return V(_.component.subTree);if(_.shapeFlag&128)return _.suspense.next();const g=f(_.anchor||_.el),b=g&&g[Dc];return b?f(b):g};let Y=!1;const K=(_,g,b)=>{_==null?g._vnode&&Ie(g._vnode,null,null,!0):w(g._vnode||null,_,g,null,null,null,b),g._vnode=_,Y||(Y=!0,ua(),Ar(),Y=!1)},Z={p:w,um:Ie,m:Ae,r:lt,mt:se,mc:M,pc:X,pbc:G,n:V,o:e};let ue,he;return t&&([ue,he]=t(Z)),{render:K,hydrate:ue,createApp:Nm(K,ue)}}function Ts({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function On({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function pu(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function $i(e,t,n=!1){const o=e.children,r=t.children;if(te(o)&&te(r))for(let s=0;s<o.length;s++){const i=o[s];let a=r[s];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=r[s]=gn(r[s]),a.el=i.el),!n&&a.patchFlag!==-2&&$i(i,a)),a.type===en&&a.patchFlag!==-1&&(a.el=i.el),a.type===De&&!a.el&&(a.el=i.el)}}function Dm(e){const t=e.slice(),n=[0];let o,r,s,i,a;const l=e.length;for(o=0;o<l;o++){const c=e[o];if(c!==0){if(r=n[n.length-1],e[r]<c){t[o]=r,n.push(o);continue}for(s=0,i=n.length-1;s<i;)a=s+i>>1,e[n[a]]<c?s=a+1:i=a;c<e[n[s]]&&(s>0&&(t[o]=n[s-1]),n[s]=o)}}for(s=n.length,i=n[s-1];s-- >0;)n[s]=i,i=t[i];return n}function mu(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:mu(t)}function Dr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const gu=Symbol.for("v-scx"),hu=()=>vt(gu);function Mm(e,t){return ur(e,null,t)}function Gm(e,t){return ur(e,null,{flush:"post"})}function vu(e,t){return ur(e,null,{flush:"sync"})}function ft(e,t,n){return ur(e,t,n)}function ur(e,t,n=fe){const{immediate:o,deep:r,flush:s,once:i}=n,a=Oe({},n),l=t&&o||!t&&s!=="post";let c;if(co){if(s==="sync"){const y=hu();c=y.__watcherHandles||(y.__watcherHandles=[])}else if(!l){const y=()=>{};return y.stop=gt,y.resume=gt,y.pause=gt,y}}const u=ze;a.call=(y,C,w)=>Ct(y,u,C,w);let d=!1;s==="post"?a.scheduler=y=>{Ue(y,u&&u.suspense)}:s!=="sync"&&(d=!0,a.scheduler=(y,C)=>{C?y():Ii(y)}),a.augmentJob=y=>{t&&(y.flags|=4),d&&(y.flags|=2,u&&(y.id=u.uid,y.i=u))};const f=Cp(e,t,a);return co&&(c?c.push(f):l&&f()),f}function Jm(e,t,n){const o=this.proxy,r=Ee(e)?e.includes(".")?yu(o,e):()=>o[e]:e.bind(o,o);let s;ae(t)?s=t:(s=t.handler,n=t);const i=$n(this),a=ur(r,s.bind(o),n);return i(),a}function yu(e,t){const n=t.split(".");return()=>{let o=e;for(let r=0;r<n.length&&o;r++)o=o[n[r]];return o}}function Um(e,t,n=fe){const o=$e(),r=je(t),s=et(t),i=_u(e,r),a=Oc((l,c)=>{let u,d=fe,f;return vu(()=>{const y=e[r];Xe(u,y)&&(u=y,c())}),{get(){return l(),n.get?n.get(u):u},set(y){const C=n.set?n.set(y):y;if(!Xe(C,u)&&!(d!==fe&&Xe(y,d)))return;const w=o.vnode.props;w&&(t in w||r in w||s in w)&&(`onUpdate:${t}`in w||`onUpdate:${r}`in w||`onUpdate:${s}`in w)||(u=y,c()),o.emit(`update:${t}`,C),Xe(y,C)&&Xe(y,d)&&!Xe(C,f)&&c(),d=y,f=C}}});return a[Symbol.iterator]=()=>{let l=0;return{next(){return l<2?{value:l++?i||fe:a,done:!1}:{done:!0}}}},a}const _u=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${je(t)}Modifiers`]||e[`${et(t)}Modifiers`];function Vm(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||fe;let r=n;const s=t.startsWith("update:"),i=s&&_u(o,t.slice(7));i&&(i.trim&&(r=n.map(u=>Ee(u)?u.trim():u)),i.number&&(r=n.map(Vo)));let a,l=o[a=no(t)]||o[a=no(je(t))];!l&&s&&(l=o[a=no(et(t))]),l&&Ct(l,e,6,r);const c=o[a+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Ct(c,e,6,r)}}function bu(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(r!==void 0)return r;const s=e.emits;let i={},a=!1;if(!ae(e)){const l=c=>{const u=bu(c,t,!0);u&&(a=!0,Oe(i,u))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!s&&!a?(xe(e)&&o.set(e,null),null):(te(s)?s.forEach(l=>i[l]=null):Oe(i,s),xe(e)&&o.set(e,i),i)}function is(e,t){return!e||!vo(t)?!1:(t=t.slice(2).replace(/Once$/,""),ve(e,t[0].toLowerCase()+t.slice(1))||ve(e,et(t))||ve(e,t))}function Tr(e){const{type:t,vnode:n,proxy:o,withProxy:r,propsOptions:[s],slots:i,attrs:a,emit:l,render:c,renderCache:u,props:d,data:f,setupState:y,ctx:C,inheritAttrs:w}=e,L=Ko(e);let A,I;try{if(n.shapeFlag&4){const h=r||o,S=h;A=dt(c.call(S,h,u,d,y,f,C)),I=a}else{const h=t;A=dt(h.length>1?h(d,{attrs:a,slots:i,emit:l}):h(d,null)),I=t.props?a:Hm(a)}}catch(h){Do.length=0,jn(h,e,1),A=B(De)}let p=A;if(I&&w!==!1){const h=Object.keys(I),{shapeFlag:S}=p;h.length&&S&7&&(s&&h.some(Wr)&&(I=jm(I,s)),p=Ht(p,I,!1,!0))}return n.dirs&&(p=Ht(p,null,!1,!0),p.dirs=p.dirs?p.dirs.concat(n.dirs):n.dirs),n.transition&&sn(p,n.transition),A=p,Ko(L),A}function $m(e,t=!0){let n;for(let o=0;o<e.length;o++){const r=e[o];if($t(r)){if(r.type!==De||r.children==="v-if"){if(n)return;n=r}}else return}return n}const Hm=e=>{let t;for(const n in e)(n==="class"||n==="style"||vo(n))&&((t||(t={}))[n]=e[n]);return t},jm=(e,t)=>{const n={};for(const o in e)(!Wr(o)||!(o.slice(9)in t))&&(n[o]=e[o]);return n};function Bm(e,t,n){const{props:o,children:r,component:s}=e,{props:i,children:a,patchFlag:l}=t,c=s.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return o?Ea(o,i,c):!!i;if(l&8){const u=t.dynamicProps;for(let d=0;d<u.length;d++){const f=u[d];if(i[f]!==o[f]&&!is(c,f))return!0}}}else return(r||a)&&(!a||!a.$stable)?!0:o===i?!1:o?i?Ea(o,i,c):!0:!!i;return!1}function Ea(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const s=o[r];if(t[s]!==e[s]&&!is(n,s))return!0}return!1}function as({vnode:e,parent:t},n){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o===e)(e=t.vnode).el=n,t=t.parent;else break}}const Mr=e=>e.__isSuspense;let qs=0;const Wm={name:"Suspense",__isSuspense:!0,process(e,t,n,o,r,s,i,a,l,c){if(e==null)Km(t,n,o,r,s,i,a,l,c);else{if(s&&s.deps>0&&!e.suspense.isInFallback){t.suspense=e.suspense,t.suspense.vnode=t,t.el=e.el;return}qm(e,t,n,o,r,i,a,l,c)}},hydrate:Ym,normalize:Qm},zm=Wm;function Qo(e,t){const n=e.props&&e.props[t];ae(n)&&n()}function Km(e,t,n,o,r,s,i,a,l){const{p:c,o:{createElement:u}}=l,d=u("div"),f=e.suspense=Su(e,r,o,t,d,n,s,i,a,l);c(null,f.pendingBranch=e.ssContent,d,null,o,f,s,i),f.deps>0?(Qo(e,"onPending"),Qo(e,"onFallback"),c(null,e.ssFallback,t,n,o,null,s,i),so(f,e.ssFallback)):f.resolve(!1,!0)}function qm(e,t,n,o,r,s,i,a,{p:l,um:c,o:{createElement:u}}){const d=t.suspense=e.suspense;d.vnode=t,t.el=e.el;const f=t.ssContent,y=t.ssFallback,{activeBranch:C,pendingBranch:w,isInFallback:L,isHydrating:A}=d;if(w)d.pendingBranch=f,Ot(f,w)?(l(w,f,d.hiddenContainer,null,r,d,s,i,a),d.deps<=0?d.resolve():L&&(A||(l(C,y,n,o,r,null,s,i,a),so(d,y)))):(d.pendingId=qs++,A?(d.isHydrating=!1,d.activeBranch=w):c(w,r,d),d.deps=0,d.effects.length=0,d.hiddenContainer=u("div"),L?(l(null,f,d.hiddenContainer,null,r,d,s,i,a),d.deps<=0?d.resolve():(l(C,y,n,o,r,null,s,i,a),so(d,y))):C&&Ot(f,C)?(l(C,f,n,o,r,d,s,i,a),d.resolve(!0)):(l(null,f,d.hiddenContainer,null,r,d,s,i,a),d.deps<=0&&d.resolve()));else if(C&&Ot(f,C))l(C,f,n,o,r,d,s,i,a),so(d,f);else if(Qo(t,"onPending"),d.pendingBranch=f,f.shapeFlag&512?d.pendingId=f.component.suspenseId:d.pendingId=qs++,l(null,f,d.hiddenContainer,null,r,d,s,i,a),d.deps<=0)d.resolve();else{const{timeout:I,pendingId:p}=d;I>0?setTimeout(()=>{d.pendingId===p&&d.fallback(y)},I):I===0&&d.fallback(y)}}function Su(e,t,n,o,r,s,i,a,l,c,u=!1){const{p:d,m:f,um:y,n:C,o:{parentNode:w,remove:L}}=c;let A;const I=Xm(e);I&&t&&t.pendingBranch&&(A=t.pendingId,t.deps++);const p=e.props?$o(e.props.timeout):void 0,h=s,S={vnode:e,parent:t,parentComponent:n,namespace:i,container:o,hiddenContainer:r,deps:0,pendingId:qs++,timeout:typeof p=="number"?p:-1,activeBranch:null,pendingBranch:null,isInFallback:!u,isHydrating:u,isUnmounted:!1,effects:[],resolve(E=!1,F=!1){const{vnode:M,activeBranch:N,pendingBranch:G,pendingId:$,effects:T,parentComponent:W,container:se}=S;let ee=!1;S.isHydrating?S.isHydrating=!1:E||(ee=N&&G.transition&&G.transition.mode==="out-in",ee&&(N.transition.afterLeave=()=>{$===S.pendingId&&(f(G,se,s===h?C(N):s,0),Wo(T))}),N&&(w(N.el)===se&&(s=C(N)),y(N,W,S,!0)),ee||f(G,se,s,0)),so(S,G),S.pendingBranch=null,S.isInFallback=!1;let q=S.parent,ie=!1;for(;q;){if(q.pendingBranch){q.effects.push(...T),ie=!0;break}q=q.parent}!ie&&!ee&&Wo(T),S.effects=[],I&&t&&t.pendingBranch&&A===t.pendingId&&(t.deps--,t.deps===0&&!F&&t.resolve()),Qo(M,"onResolve")},fallback(E){if(!S.pendingBranch)return;const{vnode:F,activeBranch:M,parentComponent:N,container:G,namespace:$}=S;Qo(F,"onFallback");const T=C(M),W=()=>{S.isInFallback&&(d(null,E,G,T,N,null,$,a,l),so(S,E))},se=E.transition&&E.transition.mode==="out-in";se&&(M.transition.afterLeave=W),S.isInFallback=!0,y(M,N,null,!0),se||W()},move(E,F,M){S.activeBranch&&f(S.activeBranch,E,F,M),S.container=E},next(){return S.activeBranch&&C(S.activeBranch)},registerDep(E,F,M){const N=!!S.pendingBranch;N&&S.deps++;const G=E.vnode.el;E.asyncDep.catch($=>{jn($,E,0)}).then($=>{if(E.isUnmounted||S.isUnmounted||S.pendingId!==E.suspenseId)return;E.asyncResolved=!0;const{vnode:T}=E;Zs(E,$,!1),G&&(T.el=G);const W=!G&&E.subTree.el;F(E,T,w(G||E.subTree.el),G?null:C(E.subTree),S,i,M),W&&L(W),as(E,T.el),N&&--S.deps===0&&S.resolve()})},unmount(E,F){S.isUnmounted=!0,S.activeBranch&&y(S.activeBranch,n,E,F),S.pendingBranch&&y(S.pendingBranch,n,E,F)}};return S}function Ym(e,t,n,o,r,s,i,a,l){const c=t.suspense=Su(t,o,n,e.parentNode,document.createElement("div"),null,r,s,i,a,!0),u=l(e,c.pendingBranch=t.ssContent,n,c,s,i);return c.deps===0&&c.resolve(!1,!0),u}function Qm(e){const{shapeFlag:t,children:n}=e,o=t&32;e.ssContent=xa(o?n.default:n),e.ssFallback=o?xa(n.fallback):B(De)}function xa(e){let t;if(ae(e)){const n=Un&&e._c;n&&(e._d=!1,me()),e=e(),n&&(e._d=!0,t=tt,xu())}return te(e)&&(e=$m(e)),e=dt(e),t&&!e.dynamicChildren&&(e.dynamicChildren=t.filter(n=>n!==e)),e}function Eu(e,t){t&&t.pendingBranch?te(e)?t.effects.push(...e):t.effects.push(e):Wo(e)}function so(e,t){e.activeBranch=t;const{vnode:n,parentComponent:o}=e;let r=t.el;for(;!r&&t.component;)t=t.component.subTree,r=t.el;n.el=r,o&&o.subTree===n&&(o.vnode.el=r,as(o,r))}function Xm(e){const t=e.props&&e.props.suspensible;return t!=null&&t!==!1}const Ce=Symbol.for("v-fgt"),en=Symbol.for("v-txt"),De=Symbol.for("v-cmt"),Gn=Symbol.for("v-stc"),Do=[];let tt=null;function me(e=!1){Do.push(tt=e?null:[])}function xu(){Do.pop(),tt=Do[Do.length-1]||null}let Un=1;function Ys(e,t=!1){Un+=e,e<0&&tt&&t&&(tt.hasOnce=!0)}function wu(e){return e.dynamicChildren=Un>0?tt||Fn:null,xu(),Un>0&&tt&&tt.push(e),e}function we(e,t,n,o,r,s){return wu(P(e,t,n,o,r,s,!0))}function lo(e,t,n,o,r){return wu(B(e,t,n,o,r,!0))}function $t(e){return e?e.__v_isVNode===!0:!1}function Ot(e,t){return e.type===t.type&&e.key===t.key}function Zm(e){}const Cu=({key:e})=>e??null,Or=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Ee(e)||Je(e)||ae(e)?{i:Ke,r:e,k:t,f:!!n}:e:null);function P(e,t=null,n=null,o=0,r=null,s=e===Ce?0:1,i=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Cu(t),ref:t&&Or(t),scopeId:ns,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Ke};return a?(Hi(l,n),s&128&&e.normalize(l)):n&&(l.shapeFlag|=Ee(n)?8:16),Un>0&&!i&&tt&&(l.patchFlag>0||s&6)&&l.patchFlag!==32&&tt.push(l),l}const B=eg;function eg(e,t=null,n=null,o=0,r=null,s=!1){if((!e||e===Xc)&&(e=De),$t(e)){const a=Ht(e,t,!0);return n&&Hi(a,n),Un>0&&!s&&tt&&(a.shapeFlag&6?tt[tt.indexOf(e)]=a:tt.push(a)),a.patchFlag=-2,a}if(lg(e)&&(e=e.__vccOpts),t){t=Tu(t);let{class:a,style:l}=t;a&&!Ee(a)&&(t.class=St(a)),xe(l)&&(ts(l)&&!te(l)&&(l=Oe({},l)),t.style=bn(l))}const i=Ee(e)?1:Mr(e)?128:Mc(e)?64:xe(e)?4:ae(e)?2:0;return P(e,t,n,o,r,i,s,!0)}function Tu(e){return e?ts(e)||ru(e)?Oe({},e):e:null}function Ht(e,t,n=!1,o=!1){const{props:r,ref:s,patchFlag:i,children:a,transition:l}=e,c=t?Ou(r||{},t):r,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&Cu(c),ref:t&&t.ref?n&&s?te(s)?s.concat(Or(t)):[s,Or(t)]:Or(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ce?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ht(e.ssContent),ssFallback:e.ssFallback&&Ht(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&o&&sn(u,l.clone(u)),u}function Vn(e=" ",t=0){return B(en,null,e,t)}function tg(e,t){const n=B(Gn,null,e);return n.staticCount=t,n}function Dt(e="",t=!1){return t?(me(),lo(De,null,e)):B(De,null,e)}function dt(e){return e==null||typeof e=="boolean"?B(De):te(e)?B(Ce,null,e.slice()):$t(e)?gn(e):B(en,null,String(e))}function gn(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ht(e)}function Hi(e,t){let n=0;const{shapeFlag:o}=e;if(t==null)t=null;else if(te(t))n=16;else if(typeof t=="object")if(o&65){const r=t.default;r&&(r._c&&(r._d=!1),Hi(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!ru(t)?t._ctx=Ke:r===3&&Ke&&(Ke.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else ae(t)?(t={default:t,_ctx:Ke},n=32):(t=String(t),o&64?(n=16,t=[Vn(t)]):n=8);e.children=t,e.shapeFlag|=n}function Ou(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const r in o)if(r==="class")t.class!==o.class&&(t.class=St([t.class,o.class]));else if(r==="style")t.style=bn([t.style,o.style]);else if(vo(r)){const s=t[r],i=o[r];i&&s!==i&&!(te(s)&&s.includes(i))&&(t[r]=s?[].concat(s,i):i)}else r!==""&&(t[r]=o[r])}return t}function ut(e,t,n,o=null){Ct(e,t,7,[n,o])}const ng=tu();let og=0;function Nu(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||ng,s={uid:og++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new xi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:iu(o,r),emitsOptions:bu(o,r),emit:null,emitted:null,propsDefaults:fe,inheritAttrs:o.inheritAttrs,ctx:fe,data:fe,props:fe,attrs:fe,slots:fe,refs:fe,setupState:fe,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=Vm.bind(null,s),e.ce&&e.ce(s),s}let ze=null;const $e=()=>ze||Ke;let Gr,Qs;{const e=sr(),t=(n,o)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(o),s=>{r.length>1?r.forEach(i=>i(s)):r[0](s)}};Gr=t("__VUE_INSTANCE_SETTERS__",n=>ze=n),Qs=t("__VUE_SSR_SETTERS__",n=>co=n)}const $n=e=>{const t=ze;return Gr(e),e.scope.on(),()=>{e.scope.off(),Gr(t)}},Xs=()=>{ze&&ze.scope.off(),Gr(null)};function Pu(e){return e.vnode.shapeFlag&4}let co=!1;function Iu(e,t=!1,n=!1){t&&Qs(t);const{props:o,children:r}=e.vnode,s=Pu(e);Im(e,o,s,t),Am(e,r,n||t);const i=s?rg(e,t):void 0;return t&&Qs(!1),i}function rg(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Bs);const{setup:o}=n;if(o){nn();const r=e.setupContext=o.length>1?Ru(e):null,s=$n(e),i=_o(o,e,0,[e.props,r]),a=Kr(i);if(on(),s(),(a||e.sp)&&!_n(e)&&ki(e),a){if(i.then(Xs,Xs),t)return i.then(l=>{Zs(e,l,t)}).catch(l=>{jn(l,e,0)});e.asyncDep=i}else Zs(e,i,t)}else Fu(e,t)}function Zs(e,t,n){ae(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:xe(t)&&(e.setupState=Pi(t)),Fu(e,n)}let Jr,ei;function sg(e){Jr=e,ei=t=>{t.render._rc&&(t.withProxy=new Proxy(t.ctx,lm))}}const ig=()=>!Jr;function Fu(e,t,n){const o=e.type;if(!e.render){if(!t&&Jr&&!o.render){const r=o.template||Ji(e).template;if(r){const{isCustomElement:s,compilerOptions:i}=e.appContext.config,{delimiters:a,compilerOptions:l}=o,c=Oe(Oe({isCustomElement:s,delimiters:a},i),l);o.render=Jr(r,c)}}e.render=o.render||gt,ei&&ei(e)}{const r=$n(e);nn();try{Em(e)}finally{on(),r()}}}const ag={get(e,t){return Ze(e,"get",""),e[t]}};function Ru(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,ag),slots:e.slots,emit:e.emit,expose:t}}function dr(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Pi(Cc(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Ao)return Ao[n](e)},has(t,n){return n in t||n in Ao}})):e.proxy}function ti(e,t=!0){return ae(e)?e.displayName||e.name:e.name||t&&e.__name}function lg(e){return ae(e)&&"__vccOpts"in e}const pe=(e,t)=>Sp(e,t,co);function So(e,t,n){const o=arguments.length;return o===2?xe(t)&&!te(t)?$t(t)?B(e,null,[t]):B(e,t):B(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):o===3&&$t(n)&&(n=[n]),B(e,t,n))}function cg(){}function ug(e,t,n,o){const r=n[o];if(r&&ku(r,e))return r;const s=t();return s.memo=e.slice(),s.cacheIndex=o,n[o]=s}function ku(e,t){const n=e.memo;if(n.length!=t.length)return!1;for(let o=0;o<n.length;o++)if(Xe(n[o],t[o]))return!1;return Un>0&&tt&&tt.push(e),!0}const Au="3.5.19",dg=gt,fg=Ip,pg=Yn,mg=Ac,gg={createComponentInstance:Nu,setupComponent:Iu,renderComponentRoot:Tr,setCurrentRenderingInstance:Ko,isVNode:$t,normalizeVNode:dt,getComponentPublicInstance:dr,ensureValidVNode:Gi,pushWarningContext:Tp,popWarningContext:Op},hg=gg,vg=null,yg=null,_g=null;/**
* @vue/runtime-dom v3.5.19
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ni;const wa=typeof window<"u"&&window.trustedTypes;if(wa)try{ni=wa.createPolicy("vue",{createHTML:e=>e})}catch{}const Lu=ni?e=>ni.createHTML(e):e=>e,bg="http://www.w3.org/2000/svg",Sg="http://www.w3.org/1998/Math/MathML",Kt=typeof document<"u"?document:null,Ca=Kt&&Kt.createElement("template"),Eg={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r=t==="svg"?Kt.createElementNS(bg,e):t==="mathml"?Kt.createElementNS(Sg,e):n?Kt.createElement(e,{is:n}):Kt.createElement(e);return e==="select"&&o&&o.multiple!=null&&r.setAttribute("multiple",o.multiple),r},createText:e=>Kt.createTextNode(e),createComment:e=>Kt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Kt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,s){const i=n?n.previousSibling:t.lastChild;if(r&&(r===s||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===s||!(r=r.nextSibling)););else{Ca.innerHTML=Lu(o==="svg"?`<svg>${e}</svg>`:o==="mathml"?`<math>${e}</math>`:e);const a=Ca.content;if(o==="svg"||o==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},cn="transition",Co="animation",uo=Symbol("_vtc"),Du={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Mu=Oe({},Ri,Du),xg=e=>(e.displayName="Transition",e.props=Mu,e),io=xg((e,{slots:t})=>So(Hc,Gu(e),t)),Nn=(e,t=[])=>{te(e)?e.forEach(n=>n(...t)):e&&e(...t)},Ta=e=>e?te(e)?e.some(t=>t.length>1):e.length>1:!1;function Gu(e){const t={};for(const T in e)T in Du||(t[T]=e[T]);if(e.css===!1)return t;const{name:n="v",type:o,duration:r,enterFromClass:s=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=s,appearActiveClass:c=i,appearToClass:u=a,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:y=`${n}-leave-to`}=e,C=wg(r),w=C&&C[0],L=C&&C[1],{onBeforeEnter:A,onEnter:I,onEnterCancelled:p,onLeave:h,onLeaveCancelled:S,onBeforeAppear:E=A,onAppear:F=I,onAppearCancelled:M=p}=t,N=(T,W,se,ee)=>{T._enterCancelled=ee,fn(T,W?u:a),fn(T,W?c:i),se&&se()},G=(T,W)=>{T._isLeaving=!1,fn(T,d),fn(T,y),fn(T,f),W&&W()},$=T=>(W,se)=>{const ee=T?F:I,q=()=>N(W,T,se);Nn(ee,[W,q]),Oa(()=>{fn(W,T?l:s),At(W,T?u:a),Ta(ee)||Na(W,o,w,q)})};return Oe(t,{onBeforeEnter(T){Nn(A,[T]),At(T,s),At(T,i)},onBeforeAppear(T){Nn(E,[T]),At(T,l),At(T,c)},onEnter:$(!1),onAppear:$(!0),onLeave(T,W){T._isLeaving=!0;const se=()=>G(T,W);At(T,d),T._enterCancelled?(At(T,f),oi()):(oi(),At(T,f)),Oa(()=>{T._isLeaving&&(fn(T,d),At(T,y),Ta(h)||Na(T,o,L,se))}),Nn(h,[T,se])},onEnterCancelled(T){N(T,!1,void 0,!0),Nn(p,[T])},onAppearCancelled(T){N(T,!0,void 0,!0),Nn(M,[T])},onLeaveCancelled(T){G(T),Nn(S,[T])}})}function wg(e){if(e==null)return null;if(xe(e))return[Os(e.enter),Os(e.leave)];{const t=Os(e);return[t,t]}}function Os(e){return $o(e)}function At(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[uo]||(e[uo]=new Set)).add(t)}function fn(e,t){t.split(/\s+/).forEach(o=>o&&e.classList.remove(o));const n=e[uo];n&&(n.delete(t),n.size||(e[uo]=void 0))}function Oa(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Cg=0;function Na(e,t,n,o){const r=e._endId=++Cg,s=()=>{r===e._endId&&o()};if(n!=null)return setTimeout(s,n);const{type:i,timeout:a,propCount:l}=Ju(e,t);if(!i)return o();const c=i+"end";let u=0;const d=()=>{e.removeEventListener(c,f),s()},f=y=>{y.target===e&&++u>=l&&d()};setTimeout(()=>{u<l&&d()},a+1),e.addEventListener(c,f)}function Ju(e,t){const n=window.getComputedStyle(e),o=C=>(n[C]||"").split(", "),r=o(`${cn}Delay`),s=o(`${cn}Duration`),i=Pa(r,s),a=o(`${Co}Delay`),l=o(`${Co}Duration`),c=Pa(a,l);let u=null,d=0,f=0;t===cn?i>0&&(u=cn,d=i,f=s.length):t===Co?c>0&&(u=Co,d=c,f=l.length):(d=Math.max(i,c),u=d>0?i>c?cn:Co:null,f=u?u===cn?s.length:l.length:0);const y=u===cn&&/\b(transform|all)(,|$)/.test(o(`${cn}Property`).toString());return{type:u,timeout:d,propCount:f,hasTransform:y}}function Pa(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,o)=>Ia(n)+Ia(e[o])))}function Ia(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function oi(){return document.body.offsetHeight}function Tg(e,t,n){const o=e[uo];o&&(t=(t?[t,...o]:[...o]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Ur=Symbol("_vod"),Uu=Symbol("_vsh"),Vu={beforeMount(e,{value:t},{transition:n}){e[Ur]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):To(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),To(e,!0),o.enter(e)):o.leave(e,()=>{To(e,!1)}):To(e,t))},beforeUnmount(e,{value:t}){To(e,t)}};function To(e,t){e.style.display=t?e[Ur]:"none",e[Uu]=!t}function Og(){Vu.getSSRProps=({value:e})=>{if(!e)return{style:{display:"none"}}}}const $u=Symbol("");function Ng(e){const t=$e();if(!t)return;const n=t.ut=(r=e(t.proxy))=>{Array.from(document.querySelectorAll(`[data-v-owner="${t.uid}"]`)).forEach(s=>Vr(s,r))},o=()=>{const r=e(t.proxy);t.ce?Vr(t.ce,r):ri(t.subTree,r),n(r)};Li(()=>{Wo(o)}),ln(()=>{ft(o,gt,{flush:"post"});const r=new MutationObserver(o);r.observe(t.subTree.el.parentNode,{childList:!0}),bo(()=>r.disconnect())})}function ri(e,t){if(e.shapeFlag&128){const n=e.suspense;e=n.activeBranch,n.pendingBranch&&!n.isHydrating&&n.effects.push(()=>{ri(n.activeBranch,t)})}for(;e.component;)e=e.component.subTree;if(e.shapeFlag&1&&e.el)Vr(e.el,t);else if(e.type===Ce)e.children.forEach(n=>ri(n,t));else if(e.type===Gn){let{el:n,anchor:o}=e;for(;n&&(Vr(n,t),n!==o);)n=n.nextSibling}}function Vr(e,t){if(e.nodeType===1){const n=e.style;let o="";for(const r in t){const s=ac(t[r]);n.setProperty(`--${r}`,s),o+=`--${r}: ${s};`}n[$u]=o}}const Pg=/(^|;)\s*display\s*:/;function Ig(e,t,n){const o=e.style,r=Ee(n);let s=!1;if(n&&!r){if(t)if(Ee(t))for(const i of t.split(";")){const a=i.slice(0,i.indexOf(":")).trim();n[a]==null&&Nr(o,a,"")}else for(const i in t)n[i]==null&&Nr(o,i,"");for(const i in n)i==="display"&&(s=!0),Nr(o,i,n[i])}else if(r){if(t!==n){const i=o[$u];i&&(n+=";"+i),o.cssText=n,s=Pg.test(n)}}else t&&e.removeAttribute("style");Ur in e&&(e[Ur]=s?o.display:"",e[Uu]&&(o.display="none"))}const Fa=/\s*!important$/;function Nr(e,t,n){if(te(n))n.forEach(o=>Nr(e,t,o));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const o=Fg(e,t);Fa.test(n)?e.setProperty(et(o),n.replace(Fa,""),"important"):e[o]=n}}const Ra=["Webkit","Moz","ms"],Ns={};function Fg(e,t){const n=Ns[t];if(n)return n;let o=je(t);if(o!=="filter"&&o in e)return Ns[t]=o;o=yo(o);for(let r=0;r<Ra.length;r++){const s=Ra[r]+o;if(s in e)return Ns[t]=s}return t}const ka="http://www.w3.org/1999/xlink";function Aa(e,t,n,o,r,s=oc(t)){o&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(ka,t.slice(6,t.length)):e.setAttributeNS(ka,t,n):n==null||s&&!Ei(n)?e.removeAttribute(t):e.setAttribute(t,s?"":wt(n)?String(n):n)}function La(e,t,n,o,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Lu(n):n);return}const s=e.tagName;if(t==="value"&&s!=="PROGRESS"&&!s.includes("-")){const a=s==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=Ei(n):n==null&&a==="string"?(n="",i=!0):a==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function Xt(e,t,n,o){e.addEventListener(t,n,o)}function Rg(e,t,n,o){e.removeEventListener(t,n,o)}const Da=Symbol("_vei");function kg(e,t,n,o,r=null){const s=e[Da]||(e[Da]={}),i=s[t];if(o&&i)i.value=o;else{const[a,l]=Ag(t);if(o){const c=s[t]=Mg(o,r);Xt(e,a,c,l)}else i&&(Rg(e,a,i,l),s[t]=void 0)}}const Ma=/(?:Once|Passive|Capture)$/;function Ag(e){let t;if(Ma.test(e)){t={};let o;for(;o=e.match(Ma);)e=e.slice(0,e.length-o[0].length),t[o[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):et(e.slice(2)),t]}let Ps=0;const Lg=Promise.resolve(),Dg=()=>Ps||(Lg.then(()=>Ps=0),Ps=Date.now());function Mg(e,t){const n=o=>{if(!o._vts)o._vts=Date.now();else if(o._vts<=n.attached)return;Ct(Gg(o,n.value),t,5,[o])};return n.value=e,n.attached=Dg(),n}function Gg(e,t){if(te(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(o=>r=>!r._stopped&&o&&o(r))}else return t}const Ga=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Jg=(e,t,n,o,r,s)=>{const i=r==="svg";t==="class"?Tg(e,o,i):t==="style"?Ig(e,n,o):vo(t)?Wr(t)||kg(e,t,n,o,s):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Ug(e,t,o,i))?(La(e,t,o),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Aa(e,t,o,i,s,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Ee(o))?La(e,je(t),o,s,t):(t==="true-value"?e._trueValue=o:t==="false-value"&&(e._falseValue=o),Aa(e,t,o,i))};function Ug(e,t,n,o){if(o)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ga(t)&&ae(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Ga(t)&&Ee(n)?!1:t in e}const Ja={};/*! #__NO_SIDE_EFFECTS__ */function Hu(e,t,n){const o=nt(e,t);rr(o)&&Oe(o,t);class r extends ls{constructor(i){super(o,i,n)}}return r.def=o,r}const Vg=((e,t)=>Hu(e,t,nd)),$g=typeof HTMLElement<"u"?HTMLElement:class{};class ls extends $g{constructor(t,n={},o=Hr){super(),this._def=t,this._props=n,this._createApp=o,this._isVueCE=!0,this._instance=null,this._app=null,this._nonce=this._def.nonce,this._connected=!1,this._resolved=!1,this._numberProps=null,this._styleChildren=new WeakSet,this._ob=null,this.shadowRoot&&o!==Hr?this._root=this.shadowRoot:t.shadowRoot!==!1?(this.attachShadow({mode:"open"}),this._root=this.shadowRoot):this._root=this}connectedCallback(){if(!this.isConnected)return;!this.shadowRoot&&!this._resolved&&this._parseSlots(),this._connected=!0;let t=this;for(;t=t&&(t.parentNode||t.host);)if(t instanceof ls){this._parent=t;break}this._instance||(this._resolved?this._mount(this._def):t&&t._pendingResolve?this._pendingResolve=t._pendingResolve.then(()=>{this._pendingResolve=void 0,this._resolveDef()}):this._resolveDef())}_setParent(t=this._parent){t&&(this._instance.parent=t._instance,this._inheritParentContext(t))}_inheritParentContext(t=this._parent){t&&this._app&&Object.setPrototypeOf(this._app._context.provides,t._instance.provides)}disconnectedCallback(){this._connected=!1,Jn(()=>{this._connected||(this._ob&&(this._ob.disconnect(),this._ob=null),this._app&&this._app.unmount(),this._instance&&(this._instance.ce=void 0),this._app=this._instance=null)})}_resolveDef(){if(this._pendingResolve)return;for(let o=0;o<this.attributes.length;o++)this._setAttr(this.attributes[o].name);this._ob=new MutationObserver(o=>{for(const r of o)this._setAttr(r.attributeName)}),this._ob.observe(this,{attributes:!0});const t=(o,r=!1)=>{this._resolved=!0,this._pendingResolve=void 0;const{props:s,styles:i}=o;let a;if(s&&!te(s))for(const l in s){const c=s[l];(c===Number||c&&c.type===Number)&&(l in this._props&&(this._props[l]=$o(this._props[l])),(a||(a=Object.create(null)))[je(l)]=!0)}this._numberProps=a,this._resolveProps(o),this.shadowRoot&&this._applyStyles(i),this._mount(o)},n=this._def.__asyncLoader;n?this._pendingResolve=n().then(o=>{o.configureApp=this._def.configureApp,t(this._def=o,!0)}):t(this._def)}_mount(t){this._app=this._createApp(t),this._inheritParentContext(),t.configureApp&&t.configureApp(this._app),this._app._ceVNode=this._createVNode(),this._app.mount(this._root);const n=this._instance&&this._instance.exposed;if(n)for(const o in n)ve(this,o)||Object.defineProperty(this,o,{get:()=>Ut(n[o])})}_resolveProps(t){const{props:n}=t,o=te(n)?n:Object.keys(n||{});for(const r of Object.keys(this))r[0]!=="_"&&o.includes(r)&&this._setProp(r,this[r]);for(const r of o.map(je))Object.defineProperty(this,r,{get(){return this._getProp(r)},set(s){this._setProp(r,s,!0,!0)}})}_setAttr(t){if(t.startsWith("data-v-"))return;const n=this.hasAttribute(t);let o=n?this.getAttribute(t):Ja;const r=je(t);n&&this._numberProps&&this._numberProps[r]&&(o=$o(o)),this._setProp(r,o,!1,!0)}_getProp(t){return this._props[t]}_setProp(t,n,o=!0,r=!1){if(n!==this._props[t]&&(n===Ja?delete this._props[t]:(this._props[t]=n,t==="key"&&this._app&&(this._app._ceVNode.key=n)),r&&this._instance&&this._update(),o)){const s=this._ob;s&&s.disconnect(),n===!0?this.setAttribute(et(t),""):typeof n=="string"||typeof n=="number"?this.setAttribute(et(t),n+""):n||this.removeAttribute(et(t)),s&&s.observe(this,{attributes:!0})}}_update(){const t=this._createVNode();this._app&&(t.appContext=this._app._context),td(t,this._root)}_createVNode(){const t={};this.shadowRoot||(t.onVnodeMounted=t.onVnodeUpdated=this._renderSlots.bind(this));const n=B(this._def,Oe(t,this._props));return this._instance||(n.ce=o=>{this._instance=o,o.ce=this,o.isCE=!0;const r=(s,i)=>{this.dispatchEvent(new CustomEvent(s,rr(i[0])?Oe({detail:i},i[0]):{detail:i}))};o.emit=(s,...i)=>{r(s,i),et(s)!==s&&r(et(s),i)},this._setParent()}),n}_applyStyles(t,n){if(!t)return;if(n){if(n===this._def||this._styleChildren.has(n))return;this._styleChildren.add(n)}const o=this._nonce;for(let r=t.length-1;r>=0;r--){const s=document.createElement("style");o&&s.setAttribute("nonce",o),s.textContent=t[r],this.shadowRoot.prepend(s)}}_parseSlots(){const t=this._slots={};let n;for(;n=this.firstChild;){const o=n.nodeType===1&&n.getAttribute("slot")||"default";(t[o]||(t[o]=[])).push(n),this.removeChild(n)}}_renderSlots(){const t=(this._teleportTarget||this).querySelectorAll("slot"),n=this._instance.type.__scopeId;for(let o=0;o<t.length;o++){const r=t[o],s=r.getAttribute("name")||"default",i=this._slots[s],a=r.parentNode;if(i)for(const l of i){if(n&&l.nodeType===1){const c=n+"-s",u=document.createTreeWalker(l,1);l.setAttribute(c,"");let d;for(;d=u.nextNode();)d.setAttribute(c,"")}a.insertBefore(l,r)}else for(;r.firstChild;)a.insertBefore(r.firstChild,r);a.removeChild(r)}}_injectChildStyle(t){this._applyStyles(t.styles,t)}_removeChildStyle(t){}}function ju(e){const t=$e(),n=t&&t.ce;return n||null}function Hg(){const e=ju();return e&&e.shadowRoot}function jg(e="$style"){{const t=$e();if(!t)return fe;const n=t.type.__cssModules;if(!n)return fe;const o=n[e];return o||fe}}const Bu=new WeakMap,Wu=new WeakMap,$r=Symbol("_moveCb"),Ua=Symbol("_enterCb"),Bg=e=>(delete e.props.mode,e),Wg=Bg({name:"TransitionGroup",props:Oe({},Mu,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=$e(),o=Fi();let r,s;return ss(()=>{if(!r.length)return;const i=e.moveClass||`${e.name||"v"}-move`;if(!Qg(r[0].el,n.vnode.el,i)){r=[];return}r.forEach(Kg),r.forEach(qg);const a=r.filter(Yg);oi(),a.forEach(l=>{const c=l.el,u=c.style;At(c,i),u.transform=u.webkitTransform=u.transitionDuration="";const d=c[$r]=f=>{f&&f.target!==c||(!f||/transform$/.test(f.propertyName))&&(c.removeEventListener("transitionend",d),c[$r]=null,fn(c,i))};c.addEventListener("transitionend",d)}),r=[]}),()=>{const i=ge(e),a=Gu(i);let l=i.tag||Ce;if(r=[],s)for(let c=0;c<s.length;c++){const u=s[c];u.el&&u.el instanceof Element&&(r.push(u),sn(u,ao(u,a,o,n)),Bu.set(u,u.el.getBoundingClientRect()))}s=t.default?os(t.default()):[];for(let c=0;c<s.length;c++){const u=s[c];u.key!=null&&sn(u,ao(u,a,o,n))}return B(l,null,s)}}}),zg=Wg;function Kg(e){const t=e.el;t[$r]&&t[$r](),t[Ua]&&t[Ua]()}function qg(e){Wu.set(e,e.el.getBoundingClientRect())}function Yg(e){const t=Bu.get(e),n=Wu.get(e),o=t.left-n.left,r=t.top-n.top;if(o||r){const s=e.el.style;return s.transform=s.webkitTransform=`translate(${o}px,${r}px)`,s.transitionDuration="0s",e}}function Qg(e,t,n){const o=e.cloneNode(),r=e[uo];r&&r.forEach(a=>{a.split(/\s+/).forEach(l=>l&&o.classList.remove(l))}),n.split(/\s+/).forEach(a=>a&&o.classList.add(a)),o.style.display="none";const s=t.nodeType===1?t:t.parentNode;s.appendChild(o);const{hasTransform:i}=Ju(o);return s.removeChild(o),i}const Sn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return te(t)?n=>An(t,n):t};function Xg(e){e.target.composing=!0}function Va(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const xt=Symbol("_assign"),Xo={created(e,{modifiers:{lazy:t,trim:n,number:o}},r){e[xt]=Sn(r);const s=o||r.props&&r.props.type==="number";Xt(e,t?"change":"input",i=>{if(i.target.composing)return;let a=e.value;n&&(a=a.trim()),s&&(a=Vo(a)),e[xt](a)}),n&&Xt(e,"change",()=>{e.value=e.value.trim()}),t||(Xt(e,"compositionstart",Xg),Xt(e,"compositionend",Va),Xt(e,"change",Va))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:o,trim:r,number:s}},i){if(e[xt]=Sn(i),e.composing)return;const a=(s||e.type==="number")&&!/^0\d/.test(e.value)?Vo(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(o&&t===n||r&&e.value.trim()===l)||(e.value=l))}},ji={deep:!0,created(e,t,n){e[xt]=Sn(n),Xt(e,"change",()=>{const o=e._modelValue,r=fo(e),s=e.checked,i=e[xt];if(te(o)){const a=ir(o,r),l=a!==-1;if(s&&!l)i(o.concat(r));else if(!s&&l){const c=[...o];c.splice(a,1),i(c)}}else if(xn(o)){const a=new Set(o);s?a.add(r):a.delete(r),i(a)}else i(Ku(e,s))})},mounted:$a,beforeUpdate(e,t,n){e[xt]=Sn(n),$a(e,t,n)}};function $a(e,{value:t,oldValue:n},o){e._modelValue=t;let r;if(te(t))r=ir(t,o.props.value)>-1;else if(xn(t))r=t.has(o.props.value);else{if(t===n)return;r=tn(t,Ku(e,!0))}e.checked!==r&&(e.checked=r)}const Bi={created(e,{value:t},n){e.checked=tn(t,n.props.value),e[xt]=Sn(n),Xt(e,"change",()=>{e[xt](fo(e))})},beforeUpdate(e,{value:t,oldValue:n},o){e[xt]=Sn(o),t!==n&&(e.checked=tn(t,o.props.value))}},zu={deep:!0,created(e,{value:t,modifiers:{number:n}},o){const r=xn(t);Xt(e,"change",()=>{const s=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?Vo(fo(i)):fo(i));e[xt](e.multiple?r?new Set(s):s:s[0]),e._assigning=!0,Jn(()=>{e._assigning=!1})}),e[xt]=Sn(o)},mounted(e,{value:t}){Ha(e,t)},beforeUpdate(e,t,n){e[xt]=Sn(n)},updated(e,{value:t}){e._assigning||Ha(e,t)}};function Ha(e,t){const n=e.multiple,o=te(t);if(!(n&&!o&&!xn(t))){for(let r=0,s=e.options.length;r<s;r++){const i=e.options[r],a=fo(i);if(n)if(o){const l=typeof a;l==="string"||l==="number"?i.selected=t.some(c=>String(c)===String(a)):i.selected=ir(t,a)>-1}else i.selected=t.has(a);else if(tn(fo(i),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function fo(e){return"_value"in e?e._value:e.value}function Ku(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const qu={created(e,t,n){Sr(e,t,n,null,"created")},mounted(e,t,n){Sr(e,t,n,null,"mounted")},beforeUpdate(e,t,n,o){Sr(e,t,n,o,"beforeUpdate")},updated(e,t,n,o){Sr(e,t,n,o,"updated")}};function Yu(e,t){switch(e){case"SELECT":return zu;case"TEXTAREA":return Xo;default:switch(t){case"checkbox":return ji;case"radio":return Bi;default:return Xo}}}function Sr(e,t,n,o,r){const i=Yu(e.tagName,n.props&&n.props.type)[r];i&&i(e,t,n,o)}function Zg(){Xo.getSSRProps=({value:e})=>({value:e}),Bi.getSSRProps=({value:e},t)=>{if(t.props&&tn(t.props.value,e))return{checked:!0}},ji.getSSRProps=({value:e},t)=>{if(te(e)){if(t.props&&ir(e,t.props.value)>-1)return{checked:!0}}else if(xn(e)){if(t.props&&e.has(t.props.value))return{checked:!0}}else if(e)return{checked:!0}},qu.getSSRProps=(e,t)=>{if(typeof t.type!="string")return;const n=Yu(t.type.toUpperCase(),t.props&&t.props.type);if(n.getSSRProps)return n.getSSRProps(e,t)}}const eh=["ctrl","shift","alt","meta"],th={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>eh.some(n=>e[`${n}Key`]&&!t.includes(n))},Qu=(e,t)=>{const n=e._withMods||(e._withMods={}),o=t.join(".");return n[o]||(n[o]=((r,...s)=>{for(let i=0;i<t.length;i++){const a=th[t[i]];if(a&&a(r,t))return}return e(r,...s)}))},nh={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},oh=(e,t)=>{const n=e._withKeys||(e._withKeys={}),o=t.join(".");return n[o]||(n[o]=(r=>{if(!("key"in r))return;const s=et(r.key);if(t.some(i=>i===s||nh[i]===s))return e(r)}))},Xu=Oe({patchProp:Jg},Eg);let Mo,ja=!1;function Zu(){return Mo||(Mo=uu(Xu))}function ed(){return Mo=ja?Mo:du(Xu),ja=!0,Mo}const td=((...e)=>{Zu().render(...e)}),rh=((...e)=>{ed().hydrate(...e)}),Hr=((...e)=>{const t=Zu().createApp(...e),{mount:n}=t;return t.mount=o=>{const r=rd(o);if(!r)return;const s=t._component;!ae(s)&&!s.render&&!s.template&&(s.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,od(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t}),nd=((...e)=>{const t=ed().createApp(...e),{mount:n}=t;return t.mount=o=>{const r=rd(o);if(r)return n(r,!0,od(r))},t});function od(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function rd(e){return Ee(e)?document.querySelector(e):e}let Ba=!1;const sh=()=>{Ba||(Ba=!0,Zg(),Og())},g1=Object.freeze(Object.defineProperty({__proto__:null,BaseTransition:Hc,BaseTransitionPropsValidators:Ri,Comment:De,DeprecationTypes:_g,EffectScope:xi,ErrorCodes:Pp,ErrorTypeStrings:fg,Fragment:Ce,KeepAlive:em,ReactiveEffect:Ho,Static:Gn,Suspense:zm,Teleport:Jc,Text:en,TrackOpTypes:Ep,Transition:io,TransitionGroup:zg,TriggerOpTypes:xp,VueElement:ls,assertNumber:Np,callWithAsyncErrorHandling:Ct,callWithErrorHandling:_o,camelize:je,capitalize:yo,cloneVNode:Ht,compatUtils:yg,computed:pe,createApp:Hr,createBlock:lo,createCommentVNode:Dt,createElementBlock:we,createElementVNode:P,createHydrationRenderer:du,createPropsRestProxy:bm,createRenderer:uu,createSSRApp:nd,createSlots:sm,createStaticVNode:tg,createTextVNode:Vn,createVNode:B,customRef:Oc,defineAsyncComponent:Xp,defineComponent:nt,defineCustomElement:Hu,defineEmits:um,defineExpose:dm,defineModel:mm,defineOptions:fm,defineProps:cm,defineSSRCustomElement:Vg,defineSlots:pm,devtools:pg,effect:jf,effectScope:lc,getCurrentInstance:$e,getCurrentScope:cc,getCurrentWatcher:wp,getTransitionRawChildren:os,guardReactiveProps:Tu,h:So,handleError:jn,hasInjectionContext:Pm,hydrate:rh,hydrateOnIdle:Wp,hydrateOnInteraction:Yp,hydrateOnMediaQuery:qp,hydrateOnVisible:Kp,initCustomFormatter:cg,initDirectivesForSSR:sh,inject:vt,isMemoSame:ku,isProxy:ts,isReactive:yn,isReadonly:rn,isRef:Je,isRuntimeOnly:ig,isShallow:ht,isVNode:$t,markRaw:Cc,mergeDefaults:ym,mergeModels:_m,mergeProps:Ou,nextTick:Jn,normalizeClass:St,normalizeProps:tc,normalizeStyle:bn,onActivated:Bc,onBeforeMount:Ai,onBeforeUnmount:cr,onBeforeUpdate:Li,onDeactivated:Wc,onErrorCaptured:Qc,onMounted:ln,onRenderTracked:Yc,onRenderTriggered:qc,onScopeDispose:$f,onServerPrefetch:Kc,onUnmounted:bo,onUpdated:ss,onWatcherCleanup:Pc,openBlock:me,popScopeId:Ap,provide:Lo,proxyRefs:Pi,pushScopeId:kp,queuePostFlushCb:Wo,reactive:vn,readonly:Ni,ref:Fe,registerRuntimeCompiler:sg,render:td,renderList:Dn,renderSlot:im,resolveComponent:qo,resolveDirective:rm,resolveDynamicComponent:om,resolveFilter:vg,resolveTransitionHooks:ao,setBlockTracking:Ys,setDevtoolsHook:mg,setTransitionHooks:sn,shallowReactive:Oi,shallowReadonly:up,shallowRef:ar,ssrContextKey:gu,ssrUtils:hg,stop:Bf,toDisplayString:re,toHandlerKey:no,toHandlers:am,toRaw:ge,toRef:_p,toRefs:hp,toValue:pp,transformVNodeArgs:Zm,triggerRef:fp,unref:Ut,useAttrs:vm,useCssModule:jg,useCssVars:Ng,useHost:ju,useId:Gp,useModel:Um,useSSRContext:hu,useShadowRoot:Hg,useSlots:hm,useTemplateRef:Jp,useTransitionState:Fi,vModelCheckbox:ji,vModelDynamic:qu,vModelRadio:Bi,vModelSelect:zu,vModelText:Xo,vShow:Vu,version:Au,warn:dg,watch:ft,watchEffect:Mm,watchPostEffect:Gm,watchSyncEffect:vu,withAsyncContext:Sm,withCtx:Gt,withDefaults:gm,withDirectives:Lc,withKeys:oh,withMemo:ug,withModifiers:Qu,withScopeId:Lp},Symbol.toStringTag,{value:"Module"})),ih="modulepreload",ah=function(e){return"/"+e},Wa={},ke=function(t,n,o){let r=Promise.resolve();if(n&&n.length>0){let l=function(c){return Promise.all(c.map(u=>Promise.resolve(u).then(d=>({status:"fulfilled",value:d}),d=>({status:"rejected",reason:d}))))};document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),a=i?.nonce||i?.getAttribute("nonce");r=l(n.map(c=>{if(c=ah(c),c in Wa)return;Wa[c]=!0;const u=c.endsWith(".css"),d=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${d}`))return;const f=document.createElement("link");if(f.rel=u?"stylesheet":ih,u||(f.as="script"),f.crossOrigin="",f.href=c,a&&f.setAttribute("nonce",a),document.head.appendChild(f),u)return new Promise((y,C)=>{f.addEventListener("load",y),f.addEventListener("error",()=>C(new Error(`Unable to preload CSS for ${c}`)))})}))}function s(i){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=i,window.dispatchEvent(a),!a.defaultPrevented)throw i}return r.then(i=>{for(const a of i||[])a.status==="rejected"&&s(a.reason);return t().catch(s)})};/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Qn=typeof document<"u";function sd(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function lh(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&sd(e.default)}const Se=Object.assign;function Is(e,t){const n={};for(const o in t){const r=t[o];n[o]=Rt(r)?r.map(e):e(r)}return n}const Go=()=>{},Rt=Array.isArray,id=/#/g,ch=/&/g,uh=/\//g,dh=/=/g,fh=/\?/g,ad=/\+/g,ph=/%5B/g,mh=/%5D/g,ld=/%5E/g,gh=/%60/g,cd=/%7B/g,hh=/%7C/g,ud=/%7D/g,vh=/%20/g;function Wi(e){return encodeURI(""+e).replace(hh,"|").replace(ph,"[").replace(mh,"]")}function yh(e){return Wi(e).replace(cd,"{").replace(ud,"}").replace(ld,"^")}function si(e){return Wi(e).replace(ad,"%2B").replace(vh,"+").replace(id,"%23").replace(ch,"%26").replace(gh,"`").replace(cd,"{").replace(ud,"}").replace(ld,"^")}function _h(e){return si(e).replace(dh,"%3D")}function bh(e){return Wi(e).replace(id,"%23").replace(fh,"%3F")}function Sh(e){return e==null?"":bh(e).replace(uh,"%2F")}function Zo(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Eh=/\/$/,xh=e=>e.replace(Eh,"");function Fs(e,t,n="/"){let o,r={},s="",i="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(o=t.slice(0,l),s=t.slice(l+1,a>-1?a:t.length),r=e(s)),a>-1&&(o=o||t.slice(0,a),i=t.slice(a,t.length)),o=Oh(o??t,n),{fullPath:o+(s&&"?")+s+i,path:o,query:r,hash:Zo(i)}}function wh(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function za(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Ch(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&po(t.matched[o],n.matched[r])&&dd(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function po(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function dd(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Th(e[n],t[n]))return!1;return!0}function Th(e,t){return Rt(e)?Ka(e,t):Rt(t)?Ka(t,e):e===t}function Ka(e,t){return Rt(t)?e.length===t.length&&e.every((n,o)=>n===t[o]):e.length===1&&e[0]===t}function Oh(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];(r===".."||r===".")&&o.push("");let s=n.length-1,i,a;for(i=0;i<o.length;i++)if(a=o[i],a!==".")if(a==="..")s>1&&s--;else break;return n.slice(0,s).join("/")+"/"+o.slice(i).join("/")}const un={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var er;(function(e){e.pop="pop",e.push="push"})(er||(er={}));var Jo;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Jo||(Jo={}));function Nh(e){if(!e)if(Qn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),xh(e)}const Ph=/^[^#]+#/;function Ih(e,t){return e.replace(Ph,"#")+t}function Fh(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}const cs=()=>({left:window.scrollX,top:window.scrollY});function Rh(e){let t;if("el"in e){const n=e.el,o=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Fh(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function qa(e,t){return(history.state?history.state.position-t:-1)+e}const ii=new Map;function kh(e,t){ii.set(e,t)}function Ah(e){const t=ii.get(e);return ii.delete(e),t}let Lh=()=>location.protocol+"//"+location.host;function fd(e,t){const{pathname:n,search:o,hash:r}=t,s=e.indexOf("#");if(s>-1){let a=r.includes(e.slice(s))?e.slice(s).length:1,l=r.slice(a);return l[0]!=="/"&&(l="/"+l),za(l,"")}return za(n,e)+o+r}function Dh(e,t,n,o){let r=[],s=[],i=null;const a=({state:f})=>{const y=fd(e,location),C=n.value,w=t.value;let L=0;if(f){if(n.value=y,t.value=f,i&&i===C){i=null;return}L=w?f.position-w.position:0}else o(y);r.forEach(A=>{A(n.value,C,{delta:L,type:er.pop,direction:L?L>0?Jo.forward:Jo.back:Jo.unknown})})};function l(){i=n.value}function c(f){r.push(f);const y=()=>{const C=r.indexOf(f);C>-1&&r.splice(C,1)};return s.push(y),y}function u(){const{history:f}=window;f.state&&f.replaceState(Se({},f.state,{scroll:cs()}),"")}function d(){for(const f of s)f();s=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:l,listen:c,destroy:d}}function Ya(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?cs():null}}function Mh(e){const{history:t,location:n}=window,o={value:fd(e,n)},r={value:t.state};r.value||s(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function s(l,c,u){const d=e.indexOf("#"),f=d>-1?(n.host&&document.querySelector("base")?e:e.slice(d))+l:Lh()+e+l;try{t[u?"replaceState":"pushState"](c,"",f),r.value=c}catch(y){console.error(y),n[u?"replace":"assign"](f)}}function i(l,c){const u=Se({},t.state,Ya(r.value.back,l,r.value.forward,!0),c,{position:r.value.position});s(l,u,!0),o.value=l}function a(l,c){const u=Se({},r.value,t.state,{forward:l,scroll:cs()});s(u.current,u,!0);const d=Se({},Ya(o.value,l,null),{position:u.position+1},c);s(l,d,!1),o.value=l}return{location:o,state:r,push:a,replace:i}}function Gh(e){e=Nh(e);const t=Mh(e),n=Dh(e,t.state,t.location,t.replace);function o(s,i=!0){i||n.pauseListeners(),history.go(s)}const r=Se({location:"",base:e,go:o,createHref:Ih.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Jh(e){return typeof e=="string"||e&&typeof e=="object"}function pd(e){return typeof e=="string"||typeof e=="symbol"}const md=Symbol("");var Qa;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Qa||(Qa={}));function mo(e,t){return Se(new Error,{type:e,[md]:!0},t)}function Bt(e,t){return e instanceof Error&&md in e&&(t==null||!!(e.type&t))}const Xa="[^/]+?",Uh={sensitive:!1,strict:!1,start:!0,end:!0},Vh=/[.+*?^${}()[\]/\\]/g;function $h(e,t){const n=Se({},Uh,t),o=[];let r=n.start?"^":"";const s=[];for(const c of e){const u=c.length?[]:[90];n.strict&&!c.length&&(r+="/");for(let d=0;d<c.length;d++){const f=c[d];let y=40+(n.sensitive?.25:0);if(f.type===0)d||(r+="/"),r+=f.value.replace(Vh,"\\$&"),y+=40;else if(f.type===1){const{value:C,repeatable:w,optional:L,regexp:A}=f;s.push({name:C,repeatable:w,optional:L});const I=A||Xa;if(I!==Xa){y+=10;try{new RegExp(`(${I})`)}catch(h){throw new Error(`Invalid custom RegExp for param "${C}" (${I}): `+h.message)}}let p=w?`((?:${I})(?:/(?:${I}))*)`:`(${I})`;d||(p=L&&c.length<2?`(?:/${p})`:"/"+p),L&&(p+="?"),r+=p,y+=20,L&&(y+=-8),w&&(y+=-20),I===".*"&&(y+=-50)}u.push(y)}o.push(u)}if(n.strict&&n.end){const c=o.length-1;o[c][o[c].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function a(c){const u=c.match(i),d={};if(!u)return null;for(let f=1;f<u.length;f++){const y=u[f]||"",C=s[f-1];d[C.name]=y&&C.repeatable?y.split("/"):y}return d}function l(c){let u="",d=!1;for(const f of e){(!d||!u.endsWith("/"))&&(u+="/"),d=!1;for(const y of f)if(y.type===0)u+=y.value;else if(y.type===1){const{value:C,repeatable:w,optional:L}=y,A=C in c?c[C]:"";if(Rt(A)&&!w)throw new Error(`Provided param "${C}" is an array but it is not repeatable (* or + modifiers)`);const I=Rt(A)?A.join("/"):A;if(!I)if(L)f.length<2&&(u.endsWith("/")?u=u.slice(0,-1):d=!0);else throw new Error(`Missing required param "${C}"`);u+=I}}return u||"/"}return{re:i,score:o,keys:s,parse:a,stringify:l}}function Hh(e,t){let n=0;for(;n<e.length&&n<t.length;){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function gd(e,t){let n=0;const o=e.score,r=t.score;for(;n<o.length&&n<r.length;){const s=Hh(o[n],r[n]);if(s)return s;n++}if(Math.abs(r.length-o.length)===1){if(Za(o))return 1;if(Za(r))return-1}return r.length-o.length}function Za(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const jh={type:0,value:""},Bh=/[a-zA-Z0-9_]/;function Wh(e){if(!e)return[[]];if(e==="/")return[[jh]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(y){throw new Error(`ERR (${n})/"${c}": ${y}`)}let n=0,o=n;const r=[];let s;function i(){s&&r.push(s),s=[]}let a=0,l,c="",u="";function d(){c&&(n===0?s.push({type:0,value:c}):n===1||n===2||n===3?(s.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),s.push({type:1,value:c,regexp:u,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),c="")}function f(){c+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){o=n,n=4;continue}switch(n){case 0:l==="/"?(c&&d(),i()):l===":"?(d(),n=1):f();break;case 4:f(),n=o;break;case 1:l==="("?n=2:Bh.test(l)?f():(d(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+l:n=3:u+=l;break;case 3:d(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${c}"`),d(),i(),r}function zh(e,t,n){const o=$h(Wh(e.path),n),r=Se(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Kh(e,t){const n=[],o=new Map;t=ol({strict:!1,end:!0,sensitive:!1},t);function r(d){return o.get(d)}function s(d,f,y){const C=!y,w=tl(d);w.aliasOf=y&&y.record;const L=ol(t,d),A=[w];if("alias"in d){const h=typeof d.alias=="string"?[d.alias]:d.alias;for(const S of h)A.push(tl(Se({},w,{components:y?y.record.components:w.components,path:S,aliasOf:y?y.record:w})))}let I,p;for(const h of A){const{path:S}=h;if(f&&S[0]!=="/"){const E=f.record.path,F=E[E.length-1]==="/"?"":"/";h.path=f.record.path+(S&&F+S)}if(I=zh(h,f,L),y?y.alias.push(I):(p=p||I,p!==I&&p.alias.push(I),C&&d.name&&!nl(I)&&i(d.name)),hd(I)&&l(I),w.children){const E=w.children;for(let F=0;F<E.length;F++)s(E[F],I,y&&y.children[F])}y=y||I}return p?()=>{i(p)}:Go}function i(d){if(pd(d)){const f=o.get(d);f&&(o.delete(d),n.splice(n.indexOf(f),1),f.children.forEach(i),f.alias.forEach(i))}else{const f=n.indexOf(d);f>-1&&(n.splice(f,1),d.record.name&&o.delete(d.record.name),d.children.forEach(i),d.alias.forEach(i))}}function a(){return n}function l(d){const f=Qh(d,n);n.splice(f,0,d),d.record.name&&!nl(d)&&o.set(d.record.name,d)}function c(d,f){let y,C={},w,L;if("name"in d&&d.name){if(y=o.get(d.name),!y)throw mo(1,{location:d});L=y.record.name,C=Se(el(f.params,y.keys.filter(p=>!p.optional).concat(y.parent?y.parent.keys.filter(p=>p.optional):[]).map(p=>p.name)),d.params&&el(d.params,y.keys.map(p=>p.name))),w=y.stringify(C)}else if(d.path!=null)w=d.path,y=n.find(p=>p.re.test(w)),y&&(C=y.parse(w),L=y.record.name);else{if(y=f.name?o.get(f.name):n.find(p=>p.re.test(f.path)),!y)throw mo(1,{location:d,currentLocation:f});L=y.record.name,C=Se({},f.params,d.params),w=y.stringify(C)}const A=[];let I=y;for(;I;)A.unshift(I.record),I=I.parent;return{name:L,path:w,params:C,matched:A,meta:Yh(A)}}e.forEach(d=>s(d));function u(){n.length=0,o.clear()}return{addRoute:s,resolve:c,removeRoute:i,clearRoutes:u,getRoutes:a,getRecordMatcher:r}}function el(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function tl(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:qh(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function qh(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]=typeof n=="object"?n[o]:n;return t}function nl(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Yh(e){return e.reduce((t,n)=>Se(t,n.meta),{})}function ol(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function Qh(e,t){let n=0,o=t.length;for(;n!==o;){const s=n+o>>1;gd(e,t[s])<0?o=s:n=s+1}const r=Xh(e);return r&&(o=t.lastIndexOf(r,o-1)),o}function Xh(e){let t=e;for(;t=t.parent;)if(hd(t)&&gd(e,t)===0)return t}function hd({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Zh(e){const t={};if(e===""||e==="?")return t;const o=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<o.length;++r){const s=o[r].replace(ad," "),i=s.indexOf("="),a=Zo(i<0?s:s.slice(0,i)),l=i<0?null:Zo(s.slice(i+1));if(a in t){let c=t[a];Rt(c)||(c=t[a]=[c]),c.push(l)}else t[a]=l}return t}function rl(e){let t="";for(let n in e){const o=e[n];if(n=_h(n),o==null){o!==void 0&&(t+=(t.length?"&":"")+n);continue}(Rt(o)?o.map(s=>s&&si(s)):[o&&si(o)]).forEach(s=>{s!==void 0&&(t+=(t.length?"&":"")+n,s!=null&&(t+="="+s))})}return t}function ev(e){const t={};for(const n in e){const o=e[n];o!==void 0&&(t[n]=Rt(o)?o.map(r=>r==null?null:""+r):o==null?o:""+o)}return t}const tv=Symbol(""),sl=Symbol(""),us=Symbol(""),zi=Symbol(""),ai=Symbol("");function Oo(){let e=[];function t(o){return e.push(o),()=>{const r=e.indexOf(o);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function hn(e,t,n,o,r,s=i=>i()){const i=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise((a,l)=>{const c=f=>{f===!1?l(mo(4,{from:n,to:t})):f instanceof Error?l(f):Jh(f)?l(mo(2,{from:t,to:f})):(i&&o.enterCallbacks[r]===i&&typeof f=="function"&&i.push(f),a())},u=s(()=>e.call(o&&o.instances[r],t,n,c));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch(f=>l(f))})}function Rs(e,t,n,o,r=s=>s()){const s=[];for(const i of e)for(const a in i.components){let l=i.components[a];if(!(t!=="beforeRouteEnter"&&!i.instances[a]))if(sd(l)){const u=(l.__vccOpts||l)[t];u&&s.push(hn(u,n,o,i,a,r))}else{let c=l();s.push(()=>c.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${a}" at "${i.path}"`);const d=lh(u)?u.default:u;i.mods[a]=u,i.components[a]=d;const y=(d.__vccOpts||d)[t];return y&&hn(y,n,o,i,a,r)()}))}}return s}function il(e){const t=vt(us),n=vt(zi),o=pe(()=>{const l=Ut(e.to);return t.resolve(l)}),r=pe(()=>{const{matched:l}=o.value,{length:c}=l,u=l[c-1],d=n.matched;if(!u||!d.length)return-1;const f=d.findIndex(po.bind(null,u));if(f>-1)return f;const y=al(l[c-2]);return c>1&&al(u)===y&&d[d.length-1].path!==y?d.findIndex(po.bind(null,l[c-2])):f}),s=pe(()=>r.value>-1&&iv(n.params,o.value.params)),i=pe(()=>r.value>-1&&r.value===n.matched.length-1&&dd(n.params,o.value.params));function a(l={}){if(sv(l)){const c=t[Ut(e.replace)?"replace":"push"](Ut(e.to)).catch(Go);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>c),c}return Promise.resolve()}return{route:o,href:pe(()=>o.value.href),isActive:s,isExactActive:i,navigate:a}}function nv(e){return e.length===1?e[0]:e}const ov=nt({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:il,setup(e,{slots:t}){const n=vn(il(e)),{options:o}=vt(us),r=pe(()=>({[ll(e.activeClass,o.linkActiveClass,"router-link-active")]:n.isActive,[ll(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const s=t.default&&nv(t.default(n));return e.custom?s:So("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},s)}}}),rv=ov;function sv(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function iv(e,t){for(const n in t){const o=t[n],r=e[n];if(typeof o=="string"){if(o!==r)return!1}else if(!Rt(r)||r.length!==o.length||o.some((s,i)=>s!==r[i]))return!1}return!0}function al(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const ll=(e,t,n)=>e??t??n,av=nt({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const o=vt(ai),r=pe(()=>e.route||o.value),s=vt(sl,0),i=pe(()=>{let c=Ut(s);const{matched:u}=r.value;let d;for(;(d=u[c])&&!d.components;)c++;return c}),a=pe(()=>r.value.matched[i.value]);Lo(sl,pe(()=>i.value+1)),Lo(tv,a),Lo(ai,r);const l=Fe();return ft(()=>[l.value,a.value,e.name],([c,u,d],[f,y,C])=>{u&&(u.instances[d]=c,y&&y!==u&&c&&c===f&&(u.leaveGuards.size||(u.leaveGuards=y.leaveGuards),u.updateGuards.size||(u.updateGuards=y.updateGuards))),c&&u&&(!y||!po(u,y)||!f)&&(u.enterCallbacks[d]||[]).forEach(w=>w(c))},{flush:"post"}),()=>{const c=r.value,u=e.name,d=a.value,f=d&&d.components[u];if(!f)return cl(n.default,{Component:f,route:c});const y=d.props[u],C=y?y===!0?c.params:typeof y=="function"?y(c):y:null,L=So(f,Se({},C,t,{onVnodeUnmounted:A=>{A.component.isUnmounted&&(d.instances[u]=null)},ref:l}));return cl(n.default,{Component:L,route:c})||L}}});function cl(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const lv=av;function cv(e){const t=Kh(e.routes,e),n=e.parseQuery||Zh,o=e.stringifyQuery||rl,r=e.history,s=Oo(),i=Oo(),a=Oo(),l=ar(un);let c=un;Qn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Is.bind(null,V=>""+V),d=Is.bind(null,Sh),f=Is.bind(null,Zo);function y(V,Y){let K,Z;return pd(V)?(K=t.getRecordMatcher(V),Z=Y):Z=V,t.addRoute(Z,K)}function C(V){const Y=t.getRecordMatcher(V);Y&&t.removeRoute(Y)}function w(){return t.getRoutes().map(V=>V.record)}function L(V){return!!t.getRecordMatcher(V)}function A(V,Y){if(Y=Se({},Y||l.value),typeof V=="string"){const g=Fs(n,V,Y.path),b=t.resolve({path:g.path},Y),R=r.createHref(g.fullPath);return Se(g,b,{params:f(b.params),hash:Zo(g.hash),redirectedFrom:void 0,href:R})}let K;if(V.path!=null)K=Se({},V,{path:Fs(n,V.path,Y.path).path});else{const g=Se({},V.params);for(const b in g)g[b]==null&&delete g[b];K=Se({},V,{params:d(g)}),Y.params=d(Y.params)}const Z=t.resolve(K,Y),ue=V.hash||"";Z.params=u(f(Z.params));const he=wh(o,Se({},V,{hash:yh(ue),path:Z.path})),_=r.createHref(he);return Se({fullPath:he,hash:ue,query:o===rl?ev(V.query):V.query||{}},Z,{redirectedFrom:void 0,href:_})}function I(V){return typeof V=="string"?Fs(n,V,l.value.path):Se({},V)}function p(V,Y){if(c!==V)return mo(8,{from:Y,to:V})}function h(V){return F(V)}function S(V){return h(Se(I(V),{replace:!0}))}function E(V){const Y=V.matched[V.matched.length-1];if(Y&&Y.redirect){const{redirect:K}=Y;let Z=typeof K=="function"?K(V):K;return typeof Z=="string"&&(Z=Z.includes("?")||Z.includes("#")?Z=I(Z):{path:Z},Z.params={}),Se({query:V.query,hash:V.hash,params:Z.path!=null?{}:V.params},Z)}}function F(V,Y){const K=c=A(V),Z=l.value,ue=V.state,he=V.force,_=V.replace===!0,g=E(K);if(g)return F(Se(I(g),{state:typeof g=="object"?Se({},ue,g.state):ue,force:he,replace:_}),Y||K);const b=K;b.redirectedFrom=Y;let R;return!he&&Ch(o,Z,K)&&(R=mo(16,{to:b,from:Z}),Ae(Z,Z,!0,!1)),(R?Promise.resolve(R):G(b,Z)).catch(k=>Bt(k)?Bt(k,2)?k:Ye(k):X(k,b,Z)).then(k=>{if(k){if(Bt(k,2))return F(Se({replace:_},I(k.to),{state:typeof k.to=="object"?Se({},ue,k.to.state):ue,force:he}),Y||b)}else k=T(b,Z,!0,_,ue);return $(b,Z,k),k})}function M(V,Y){const K=p(V,Y);return K?Promise.reject(K):Promise.resolve()}function N(V){const Y=ot.values().next().value;return Y&&typeof Y.runWithContext=="function"?Y.runWithContext(V):V()}function G(V,Y){let K;const[Z,ue,he]=uv(V,Y);K=Rs(Z.reverse(),"beforeRouteLeave",V,Y);for(const g of Z)g.leaveGuards.forEach(b=>{K.push(hn(b,V,Y))});const _=M.bind(null,V,Y);return K.push(_),Ge(K).then(()=>{K=[];for(const g of s.list())K.push(hn(g,V,Y));return K.push(_),Ge(K)}).then(()=>{K=Rs(ue,"beforeRouteUpdate",V,Y);for(const g of ue)g.updateGuards.forEach(b=>{K.push(hn(b,V,Y))});return K.push(_),Ge(K)}).then(()=>{K=[];for(const g of he)if(g.beforeEnter)if(Rt(g.beforeEnter))for(const b of g.beforeEnter)K.push(hn(b,V,Y));else K.push(hn(g.beforeEnter,V,Y));return K.push(_),Ge(K)}).then(()=>(V.matched.forEach(g=>g.enterCallbacks={}),K=Rs(he,"beforeRouteEnter",V,Y,N),K.push(_),Ge(K))).then(()=>{K=[];for(const g of i.list())K.push(hn(g,V,Y));return K.push(_),Ge(K)}).catch(g=>Bt(g,8)?g:Promise.reject(g))}function $(V,Y,K){a.list().forEach(Z=>N(()=>Z(V,Y,K)))}function T(V,Y,K,Z,ue){const he=p(V,Y);if(he)return he;const _=Y===un,g=Qn?history.state:{};K&&(Z||_?r.replace(V.fullPath,Se({scroll:_&&g&&g.scroll},ue)):r.push(V.fullPath,ue)),l.value=V,Ae(V,Y,K,_),Ye()}let W;function se(){W||(W=r.listen((V,Y,K)=>{if(!yt.listening)return;const Z=A(V),ue=E(Z);if(ue){F(Se(ue,{replace:!0,force:!0}),Z).catch(Go);return}c=Z;const he=l.value;Qn&&kh(qa(he.fullPath,K.delta),cs()),G(Z,he).catch(_=>Bt(_,12)?_:Bt(_,2)?(F(Se(I(_.to),{force:!0}),Z).then(g=>{Bt(g,20)&&!K.delta&&K.type===er.pop&&r.go(-1,!1)}).catch(Go),Promise.reject()):(K.delta&&r.go(-K.delta,!1),X(_,Z,he))).then(_=>{_=_||T(Z,he,!1),_&&(K.delta&&!Bt(_,8)?r.go(-K.delta,!1):K.type===er.pop&&Bt(_,20)&&r.go(-1,!1)),$(Z,he,_)}).catch(Go)}))}let ee=Oo(),q=Oo(),ie;function X(V,Y,K){Ye(V);const Z=q.list();return Z.length?Z.forEach(ue=>ue(V,Y,K)):console.error(V),Promise.reject(V)}function be(){return ie&&l.value!==un?Promise.resolve():new Promise((V,Y)=>{ee.add([V,Y])})}function Ye(V){return ie||(ie=!V,se(),ee.list().forEach(([Y,K])=>V?K(V):Y()),ee.reset()),V}function Ae(V,Y,K,Z){const{scrollBehavior:ue}=e;if(!Qn||!ue)return Promise.resolve();const he=!K&&Ah(qa(V.fullPath,0))||(Z||!K)&&history.state&&history.state.scroll||null;return Jn().then(()=>ue(V,Y,he)).then(_=>_&&Rh(_)).catch(_=>X(_,V,Y))}const Ie=V=>r.go(V);let lt;const ot=new Set,yt={currentRoute:l,listening:!0,addRoute:y,removeRoute:C,clearRoutes:t.clearRoutes,hasRoute:L,getRoutes:w,resolve:A,options:e,push:h,replace:S,go:Ie,back:()=>Ie(-1),forward:()=>Ie(1),beforeEach:s.add,beforeResolve:i.add,afterEach:a.add,onError:q.add,isReady:be,install(V){const Y=this;V.component("RouterLink",rv),V.component("RouterView",lv),V.config.globalProperties.$router=Y,Object.defineProperty(V.config.globalProperties,"$route",{enumerable:!0,get:()=>Ut(l)}),Qn&&!lt&&l.value===un&&(lt=!0,h(r.location).catch(ue=>{}));const K={};for(const ue in un)Object.defineProperty(K,ue,{get:()=>l.value[ue],enumerable:!0});V.provide(us,Y),V.provide(zi,Oi(K)),V.provide(ai,l);const Z=V.unmount;ot.add(V),V.unmount=function(){ot.delete(V),ot.size<1&&(c=un,W&&W(),W=null,l.value=un,lt=!1,ie=!1),Z()}}};function Ge(V){return V.reduce((Y,K)=>Y.then(()=>N(K)),Promise.resolve())}return yt}function uv(e,t){const n=[],o=[],r=[],s=Math.max(t.matched.length,e.matched.length);for(let i=0;i<s;i++){const a=t.matched[i];a&&(e.matched.find(c=>po(c,a))?o.push(a):n.push(a));const l=e.matched[i];l&&(t.matched.find(c=>po(c,l))||r.push(l))}return[n,o,r]}function Ki(){return vt(us)}function dv(e){return vt(zi)}const fv=()=>ke(()=>import("./ComingSoon-pTr55CkK.js"),[]),ul=(e,t="active")=>t==="coming-soon"?fv:e,pv=()=>ke(()=>import("./UniversalConverter-u29ZJqGD.js"),__vite__mapDeps([0,1])),mv=()=>ke(()=>import("./HtmlExtractor-VE192dam.js"),__vite__mapDeps([2,1])),gv=()=>ke(()=>import("./FileRenamer-8iwcmLug.js"),__vite__mapDeps([3,1,4,5,6])),hv=()=>ke(()=>import("./FaviconGenerator-BzYPCUOq.js"),__vite__mapDeps([7,1,5,8,9])),vv=()=>ke(()=>import("./ImageCompressor-tv9weCbE.js"),__vite__mapDeps([10,1,11,4,12,13,14])),yv=()=>ke(()=>import("./ImageListProcessor-CS2UUMkS.js"),__vite__mapDeps([15,1,16])),_v=()=>ke(()=>import("./VideoToGifConverter-tQni_wuY.js"),__vite__mapDeps([17,1,11,4,12,18])),bv=()=>ke(()=>import("./ImageToGifConverter-C61BmNac.js"),__vite__mapDeps([19,1,11,4,12,20])),Sv=()=>ke(()=>import("./GifEditor-DZ7MNy3Z.js"),__vite__mapDeps([21,1,11,4,12,13,22])),Ev=()=>ke(()=>import("./BackgroundRemover-Dd2_rEwa.js"),__vite__mapDeps([23,1,24])),xv=()=>ke(()=>import("./ImageWatermark-TW1xIJOm.js"),__vite__mapDeps([25,1,11,4,12,13,26])),wv=()=>ke(()=>import("./JsonMissingKeyFinder-F07T3fHk.js"),__vite__mapDeps([27,1,5])),Cv=()=>ke(()=>import("./JsonArraySlicer-DPwaXy1_.js"),__vite__mapDeps([28,1])),Tv=()=>ke(()=>import("./JsonPathExtractor-DHW-5XPk.js"),__vite__mapDeps([29,5,1,30])),Ov=()=>ke(()=>import("./JsonArrayDeduplicator-DjIMs4z-.js"),__vite__mapDeps([31,1])),Nv=()=>ke(()=>import("./QrCodeTool-D0VkbNjs.js"),__vite__mapDeps([32,1,4,33])),Pv=()=>ke(()=>import("./WebRtcFileTransfer-CpGmTkhY.js"),[]),Iv=()=>ke(()=>import("./TextSteganography-eIdyE9xT.js"),__vite__mapDeps([34,1,5,8])),Fv=()=>ke(()=>import("./ImageSteganography-V62__44H.js"),__vite__mapDeps([35,1])),Rv=()=>ke(()=>import("./TextProcessor-BfAS-qOI.js"),__vite__mapDeps([36,1,4,12])),kv=()=>ke(()=>import("./ColorPickerTool-CGVm7RF9.js"),__vite__mapDeps([37,1,38])),Av=()=>ke(()=>import("./HeartCollage-DZj6PT0P.js"),__vite__mapDeps([39,1,40])),Lv=()=>ke(()=>import("./PdfViewer-DS84vcUy.js"),__vite__mapDeps([41,42])),vd=[{path:"json-tools",name:"jsonTools",meta:{title:"JSON Tools",icon:"📋",description:"Comprehensive JSON processing and conversion utilities"},children:[{path:"json-path-extractor",name:"jsonPathExtractor",component:Tv,meta:{title:"JSON Path Extractor",icon:"🛤️",status:"active"}},{path:"json-missing-key-finder",name:"jsonMissingKeyFinder",component:wv,meta:{title:"JSON Missing Key Finder",icon:"🔍",status:"active"}},{path:"json-array-slicer",name:"jsonArraySlicer",component:Cv,meta:{title:"JSON Array Slicer",icon:"📊",status:"active"}},{path:"json-array-deduplicator",name:"jsonArrayDeduplicator",component:Ov,meta:{title:"JSON Array Deduplicator",icon:"🔄",status:"active"}}]},{path:"web-tools",name:"webTools",meta:{title:"Web Tools",icon:"🌐"},children:[{path:"html-extractor",name:"htmlExtractor",component:mv,meta:{title:"HTML Content Extractor",icon:"🖼️",status:"active"}},{path:"universal-converter",name:"universalConverter",component:pv,meta:{title:"Universal Format Converter",icon:"🔄",status:"active"}}]},{path:"image-tools",name:"imageTools",meta:{title:"Image Tools",icon:"🖼️"},children:[{path:"image-list-processor",name:"imageListProcessor",component:yv,meta:{title:"Image List Processor",icon:"🖼️",status:"active"}},{path:"image-compressor",name:"imageCompressor",component:vv,meta:{title:"Image Compressor",icon:"🗂",status:"active"}},{path:"background-remover",name:"backgroundRemover",component:Ev,meta:{title:"Background Remover",icon:"✂️",status:"active"}},{path:"video-to-gif-converter",name:"videoToGifConverter",component:_v,meta:{title:"Video to GIF Converter",icon:"🎬",status:"active"}},{path:"image-to-gif-converter",name:"imageToGifConverter",component:bv,meta:{title:"Image to GIF Converter",icon:"🖼️",status:"active"}},{path:"gif-editor",name:"gifEditor",component:Sv,meta:{title:"GIF Editor",icon:"🎞️",status:"active"}},{path:"svg-editor",name:"svgEditor",component:()=>ke(()=>import("./SvgEditor-DWvxc48g.js"),__vite__mapDeps([43,1,44])),meta:{title:"SVG Editor",icon:"🎨",status:"active"}},{path:"image-watermark",name:"imageWatermark",component:xv,meta:{title:"Image Watermark",icon:"✂️",status:"active"}},{path:"heart-collage",name:"heartCollage",component:()=>ul(Av,"coming-soon"),meta:{title:"Heart Collage",icon:"❤️",status:"coming-soon"}},{path:"color-picker",name:"colorPicker",component:kv,meta:{title:"Color Picker",icon:"🎨",status:"active"}}]},{path:"converters",name:"converters",meta:{title:"Converters",icon:"🔄"},children:[{path:"file-renamer",name:"fileRenamer",component:gv,meta:{title:"File Renamer",icon:"📝",status:"active"}},{path:"text-processor",name:"textProcessor",component:Rv,meta:{title:"Text Processor",icon:"📝",status:"active"}}]},{path:"generators",name:"generators",meta:{title:"Generators",icon:"⚡"},children:[{path:"favicon-generator",name:"faviconGenerator",component:hv,meta:{title:"Favicon Generator",icon:"🎯",status:"active"}},{path:"qr-generator",name:"qrCodeTool",component:Nv,meta:{title:"QR Code Tool",icon:"📱",status:"active"}}]},{path:"data-tools",name:"dataTools",meta:{title:"Data Tools",icon:"📊"},children:[{path:"web-rtc-file-transfer",name:"webRtcFileTransfer",component:Pv,meta:{title:"WebRTC File Transfer",icon:"📡",status:"active"}},{path:"text-steganography",name:"textSteganography",component:Iv,meta:{title:"Text Steganography",icon:"🔒",status:"active"}},{path:"image-steganography",name:"imageSteganography",component:ul(Fv,"coming-soon"),meta:{title:"Image Steganography",icon:"🖼️",status:"coming-soon"}},{path:"pdf-viewer",name:"pdfViewer",component:Lv,meta:{title:"PDF Viewer",icon:"📄",status:"active"}}]}],Er=vd.map(e=>({id:e.name,name:e.meta?.title,icon:e.meta?.icon||"📦",description:e.meta?.description,children:e.children?.map(t=>({id:t.name,name:t.meta?.title,icon:t.meta?.icon||"🔧",path:`/${e.path}/${t.path}`,status:t.meta?.status}))||[]}));/*!
  * shared v9.14.5
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Dv(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const jr=typeof window<"u",wn=(e,t=!1)=>t?Symbol.for(e):Symbol(e),Mv=(e,t,n)=>Gv({l:e,k:t,s:n}),Gv=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),Me=e=>typeof e=="number"&&isFinite(e),Jv=e=>_d(e)==="[object Date]",En=e=>_d(e)==="[object RegExp]",ds=e=>ce(e)&&Object.keys(e).length===0,qe=Object.assign,Uv=Object.create,Te=(e=null)=>Uv(e);let dl;const Zt=()=>dl||(dl=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:Te());function fl(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/\//g,"&#x2F;").replace(/=/g,"&#x3D;")}function pl(e){return e.replace(/&(?![a-zA-Z0-9#]{2,6};)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function Vv(e){return e=e.replace(/(\w+)\s*=\s*"([^"]*)"/g,(o,r,s)=>`${r}="${pl(s)}"`),e=e.replace(/(\w+)\s*=\s*'([^']*)'/g,(o,r,s)=>`${r}='${pl(s)}'`),/\s*on\w+\s*=\s*["']?[^"'>]+["']?/gi.test(e)&&(e=e.replace(/(\s+)(on)(\w+\s*=)/gi,"$1&#111;n$3")),[/(\s+(?:href|src|action|formaction)\s*=\s*["']?)\s*javascript:/gi,/(style\s*=\s*["'][^"']*url\s*\(\s*)javascript:/gi].forEach(o=>{e=e.replace(o,"$1javascript&#58;")}),e}const $v=Object.prototype.hasOwnProperty;function Pt(e,t){return $v.call(e,t)}const Re=Array.isArray,Pe=e=>typeof e=="function",Q=e=>typeof e=="string",de=e=>typeof e=="boolean",ye=e=>e!==null&&typeof e=="object",Hv=e=>ye(e)&&Pe(e.then)&&Pe(e.catch),yd=Object.prototype.toString,_d=e=>yd.call(e),ce=e=>{if(!ye(e))return!1;const t=Object.getPrototypeOf(e);return t===null||t.constructor===Object},jv=e=>e==null?"":Re(e)||ce(e)&&e.toString===yd?JSON.stringify(e,null,2):String(e);function Bv(e,t=""){return e.reduce((n,o,r)=>r===0?n+o:n+t+o,"")}function fs(e){let t=e;return()=>++t}const xr=e=>!ye(e)||Re(e);function Pr(e,t){if(xr(e)||xr(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:o,des:r}=n.pop();Object.keys(o).forEach(s=>{s!=="__proto__"&&(ye(o[s])&&!ye(r[s])&&(r[s]=Array.isArray(o[s])?[]:Te()),xr(r[s])||xr(o[s])?r[s]=o[s]:n.push({src:o[s],des:r[s]}))})}}/*!
  * message-compiler v9.14.5
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function Wv(e,t,n){return{line:e,column:t,offset:n}}function Br(e,t,n){return{start:e,end:t}}const zv=/\{([0-9a-zA-Z]+)\}/g;function bd(e,...t){return t.length===1&&Kv(t[0])&&(t=t[0]),(!t||!t.hasOwnProperty)&&(t={}),e.replace(zv,(n,o)=>t.hasOwnProperty(o)?t[o]:"")}const Sd=Object.assign,ml=e=>typeof e=="string",Kv=e=>e!==null&&typeof e=="object";function Ed(e,t=""){return e.reduce((n,o,r)=>r===0?n+o:n+t+o,"")}const qi={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2},qv={[qi.USE_MODULO_SYNTAX]:"Use modulo before '{{0}}'."};function Yv(e,t,...n){const o=bd(qv[e],...n||[]),r={message:String(o),code:e};return t&&(r.location=t),r}const le={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17},Qv={[le.EXPECTED_TOKEN]:"Expected token: '{0}'",[le.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[le.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[le.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[le.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[le.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[le.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[le.EMPTY_PLACEHOLDER]:"Empty placeholder",[le.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[le.INVALID_LINKED_FORMAT]:"Invalid linked format",[le.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[le.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[le.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[le.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[le.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[le.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function Eo(e,t,n={}){const{domain:o,messages:r,args:s}=n,i=bd((r||Qv)[e]||"",...s||[]),a=new SyntaxError(String(i));return a.code=e,t&&(a.location=t),a.domain=o,a}function Xv(e){throw e}const Wt=" ",Zv="\r",it=`
`,e0="\u2028",t0="\u2029";function n0(e){const t=e;let n=0,o=1,r=1,s=0;const i=F=>t[F]===Zv&&t[F+1]===it,a=F=>t[F]===it,l=F=>t[F]===t0,c=F=>t[F]===e0,u=F=>i(F)||a(F)||l(F)||c(F),d=()=>n,f=()=>o,y=()=>r,C=()=>s,w=F=>i(F)||l(F)||c(F)?it:t[F],L=()=>w(n),A=()=>w(n+s);function I(){return s=0,u(n)&&(o++,r=0),i(n)&&n++,n++,r++,t[n]}function p(){return i(n+s)&&s++,s++,t[n+s]}function h(){n=0,o=1,r=1,s=0}function S(F=0){s=F}function E(){const F=n+s;for(;F!==n;)I();s=0}return{index:d,line:f,column:y,peekOffset:C,charAt:w,currentChar:L,currentPeek:A,next:I,peek:p,reset:h,resetPeek:S,skipToPeek:E}}const dn=void 0,o0=".",gl="'",r0="tokenizer";function s0(e,t={}){const n=t.location!==!1,o=n0(e),r=()=>o.index(),s=()=>Wv(o.line(),o.column(),o.index()),i=s(),a=r(),l={currentType:14,offset:a,startLoc:i,endLoc:i,lastType:14,lastOffset:a,lastStartLoc:i,lastEndLoc:i,braceNest:0,inLinked:!1,text:""},c=()=>l,{onError:u}=t;function d(m,v,x,...U){const j=c();if(v.column+=x,v.offset+=x,u){const H=n?Br(j.startLoc,v):null,O=Eo(m,H,{domain:r0,args:U});u(O)}}function f(m,v,x){m.endLoc=s(),m.currentType=v;const U={type:v};return n&&(U.loc=Br(m.startLoc,m.endLoc)),x!=null&&(U.value=x),U}const y=m=>f(m,14);function C(m,v){return m.currentChar()===v?(m.next(),v):(d(le.EXPECTED_TOKEN,s(),0,v),"")}function w(m){let v="";for(;m.currentPeek()===Wt||m.currentPeek()===it;)v+=m.currentPeek(),m.peek();return v}function L(m){const v=w(m);return m.skipToPeek(),v}function A(m){if(m===dn)return!1;const v=m.charCodeAt(0);return v>=97&&v<=122||v>=65&&v<=90||v===95}function I(m){if(m===dn)return!1;const v=m.charCodeAt(0);return v>=48&&v<=57}function p(m,v){const{currentType:x}=v;if(x!==2)return!1;w(m);const U=A(m.currentPeek());return m.resetPeek(),U}function h(m,v){const{currentType:x}=v;if(x!==2)return!1;w(m);const U=m.currentPeek()==="-"?m.peek():m.currentPeek(),j=I(U);return m.resetPeek(),j}function S(m,v){const{currentType:x}=v;if(x!==2)return!1;w(m);const U=m.currentPeek()===gl;return m.resetPeek(),U}function E(m,v){const{currentType:x}=v;if(x!==8)return!1;w(m);const U=m.currentPeek()===".";return m.resetPeek(),U}function F(m,v){const{currentType:x}=v;if(x!==9)return!1;w(m);const U=A(m.currentPeek());return m.resetPeek(),U}function M(m,v){const{currentType:x}=v;if(!(x===8||x===12))return!1;w(m);const U=m.currentPeek()===":";return m.resetPeek(),U}function N(m,v){const{currentType:x}=v;if(x!==10)return!1;const U=()=>{const H=m.currentPeek();return H==="{"?A(m.peek()):H==="@"||H==="%"||H==="|"||H===":"||H==="."||H===Wt||!H?!1:H===it?(m.peek(),U()):T(m,!1)},j=U();return m.resetPeek(),j}function G(m){w(m);const v=m.currentPeek()==="|";return m.resetPeek(),v}function $(m){const v=w(m),x=m.currentPeek()==="%"&&m.peek()==="{";return m.resetPeek(),{isModulo:x,hasSpace:v.length>0}}function T(m,v=!0){const x=(j=!1,H="",O=!1)=>{const D=m.currentPeek();return D==="{"?H==="%"?!1:j:D==="@"||!D?H==="%"?!0:j:D==="%"?(m.peek(),x(j,"%",!0)):D==="|"?H==="%"||O?!0:!(H===Wt||H===it):D===Wt?(m.peek(),x(!0,Wt,O)):D===it?(m.peek(),x(!0,it,O)):!0},U=x();return v&&m.resetPeek(),U}function W(m,v){const x=m.currentChar();return x===dn?dn:v(x)?(m.next(),x):null}function se(m){const v=m.charCodeAt(0);return v>=97&&v<=122||v>=65&&v<=90||v>=48&&v<=57||v===95||v===36}function ee(m){return W(m,se)}function q(m){const v=m.charCodeAt(0);return v>=97&&v<=122||v>=65&&v<=90||v>=48&&v<=57||v===95||v===36||v===45}function ie(m){return W(m,q)}function X(m){const v=m.charCodeAt(0);return v>=48&&v<=57}function be(m){return W(m,X)}function Ye(m){const v=m.charCodeAt(0);return v>=48&&v<=57||v>=65&&v<=70||v>=97&&v<=102}function Ae(m){return W(m,Ye)}function Ie(m){let v="",x="";for(;v=be(m);)x+=v;return x}function lt(m){L(m);const v=m.currentChar();return v!=="%"&&d(le.EXPECTED_TOKEN,s(),0,v),m.next(),"%"}function ot(m){let v="";for(;;){const x=m.currentChar();if(x==="{"||x==="}"||x==="@"||x==="|"||!x)break;if(x==="%")if(T(m))v+=x,m.next();else break;else if(x===Wt||x===it)if(T(m))v+=x,m.next();else{if(G(m))break;v+=x,m.next()}else v+=x,m.next()}return v}function yt(m){L(m);let v="",x="";for(;v=ie(m);)x+=v;return m.currentChar()===dn&&d(le.UNTERMINATED_CLOSING_BRACE,s(),0),x}function Ge(m){L(m);let v="";return m.currentChar()==="-"?(m.next(),v+=`-${Ie(m)}`):v+=Ie(m),m.currentChar()===dn&&d(le.UNTERMINATED_CLOSING_BRACE,s(),0),v}function V(m){return m!==gl&&m!==it}function Y(m){L(m),C(m,"'");let v="",x="";for(;v=W(m,V);)v==="\\"?x+=K(m):x+=v;const U=m.currentChar();return U===it||U===dn?(d(le.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,s(),0),U===it&&(m.next(),C(m,"'")),x):(C(m,"'"),x)}function K(m){const v=m.currentChar();switch(v){case"\\":case"'":return m.next(),`\\${v}`;case"u":return Z(m,v,4);case"U":return Z(m,v,6);default:return d(le.UNKNOWN_ESCAPE_SEQUENCE,s(),0,v),""}}function Z(m,v,x){C(m,v);let U="";for(let j=0;j<x;j++){const H=Ae(m);if(!H){d(le.INVALID_UNICODE_ESCAPE_SEQUENCE,s(),0,`\\${v}${U}${m.currentChar()}`);break}U+=H}return`\\${v}${U}`}function ue(m){return m!=="{"&&m!=="}"&&m!==Wt&&m!==it}function he(m){L(m);let v="",x="";for(;v=W(m,ue);)x+=v;return x}function _(m){let v="",x="";for(;v=ee(m);)x+=v;return x}function g(m){const v=x=>{const U=m.currentChar();return U==="{"||U==="%"||U==="@"||U==="|"||U==="("||U===")"||!U||U===Wt?x:(x+=U,m.next(),v(x))};return v("")}function b(m){L(m);const v=C(m,"|");return L(m),v}function R(m,v){let x=null;switch(m.currentChar()){case"{":return v.braceNest>=1&&d(le.NOT_ALLOW_NEST_PLACEHOLDER,s(),0),m.next(),x=f(v,2,"{"),L(m),v.braceNest++,x;case"}":return v.braceNest>0&&v.currentType===2&&d(le.EMPTY_PLACEHOLDER,s(),0),m.next(),x=f(v,3,"}"),v.braceNest--,v.braceNest>0&&L(m),v.inLinked&&v.braceNest===0&&(v.inLinked=!1),x;case"@":return v.braceNest>0&&d(le.UNTERMINATED_CLOSING_BRACE,s(),0),x=k(m,v)||y(v),v.braceNest=0,x;default:{let j=!0,H=!0,O=!0;if(G(m))return v.braceNest>0&&d(le.UNTERMINATED_CLOSING_BRACE,s(),0),x=f(v,1,b(m)),v.braceNest=0,v.inLinked=!1,x;if(v.braceNest>0&&(v.currentType===5||v.currentType===6||v.currentType===7))return d(le.UNTERMINATED_CLOSING_BRACE,s(),0),v.braceNest=0,J(m,v);if(j=p(m,v))return x=f(v,5,yt(m)),L(m),x;if(H=h(m,v))return x=f(v,6,Ge(m)),L(m),x;if(O=S(m,v))return x=f(v,7,Y(m)),L(m),x;if(!j&&!H&&!O)return x=f(v,13,he(m)),d(le.INVALID_TOKEN_IN_PLACEHOLDER,s(),0,x.value),L(m),x;break}}return x}function k(m,v){const{currentType:x}=v;let U=null;const j=m.currentChar();switch((x===8||x===9||x===12||x===10)&&(j===it||j===Wt)&&d(le.INVALID_LINKED_FORMAT,s(),0),j){case"@":return m.next(),U=f(v,8,"@"),v.inLinked=!0,U;case".":return L(m),m.next(),f(v,9,".");case":":return L(m),m.next(),f(v,10,":");default:return G(m)?(U=f(v,1,b(m)),v.braceNest=0,v.inLinked=!1,U):E(m,v)||M(m,v)?(L(m),k(m,v)):F(m,v)?(L(m),f(v,12,_(m))):N(m,v)?(L(m),j==="{"?R(m,v)||U:f(v,11,g(m))):(x===8&&d(le.INVALID_LINKED_FORMAT,s(),0),v.braceNest=0,v.inLinked=!1,J(m,v))}}function J(m,v){let x={type:14};if(v.braceNest>0)return R(m,v)||y(v);if(v.inLinked)return k(m,v)||y(v);switch(m.currentChar()){case"{":return R(m,v)||y(v);case"}":return d(le.UNBALANCED_CLOSING_BRACE,s(),0),m.next(),f(v,3,"}");case"@":return k(m,v)||y(v);default:{if(G(m))return x=f(v,1,b(m)),v.braceNest=0,v.inLinked=!1,x;const{isModulo:j,hasSpace:H}=$(m);if(j)return H?f(v,0,ot(m)):f(v,4,lt(m));if(T(m))return f(v,0,ot(m));break}}return x}function z(){const{currentType:m,offset:v,startLoc:x,endLoc:U}=l;return l.lastType=m,l.lastOffset=v,l.lastStartLoc=x,l.lastEndLoc=U,l.offset=r(),l.startLoc=s(),o.currentChar()===dn?f(l,14):J(o,l)}return{nextToken:z,currentOffset:r,currentPosition:s,context:c}}const i0="parser",a0=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function l0(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const o=parseInt(t||n,16);return o<=55295||o>=57344?String.fromCodePoint(o):"�"}}}function c0(e={}){const t=e.location!==!1,{onError:n,onWarn:o}=e;function r(p,h,S,E,...F){const M=p.currentPosition();if(M.offset+=E,M.column+=E,n){const N=t?Br(S,M):null,G=Eo(h,N,{domain:i0,args:F});n(G)}}function s(p,h,S,E,...F){const M=p.currentPosition();if(M.offset+=E,M.column+=E,o){const N=t?Br(S,M):null;o(Yv(h,N,F))}}function i(p,h,S){const E={type:p};return t&&(E.start=h,E.end=h,E.loc={start:S,end:S}),E}function a(p,h,S,E){t&&(p.end=h,p.loc&&(p.loc.end=S))}function l(p,h){const S=p.context(),E=i(3,S.offset,S.startLoc);return E.value=h,a(E,p.currentOffset(),p.currentPosition()),E}function c(p,h){const S=p.context(),{lastOffset:E,lastStartLoc:F}=S,M=i(5,E,F);return M.index=parseInt(h,10),p.nextToken(),a(M,p.currentOffset(),p.currentPosition()),M}function u(p,h,S){const E=p.context(),{lastOffset:F,lastStartLoc:M}=E,N=i(4,F,M);return N.key=h,S===!0&&(N.modulo=!0),p.nextToken(),a(N,p.currentOffset(),p.currentPosition()),N}function d(p,h){const S=p.context(),{lastOffset:E,lastStartLoc:F}=S,M=i(9,E,F);return M.value=h.replace(a0,l0),p.nextToken(),a(M,p.currentOffset(),p.currentPosition()),M}function f(p){const h=p.nextToken(),S=p.context(),{lastOffset:E,lastStartLoc:F}=S,M=i(8,E,F);return h.type!==12?(r(p,le.UNEXPECTED_EMPTY_LINKED_MODIFIER,S.lastStartLoc,0),M.value="",a(M,E,F),{nextConsumeToken:h,node:M}):(h.value==null&&r(p,le.UNEXPECTED_LEXICAL_ANALYSIS,S.lastStartLoc,0,Tt(h)),M.value=h.value||"",a(M,p.currentOffset(),p.currentPosition()),{node:M})}function y(p,h){const S=p.context(),E=i(7,S.offset,S.startLoc);return E.value=h,a(E,p.currentOffset(),p.currentPosition()),E}function C(p){const h=p.context(),S=i(6,h.offset,h.startLoc);let E=p.nextToken();if(E.type===9){const F=f(p);S.modifier=F.node,E=F.nextConsumeToken||p.nextToken()}switch(E.type!==10&&r(p,le.UNEXPECTED_LEXICAL_ANALYSIS,h.lastStartLoc,0,Tt(E)),E=p.nextToken(),E.type===2&&(E=p.nextToken()),E.type){case 11:E.value==null&&r(p,le.UNEXPECTED_LEXICAL_ANALYSIS,h.lastStartLoc,0,Tt(E)),S.key=y(p,E.value||"");break;case 5:E.value==null&&r(p,le.UNEXPECTED_LEXICAL_ANALYSIS,h.lastStartLoc,0,Tt(E)),S.key=u(p,E.value||"");break;case 6:E.value==null&&r(p,le.UNEXPECTED_LEXICAL_ANALYSIS,h.lastStartLoc,0,Tt(E)),S.key=c(p,E.value||"");break;case 7:E.value==null&&r(p,le.UNEXPECTED_LEXICAL_ANALYSIS,h.lastStartLoc,0,Tt(E)),S.key=d(p,E.value||"");break;default:{r(p,le.UNEXPECTED_EMPTY_LINKED_KEY,h.lastStartLoc,0);const F=p.context(),M=i(7,F.offset,F.startLoc);return M.value="",a(M,F.offset,F.startLoc),S.key=M,a(S,F.offset,F.startLoc),{nextConsumeToken:E,node:S}}}return a(S,p.currentOffset(),p.currentPosition()),{node:S}}function w(p){const h=p.context(),S=h.currentType===1?p.currentOffset():h.offset,E=h.currentType===1?h.endLoc:h.startLoc,F=i(2,S,E);F.items=[];let M=null,N=null;do{const T=M||p.nextToken();switch(M=null,T.type){case 0:T.value==null&&r(p,le.UNEXPECTED_LEXICAL_ANALYSIS,h.lastStartLoc,0,Tt(T)),F.items.push(l(p,T.value||""));break;case 6:T.value==null&&r(p,le.UNEXPECTED_LEXICAL_ANALYSIS,h.lastStartLoc,0,Tt(T)),F.items.push(c(p,T.value||""));break;case 4:N=!0;break;case 5:T.value==null&&r(p,le.UNEXPECTED_LEXICAL_ANALYSIS,h.lastStartLoc,0,Tt(T)),F.items.push(u(p,T.value||"",!!N)),N&&(s(p,qi.USE_MODULO_SYNTAX,h.lastStartLoc,0,Tt(T)),N=null);break;case 7:T.value==null&&r(p,le.UNEXPECTED_LEXICAL_ANALYSIS,h.lastStartLoc,0,Tt(T)),F.items.push(d(p,T.value||""));break;case 8:{const W=C(p);F.items.push(W.node),M=W.nextConsumeToken||null;break}}}while(h.currentType!==14&&h.currentType!==1);const G=h.currentType===1?h.lastOffset:p.currentOffset(),$=h.currentType===1?h.lastEndLoc:p.currentPosition();return a(F,G,$),F}function L(p,h,S,E){const F=p.context();let M=E.items.length===0;const N=i(1,h,S);N.cases=[],N.cases.push(E);do{const G=w(p);M||(M=G.items.length===0),N.cases.push(G)}while(F.currentType!==14);return M&&r(p,le.MUST_HAVE_MESSAGES_IN_PLURAL,S,0),a(N,p.currentOffset(),p.currentPosition()),N}function A(p){const h=p.context(),{offset:S,startLoc:E}=h,F=w(p);return h.currentType===14?F:L(p,S,E,F)}function I(p){const h=s0(p,Sd({},e)),S=h.context(),E=i(0,S.offset,S.startLoc);return t&&E.loc&&(E.loc.source=p),E.body=A(h),e.onCacheKey&&(E.cacheKey=e.onCacheKey(p)),S.currentType!==14&&r(h,le.UNEXPECTED_LEXICAL_ANALYSIS,S.lastStartLoc,0,p[S.offset]||""),a(E,h.currentOffset(),h.currentPosition()),E}return{parse:I}}function Tt(e){if(e.type===14)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function u0(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:s=>(n.helpers.add(s),s)}}function hl(e,t){for(let n=0;n<e.length;n++)Yi(e[n],t)}function Yi(e,t){switch(e.type){case 1:hl(e.cases,t),t.helper("plural");break;case 2:hl(e.items,t);break;case 6:{Yi(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function d0(e,t={}){const n=u0(e);n.helper("normalize"),e.body&&Yi(e.body,n);const o=n.context();e.helpers=Array.from(o.helpers)}function f0(e){const t=e.body;return t.type===2?vl(t):t.cases.forEach(n=>vl(n)),e}function vl(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const o=e.items[n];if(!(o.type===3||o.type===9)||o.value==null)break;t.push(o.value)}if(t.length===e.items.length){e.static=Ed(t);for(let n=0;n<e.items.length;n++){const o=e.items[n];(o.type===3||o.type===9)&&delete o.value}}}}const p0="minifier";function Xn(e){switch(e.t=e.type,e.type){case 0:{const t=e;Xn(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let o=0;o<n.length;o++)Xn(n[o]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let o=0;o<n.length;o++)Xn(n[o]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;Xn(t.key),t.k=t.key,delete t.key,t.modifier&&(Xn(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}default:throw Eo(le.UNHANDLED_MINIFIER_NODE_TYPE,null,{domain:p0,args:[e.type]})}delete e.type}const m0="parser";function g0(e,t){const{filename:n,breakLineCode:o,needIndent:r}=t,s=t.location!==!1,i={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:o,needIndent:r,indentLevel:0};s&&e.loc&&(i.source=e.loc.source);const a=()=>i;function l(w,L){i.code+=w}function c(w,L=!0){const A=L?o:"";l(r?A+"  ".repeat(w):A)}function u(w=!0){const L=++i.indentLevel;w&&c(L)}function d(w=!0){const L=--i.indentLevel;w&&c(L)}function f(){c(i.indentLevel)}return{context:a,push:l,indent:u,deindent:d,newline:f,helper:w=>`_${w}`,needIndent:()=>i.needIndent}}function h0(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),go(e,t.key),t.modifier?(e.push(", "),go(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function v0(e,t){const{helper:n,needIndent:o}=e;e.push(`${n("normalize")}([`),e.indent(o());const r=t.items.length;for(let s=0;s<r&&(go(e,t.items[s]),s!==r-1);s++)e.push(", ");e.deindent(o()),e.push("])")}function y0(e,t){const{helper:n,needIndent:o}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(o());const r=t.cases.length;for(let s=0;s<r&&(go(e,t.cases[s]),s!==r-1);s++)e.push(", ");e.deindent(o()),e.push("])")}}function _0(e,t){t.body?go(e,t.body):e.push("null")}function go(e,t){const{helper:n}=e;switch(t.type){case 0:_0(e,t);break;case 1:y0(e,t);break;case 2:v0(e,t);break;case 6:h0(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break;default:throw Eo(le.UNHANDLED_CODEGEN_NODE_TYPE,null,{domain:m0,args:[t.type]})}}const b0=(e,t={})=>{const n=ml(t.mode)?t.mode:"normal",o=ml(t.filename)?t.filename:"message.intl";t.sourceMap;const r=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":`
`,s=t.needIndent?t.needIndent:n!=="arrow",i=e.helpers||[],a=g0(e,{filename:o,breakLineCode:r,needIndent:s});a.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),a.indent(s),i.length>0&&(a.push(`const { ${Ed(i.map(u=>`${u}: _${u}`),", ")} } = ctx`),a.newline()),a.push("return "),go(a,e),a.deindent(s),a.push("}"),delete e.helpers;const{code:l,map:c}=a.context();return{ast:e,code:l,map:c?c.toJSON():void 0}};function S0(e,t={}){const n=Sd({},t),o=!!n.jit,r=!!n.minify,s=n.optimize==null?!0:n.optimize,a=c0(n).parse(e);return o?(s&&f0(a),r&&Xn(a),{ast:a,code:""}):(d0(a,n),b0(a,n))}/*!
  * core-base v9.14.5
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */function E0(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(Zt().__INTLIFY_PROD_DEVTOOLS__=!1),typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(Zt().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Zt().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}function Vt(e){return ye(e)&&Qi(e)===0&&(Pt(e,"b")||Pt(e,"body"))}const xd=["b","body"];function x0(e){return Cn(e,xd)}const wd=["c","cases"];function w0(e){return Cn(e,wd,[])}const Cd=["s","static"];function C0(e){return Cn(e,Cd)}const Td=["i","items"];function T0(e){return Cn(e,Td,[])}const Od=["t","type"];function Qi(e){return Cn(e,Od)}const Nd=["v","value"];function wr(e,t){const n=Cn(e,Nd);if(n!=null)return n;throw tr(t)}const Pd=["m","modifier"];function O0(e){return Cn(e,Pd)}const Id=["k","key"];function N0(e){const t=Cn(e,Id);if(t)return t;throw tr(6)}function Cn(e,t,n){for(let o=0;o<t.length;o++){const r=t[o];if(Pt(e,r)&&e[r]!=null)return e[r]}return n}const Fd=[...xd,...wd,...Cd,...Td,...Id,...Pd,...Nd,...Od];function tr(e){return new Error(`unhandled node type: ${e}`)}const Tn=[];Tn[0]={w:[0],i:[3,0],"[":[4],o:[7]};Tn[1]={w:[1],".":[2],"[":[4],o:[7]};Tn[2]={w:[2],i:[3,0],0:[3,0]};Tn[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};Tn[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};Tn[5]={"'":[4,0],o:8,l:[5,0]};Tn[6]={'"':[4,0],o:8,l:[6,0]};const P0=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function I0(e){return P0.test(e)}function F0(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function R0(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function k0(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:I0(t)?F0(t):"*"+t}function A0(e){const t=[];let n=-1,o=0,r=0,s,i,a,l,c,u,d;const f=[];f[0]=()=>{i===void 0?i=a:i+=a},f[1]=()=>{i!==void 0&&(t.push(i),i=void 0)},f[2]=()=>{f[0](),r++},f[3]=()=>{if(r>0)r--,o=4,f[0]();else{if(r=0,i===void 0||(i=k0(i),i===!1))return!1;f[1]()}};function y(){const C=e[n+1];if(o===5&&C==="'"||o===6&&C==='"')return n++,a="\\"+C,f[0](),!0}for(;o!==null;)if(n++,s=e[n],!(s==="\\"&&y())){if(l=R0(s),d=Tn[o],c=d[l]||d.l||8,c===8||(o=c[0],c[1]!==void 0&&(u=f[c[1]],u&&(a=s,u()===!1))))return;if(o===7)return t}}const yl=new Map;function L0(e,t){return ye(e)?e[t]:null}function D0(e,t){if(!ye(e))return null;let n=yl.get(t);if(n||(n=A0(t),n&&yl.set(t,n)),!n)return null;const o=n.length;let r=e,s=0;for(;s<o;){const i=n[s];if(Fd.includes(i)&&Vt(r))return null;const a=r[i];if(a===void 0||Pe(r))return null;r=a,s++}return r}const M0=e=>e,G0=e=>"",J0="text",U0=e=>e.length===0?"":Bv(e),V0=jv;function _l(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function $0(e){const t=Me(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(Me(e.named.count)||Me(e.named.n))?Me(e.named.count)?e.named.count:Me(e.named.n)?e.named.n:t:t}function H0(e,t){t.count||(t.count=e),t.n||(t.n=e)}function j0(e={}){const t=e.locale,n=$0(e),o=ye(e.pluralRules)&&Q(t)&&Pe(e.pluralRules[t])?e.pluralRules[t]:_l,r=ye(e.pluralRules)&&Q(t)&&Pe(e.pluralRules[t])?_l:void 0,s=A=>A[o(n,A.length,r)],i=e.list||[],a=A=>i[A],l=e.named||Te();Me(e.pluralIndex)&&H0(n,l);const c=A=>l[A];function u(A){const I=Pe(e.messages)?e.messages(A):ye(e.messages)?e.messages[A]:!1;return I||(e.parent?e.parent.message(A):G0)}const d=A=>e.modifiers?e.modifiers[A]:M0,f=ce(e.processor)&&Pe(e.processor.normalize)?e.processor.normalize:U0,y=ce(e.processor)&&Pe(e.processor.interpolate)?e.processor.interpolate:V0,C=ce(e.processor)&&Q(e.processor.type)?e.processor.type:J0,L={list:a,named:c,plural:s,linked:(A,...I)=>{const[p,h]=I;let S="text",E="";I.length===1?ye(p)?(E=p.modifier||E,S=p.type||S):Q(p)&&(E=p||E):I.length===2&&(Q(p)&&(E=p||E),Q(h)&&(S=h||S));const F=u(A)(L),M=S==="vnode"&&Re(F)&&E?F[0]:F;return E?d(E)(M,S):M},message:u,type:C,interpolate:y,normalize:f,values:qe(Te(),i,l)};return L}let nr=null;function B0(e){nr=e}function W0(e,t,n){nr&&nr.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:n})}const z0=K0("function:translate");function K0(e){return t=>nr&&nr.emit(e,t)}const q0=qi.__EXTEND_POINT__,Pn=fs(q0),Y0={FALLBACK_TO_TRANSLATE:Pn(),CANNOT_FORMAT_NUMBER:Pn(),FALLBACK_TO_NUMBER_FORMAT:Pn(),CANNOT_FORMAT_DATE:Pn(),FALLBACK_TO_DATE_FORMAT:Pn(),EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER:Pn(),__EXTEND_POINT__:Pn()},Rd=le.__EXTEND_POINT__,In=fs(Rd),It={INVALID_ARGUMENT:Rd,INVALID_DATE_ARGUMENT:In(),INVALID_ISO_DATE_ARGUMENT:In(),NOT_SUPPORT_NON_STRING_MESSAGE:In(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:In(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:In(),NOT_SUPPORT_LOCALE_TYPE:In(),__EXTEND_POINT__:In()};function Jt(e){return Eo(e,null,void 0)}function Xi(e,t){return t.locale!=null?bl(t.locale):bl(e.locale)}let ks;function bl(e){if(Q(e))return e;if(Pe(e)){if(e.resolvedOnce&&ks!=null)return ks;if(e.constructor.name==="Function"){const t=e();if(Hv(t))throw Jt(It.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return ks=t}else throw Jt(It.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw Jt(It.NOT_SUPPORT_LOCALE_TYPE)}function Q0(e,t,n){return[...new Set([n,...Re(t)?t:ye(t)?Object.keys(t):Q(t)?[t]:[n]])]}function kd(e,t,n){const o=Q(n)?n:ho,r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let s=r.__localeChainCache.get(o);if(!s){s=[];let i=[n];for(;Re(i);)i=Sl(s,i,t);const a=Re(t)||!ce(t)?t:t.default?t.default:null;i=Q(a)?[a]:a,Re(i)&&Sl(s,i,!1),r.__localeChainCache.set(o,s)}return s}function Sl(e,t,n){let o=!0;for(let r=0;r<t.length&&de(o);r++){const s=t[r];Q(s)&&(o=X0(e,t[r],n))}return o}function X0(e,t,n){let o;const r=t.split("-");do{const s=r.join("-");o=Z0(e,s,n),r.splice(-1,1)}while(r.length&&o===!0);return o}function Z0(e,t,n){let o=!1;if(!e.includes(t)&&(o=!0,t)){o=t[t.length-1]!=="!";const r=t.replace(/!/g,"");e.push(r),(Re(n)||ce(n))&&n[r]&&(o=n[r])}return o}const ey="9.14.5",ps=-1,ho="en-US",El="",xl=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function ty(){return{upper:(e,t)=>t==="text"&&Q(e)?e.toUpperCase():t==="vnode"&&ye(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&Q(e)?e.toLowerCase():t==="vnode"&&ye(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&Q(e)?xl(e):t==="vnode"&&ye(e)&&"__v_isVNode"in e?xl(e.children):e}}let Ad;function wl(e){Ad=e}let Ld;function ny(e){Ld=e}let Dd;function oy(e){Dd=e}let Md=null;const ry=e=>{Md=e},sy=()=>Md;let Gd=null;const Cl=e=>{Gd=e},iy=()=>Gd;let Tl=0;function ay(e={}){const t=Pe(e.onWarn)?e.onWarn:Dv,n=Q(e.version)?e.version:ey,o=Q(e.locale)||Pe(e.locale)?e.locale:ho,r=Pe(o)?ho:o,s=Re(e.fallbackLocale)||ce(e.fallbackLocale)||Q(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:r,i=ce(e.messages)?e.messages:As(r),a=ce(e.datetimeFormats)?e.datetimeFormats:As(r),l=ce(e.numberFormats)?e.numberFormats:As(r),c=qe(Te(),e.modifiers,ty()),u=e.pluralRules||Te(),d=Pe(e.missing)?e.missing:null,f=de(e.missingWarn)||En(e.missingWarn)?e.missingWarn:!0,y=de(e.fallbackWarn)||En(e.fallbackWarn)?e.fallbackWarn:!0,C=!!e.fallbackFormat,w=!!e.unresolving,L=Pe(e.postTranslation)?e.postTranslation:null,A=ce(e.processor)?e.processor:null,I=de(e.warnHtmlMessage)?e.warnHtmlMessage:!0,p=!!e.escapeParameter,h=Pe(e.messageCompiler)?e.messageCompiler:Ad,S=Pe(e.messageResolver)?e.messageResolver:Ld||L0,E=Pe(e.localeFallbacker)?e.localeFallbacker:Dd||Q0,F=ye(e.fallbackContext)?e.fallbackContext:void 0,M=e,N=ye(M.__datetimeFormatters)?M.__datetimeFormatters:new Map,G=ye(M.__numberFormatters)?M.__numberFormatters:new Map,$=ye(M.__meta)?M.__meta:{};Tl++;const T={version:n,cid:Tl,locale:o,fallbackLocale:s,messages:i,modifiers:c,pluralRules:u,missing:d,missingWarn:f,fallbackWarn:y,fallbackFormat:C,unresolving:w,postTranslation:L,processor:A,warnHtmlMessage:I,escapeParameter:p,messageCompiler:h,messageResolver:S,localeFallbacker:E,fallbackContext:F,onWarn:t,__meta:$};return T.datetimeFormats=a,T.numberFormats=l,T.__datetimeFormatters=N,T.__numberFormatters=G,__INTLIFY_PROD_DEVTOOLS__&&W0(T,n,$),T}const As=e=>({[e]:Te()});function Zi(e,t,n,o,r){const{missing:s,onWarn:i}=e;if(s!==null){const a=s(e,n,t,r);return Q(a)?a:t}else return t}function No(e,t,n){const o=e;o.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function ly(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function cy(e,t){const n=t.indexOf(e);if(n===-1)return!1;for(let o=n+1;o<t.length;o++)if(ly(e,t[o]))return!0;return!1}function Ls(e){return n=>uy(n,e)}function uy(e,t){const n=x0(t);if(n==null)throw tr(0);if(Qi(n)===1){const s=w0(n);return e.plural(s.reduce((i,a)=>[...i,Ol(e,a)],[]))}else return Ol(e,n)}function Ol(e,t){const n=C0(t);if(n!=null)return e.type==="text"?n:e.normalize([n]);{const o=T0(t).reduce((r,s)=>[...r,li(e,s)],[]);return e.normalize(o)}}function li(e,t){const n=Qi(t);switch(n){case 3:return wr(t,n);case 9:return wr(t,n);case 4:{const o=t;if(Pt(o,"k")&&o.k)return e.interpolate(e.named(o.k));if(Pt(o,"key")&&o.key)return e.interpolate(e.named(o.key));throw tr(n)}case 5:{const o=t;if(Pt(o,"i")&&Me(o.i))return e.interpolate(e.list(o.i));if(Pt(o,"index")&&Me(o.index))return e.interpolate(e.list(o.index));throw tr(n)}case 6:{const o=t,r=O0(o),s=N0(o);return e.linked(li(e,s),r?li(e,r):void 0,e.type)}case 7:return wr(t,n);case 8:return wr(t,n);default:throw new Error(`unhandled node on format message part: ${n}`)}}const Jd=e=>e;let to=Te();function Ud(e,t={}){let n=!1;const o=t.onError||Xv;return t.onError=r=>{n=!0,o(r)},{...S0(e,t),detectError:n}}const dy=(e,t)=>{if(!Q(e))throw Jt(It.NOT_SUPPORT_NON_STRING_MESSAGE);{de(t.warnHtmlMessage)&&t.warnHtmlMessage;const o=(t.onCacheKey||Jd)(e),r=to[o];if(r)return r;const{code:s,detectError:i}=Ud(e,t),a=new Function(`return ${s}`)();return i?a:to[o]=a}};function fy(e,t){if(__INTLIFY_JIT_COMPILATION__&&!__INTLIFY_DROP_MESSAGE_COMPILER__&&Q(e)){de(t.warnHtmlMessage)&&t.warnHtmlMessage;const o=(t.onCacheKey||Jd)(e),r=to[o];if(r)return r;const{ast:s,detectError:i}=Ud(e,{...t,location:!1,jit:!0}),a=Ls(s);return i?a:to[o]=a}else{const n=e.cacheKey;if(n){const o=to[n];return o||(to[n]=Ls(e))}else return Ls(e)}}const Nl=()=>"",Et=e=>Pe(e);function Pl(e,...t){const{fallbackFormat:n,postTranslation:o,unresolving:r,messageCompiler:s,fallbackLocale:i,messages:a}=e,[l,c]=ci(...t),u=de(c.missingWarn)?c.missingWarn:e.missingWarn,d=de(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,f=de(c.escapeParameter)?c.escapeParameter:e.escapeParameter,y=!!c.resolvedMessage,C=Q(c.default)||de(c.default)?de(c.default)?s?l:()=>l:c.default:n?s?l:()=>l:"",w=n||C!=="",L=Xi(e,c);f&&py(c);let[A,I,p]=y?[l,L,a[L]||Te()]:Vd(e,l,L,i,d,u),h=A,S=l;if(!y&&!(Q(h)||Vt(h)||Et(h))&&w&&(h=C,S=h),!y&&(!(Q(h)||Vt(h)||Et(h))||!Q(I)))return r?ps:l;let E=!1;const F=()=>{E=!0},M=Et(h)?h:$d(e,l,I,h,S,F);if(E)return h;const N=hy(e,I,p,c),G=j0(N),$=my(e,M,G);let T=o?o($,l):$;if(f&&Q(T)&&(T=Vv(T)),__INTLIFY_PROD_DEVTOOLS__){const W={timestamp:Date.now(),key:Q(l)?l:Et(h)?h.key:"",locale:I||(Et(h)?h.locale:""),format:Q(h)?h:Et(h)?h.source:"",message:T};W.meta=qe({},e.__meta,sy()||{}),z0(W)}return T}function py(e){Re(e.list)?e.list=e.list.map(t=>Q(t)?fl(t):t):ye(e.named)&&Object.keys(e.named).forEach(t=>{Q(e.named[t])&&(e.named[t]=fl(e.named[t]))})}function Vd(e,t,n,o,r,s){const{messages:i,onWarn:a,messageResolver:l,localeFallbacker:c}=e,u=c(e,o,n);let d=Te(),f,y=null;const C="translate";for(let w=0;w<u.length&&(f=u[w],d=i[f]||Te(),(y=l(d,t))===null&&(y=d[t]),!(Q(y)||Vt(y)||Et(y)));w++)if(!cy(f,u)){const L=Zi(e,t,f,s,C);L!==t&&(y=L)}return[y,f,d]}function $d(e,t,n,o,r,s){const{messageCompiler:i,warnHtmlMessage:a}=e;if(Et(o)){const c=o;return c.locale=c.locale||n,c.key=c.key||t,c}if(i==null){const c=(()=>o);return c.locale=n,c.key=t,c}const l=i(o,gy(e,n,r,o,a,s));return l.locale=n,l.key=t,l.source=o,l}function my(e,t,n){return t(n)}function ci(...e){const[t,n,o]=e,r=Te();if(!Q(t)&&!Me(t)&&!Et(t)&&!Vt(t))throw Jt(It.INVALID_ARGUMENT);const s=Me(t)?String(t):(Et(t),t);return Me(n)?r.plural=n:Q(n)?r.default=n:ce(n)&&!ds(n)?r.named=n:Re(n)&&(r.list=n),Me(o)?r.plural=o:Q(o)?r.default=o:ce(o)&&qe(r,o),[s,r]}function gy(e,t,n,o,r,s){return{locale:t,key:n,warnHtmlMessage:r,onError:i=>{throw s&&s(i),i},onCacheKey:i=>Mv(t,n,i)}}function hy(e,t,n,o){const{modifiers:r,pluralRules:s,messageResolver:i,fallbackLocale:a,fallbackWarn:l,missingWarn:c,fallbackContext:u}=e,f={locale:t,modifiers:r,pluralRules:s,messages:y=>{let C=i(n,y);if(C==null&&u){const[,,w]=Vd(u,y,t,a,l,c);C=i(w,y)}if(Q(C)||Vt(C)){let w=!1;const A=$d(e,y,t,C,y,()=>{w=!0});return w?Nl:A}else return Et(C)?C:Nl}};return e.processor&&(f.processor=e.processor),o.list&&(f.list=o.list),o.named&&(f.named=o.named),Me(o.plural)&&(f.pluralIndex=o.plural),f}function Il(e,...t){const{datetimeFormats:n,unresolving:o,fallbackLocale:r,onWarn:s,localeFallbacker:i}=e,{__datetimeFormatters:a}=e,[l,c,u,d]=ui(...t),f=de(u.missingWarn)?u.missingWarn:e.missingWarn;de(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const y=!!u.part,C=Xi(e,u),w=i(e,r,C);if(!Q(l)||l==="")return new Intl.DateTimeFormat(C,d).format(c);let L={},A,I=null;const p="datetime format";for(let E=0;E<w.length&&(A=w[E],L=n[A]||{},I=L[l],!ce(I));E++)Zi(e,l,A,f,p);if(!ce(I)||!Q(A))return o?ps:l;let h=`${A}__${l}`;ds(d)||(h=`${h}__${JSON.stringify(d)}`);let S=a.get(h);return S||(S=new Intl.DateTimeFormat(A,qe({},I,d)),a.set(h,S)),y?S.formatToParts(c):S.format(c)}const Hd=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function ui(...e){const[t,n,o,r]=e,s=Te();let i=Te(),a;if(Q(t)){const l=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!l)throw Jt(It.INVALID_ISO_DATE_ARGUMENT);const c=l[3]?l[3].trim().startsWith("T")?`${l[1].trim()}${l[3].trim()}`:`${l[1].trim()}T${l[3].trim()}`:l[1].trim();a=new Date(c);try{a.toISOString()}catch{throw Jt(It.INVALID_ISO_DATE_ARGUMENT)}}else if(Jv(t)){if(isNaN(t.getTime()))throw Jt(It.INVALID_DATE_ARGUMENT);a=t}else if(Me(t))a=t;else throw Jt(It.INVALID_ARGUMENT);return Q(n)?s.key=n:ce(n)&&Object.keys(n).forEach(l=>{Hd.includes(l)?i[l]=n[l]:s[l]=n[l]}),Q(o)?s.locale=o:ce(o)&&(i=o),ce(r)&&(i=r),[s.key||"",a,s,i]}function Fl(e,t,n){const o=e;for(const r in n){const s=`${t}__${r}`;o.__datetimeFormatters.has(s)&&o.__datetimeFormatters.delete(s)}}function Rl(e,...t){const{numberFormats:n,unresolving:o,fallbackLocale:r,onWarn:s,localeFallbacker:i}=e,{__numberFormatters:a}=e,[l,c,u,d]=di(...t),f=de(u.missingWarn)?u.missingWarn:e.missingWarn;de(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const y=!!u.part,C=Xi(e,u),w=i(e,r,C);if(!Q(l)||l==="")return new Intl.NumberFormat(C,d).format(c);let L={},A,I=null;const p="number format";for(let E=0;E<w.length&&(A=w[E],L=n[A]||{},I=L[l],!ce(I));E++)Zi(e,l,A,f,p);if(!ce(I)||!Q(A))return o?ps:l;let h=`${A}__${l}`;ds(d)||(h=`${h}__${JSON.stringify(d)}`);let S=a.get(h);return S||(S=new Intl.NumberFormat(A,qe({},I,d)),a.set(h,S)),y?S.formatToParts(c):S.format(c)}const jd=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function di(...e){const[t,n,o,r]=e,s=Te();let i=Te();if(!Me(t))throw Jt(It.INVALID_ARGUMENT);const a=t;return Q(n)?s.key=n:ce(n)&&Object.keys(n).forEach(l=>{jd.includes(l)?i[l]=n[l]:s[l]=n[l]}),Q(o)?s.locale=o:ce(o)&&(i=o),ce(r)&&(i=r),[s.key||"",a,s,i]}function kl(e,t,n){const o=e;for(const r in n){const s=`${t}__${r}`;o.__numberFormatters.has(s)&&o.__numberFormatters.delete(s)}}E0();/*!
  * vue-i18n v9.14.5
  * (c) 2025 kazuya kawaguchi
  * Released under the MIT License.
  */const vy="9.14.5";function yy(){typeof __VUE_I18N_FULL_INSTALL__!="boolean"&&(Zt().__VUE_I18N_FULL_INSTALL__=!0),typeof __VUE_I18N_LEGACY_API__!="boolean"&&(Zt().__VUE_I18N_LEGACY_API__=!0),typeof __INTLIFY_JIT_COMPILATION__!="boolean"&&(Zt().__INTLIFY_JIT_COMPILATION__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(Zt().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(Zt().__INTLIFY_PROD_DEVTOOLS__=!1)}const _y=Y0.__EXTEND_POINT__,zt=fs(_y);zt(),zt(),zt(),zt(),zt(),zt(),zt(),zt(),zt();const Bd=It.__EXTEND_POINT__,ct=fs(Bd),Ve={UNEXPECTED_RETURN_TYPE:Bd,INVALID_ARGUMENT:ct(),MUST_BE_CALL_SETUP_TOP:ct(),NOT_INSTALLED:ct(),NOT_AVAILABLE_IN_LEGACY_MODE:ct(),REQUIRED_VALUE:ct(),INVALID_VALUE:ct(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:ct(),NOT_INSTALLED_WITH_PROVIDE:ct(),UNEXPECTED_ERROR:ct(),NOT_COMPATIBLE_LEGACY_VUE_I18N:ct(),BRIDGE_SUPPORT_VUE_2_ONLY:ct(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:ct(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:ct(),__EXTEND_POINT__:ct()};function Be(e,...t){return Eo(e,null,void 0)}const fi=wn("__translateVNode"),pi=wn("__datetimeParts"),mi=wn("__numberParts"),Wd=wn("__setPluralRules"),zd=wn("__injectWithOption"),gi=wn("__dispose");function or(e){if(!ye(e)||Vt(e))return e;for(const t in e)if(Pt(e,t))if(!t.includes("."))ye(e[t])&&or(e[t]);else{const n=t.split("."),o=n.length-1;let r=e,s=!1;for(let i=0;i<o;i++){if(n[i]==="__proto__")throw new Error(`unsafe key: ${n[i]}`);if(n[i]in r||(r[n[i]]=Te()),!ye(r[n[i]])){s=!0;break}r=r[n[i]]}if(s||(Vt(r)?Fd.includes(n[o])||delete e[t]:(r[n[o]]=e[t],delete e[t])),!Vt(r)){const i=r[n[o]];ye(i)&&or(i)}}return e}function ms(e,t){const{messages:n,__i18n:o,messageResolver:r,flatJson:s}=t,i=ce(n)?n:Re(o)?Te():{[e]:Te()};if(Re(o)&&o.forEach(a=>{if("locale"in a&&"resource"in a){const{locale:l,resource:c}=a;l?(i[l]=i[l]||Te(),Pr(c,i[l])):Pr(c,i)}else Q(a)&&Pr(JSON.parse(a),i)}),r==null&&s)for(const a in i)Pt(i,a)&&or(i[a]);return i}function Kd(e){return e.type}function qd(e,t,n){let o=ye(t.messages)?t.messages:Te();"__i18nGlobal"in n&&(o=ms(e.locale.value,{messages:o,__i18n:n.__i18nGlobal}));const r=Object.keys(o);r.length&&r.forEach(s=>{e.mergeLocaleMessage(s,o[s])});{if(ye(t.datetimeFormats)){const s=Object.keys(t.datetimeFormats);s.length&&s.forEach(i=>{e.mergeDateTimeFormat(i,t.datetimeFormats[i])})}if(ye(t.numberFormats)){const s=Object.keys(t.numberFormats);s.length&&s.forEach(i=>{e.mergeNumberFormat(i,t.numberFormats[i])})}}}function Al(e){return B(en,null,e,0)}const Ll="__INTLIFY_META__",Dl=()=>[],by=()=>!1;let Ml=0;function Gl(e){return((t,n,o,r)=>e(n,o,$e()||void 0,r))}const Sy=()=>{const e=$e();let t=null;return e&&(t=Kd(e)[Ll])?{[Ll]:t}:null};function ea(e={},t){const{__root:n,__injectWithOption:o}=e,r=n===void 0,s=e.flatJson,i=jr?Fe:ar,a=!!e.translateExistCompatible;let l=de(e.inheritLocale)?e.inheritLocale:!0;const c=i(n&&l?n.locale.value:Q(e.locale)?e.locale:ho),u=i(n&&l?n.fallbackLocale.value:Q(e.fallbackLocale)||Re(e.fallbackLocale)||ce(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:c.value),d=i(ms(c.value,e)),f=i(ce(e.datetimeFormats)?e.datetimeFormats:{[c.value]:{}}),y=i(ce(e.numberFormats)?e.numberFormats:{[c.value]:{}});let C=n?n.missingWarn:de(e.missingWarn)||En(e.missingWarn)?e.missingWarn:!0,w=n?n.fallbackWarn:de(e.fallbackWarn)||En(e.fallbackWarn)?e.fallbackWarn:!0,L=n?n.fallbackRoot:de(e.fallbackRoot)?e.fallbackRoot:!0,A=!!e.fallbackFormat,I=Pe(e.missing)?e.missing:null,p=Pe(e.missing)?Gl(e.missing):null,h=Pe(e.postTranslation)?e.postTranslation:null,S=n?n.warnHtmlMessage:de(e.warnHtmlMessage)?e.warnHtmlMessage:!0,E=!!e.escapeParameter;const F=n?n.modifiers:ce(e.modifiers)?e.modifiers:{};let M=e.pluralRules||n&&n.pluralRules,N;N=(()=>{r&&Cl(null);const O={version:vy,locale:c.value,fallbackLocale:u.value,messages:d.value,modifiers:F,pluralRules:M,missing:p===null?void 0:p,missingWarn:C,fallbackWarn:w,fallbackFormat:A,unresolving:!0,postTranslation:h===null?void 0:h,warnHtmlMessage:S,escapeParameter:E,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};O.datetimeFormats=f.value,O.numberFormats=y.value,O.__datetimeFormatters=ce(N)?N.__datetimeFormatters:void 0,O.__numberFormatters=ce(N)?N.__numberFormatters:void 0;const D=ay(O);return r&&Cl(D),D})(),No(N,c.value,u.value);function $(){return[c.value,u.value,d.value,f.value,y.value]}const T=pe({get:()=>c.value,set:O=>{c.value=O,N.locale=c.value}}),W=pe({get:()=>u.value,set:O=>{u.value=O,N.fallbackLocale=u.value,No(N,c.value,O)}}),se=pe(()=>d.value),ee=pe(()=>f.value),q=pe(()=>y.value);function ie(){return Pe(h)?h:null}function X(O){h=O,N.postTranslation=O}function be(){return I}function Ye(O){O!==null&&(p=Gl(O)),I=O,N.missing=p}const Ae=(O,D,oe,ne,_e,Le)=>{$();let He;try{__INTLIFY_PROD_DEVTOOLS__,r||(N.fallbackContext=n?iy():void 0),He=O(N)}finally{__INTLIFY_PROD_DEVTOOLS__,r||(N.fallbackContext=void 0)}if(oe!=="translate exists"&&Me(He)&&He===ps||oe==="translate exists"&&!He){const[rt,Wn]=D();return n&&L?ne(n):_e(rt)}else{if(Le(He))return He;throw Be(Ve.UNEXPECTED_RETURN_TYPE)}};function Ie(...O){return Ae(D=>Reflect.apply(Pl,null,[D,...O]),()=>ci(...O),"translate",D=>Reflect.apply(D.t,D,[...O]),D=>D,D=>Q(D))}function lt(...O){const[D,oe,ne]=O;if(ne&&!ye(ne))throw Be(Ve.INVALID_ARGUMENT);return Ie(D,oe,qe({resolvedMessage:!0},ne||{}))}function ot(...O){return Ae(D=>Reflect.apply(Il,null,[D,...O]),()=>ui(...O),"datetime format",D=>Reflect.apply(D.d,D,[...O]),()=>El,D=>Q(D))}function yt(...O){return Ae(D=>Reflect.apply(Rl,null,[D,...O]),()=>di(...O),"number format",D=>Reflect.apply(D.n,D,[...O]),()=>El,D=>Q(D))}function Ge(O){return O.map(D=>Q(D)||Me(D)||de(D)?Al(String(D)):D)}const Y={normalize:Ge,interpolate:O=>O,type:"vnode"};function K(...O){return Ae(D=>{let oe;const ne=D;try{ne.processor=Y,oe=Reflect.apply(Pl,null,[ne,...O])}finally{ne.processor=null}return oe},()=>ci(...O),"translate",D=>D[fi](...O),D=>[Al(D)],D=>Re(D))}function Z(...O){return Ae(D=>Reflect.apply(Rl,null,[D,...O]),()=>di(...O),"number format",D=>D[mi](...O),Dl,D=>Q(D)||Re(D))}function ue(...O){return Ae(D=>Reflect.apply(Il,null,[D,...O]),()=>ui(...O),"datetime format",D=>D[pi](...O),Dl,D=>Q(D)||Re(D))}function he(O){M=O,N.pluralRules=M}function _(O,D){return Ae(()=>{if(!O)return!1;const oe=Q(D)?D:c.value,ne=R(oe),_e=N.messageResolver(ne,O);return a?_e!=null:Vt(_e)||Et(_e)||Q(_e)},()=>[O],"translate exists",oe=>Reflect.apply(oe.te,oe,[O,D]),by,oe=>de(oe))}function g(O){let D=null;const oe=kd(N,u.value,c.value);for(let ne=0;ne<oe.length;ne++){const _e=d.value[oe[ne]]||{},Le=N.messageResolver(_e,O);if(Le!=null){D=Le;break}}return D}function b(O){const D=g(O);return D??(n?n.tm(O)||{}:{})}function R(O){return d.value[O]||{}}function k(O,D){if(s){const oe={[O]:D};for(const ne in oe)Pt(oe,ne)&&or(oe[ne]);D=oe[O]}d.value[O]=D,N.messages=d.value}function J(O,D){d.value[O]=d.value[O]||{};const oe={[O]:D};if(s)for(const ne in oe)Pt(oe,ne)&&or(oe[ne]);D=oe[O],Pr(D,d.value[O]),N.messages=d.value}function z(O){return f.value[O]||{}}function m(O,D){f.value[O]=D,N.datetimeFormats=f.value,Fl(N,O,D)}function v(O,D){f.value[O]=qe(f.value[O]||{},D),N.datetimeFormats=f.value,Fl(N,O,D)}function x(O){return y.value[O]||{}}function U(O,D){y.value[O]=D,N.numberFormats=y.value,kl(N,O,D)}function j(O,D){y.value[O]=qe(y.value[O]||{},D),N.numberFormats=y.value,kl(N,O,D)}Ml++,n&&jr&&(ft(n.locale,O=>{l&&(c.value=O,N.locale=O,No(N,c.value,u.value))}),ft(n.fallbackLocale,O=>{l&&(u.value=O,N.fallbackLocale=O,No(N,c.value,u.value))}));const H={id:Ml,locale:T,fallbackLocale:W,get inheritLocale(){return l},set inheritLocale(O){l=O,O&&n&&(c.value=n.locale.value,u.value=n.fallbackLocale.value,No(N,c.value,u.value))},get availableLocales(){return Object.keys(d.value).sort()},messages:se,get modifiers(){return F},get pluralRules(){return M||{}},get isGlobal(){return r},get missingWarn(){return C},set missingWarn(O){C=O,N.missingWarn=C},get fallbackWarn(){return w},set fallbackWarn(O){w=O,N.fallbackWarn=w},get fallbackRoot(){return L},set fallbackRoot(O){L=O},get fallbackFormat(){return A},set fallbackFormat(O){A=O,N.fallbackFormat=A},get warnHtmlMessage(){return S},set warnHtmlMessage(O){S=O,N.warnHtmlMessage=O},get escapeParameter(){return E},set escapeParameter(O){E=O,N.escapeParameter=O},t:Ie,getLocaleMessage:R,setLocaleMessage:k,mergeLocaleMessage:J,getPostTranslationHandler:ie,setPostTranslationHandler:X,getMissingHandler:be,setMissingHandler:Ye,[Wd]:he};return H.datetimeFormats=ee,H.numberFormats=q,H.rt=lt,H.te=_,H.tm=b,H.d=ot,H.n=yt,H.getDateTimeFormat=z,H.setDateTimeFormat=m,H.mergeDateTimeFormat=v,H.getNumberFormat=x,H.setNumberFormat=U,H.mergeNumberFormat=j,H[zd]=o,H[fi]=K,H[pi]=ue,H[mi]=Z,H}function Ey(e){const t=Q(e.locale)?e.locale:ho,n=Q(e.fallbackLocale)||Re(e.fallbackLocale)||ce(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,o=Pe(e.missing)?e.missing:void 0,r=de(e.silentTranslationWarn)||En(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,s=de(e.silentFallbackWarn)||En(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,i=de(e.fallbackRoot)?e.fallbackRoot:!0,a=!!e.formatFallbackMessages,l=ce(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=Pe(e.postTranslation)?e.postTranslation:void 0,d=Q(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,f=!!e.escapeParameterHtml,y=de(e.sync)?e.sync:!0;let C=e.messages;if(ce(e.sharedMessages)){const E=e.sharedMessages;C=Object.keys(E).reduce((M,N)=>{const G=M[N]||(M[N]={});return qe(G,E[N]),M},C||{})}const{__i18n:w,__root:L,__injectWithOption:A}=e,I=e.datetimeFormats,p=e.numberFormats,h=e.flatJson,S=e.translateExistCompatible;return{locale:t,fallbackLocale:n,messages:C,flatJson:h,datetimeFormats:I,numberFormats:p,missing:o,missingWarn:r,fallbackWarn:s,fallbackRoot:i,fallbackFormat:a,modifiers:l,pluralRules:c,postTranslation:u,warnHtmlMessage:d,escapeParameter:f,messageResolver:e.messageResolver,inheritLocale:y,translateExistCompatible:S,__i18n:w,__root:L,__injectWithOption:A}}function hi(e={},t){{const n=ea(Ey(e)),{__extender:o}=e,r={id:n.id,get locale(){return n.locale.value},set locale(s){n.locale.value=s},get fallbackLocale(){return n.fallbackLocale.value},set fallbackLocale(s){n.fallbackLocale.value=s},get messages(){return n.messages.value},get datetimeFormats(){return n.datetimeFormats.value},get numberFormats(){return n.numberFormats.value},get availableLocales(){return n.availableLocales},get formatter(){return{interpolate(){return[]}}},set formatter(s){},get missing(){return n.getMissingHandler()},set missing(s){n.setMissingHandler(s)},get silentTranslationWarn(){return de(n.missingWarn)?!n.missingWarn:n.missingWarn},set silentTranslationWarn(s){n.missingWarn=de(s)?!s:s},get silentFallbackWarn(){return de(n.fallbackWarn)?!n.fallbackWarn:n.fallbackWarn},set silentFallbackWarn(s){n.fallbackWarn=de(s)?!s:s},get modifiers(){return n.modifiers},get formatFallbackMessages(){return n.fallbackFormat},set formatFallbackMessages(s){n.fallbackFormat=s},get postTranslation(){return n.getPostTranslationHandler()},set postTranslation(s){n.setPostTranslationHandler(s)},get sync(){return n.inheritLocale},set sync(s){n.inheritLocale=s},get warnHtmlInMessage(){return n.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(s){n.warnHtmlMessage=s!=="off"},get escapeParameterHtml(){return n.escapeParameter},set escapeParameterHtml(s){n.escapeParameter=s},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(s){},get pluralizationRules(){return n.pluralRules||{}},__composer:n,t(...s){const[i,a,l]=s,c={};let u=null,d=null;if(!Q(i))throw Be(Ve.INVALID_ARGUMENT);const f=i;return Q(a)?c.locale=a:Re(a)?u=a:ce(a)&&(d=a),Re(l)?u=l:ce(l)&&(d=l),Reflect.apply(n.t,n,[f,u||d||{},c])},rt(...s){return Reflect.apply(n.rt,n,[...s])},tc(...s){const[i,a,l]=s,c={plural:1};let u=null,d=null;if(!Q(i))throw Be(Ve.INVALID_ARGUMENT);const f=i;return Q(a)?c.locale=a:Me(a)?c.plural=a:Re(a)?u=a:ce(a)&&(d=a),Q(l)?c.locale=l:Re(l)?u=l:ce(l)&&(d=l),Reflect.apply(n.t,n,[f,u||d||{},c])},te(s,i){return n.te(s,i)},tm(s){return n.tm(s)},getLocaleMessage(s){return n.getLocaleMessage(s)},setLocaleMessage(s,i){n.setLocaleMessage(s,i)},mergeLocaleMessage(s,i){n.mergeLocaleMessage(s,i)},d(...s){return Reflect.apply(n.d,n,[...s])},getDateTimeFormat(s){return n.getDateTimeFormat(s)},setDateTimeFormat(s,i){n.setDateTimeFormat(s,i)},mergeDateTimeFormat(s,i){n.mergeDateTimeFormat(s,i)},n(...s){return Reflect.apply(n.n,n,[...s])},getNumberFormat(s){return n.getNumberFormat(s)},setNumberFormat(s,i){n.setNumberFormat(s,i)},mergeNumberFormat(s,i){n.mergeNumberFormat(s,i)},getChoiceIndex(s,i){return-1}};return r.__extender=o,r}}const ta={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function xy({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((o,r)=>[...o,...r.type===Ce?r.children:[r]],[]):t.reduce((n,o)=>{const r=e[o];return r&&(n[o]=r()),n},Te())}function Yd(e){return Ce}const wy=nt({name:"i18n-t",props:qe({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>Me(e)||!isNaN(e)}},ta),setup(e,t){const{slots:n,attrs:o}=t,r=e.i18n||Bn({useScope:e.scope,__useComponent:!0});return()=>{const s=Object.keys(n).filter(d=>d!=="_"),i=Te();e.locale&&(i.locale=e.locale),e.plural!==void 0&&(i.plural=Q(e.plural)?+e.plural:e.plural);const a=xy(t,s),l=r[fi](e.keypath,a,i),c=qe(Te(),o),u=Q(e.tag)||ye(e.tag)?e.tag:Yd();return So(u,c,l)}}}),Jl=wy;function Cy(e){return Re(e)&&!Q(e[0])}function Qd(e,t,n,o){const{slots:r,attrs:s}=t;return()=>{const i={part:!0};let a=Te();e.locale&&(i.locale=e.locale),Q(e.format)?i.key=e.format:ye(e.format)&&(Q(e.format.key)&&(i.key=e.format.key),a=Object.keys(e.format).reduce((f,y)=>n.includes(y)?qe(Te(),f,{[y]:e.format[y]}):f,Te()));const l=o(e.value,i,a);let c=[i.key];Re(l)?c=l.map((f,y)=>{const C=r[f.type],w=C?C({[f.type]:f.value,index:y,parts:l}):[f.value];return Cy(w)&&(w[0].key=`${f.type}-${y}`),w}):Q(l)&&(c=[l]);const u=qe(Te(),s),d=Q(e.tag)||ye(e.tag)?e.tag:Yd();return So(d,u,c)}}const Ty=nt({name:"i18n-n",props:qe({value:{type:Number,required:!0},format:{type:[String,Object]}},ta),setup(e,t){const n=e.i18n||Bn({useScope:e.scope,__useComponent:!0});return Qd(e,t,jd,(...o)=>n[mi](...o))}}),Ul=Ty,Oy=nt({name:"i18n-d",props:qe({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},ta),setup(e,t){const n=e.i18n||Bn({useScope:e.scope,__useComponent:!0});return Qd(e,t,Hd,(...o)=>n[pi](...o))}}),Vl=Oy;function Ny(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const o=n.__getInstance(t);return o!=null?o.__composer:e.global.__composer}}function Py(e){const t=i=>{const{instance:a,modifiers:l,value:c}=i;if(!a||!a.$)throw Be(Ve.UNEXPECTED_ERROR);const u=Ny(e,a.$),d=$l(c);return[Reflect.apply(u.t,u,[...Hl(d)]),u]};return{created:(i,a)=>{const[l,c]=t(a);jr&&e.global===c&&(i.__i18nWatcher=ft(c.locale,()=>{a.instance&&a.instance.$forceUpdate()})),i.__composer=c,i.textContent=l},unmounted:i=>{jr&&i.__i18nWatcher&&(i.__i18nWatcher(),i.__i18nWatcher=void 0,delete i.__i18nWatcher),i.__composer&&(i.__composer=void 0,delete i.__composer)},beforeUpdate:(i,{value:a})=>{if(i.__composer){const l=i.__composer,c=$l(a);i.textContent=Reflect.apply(l.t,l,[...Hl(c)])}},getSSRProps:i=>{const[a]=t(i);return{textContent:a}}}}function $l(e){if(Q(e))return{path:e};if(ce(e)){if(!("path"in e))throw Be(Ve.REQUIRED_VALUE,"path");return e}else throw Be(Ve.INVALID_VALUE)}function Hl(e){const{path:t,locale:n,args:o,choice:r,plural:s}=e,i={},a=o||{};return Q(n)&&(i.locale=n),Me(r)&&(i.plural=r),Me(s)&&(i.plural=s),[t,a,i]}function Iy(e,t,...n){const o=ce(n[0])?n[0]:{},r=!!o.useI18nComponentName;(de(o.globalInstall)?o.globalInstall:!0)&&([r?"i18n":Jl.name,"I18nT"].forEach(i=>e.component(i,Jl)),[Ul.name,"I18nN"].forEach(i=>e.component(i,Ul)),[Vl.name,"I18nD"].forEach(i=>e.component(i,Vl))),e.directive("t",Py(t))}function Fy(e,t,n){return{beforeCreate(){const o=$e();if(!o)throw Be(Ve.UNEXPECTED_ERROR);const r=this.$options;if(r.i18n){const s=r.i18n;if(r.__i18n&&(s.__i18n=r.__i18n),s.__root=t,this===this.$root)this.$i18n=jl(e,s);else{s.__injectWithOption=!0,s.__extender=n.__vueI18nExtend,this.$i18n=hi(s);const i=this.$i18n;i.__extender&&(i.__disposer=i.__extender(this.$i18n))}}else if(r.__i18n)if(this===this.$root)this.$i18n=jl(e,r);else{this.$i18n=hi({__i18n:r.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:t});const s=this.$i18n;s.__extender&&(s.__disposer=s.__extender(this.$i18n))}else this.$i18n=e;r.__i18nGlobal&&qd(t,r,r),this.$t=(...s)=>this.$i18n.t(...s),this.$rt=(...s)=>this.$i18n.rt(...s),this.$tc=(...s)=>this.$i18n.tc(...s),this.$te=(s,i)=>this.$i18n.te(s,i),this.$d=(...s)=>this.$i18n.d(...s),this.$n=(...s)=>this.$i18n.n(...s),this.$tm=s=>this.$i18n.tm(s),n.__setInstance(o,this.$i18n)},mounted(){},unmounted(){const o=$e();if(!o)throw Be(Ve.UNEXPECTED_ERROR);const r=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,r.__disposer&&(r.__disposer(),delete r.__disposer,delete r.__extender),n.__deleteInstance(o),delete this.$i18n}}}function jl(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Wd](t.pluralizationRules||e.pluralizationRules);const n=ms(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach(o=>e.mergeLocaleMessage(o,n[o])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(o=>e.mergeDateTimeFormat(o,t.datetimeFormats[o])),t.numberFormats&&Object.keys(t.numberFormats).forEach(o=>e.mergeNumberFormat(o,t.numberFormats[o])),e}const Ry=wn("global-vue-i18n");function ky(e={},t){const n=__VUE_I18N_LEGACY_API__&&de(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,o=de(e.globalInjection)?e.globalInjection:!0,r=__VUE_I18N_LEGACY_API__&&n?!!e.allowComposition:!0,s=new Map,[i,a]=Ay(e,n),l=wn("");function c(f){return s.get(f)||null}function u(f,y){s.set(f,y)}function d(f){s.delete(f)}{const f={get mode(){return __VUE_I18N_LEGACY_API__&&n?"legacy":"composition"},get allowComposition(){return r},async install(y,...C){if(y.__VUE_I18N_SYMBOL__=l,y.provide(y.__VUE_I18N_SYMBOL__,f),ce(C[0])){const A=C[0];f.__composerExtend=A.__composerExtend,f.__vueI18nExtend=A.__vueI18nExtend}let w=null;!n&&o&&(w=Hy(y,f.global)),__VUE_I18N_FULL_INSTALL__&&Iy(y,f,...C),__VUE_I18N_LEGACY_API__&&n&&y.mixin(Fy(a,a.__composer,f));const L=y.unmount;y.unmount=()=>{w&&w(),f.dispose(),L()}},get global(){return a},dispose(){i.stop()},__instances:s,__getInstance:c,__setInstance:u,__deleteInstance:d};return f}}function Bn(e={}){const t=$e();if(t==null)throw Be(Ve.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Be(Ve.NOT_INSTALLED);const n=Ly(t),o=My(n),r=Kd(t),s=Dy(e,r);if(__VUE_I18N_LEGACY_API__&&n.mode==="legacy"&&!e.__useComponent){if(!n.allowComposition)throw Be(Ve.NOT_AVAILABLE_IN_LEGACY_MODE);return Vy(t,s,o,e)}if(s==="global")return qd(o,e,r),o;if(s==="parent"){let l=Gy(n,t,e.__useComponent);return l==null&&(l=o),l}const i=n;let a=i.__getInstance(t);if(a==null){const l=qe({},e);"__i18n"in r&&(l.__i18n=r.__i18n),o&&(l.__root=o),a=ea(l),i.__composerExtend&&(a[gi]=i.__composerExtend(a)),Uy(i,t,a),i.__setInstance(t,a)}return a}function Ay(e,t,n){const o=lc();{const r=__VUE_I18N_LEGACY_API__&&t?o.run(()=>hi(e)):o.run(()=>ea(e));if(r==null)throw Be(Ve.UNEXPECTED_ERROR);return[o,r]}}function Ly(e){{const t=vt(e.isCE?Ry:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Be(e.isCE?Ve.NOT_INSTALLED_WITH_PROVIDE:Ve.UNEXPECTED_ERROR);return t}}function Dy(e,t){return ds(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function My(e){return e.mode==="composition"?e.global:e.global.__composer}function Gy(e,t,n=!1){let o=null;const r=t.root;let s=Jy(t,n);for(;s!=null;){const i=e;if(e.mode==="composition")o=i.__getInstance(s);else if(__VUE_I18N_LEGACY_API__){const a=i.__getInstance(s);a!=null&&(o=a.__composer,n&&o&&!o[zd]&&(o=null))}if(o!=null||r===s)break;s=s.parent}return o}function Jy(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function Uy(e,t,n){ln(()=>{},t),bo(()=>{const o=n;e.__deleteInstance(t);const r=o[gi];r&&(r(),delete o[gi])},t)}function Vy(e,t,n,o={}){const r=t==="local",s=ar(null);if(r&&e.proxy&&!(e.proxy.$options.i18n||e.proxy.$options.__i18n))throw Be(Ve.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const i=de(o.inheritLocale)?o.inheritLocale:!Q(o.locale),a=Fe(!r||i?n.locale.value:Q(o.locale)?o.locale:ho),l=Fe(!r||i?n.fallbackLocale.value:Q(o.fallbackLocale)||Re(o.fallbackLocale)||ce(o.fallbackLocale)||o.fallbackLocale===!1?o.fallbackLocale:a.value),c=Fe(ms(a.value,o)),u=Fe(ce(o.datetimeFormats)?o.datetimeFormats:{[a.value]:{}}),d=Fe(ce(o.numberFormats)?o.numberFormats:{[a.value]:{}}),f=r?n.missingWarn:de(o.missingWarn)||En(o.missingWarn)?o.missingWarn:!0,y=r?n.fallbackWarn:de(o.fallbackWarn)||En(o.fallbackWarn)?o.fallbackWarn:!0,C=r?n.fallbackRoot:de(o.fallbackRoot)?o.fallbackRoot:!0,w=!!o.fallbackFormat,L=Pe(o.missing)?o.missing:null,A=Pe(o.postTranslation)?o.postTranslation:null,I=r?n.warnHtmlMessage:de(o.warnHtmlMessage)?o.warnHtmlMessage:!0,p=!!o.escapeParameter,h=r?n.modifiers:ce(o.modifiers)?o.modifiers:{},S=o.pluralRules||r&&n.pluralRules;function E(){return[a.value,l.value,c.value,u.value,d.value]}const F=pe({get:()=>s.value?s.value.locale.value:a.value,set:g=>{s.value&&(s.value.locale.value=g),a.value=g}}),M=pe({get:()=>s.value?s.value.fallbackLocale.value:l.value,set:g=>{s.value&&(s.value.fallbackLocale.value=g),l.value=g}}),N=pe(()=>s.value?s.value.messages.value:c.value),G=pe(()=>u.value),$=pe(()=>d.value);function T(){return s.value?s.value.getPostTranslationHandler():A}function W(g){s.value&&s.value.setPostTranslationHandler(g)}function se(){return s.value?s.value.getMissingHandler():L}function ee(g){s.value&&s.value.setMissingHandler(g)}function q(g){return E(),g()}function ie(...g){return s.value?q(()=>Reflect.apply(s.value.t,null,[...g])):q(()=>"")}function X(...g){return s.value?Reflect.apply(s.value.rt,null,[...g]):""}function be(...g){return s.value?q(()=>Reflect.apply(s.value.d,null,[...g])):q(()=>"")}function Ye(...g){return s.value?q(()=>Reflect.apply(s.value.n,null,[...g])):q(()=>"")}function Ae(g){return s.value?s.value.tm(g):{}}function Ie(g,b){return s.value?s.value.te(g,b):!1}function lt(g){return s.value?s.value.getLocaleMessage(g):{}}function ot(g,b){s.value&&(s.value.setLocaleMessage(g,b),c.value[g]=b)}function yt(g,b){s.value&&s.value.mergeLocaleMessage(g,b)}function Ge(g){return s.value?s.value.getDateTimeFormat(g):{}}function V(g,b){s.value&&(s.value.setDateTimeFormat(g,b),u.value[g]=b)}function Y(g,b){s.value&&s.value.mergeDateTimeFormat(g,b)}function K(g){return s.value?s.value.getNumberFormat(g):{}}function Z(g,b){s.value&&(s.value.setNumberFormat(g,b),d.value[g]=b)}function ue(g,b){s.value&&s.value.mergeNumberFormat(g,b)}const he={get id(){return s.value?s.value.id:-1},locale:F,fallbackLocale:M,messages:N,datetimeFormats:G,numberFormats:$,get inheritLocale(){return s.value?s.value.inheritLocale:i},set inheritLocale(g){s.value&&(s.value.inheritLocale=g)},get availableLocales(){return s.value?s.value.availableLocales:Object.keys(c.value)},get modifiers(){return s.value?s.value.modifiers:h},get pluralRules(){return s.value?s.value.pluralRules:S},get isGlobal(){return s.value?s.value.isGlobal:!1},get missingWarn(){return s.value?s.value.missingWarn:f},set missingWarn(g){s.value&&(s.value.missingWarn=g)},get fallbackWarn(){return s.value?s.value.fallbackWarn:y},set fallbackWarn(g){s.value&&(s.value.missingWarn=g)},get fallbackRoot(){return s.value?s.value.fallbackRoot:C},set fallbackRoot(g){s.value&&(s.value.fallbackRoot=g)},get fallbackFormat(){return s.value?s.value.fallbackFormat:w},set fallbackFormat(g){s.value&&(s.value.fallbackFormat=g)},get warnHtmlMessage(){return s.value?s.value.warnHtmlMessage:I},set warnHtmlMessage(g){s.value&&(s.value.warnHtmlMessage=g)},get escapeParameter(){return s.value?s.value.escapeParameter:p},set escapeParameter(g){s.value&&(s.value.escapeParameter=g)},t:ie,getPostTranslationHandler:T,setPostTranslationHandler:W,getMissingHandler:se,setMissingHandler:ee,rt:X,d:be,n:Ye,tm:Ae,te:Ie,getLocaleMessage:lt,setLocaleMessage:ot,mergeLocaleMessage:yt,getDateTimeFormat:Ge,setDateTimeFormat:V,mergeDateTimeFormat:Y,getNumberFormat:K,setNumberFormat:Z,mergeNumberFormat:ue};function _(g){g.locale.value=a.value,g.fallbackLocale.value=l.value,Object.keys(c.value).forEach(b=>{g.mergeLocaleMessage(b,c.value[b])}),Object.keys(u.value).forEach(b=>{g.mergeDateTimeFormat(b,u.value[b])}),Object.keys(d.value).forEach(b=>{g.mergeNumberFormat(b,d.value[b])}),g.escapeParameter=p,g.fallbackFormat=w,g.fallbackRoot=C,g.fallbackWarn=y,g.missingWarn=f,g.warnHtmlMessage=I}return Ai(()=>{if(e.proxy==null||e.proxy.$i18n==null)throw Be(Ve.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const g=s.value=e.proxy.$i18n.__composer;t==="global"?(a.value=g.locale.value,l.value=g.fallbackLocale.value,c.value=g.messages.value,u.value=g.datetimeFormats.value,d.value=g.numberFormats.value):r&&_(g)}),he}const $y=["locale","fallbackLocale","availableLocales"],Bl=["t","rt","d","n","tm","te"];function Hy(e,t){const n=Object.create(null);return $y.forEach(r=>{const s=Object.getOwnPropertyDescriptor(t,r);if(!s)throw Be(Ve.UNEXPECTED_ERROR);const i=Je(s.value)?{get(){return s.value.value},set(a){s.value.value=a}}:{get(){return s.get&&s.get()}};Object.defineProperty(n,r,i)}),e.config.globalProperties.$i18n=n,Bl.forEach(r=>{const s=Object.getOwnPropertyDescriptor(t,r);if(!s||!s.value)throw Be(Ve.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${r}`,s)}),()=>{delete e.config.globalProperties.$i18n,Bl.forEach(r=>{delete e.config.globalProperties[`$${r}`]})}}yy();__INTLIFY_JIT_COMPILATION__?wl(fy):wl(dy);ny(D0);oy(kd);if(__INTLIFY_PROD_DEVTOOLS__){const e=Zt();e.__INTLIFY__=!0,B0(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}const jy={key:0,class:"electron-titlebar bg-gray-800 text-white flex items-center justify-between h-8 px-2 select-none",style:{"-webkit-app-region":"drag"}},By={class:"flex items-center space-x-2 text-sm"},Wy={class:"flex items-center",style:{"-webkit-app-region":"no-drag"}},zy=["title"],Ky={key:0,width:"12",height:"12",viewBox:"0 0 12 12",fill:"currentColor"},qy={key:1,width:"12",height:"12",viewBox:"0 0 12 12",fill:"currentColor"},Yy=nt({__name:"ElectronTitleBar",setup(e){const{t}=Bn(),n=Fe(!1),o=Fe(!1);ln(async()=>{if(n.value=!!window.electronAPI,n.value)try{o.value=await window.electronAPI.windowIsMaximized()}catch(a){console.error("Failed to get window maximized state:",a)}});async function r(){if(window.electronAPI)try{await window.electronAPI.windowMinimize()}catch(a){console.error("Failed to minimize window:",a)}}async function s(){if(window.electronAPI)try{await window.electronAPI.windowMaximize(),o.value=await window.electronAPI.windowIsMaximized()}catch(a){console.error("Failed to toggle maximize window:",a)}}async function i(){if(window.electronAPI)try{await window.electronAPI.windowClose()}catch(a){console.error("Failed to close window:",a)}}return(a,l)=>(me(),we("div",null,[n.value?(me(),we("div",jy,[P("div",By,[l[0]||(l[0]=P("span",{class:"text-lg"},"🔧",-1)),P("span",null,re(a.$t("navigation.tools")),1)]),P("div",Wy,[P("button",{onClick:r,class:"window-control-btn hover:bg-gray-600 w-8 h-8 flex items-center justify-center transition-colors",title:"最小化"},[...l[1]||(l[1]=[P("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"currentColor"},[P("rect",{x:"2",y:"5",width:"8",height:"2"})],-1)])]),P("button",{onClick:s,class:"window-control-btn hover:bg-gray-600 w-8 h-8 flex items-center justify-center transition-colors",title:o.value?"还原":"最大化"},[o.value?(me(),we("svg",qy,[...l[3]||(l[3]=[P("rect",{x:"2",y:"3",width:"6",height:"6",stroke:"currentColor","stroke-width":"1",fill:"none"},null,-1),P("rect",{x:"4",y:"1",width:"6",height:"6",stroke:"currentColor","stroke-width":"1",fill:"none"},null,-1)])])):(me(),we("svg",Ky,[...l[2]||(l[2]=[P("rect",{x:"2",y:"2",width:"8",height:"8",stroke:"currentColor","stroke-width":"1",fill:"none"},null,-1)])]))],8,zy),P("button",{onClick:i,class:"window-control-btn hover:bg-red-600 w-8 h-8 flex items-center justify-center transition-colors",title:"关闭"},[...l[4]||(l[4]=[P("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"currentColor"},[P("path",{d:"M2 2 L10 10 M10 2 L2 10",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round"})],-1)])])])])):Dt("",!0),l[5]||(l[5]=P("div",{class:"h-20"},null,-1))]))}}),Qy=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},Xy=Qy(Yy,[["__scopeId","data-v-fc5f53bb"]]),Zy={class:"h-screen flex bg-dark-950 text-slate-100"},e_={class:"flex flex-1 relative"},t_={class:"p-4 border-b border-slate-700/30"},n_={class:"flex items-center justify-between"},o_={class:"flex items-center"},r_={class:"text-lg font-semibold text-slate-100 group-hover:text-primary-400 transition-colors duration-200"},s_={class:"text-xs text-slate-400 bg-slate-800/50 px-2 py-1 rounded-full"},i_={key:1,class:"text-lg font-semibold text-slate-100 text-gradient"},a_={key:0,class:"flex-1 flex flex-col min-h-0 px-4 space-y-3 overflow-y-auto"},l_=["onClick"],c_={class:"flex items-center justify-between"},u_={class:"flex items-center"},d_={class:"text-lg mr-3 group-hover:scale-110 transition-transform duration-200"},f_={class:"font-medium text-slate-100 group-hover:text-primary-400 transition-colors duration-200"},p_={class:"text-xs text-slate-400 mt-1 line-clamp-2"},m_={class:"flex items-center"},g_={class:"text-xs bg-slate-800/50 text-slate-300 px-2 py-1 rounded-full mr-2 group-hover:bg-primary-500/20 group-hover:text-primary-400 transition-all duration-200"},h_={key:1,class:"flex-1 flex flex-col min-h-0 overflow-y-auto"},v_={class:"p-4 border-b border-slate-700/30"},y_={class:"relative"},__=["placeholder"],b_={class:"flex-1 overflow-y-auto p-4"},S_={key:0,class:"text-center py-12"},E_={class:"text-slate-400 text-sm"},x_={key:1,class:"space-y-3"},w_={class:"flex items-start"},C_={class:"text-lg mr-3 mt-0.5 group-hover:scale-110 transition-transform duration-200"},T_={class:"flex-1 min-w-0"},O_={class:"text-sm font-medium text-slate-100 truncate group-hover:text-primary-400 transition-colors duration-200"},N_={class:"text-xs text-slate-400 mt-1 leading-relaxed line-clamp-2"},P_={key:0,class:"mt-2"},I_={class:"p-4 border-t border-slate-700/30 mt-auto"},F_={class:"relative space-y-2"},R_={class:"text-sm font-medium"},k_={class:"text-sm font-medium"},A_={key:0,class:"absolute bottom-full left-0 mb-2 w-full glass rounded-xl shadow-dark-xl z-50 border border-slate-600/50 animate-slide-up"},L_={class:"py-2"},D_=["onClick"],M_={class:"mr-3"},G_={key:0,class:"absolute inset-0 glass flex items-center justify-center z-50"},J_={class:"flex flex-col items-center"},U_={class:"text-slate-300 text-sm font-medium mt-6 animate-pulse"},V_=nt({__name:"App",setup(e){const t=dv(),n=Ki(),{locale:o,t:r}=Bn(),s=Fe("web-tools"),i=Fe(!1),a=Fe(!1),l=Fe(""),c=Fe(!1),u=Fe(!0),d=Fe(!1),f=Fe(!1),y=[{code:"en",name:"English",flag:"🇺🇸"},{code:"zh",name:"中文",flag:"🇨🇳"}],C=pe(()=>o.value),w=pe(()=>y.find($=>$.code===C.value)||y[0]),L=pe(()=>Er.find(T=>T.id===s.value)?.children||[]),A=pe(()=>{const $=L.value;if(!l.value.trim())return $;const T=l.value.toLowerCase().trim();return $.filter(W=>{const se=W.name.toLowerCase(),ee=W.id.toLowerCase();return se.includes(T)||ee.includes(T)})});function I(){i.value=!i.value}function p(){i.value=!1}function h(){a.value=window.innerWidth<1e3,a.value||(i.value=!1)}function S($){s.value=$,u.value=!1,l.value=""}function E(){u.value=!0,l.value="",n.replace("/")}function F(){l.value=""}function M($){const W=$.currentTarget.closest("a[href]");if(!W)return;if(W.getAttribute("href")===t.path){$.preventDefault(),a.value&&p();return}c.value=!0,a.value&&p()}function N($){o.value=$,localStorage.setItem("locale",$),document.title=r("navigation.title"),d.value=!1}function G($){$.target.closest(".relative")||(d.value=!1)}return ft(()=>t.path,($,T)=>{for(const W of Er)if(W.children){for(const se of W.children)if(se.path===$){s.value=W.id,$!=="/"&&(u.value=!1);break}}$==="/"&&(u.value=!0),$!==T?setTimeout(()=>{c.value=!1},300):c.value=!1},{immediate:!0}),ln(()=>{const $=t.path;let T=!1;for(const W of Er)if(W.children){for(const se of W.children)if(se.path===$){s.value=W.id,u.value=!1,T=!0;break}if(T)break}(!T||$==="/")&&(u.value=!0),h(),window.addEventListener("resize",h),document.addEventListener("click",G),f.value=!!window.electronAPI,a.value&&(i.value=!1)}),bo(()=>{window.removeEventListener("resize",h),document.removeEventListener("click",G)}),($,T)=>{const W=qo("router-link"),se=qo("router-view");return me(),we("div",Zy,[B(Xy),P("div",{class:"custom-mobile:hidden fixed bottom-6 left-6 z-20"},[P("button",{onClick:I,class:"p-3 rounded-xl glass hover:glass-light shadow-dark-lg text-slate-300 hover:text-white transition-all duration-300 cursor-pointer hover-lift group"},[...T[2]||(T[2]=[P("svg",{class:"w-5 h-5 group-hover:scale-110 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[P("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)])])]),P("div",e_,[i.value&&a.value?(me(),we("div",{key:0,onClick:p,class:"fixed inset-0 bg-black/60 backdrop-blur-sm z-30 custom-mobile:hidden transition-all duration-300"})):Dt("",!0),P("aside",{class:St(["glass border-r border-slate-700/50 flex flex-col transition-all duration-300 ease-in-out z-40 shadow-dark-xl","custom-mobile:relative custom-mobile:translate-x-0",a.value?["fixed inset-y-0 left-0 w-80",i.value?"translate-x-0":"-translate-x-full"]:"w-64 xl:w-72"])},[P("div",t_,[P("div",n_,[u.value?(me(),we("h2",i_,re($.$t("navigation.tools")),1)):(me(),we("div",{key:0,onClick:E,class:"flex items-center justify-between cursor-pointer hover:text-primary-400 transition-all duration-200 group"},[P("div",o_,[T[3]||(T[3]=P("svg",{class:"w-4 h-4 mr-2 text-slate-400 group-hover:text-primary-400 group-hover:-translate-x-1 transition-all duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[P("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)),P("h2",r_,re($.$t(`categories.${s.value}.name`)),1)]),P("span",s_,re(A.value.length)+"/"+re(L.value.length),1)])),a.value?(me(),we("button",{key:2,onClick:p,class:"custom-mobile:hidden p-2 rounded-lg text-slate-400 hover:text-slate-200 hover:bg-slate-800/50 transition-all duration-200 cursor-pointer"},[...T[4]||(T[4]=[P("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[P("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])])):Dt("",!0)])]),u.value?(me(),we("nav",a_,[(me(!0),we(Ce,null,Dn(Ut(Er),ee=>(me(),we("button",{key:ee.id,onClick:q=>S(ee.id),class:"w-full text-left px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 cursor-pointer hover:bg-slate-800/50 border border-slate-700/30 hover:border-primary-500/50 hover-lift group"},[P("div",c_,[P("div",u_,[P("span",d_,re(ee.icon),1),P("div",null,[P("div",f_,re($.$t(`categories.${ee.id}.name`)),1),P("div",p_,re($.$t(`categories.${ee.id}.description`)),1)])]),P("div",m_,[P("span",g_,re(ee.children?.length||0),1),T[5]||(T[5]=P("svg",{class:"w-4 h-4 text-slate-400 group-hover:text-primary-400 group-hover:translate-x-1 transition-all duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[P("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1))])])],8,l_))),128))])):Dt("",!0),u.value?Dt("",!0):(me(),we("nav",h_,[P("div",v_,[P("div",y_,[T[7]||(T[7]=P("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[P("svg",{class:"h-4 w-4 text-slate-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[P("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})])],-1)),Lc(P("input",{"onUpdate:modelValue":T[0]||(T[0]=ee=>l.value=ee),type:"text",placeholder:$.$t("navigation.search"),class:"block w-full pl-10 pr-10 py-3 text-sm bg-slate-800/50 border border-slate-600/50 rounded-xl text-slate-100 placeholder-slate-400 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200"},null,8,__),[[Xo,l.value]]),l.value?(me(),we("button",{key:0,onClick:F,class:"absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer group"},[...T[6]||(T[6]=[P("svg",{class:"h-4 w-4 text-slate-400 hover:text-slate-200 group-hover:scale-110 transition-all duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[P("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])])):Dt("",!0)])]),P("div",b_,[A.value.length===0?(me(),we("div",S_,[T[8]||(T[8]=P("div",{class:"text-slate-400 text-5xl mb-4 animate-bounce-subtle"},"🔍",-1)),P("p",E_,re(l.value?$.$t("navigation.noResults"):$.$t("navigation.noToolsInCategory")),1)])):(me(),we("div",x_,[(me(!0),we(Ce,null,Dn(A.value,ee=>(me(),lo(W,{key:ee.id,to:ee.path,onClick:M,class:St(["block p-4 rounded-xl border transition-all duration-200 cursor-pointer hover-lift group",$.$route.path===ee.path?"bg-primary-500/10 border-primary-500/50 shadow-glow":"bg-slate-800/30 border-slate-700/30 hover:border-slate-600/50 hover:bg-slate-800/50"])},{default:Gt(()=>[P("div",w_,[P("span",C_,re(ee.icon),1),P("div",T_,[P("h4",O_,re($.$t(`tools.${ee.id}.title`)),1),P("p",N_,re($.$t(`tools.${ee.id}.description`)),1),ee.status?(me(),we("div",P_,[P("span",{class:St(["inline-block px-2 py-1 text-xs rounded-full font-medium transition-all duration-200",ee.status==="active"?"bg-success-500/20 text-success-400 group-hover:bg-success-500/30":"bg-warning-500/20 text-warning-400 group-hover:bg-warning-500/30"])},re($.$t(`status.${ee.status}`)),3)])):Dt("",!0)])])]),_:2},1032,["to","class"]))),128))]))])])),P("div",I_,[P("div",F_,[B(W,{to:"/",onClick:p,class:"w-full bg-slate-800/50 p-3 rounded-xl text-slate-300 hover:text-white hover:bg-slate-800/70 transition-all duration-200 flex items-center space-x-3 cursor-pointer hover-lift group"},{default:Gt(()=>[T[9]||(T[9]=P("svg",{class:"w-5 h-5 group-hover:scale-110 transition-transform duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[P("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})],-1)),P("span",R_,re($.$t("navigation.home")),1)]),_:1}),P("button",{onClick:T[1]||(T[1]=ee=>d.value=!d.value),class:"w-full bg-slate-800/50 p-3 rounded-xl text-slate-300 hover:text-white hover:bg-slate-800/70 transition-all duration-200 flex items-center space-x-3 cursor-pointer hover-lift group"},[T[10]||(T[10]=P("span",{class:"group-hover:scale-110 transition-transform duration-200"},"🌐",-1)),P("span",k_,re(w.value.name),1),T[11]||(T[11]=P("svg",{class:"w-4 h-4 ml-auto group-hover:rotate-180 transition-transform duration-200",fill:"currentColor",viewBox:"0 0 20 20"},[P("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1))]),d.value?(me(),we("div",A_,[P("div",L_,[(me(),we(Ce,null,Dn(y,ee=>P("button",{key:ee.code,onClick:q=>N(ee.code),class:St(["block w-full text-left px-4 py-3 text-sm transition-all duration-200 cursor-pointer hover-lift",C.value===ee.code?"bg-primary-500/20 text-primary-400 font-medium":"text-slate-300 hover:bg-slate-800/50 hover:text-white"])},[P("span",M_,re(ee.flag),1),Vn(" "+re(ee.name),1)],10,D_)),64))])])):Dt("",!0)])])],2),P("main",{class:St(["flex-1 overflow-auto relative bg-gradient-to-br from-dark-950 via-dark-900 to-dark-800",{"pt-8":f.value,"electron-scrollbar":f.value}])},[B(io,{"enter-active-class":"transition-all duration-300","leave-active-class":"transition-all duration-200","enter-from-class":"opacity-0 scale-95","enter-to-class":"opacity-100 scale-100","leave-from-class":"opacity-100 scale-100","leave-to-class":"opacity-0 scale-95"},{default:Gt(()=>[c.value?(me(),we("div",G_,[P("div",J_,[T[13]||(T[13]=P("div",{class:"relative"},[P("div",{class:"w-16 h-16 border-4 border-slate-700 border-t-primary-500 rounded-full animate-spin"}),P("div",{class:"absolute inset-0 w-16 h-16 border-4 border-transparent border-r-primary-400 rounded-full animate-spin",style:{"animation-direction":"reverse","animation-duration":"1s"}})],-1)),P("p",U_,[Vn(re($.$t("common.loading")),1),T[12]||(T[12]=P("span",{class:"loading-dots"},null,-1))])])])):Dt("",!0)]),_:1}),B(io,{"enter-active-class":"transition-all duration-500 ease-out","leave-active-class":"transition-all duration-300 ease-in","enter-from-class":"opacity-0 transform translate-y-8 scale-95","enter-to-class":"opacity-100 transform translate-y-0 scale-100","leave-from-class":"opacity-100 transform translate-y-0 scale-100","leave-to-class":"opacity-0 transform translate-y-4 scale-98",mode:"out-in"},{default:Gt(()=>[(me(),we("div",{key:$.$route.path,class:St(["transition-all duration-300",c.value?"opacity-30 blur-sm":"opacity-100 blur-0"])},[B(se)],2))]),_:1})],2)])])}}}),$_={class:"min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-800 p-4 md:p-6"},H_={class:"max-w-7xl mx-auto"},j_={class:"text-center mb-16 relative"},B_={class:"text-4xl md:text-6xl lg:text-7xl font-bold text-gradient mb-6 animate-fade-in"},W_={class:"text-lg md:text-xl text-slate-300 max-w-3xl mx-auto leading-relaxed animate-slide-up"},z_={class:"mb-16"},K_={class:"flex items-center justify-between mb-10"},q_={class:"text-3xl font-bold text-slate-100 mb-2"},Y_={class:"text-sm text-slate-400 bg-slate-800/50 px-3 py-1 rounded-full"},Q_={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},X_={class:"flex items-start"},Z_={class:"text-3xl mr-4 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300"},eb={class:"flex-1 min-w-0"},tb={class:"text-lg font-semibold text-slate-100 mb-2 group-hover:text-primary-400 transition-colors duration-200"},nb={class:"text-sm text-slate-400 leading-relaxed line-clamp-3 group-hover:text-slate-300 transition-colors duration-200"},ob={class:"mt-4"},rb={class:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-500/20 text-primary-400 group-hover:bg-primary-500/30 transition-all duration-200"},sb={class:"mb-16"},ib={class:"flex items-center justify-between mb-10"},ab={class:"text-3xl font-bold text-slate-100 mb-2"},lb={class:"text-sm text-slate-400 bg-slate-800/50 px-3 py-1 rounded-full"},cb={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},ub=["onClick"],db={class:"flex items-center justify-between mb-6"},fb={class:"text-4xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-300"},pb={class:"text-xs bg-slate-800/50 text-slate-300 px-3 py-1.5 rounded-full group-hover:bg-primary-500/20 group-hover:text-primary-400 transition-all duration-200"},mb={class:"text-xl font-semibold text-slate-100 mb-3 group-hover:text-primary-400 transition-colors duration-200"},gb={class:"text-sm text-slate-400 leading-relaxed group-hover:text-slate-300 transition-colors duration-200"},hb={class:"glass rounded-3xl border border-slate-700/30 p-8 md:p-10 relative overflow-hidden"},vb={class:"relative"},yb={class:"grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8"},_b={class:"text-center group"},bb={class:"bg-primary-500/10 rounded-2xl p-4 mb-4 group-hover:bg-primary-500/20 transition-all duration-300"},Sb={class:"text-4xl font-bold text-primary-400 mb-2 group-hover:scale-110 transition-transform duration-300"},Eb={class:"text-sm text-slate-400 group-hover:text-slate-300 transition-colors duration-200"},xb={class:"text-center group"},wb={class:"bg-success-500/10 rounded-2xl p-4 mb-4 group-hover:bg-success-500/20 transition-all duration-300"},Cb={class:"text-4xl font-bold text-success-400 mb-2 group-hover:scale-110 transition-transform duration-300"},Tb={class:"text-sm text-slate-400 group-hover:text-slate-300 transition-colors duration-200"},Ob={class:"text-center group"},Nb={class:"bg-purple-500/10 rounded-2xl p-4 mb-4 group-hover:bg-purple-500/20 transition-all duration-300"},Pb={class:"text-4xl font-bold text-purple-400 mb-2 group-hover:scale-110 transition-transform duration-300"},Ib={class:"text-sm text-slate-400 group-hover:text-slate-300 transition-colors duration-200"},Fb={class:"text-center group"},Rb={class:"bg-warning-500/10 rounded-2xl p-4 mb-4 group-hover:bg-warning-500/20 transition-all duration-300"},kb={class:"text-4xl font-bold text-warning-400 mb-2 group-hover:scale-110 transition-transform duration-300"},Ab={class:"text-sm text-slate-400 group-hover:text-slate-300 transition-colors duration-200"},Lb=nt({__name:"Homepage",setup(e){const t=Ki(),{t:n}=Bn(),o=[{id:"webTools",name:"Web Tools",icon:"🌐",tools:[{id:"htmlExtractor",pid:"webTools",name:"HTML Content Extractor",icon:"🖼️",path:"/web-tools/html-extractor",status:"active"}]},{id:"jsonTools",name:"JSON Tools",icon:"📋",tools:[{id:"jsonToExcel",pid:"jsonTools",name:"JSON to Excel Converter",icon:"📊",path:"/json-tools/json-to-excel",status:"active"},{id:"excelToJson",pid:"jsonTools",name:"Excel to JSON Converter",icon:"📈",path:"/json-tools/excel-to-json",status:"active"},{id:"jsonFormatter",pid:"jsonTools",name:"JSON Formatter",icon:"🎨",path:"/json-tools/json-formatter",status:"active"},{id:"jsonExtractor",pid:"jsonTools",name:"JSON Extractor",icon:"🔍",path:"/json-tools/json-extractor",status:"active"},{id:"jsonPathExtractor",pid:"jsonTools",name:"JSON Path Extractor",icon:"🛤️",path:"/json-tools/json-path-extractor",status:"active"}]},{id:"imageTools",name:"Image Tools",icon:"🖼️",tools:[{id:"imageListProcessor",pid:"imageTools",name:"Image List Processor",icon:"🖼️",path:"/image-tools/image-list-processor",status:"active"},{id:"imageCompressor",pid:"imageTools",name:"Image Compressor",icon:"🗂",path:"/image-tools/image-compressor",status:"active"},{id:"apngGenerator",pid:"imageTools",name:"APNG Generator",icon:"🎬",path:"/image-tools/apng-generator",status:"active"}]},{id:"converters",name:"Converters",icon:"🔄",tools:[{id:"fileRenamer",pid:"converters",name:"File Renamer",icon:"📝",path:"/converters/file-renamer",status:"active"}]},{id:"generators",name:"Generators",icon:"⚡",tools:[{id:"faviconGenerator",pid:"generators",name:"Favicon Generator",icon:"🎯",path:"/generators/favicon-generator",status:"active"}]}],r=pe(()=>{const c=o.flatMap(d=>d.tools.filter(f=>f.status==="active")),u=["jsonToExcel","htmlExtractor","imageListProcessor","jsonFormatter","faviconGenerator","fileRenamer","jsonExtractor","imageCompressor"];return c.filter(d=>u.includes(d.id))}),s=pe(()=>o.reduce((c,u)=>c+u.tools.length,0)),i=pe(()=>o.reduce((c,u)=>c+u.tools.filter(d=>d.status==="active").length,0)),a=pe(()=>o.reduce((c,u)=>c+u.tools.filter(d=>d.status==="coming-soon").length,0));function l(c){const u=o.find(d=>d.id===c);if(u&&u.tools.length>0){const d=u.tools.find(f=>f.status==="active");d&&t.push(d.path)}}return(c,u)=>{const d=qo("router-link");return me(),we("div",$_,[P("div",H_,[P("div",j_,[u[0]||(u[0]=P("div",{class:"absolute inset-0 -z-10"},[P("div",{class:"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-primary-500/10 rounded-full blur-3xl animate-pulse-glow"}),P("div",{class:"absolute top-1/3 left-1/4 w-64 h-64 bg-success-500/5 rounded-full blur-2xl animate-float"}),P("div",{class:"absolute bottom-1/3 right-1/4 w-48 h-48 bg-warning-500/5 rounded-full blur-2xl animate-float",style:{"animation-delay":"1s"}})],-1)),P("h1",B_,re(c.$t("homepage.title")),1),P("p",W_,re(c.$t("homepage.subtitle")),1),u[1]||(u[1]=P("div",{class:"absolute -top-4 left-1/4 text-2xl animate-bounce-subtle opacity-60"},"⚡",-1)),u[2]||(u[2]=P("div",{class:"absolute top-8 right-1/4 text-2xl animate-bounce-subtle opacity-60",style:{"animation-delay":"0.5s"}}," 🚀 ",-1)),u[3]||(u[3]=P("div",{class:"absolute -bottom-4 left-1/3 text-2xl animate-bounce-subtle opacity-60",style:{"animation-delay":"1s"}}," ✨ ",-1))]),P("div",z_,[P("div",K_,[P("div",null,[P("h2",q_,re(c.$t("homepage.recommendedTools")),1),u[4]||(u[4]=P("p",{class:"text-slate-400"},"精选最受欢迎的工具，助力您的工作效率",-1))]),P("span",Y_,re(r.value.length)+" "+re(c.$t("common.items")),1)]),P("div",Q_,[(me(!0),we(Ce,null,Dn(r.value,(f,y)=>(me(),lo(d,{key:f.id,to:f.path,class:"glass rounded-2xl border border-slate-700/30 hover:border-primary-500/50 hover:shadow-glow transition-all duration-300 p-6 group cursor-pointer hover-lift animate-slide-up",style:bn({animationDelay:`${y*100}ms`})},{default:Gt(()=>[P("div",X_,[P("div",Z_,re(f.icon),1),P("div",eb,[P("h3",tb,re(c.$t(`tools.${f.id}.title`)),1),P("p",nb,re(c.$t(`tools.${f.id}.description`)),1),P("div",ob,[P("span",rb,re(c.$t(`categories.${f.pid}.name`)),1)])])]),u[5]||(u[5]=P("div",{class:"absolute inset-0 bg-gradient-to-r from-primary-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl pointer-events-none"},null,-1))]),_:2},1032,["to","style"]))),128))])]),P("div",sb,[P("div",ib,[P("div",null,[P("h2",ab,re(c.$t("homepage.exploreCategories")),1),u[6]||(u[6]=P("p",{class:"text-slate-400"},"探索所有工具分类，找到您需要的功能",-1))]),P("span",lb,re(o.length)+" "+re(c.$t("navigation.categories")),1)]),P("div",cb,[(me(),we(Ce,null,Dn(o,(f,y)=>P("div",{key:f.id,onClick:C=>l(f.id),class:"glass rounded-2xl border border-slate-700/30 hover:border-primary-500/50 hover:shadow-glow transition-all duration-300 p-6 cursor-pointer group hover-lift animate-slide-up",style:bn({animationDelay:`${y*150}ms`})},[P("div",db,[P("div",fb,re(f.icon),1),P("span",pb,re(f.tools.length),1)]),P("h3",mb,re(c.$t(`categories.${f.id}.name`)),1),P("p",gb,re(c.$t(`categories.${f.id}.description`)),1),u[7]||(u[7]=P("div",{class:"mt-4 flex justify-end"},[P("svg",{class:"w-5 h-5 text-slate-500 group-hover:text-primary-400 group-hover:translate-x-1 transition-all duration-200",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[P("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})])],-1)),u[8]||(u[8]=P("div",{class:"absolute inset-0 bg-gradient-to-br from-primary-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl pointer-events-none"},null,-1))],12,ub)),64))])]),P("div",hb,[u[10]||(u[10]=P("div",{class:"absolute inset-0 bg-gradient-to-r from-primary-500/5 via-transparent to-success-500/5"},null,-1)),P("div",vb,[u[9]||(u[9]=P("div",{class:"text-center mb-8"},[P("h3",{class:"text-2xl font-bold text-slate-100 mb-2"},"平台统计"),P("p",{class:"text-slate-400"},"实时数据展示")],-1)),P("div",yb,[P("div",_b,[P("div",bb,[P("div",Sb,re(s.value),1),P("div",Eb,re(c.$t("homepage.stats.totalTools")),1)])]),P("div",xb,[P("div",wb,[P("div",Cb,re(i.value),1),P("div",Tb,re(c.$t("homepage.stats.activeTools")),1)])]),P("div",Ob,[P("div",Nb,[P("div",Pb,re(o.length),1),P("div",Ib,re(c.$t("homepage.stats.categories")),1)])]),P("div",Fb,[P("div",Rb,[P("div",kb,re(a.value),1),P("div",Ab,re(c.$t("homepage.stats.comingSoon")),1)])])])])]),u[11]||(u[11]=P("div",{class:"h-16"},null,-1))])])}}}),Db={class:"min-h-screen bg-gray-50 flex items-center justify-center p-6"},Mb={class:"max-w-2xl mx-auto text-center"},Gb={class:"mb-8"},Jb={class:"text-2xl md:text-3xl font-semibold text-gray-700 mb-2"},Ub={class:"text-lg text-gray-600"},Vb={class:"flex flex-col sm:flex-row gap-4 justify-center mb-12"},$b={class:"text-left"},Hb={class:"text-xl font-semibold text-gray-900 mb-6 text-center"},jb={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Bb={class:"flex items-center"},Wb={class:"text-2xl mr-3 group-hover:scale-110 transition-transform duration-200"},zb={class:"flex-1 min-w-0"},Kb={class:"text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors"},qb={class:"text-xs text-gray-500 mt-1 truncate"},Yb={class:"mt-12 text-center"},Qb={class:"text-sm text-gray-500"},Xb=nt({__name:"NotFound",setup(e){const t=Ki();Bn();const n=[{id:"jsonToExcel",name:"JSON to Excel Converter",icon:"📊",path:"/json-tools/json-to-excel"},{id:"htmlExtractor",name:"HTML Content Extractor",icon:"🖼️",path:"/web-tools/html-extractor"},{id:"imageListProcessor",name:"Image List Processor",icon:"🖼️",path:"/image-tools/image-list-processor"},{id:"jsonFormatter",name:"JSON Formatter",icon:"🎨",path:"/json-tools/json-formatter"},{id:"faviconGenerator",name:"Favicon Generator",icon:"🎯",path:"/generators/favicon-generator"},{id:"fileRenamer",name:"File Renamer",icon:"📝",path:"/converters/file-renamer"}];function o(){window.history.length>1?t.back():t.push("/")}return(r,s)=>{const i=qo("router-link");return me(),we("div",Db,[P("div",Mb,[P("div",Gb,[s[0]||(s[0]=P("div",{class:"text-8xl md:text-9xl mb-4"},"🔍",-1)),s[1]||(s[1]=P("h1",{class:"text-6xl md:text-7xl font-bold text-gray-900 mb-4"},"404",-1)),P("h2",Jb,re(r.$t("notFound.title")),1),P("p",Ub,re(r.$t("notFound.description")),1)]),P("div",Vb,[B(i,{to:"/",class:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-200"},{default:Gt(()=>[s[2]||(s[2]=P("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[P("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"})],-1)),Vn(" "+re(r.$t("notFound.backToHome")),1)]),_:1}),P("button",{onClick:o,class:"inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200"},[s[3]||(s[3]=P("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[P("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10 19l-7-7m0 0l7-7m-7 7h18"})],-1)),Vn(" "+re(r.$t("notFound.goBack")),1)])]),P("div",$b,[P("h3",Hb,re(r.$t("notFound.popularTools")),1),P("div",jb,[(me(),we(Ce,null,Dn(n,a=>B(i,{key:a.id,to:a.path,class:"bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-200 p-4 group cursor-pointer"},{default:Gt(()=>[P("div",Bb,[P("div",Wb,re(a.icon),1),P("div",zb,[P("h4",Kb,re(r.$t(`tools.${a.id}.title`)),1),P("p",qb,re(r.$t(`tools.${a.id}.description`)),1)])])]),_:2},1032,["to"])),64))])]),P("div",Yb,[P("p",Qb,re(r.$t("notFound.helpText")),1)])])])}}}),Zb={common:{clear:"Clear",copy:"Copy",close:"Close",download:"Download",loadExample:"Load Example",loadObjectExample:"Load Object Example",selectAll:"Select All",clearSelection:"Clear Selection",extract:"Extract",results:"Results",options:"Options",input:"Input",preview:"Preview",statistics:"Statistics",fields:"Fields",items:"items",found:"found",extracted:"extracted",with:"with",total:"Total",unique:"Unique",nonEmpty:"Non-empty",loading:"Loading...",remove:"Remove",more:"More"},toast:{success:"Success",error:"Error",warning:"Warning",info:"Info",copied:"Results copied to clipboard!",copyFailed:"Failed to copy to clipboard",downloadSuccess:"File downloaded successfully!"},footer:{madeWith:"Made with",by:"by"},categories:{webTools:{name:"Web Tools",description:"Tools for web development and analysis"},jsonTools:{name:"JSON Tools",description:"Comprehensive JSON processing and conversion utilities"},dataTools:{name:"Data Tools",description:"Tools for data processing and manipulation"},imageTools:{name:"Image Tools",description:"Tools for image processing and management"},converters:{name:"Converters",description:"Format conversion utilities"},generators:{name:"Generators",description:"Code and content generators"}},pagination:{previous:"Previous",next:"Next",page:"Page",of:"of"},status:{active:"Active","coming-soon":"Coming Soon"},navigation:{title:"Professional web development utilities",tools:"Tools",language:"Language",categories:"Tool Categories",menu:"Menu",close:"Close",search:"Search tools...",noResults:"No tools found matching your search.",noToolsInCategory:"No tools available in this category.",home:"Home"},homepage:{title:"Developer Tools Collection",subtitle:"Powerful online tools for developers, designers, and content creators. Everything you need to boost your productivity.",recommendedTools:"Recommended Tools",exploreCategories:"Explore Categories",stats:{totalTools:"Total Tools",activeTools:"Active Tools",categories:"Categories",comingSoon:"Coming Soon"}},notFound:{title:"Page Not Found",description:"The tool or page you are looking for does not exist or has been moved.",backToHome:"Back to Home",goBack:"Go Back",popularTools:"Popular Tools",helpText:"If you need help finding a specific tool, please check our categories in the sidebar."},tools:{htmlExtractor:{title:"HTML Content Extractor",description:"Extract images, videos, links, and other resources from HTML code with one click",contentTypes:"Content Types",baseUrl:"Base URL",inputPlaceholder:"Paste your HTML code here...",extractionResults:"Extraction Results",noResults:"No extraction results yet. Please input HTML code and select content types to extract.",features:{imageExtraction:{title:"Image Extraction",description:"Automatically extract all image URLs from HTML, including img tags and CSS background images. Supports relative to absolute URL conversion for easy use."},mediaProcessing:{title:"Media Processing",description:"Batch extract video and audio file links, supporting multiple formats (MP4, WebM, Ogg, MP3, etc.). Automatically recognizes source files in video and audio tags."},linkAnalysis:{title:"Link Analysis",description:"Extract all hyperlinks from the page, including href attributes of a tags. Supports filtering internal and external links to help analyze website structure."}},options:{uniqueOnly:"Unique Results Only",absoluteUrls:"Convert to Absolute URLs"},types:{images:"Images",videos:"Videos",audio:"Audio",links:"Links",css:"CSS",javascript:"JavaScript",iframes:"iFrames",metadata:"Metadata",forms:"Forms"}},jsonExtractor:{title:"JSON Field Extractor",description:"Extract specific fields from JSON array data with one click",availableFields:"Available Fields",inputTitle:"Input JSON Array",inputNote:"Please paste JSON array data in the format:",inputDescription:"The tool will automatically parse the JSON and list all available fields for selection.",inputPlaceholder:"Paste your JSON here.",extractedData:"EXTRACTED DATA",fieldStatistics:"FIELD STATISTICS",noResults:"No extraction results yet. Please input JSON array data and select fields to extract.",options:{preserveStructure:"Preserve Object Structure",removeEmpty:"Remove Empty Values",flattenNested:"Flatten Nested Objects"},features:{fieldExtraction:{title:"Field Extraction",description:"Automatically parse JSON array and extract selected fields. Supports nested objects and preserves data types for accurate extraction."},smartFiltering:{title:"Smart Filtering",description:"Choose specific fields to include in the output. Option to remove empty values and preserve original object structure for clean results."},exportOptions:{title:"Export Options",description:"Copy extracted data to clipboard or download as JSON file. Includes field statistics and data analysis for better understanding of your dataset."}},errors:{invalidFormat:"Input must be a JSON array in format: [{},{},...]",emptyArray:"JSON array cannot be empty",noFields:"Please select at least one field to extract",invalidJson:"Invalid JSON format:",noData:"Please provide JSON data to extract from"}},jsonKeysExtractor:{title:"JSON Keys/Values Extractor",description:"Extract all unique keys or values from JSON objects and arrays",inputTitle:"Input JSON",inputPlaceholder:"Paste your JSON here...",extractedKeys:"Extracted Keys",extractedValues:"Extracted Values",noResults:"No keys or values extracted yet. Please input JSON to analyze.",extractionOptions:"Extraction Options",includeNested:"Include Nested Keys (with dot notation)",sortResults:"Sort Results Alphabetically",includeArrayIndices:"Include Array Indices",outputFormat:"Output Format",formatOptions:{array:"JSON Array",list:"Line-separated List",tree:"Tree Structure"},modeToggle:{keys:"Extract Keys",values:"Extract Values"},features:{keyDiscovery:{title:"Key Discovery",description:"Automatically discover all keys from complex JSON structures"},valueExtraction:{title:"Value Extraction",description:"Extract all values from complex JSON structures"},nestedSupport:{title:"Nested Support",description:"Handle nested objects with path notation for deep structures"},valueTypes:{title:"Value Types",description:"Extract values of all types (strings, numbers, booleans, etc.)"},exportOptions:{title:"Multiple Formats",description:"Export as array, list, or tree structure"}},errors:{invalidJson:"Invalid JSON format:"}},imageListProcessor:{title:"Image List Processor",description:"Input a list of image URLs and display them in a visual gallery format",inputTitle:"Input Image URLs",inputNote:"Paste your image URLs below, one per line:",inputPlaceholder:`Paste image URLs here, one per line...

Example:
https://example.com/image1.jpg
https://example.com/image2.png
https://example.com/image3.webp`,imagePreview:"Image Gallery",noResults:"No valid image URLs found. Please enter valid image URLs.",imageError:"Failed to load",emptyState:{title:"No images to display",description:"Enter some image URLs above to see them displayed in the gallery below."},features:{simple:{title:"Simple Input",description:"Just paste image URLs line by line - no complex formatting needed."},gallery:{title:"Enhanced Gallery",description:"View all images in a clean 4-column layout with full-featured lightbox preview supporting zoom, pan, and keyboard navigation."},fast:{title:"Professional Preview",description:"Advanced image viewer with zoom, scroll, drag, and full-screen capabilities for detailed inspection."}}},videoToGifConverter:{title:"Video to GIF Converter",description:"Convert videos to animated GIFs with customizable text overlays and timing controls",howToUse:{title:"How to Use",step1:'Upload a video file by clicking "Select Video File" or dragging and dropping',step2:"Adjust GIF settings (width, quality, frame rate)",step3:"Set the time range for the GIF and add text overlays if desired",step4:'Click "Generate GIF" to create your animated GIF'},tips:{title:"Tips for Best Results",tip1:"For best results, use short video clips (under 10 seconds)",tip2:"Lower frame rates (10-15 FPS) create smaller file sizes",tip3:"Smaller GIF widths (200-400px) load faster and consume less memory",tip4:"Use medium quality for a good balance between file size and image quality"},loadingVideo:"Loading video...",upload:{title:"Upload Video",dragDrop:"Drag & drop your video here",selectFile:"Select Video File",supportedFormats:"Supports MP4, AVI, MOV, WebM and other video formats (Max: 100MB)"},settings:{width:"GIF Width (px)",quality:"Quality",fps:"Frame Rate (FPS)",qualityOptions:{high:"High Quality",medium:"Medium Quality",low:"Low Quality (Smaller File)"}},preview:{title:"Video Preview & Controls"},actions:{startCapture:"Start Capture",stopCapture:"Stop Capture",generateGif:"Generate GIF"},timeRange:{title:"Time Range Selection",start:"Start",end:"End",setStart:"Set Start",setEnd:"Set End"},textOverlay:{title:"Text Overlays",add:"Add Text",text:"Text",placeholder:"Enter overlay text...",startTime:"Start Time (s)",endTime:"End Time (s)",fontSize:"Font Size",color:"Color",position:"Position",positions:{top:"Top",center:"Center",bottom:"Bottom"}},processing:{title:"Processing Video",description:"Converting your video to GIF with text overlays. This may take a moment...",preview:"Preview"},result:{title:"Generated GIF",download:"Download GIF",createNew:"Create New GIF"},features:{conversion:{title:"Video Conversion",description:"Convert videos to high-quality animated GIFs with customizable frame rate and dimensions."},textOverlay:{title:"Text Overlays",description:"Add multiple text overlays with precise timing, custom colors, fonts, and positioning."},customization:{title:"Full Customization",description:"Control every aspect including quality, size, timing, and text appearance for perfect results."}},errors:{invalidFile:"Please select a valid video file.",fileTooLarge:"File size must be less than 100MB.",processingFailed:"Failed to process video. Please try again.",noVideoSelected:"Please select a video file first.",invalidTimeRange:"Invalid time range. End time must be greater than start time."},messages:{fileLoaded:"Video file loaded successfully!",gifGenerated:"GIF generated successfully!",filePasted:"Video file pasted from clipboard!"}},apngGenerator:{title:"APNG Generator",description:"Create animated PNG files from multiple static images with customizable settings",uploadTitle:"Upload Image Frames",uploadDescription:"Drag and drop multiple images or click to select frames for your animation",selectFiles:"Select Image Files",supportedFormats:"Supported formats",settings:"Animation Settings",frameDelay:"Frame Delay",loopCount:"Loop Count",infinite:"Infinite",outputWidth:"Output Width",outputHeight:"Output Height",advancedOptions:"Advanced Options",maintainAspectRatio:"Maintain Aspect Ratio",optimizeSize:"Optimize File Size",frameList:"Animation Frames",generateAPNG:"Generate APNG",generating:"Generating...",preview:"Preview Animation",animationPreview:"Animation Preview",downloadAPNG:"Download APNG",reorderHint:"Frames will be animated in the order shown above. You can remove unwanted frames by clicking the × button.",features:{title:"Key Features",highQuality:{title:"High Quality Output",description:"Generate lossless animated PNG files with support for transparency and 24-bit color"},customizable:{title:"Fully Customizable",description:"Control frame timing, loop count, dimensions, and optimization settings"},easyToUse:{title:"Easy to Use",description:"Simple drag-and-drop interface with real-time preview and instant download"}}},backgroundRemover:{title:"Background Remover",description:"Remove backgrounds from images automatically using AI technology",features:{aiPowered:{title:"AI-Powered",description:"Advanced machine learning algorithms for precise background detection and removal"},fastProcessing:{title:"Fast Processing",description:"Quick background removal with high-quality results in seconds"},highQuality:{title:"High Quality",description:"Preserve image quality and details while removing backgrounds cleanly"}},upload:{title:"Upload Image",dragDrop:"Drag & Drop Image Here",supportedFormats:"Supports JPG, PNG, GIF, and other image formats",selectFile:"Select Image"},preview:{original:"Original Image",originalAlt:"Original image",processed:"Background Removed",processedAlt:"Processed image with background removed"},options:{title:"Output Options",model:"AI Model",outputFormat:"Output Format",transparent:"Transparent background",whiteBackground:"White background",backgroundColor:"Background Color",quality:"Output Quality"},models:{small:"Small (Fast)",medium:"Medium (Balanced)",large:"Large (Best Quality)"},actions:{remove:"Remove Background"},processing:{inProgress:"Processing...",analyzing:"Analyzing image and removing background...",pleaseWait:"This may take a few seconds"},result:{title:"Result",noResult:"No processed image yet. Please upload an image to remove its background.",complete:"Background Removal Complete",ready:"Your image is ready for download"},imageInfo:{size:"File size",format:"Format"},tips:{title:"Tips for Best Results",tip1:"Use high-resolution images with clear subject boundaries for best results",tip2:"Images with good contrast between subject and background work better",tip3:"Avoid images with complex backgrounds or similar colors to the main subject",tip4:"PNG format preserves transparency, while JPG uses white background",tip5:"Use the comparison view to see before and after results side by side"},comparison:{before:"Before",after:"After"}},csvtojson:{title:"CSV to JSON Converter",description:"Convert CSV data to JSON format with customizable parsing options",introduction:{title:"Tool Introduction",description:"Online CSV to JSON converter for converting fixed-symbol separated CSV format data to JSON format data.",usage:"Default delimiter is tab (\\t), but you can change it to comma or other symbols. Supports converting CSV to JSON objects or arrays."},example:{title:"Example",input:"CSV Input:",output:"JSON Output:"},input:{title:"Input CSV Data",placeholder:`Paste your CSV data here...

Example:
name,age,score
Li Hua,25,89
Xiao Ming,22,85`,fileUpload:"Upload CSV File"},options:{title:"Parsing Options",delimiter:"Delimiter",outputFormat:"Output Format",hasHeaders:"First Row as Headers",skipEmptyLines:"Skip Empty Lines",autoDetectNumbers:"Auto-detect Numbers",autoDetectBooleans:"Auto-detect Booleans"},delimiters:{comma:"Comma (,)",semicolon:"Semicolon (;)",tab:"Tab (\\t)",pipe:"Pipe (|)",space:"Space"},formats:{jsonObject:"JSON Objects",jsonArray:"JSON Array"},preview:{title:"Data Preview",firstRows:"first {count} rows",rowsDetected:"{count} rows detected"},convert:"Convert to JSON",output:{title:"JSON Output",complete:"Conversion Complete",recordsConverted:"{count} records converted",noOutput:"No JSON output yet. Please input CSV data to convert."}},excelTextToJson:{title:"Excel Text to JSON",description:"Convert Excel clipboard data directly to JSON format",introduction:{title:"Tool Introduction",description:"Online Excel text to JSON converter for converting tab-separated Excel data to JSON format.",usage:"Copy data from Excel and paste it here. Default delimiter is tab (\\t). First row should contain headers for object format."},example:{title:"Example",input:"Excel Input:",output:"JSON Output:"},input:{title:"Input Excel Data",placeholder:`Paste your Excel data here...

Example:
name	age	score
Li Hua	25	89
Xiao Ming	22	85`,fileUpload:"Upload Text File"},options:{title:"Parsing Options",delimiter:"Delimiter",outputFormat:"Output Format",hasHeaders:"First Row as Headers",skipEmptyLines:"Skip Empty Lines",autoDetectNumbers:"Auto-detect Numbers",autoDetectBooleans:"Auto-detect Booleans"},delimiters:{comma:"Comma (,)",semicolon:"Semicolon (;)",tab:"Tab (\\t)",pipe:"Pipe (|)",space:"Space"},formats:{jsonObject:"JSON Objects",jsonArray:"JSON Array"},preview:{title:"Data Preview",firstRows:"first {count} rows",rowsDetected:"{count} rows detected"},convert:"Convert to JSON",output:{title:"JSON Output",complete:"Conversion Complete",recordsConverted:"{count} records converted",noOutput:"No JSON output yet. Please input Excel data to convert."}},jsonPathExtractor:{title:"JSON Path Extractor",description:"Extract data from JSON using JSONPath expressions with advanced filtering capabilities",extractButton:"Extract Data",mode:{path:"Path Mode",field:"Field Mode"},tabs:{path:"Path Extractor",formatter:"JSON Formatter",excelTojson:"Excel to JSON",jsonMerge:"JSON Merge",excelTextToJson:"Excel Text to JSON",jsonToExcel:"JSON to Excel"},features:{pathExtraction:{title:"Path Extraction",description:"Use JSONPath expressions to precisely extract data from complex JSON structures with dot notation and array indexing."},filtering:{title:"Advanced Filtering",description:"Support for wildcards, array slicing, and conditional filtering to extract exactly the data you need."},export:{title:"Export Results",description:"Copy extracted data to clipboard or download as JSON file with formatted output and statistics."}},syntaxGuide:{title:"JSONPath Syntax Guide",basicSyntax:"Basic Syntax",examples:"Common Examples",rootSymbol:"Root object or element",currentSymbol:"Current object or element",childOperator:"Child element operator",recursiveMatch:"Recursive match all child elements",wildcard:"Wildcard. Match all objects or elements",subscriptOperator:"Subscript operator, JSONPath indexes start from 0",unionOperator:"Union operator, combines multiple results into array, JSONPath allows aliases",arraySlice:"Array slice operator",filterExpression:"Filter (script) expression",scriptExpression:"Script expression",exampleDesc1:"Get first book title",exampleDesc2:"Get all book authors",exampleDesc3:"Recursively find all authors",exampleDesc4:"Find books with price less than 10",tutorialTitle:"JSONPath Tutorial",tutorialDesc:"Learn more about JSONPath syntax and usage:"},inputSection:{title:"JSON Data & Path",jsonData:"JSON Data",jsonPath:"JSONPath Expression",jsonPlaceholder:"Paste your JSON data here...",pathPlaceholder:"Enter JSONPath expression (e.g., $.users[*].name)",quickPaths:"Quick Path Templates"},outputSection:{title:"Extracted Results",noResults:"No extraction results yet. Please input JSON data and JSONPath expression.",extractedData:"Extracted Data"},quickPaths:{root:"Root element",allProperties:"All properties",firstArrayItem:"First array item",allArrayItems:"All array items",lastArrayItem:"Last array item",arraySlice:"Array slice (0-2)",recursiveMatch:"Recursive match all authors",filterExpression:"Filter books with price < 10",unionOperator:"Union operator for multiple items",scriptExpression:"Script expression for last item"},success:{validJson:"Valid JSON format",extracted:"Data Extracted Successfully",arrayResults:"Found {count} array items",objectResults:"Found object with {count} properties",primitiveResult:"Found {type} value"},errors:{invalidJson:"Invalid JSON format",pathError:"JSONPath Expression Error",noMatches:"No data matches the specified path"},messages:{copied:"Extracted data copied to clipboard successfully!",copyFailed:"Failed to copy to clipboard",downloaded:"JSON file downloaded successfully!",downloadFailed:"Failed to download file"}},jsonFormatter:{title:"JSON Formatter",description:"Format, beautify, and validate JSON data with customizable indentation",inputTitle:"Input JSON",outputTitle:"Formatted JSON",inputPlaceholder:"Paste your JSON here...",noResults:"No formatted JSON yet. Please input valid JSON to format.",validJson:"Valid JSON",invalidJson:"Invalid JSON",formattingComplete:"Formatting Complete",formatOptions:"Format Options",indent:"Indent",outputFormat:"Output Format",prettyFormat:"Pretty Format",compactFormat:"Compact Format",sortKeys:"Sort Keys",escapeUnicode:"Escape Unicode",formatJson:"Format JSON",spaces2:"2 spaces",spaces4:"4 spaces",tab:"Tab",lines:"lines",characters:"characters",keys:"keys",depth:"depth",caseOptions:"Case Options",keyCase:"Key Case",valueCase:"Value Case",preserveCase:"Preserve Original Case",toUpperCase:"Convert to Uppercase",toLowerCase:"Convert to Lowercase",features:{prettyFormat:{title:"Pretty Format",description:"Automatically format and beautify JSON with proper indentation and spacing"},validation:{title:"Validation",description:"Real-time JSON validation with detailed error messages and line numbers"},customization:{title:"Customization",description:"Choose indentation size, sorting, and compact formatting options"}},messages:{formatSuccess:"JSON formatted successfully!",formatError:"Failed to format JSON: ",provideData:"Please provide JSON data to format",copied:"JSON copied to clipboard successfully!",copyFailed:"Failed to copy to clipboard",downloaded:"JSON file downloaded successfully!",downloadFailed:"Failed to download file"}},jsonMerge:{title:"JSON File Merger",description:"Merge multiple JSON files into a single file",introduction:{title:"Tool Introduction",description:"Online JSON file merger tool to combine multiple JSON files into one large JSON file.",usage:"JSON files will be merged in import order. If order matters, please note the file sequence."},fileUpload:{title:"Upload JSON Files",description:"Select multiple JSON files to merge. Files will be processed in the order shown below.",selectFiles:"Select JSON Files",supportedFormats:"Supports .json files",noFiles:"No files selected yet. Please select JSON files to merge."},filePreview:{title:"File Preview",fileName:"File Name",fileSize:"File Size",jsonStructure:"JSON Structure",arrayItems:"{count} array items",object:"JSON Object",remove:"Remove",moveUp:"Move Up",moveDown:"Move Down"},options:{title:"Merge Options",outputFileName:"Output File Name",outputFileNamePlaceholder:"Enter output file name (without extension)",defaultFileName:"merged-json"},actions:{merge:"Merge JSON Files",clear:"Clear All Files",download:"Download Merged JSON"},output:{title:"Merged JSON Output",noOutput:"No merged output yet. Please upload JSON files and click Merge.",complete:"Merge Complete",itemsMerged:"{count} items merged",downloadReady:"Merged JSON file is ready for download."},features:{multipleFiles:{title:"Multiple File Support",description:"Upload and merge multiple JSON files with drag-and-drop support."},orderControl:{title:"Order Control",description:"Reorder files before merging to control the output sequence."},preview:{title:"File Preview",description:"Preview file structure and content before merging."}},errors:{noFiles:"Please select at least one JSON file to merge",invalidJson:"Invalid JSON in file: {fileName}",mergeFailed:"Failed to merge JSON files: {error}",emptyArray:"JSON file must contain an array at the root level"},success:{filesAdded:"{count} file(s) added successfully",mergeComplete:"JSON files merged successfully!"}},jsonToExcel:{title:"JSON to Excel/CSV/SQL Converter",description:"Convert JSON data to Excel, CSV, or SQL format with customizable options",inputTitle:"Input JSON Data",outputTitle:"Excel Output",csvOutputTitle:"CSV Output",sqlOutputTitle:"SQL Output",inputPlaceholder:"Paste your JSON array here...",noResults:"No conversion results yet. Please input JSON data to convert.",conversionComplete:"Conversion completed successfully!",readyForDownload:"Excel file is ready for download.",csvReadyForDownload:"CSV file is ready for download.",sqlReadyForDownload:"SQL file is ready for download.",previewTitle:"Data Preview",convertToExcel:"Convert to Excel",convertToCsv:"Convert to CSV",convertToSql:"Generate SQL",showingRows:"Showing {shown} of {total} rows",options:{conversionType:"Conversion Type",includeHeaders:"Include Headers",autoFitColumns:"Auto-fit Columns",sheetName:"Sheet Name",sheetNamePlaceholder:"Enter sheet name",delimiter:"Delimiter",quoteChar:"Quote Character",flattenNested:"Flatten Nested Objects",comma:"Comma",semicolon:"Semicolon",tab:"Tab",pipe:"Pipe",doubleQuote:"Double Quote",singleQuote:"Single Quote",none:"None",sqlOptions:"SQL Options",tableName:"Table Name",tableNamePlaceholder:"Enter table name",sqlType:"SQL Type",whereField:"WHERE Field",whereFieldPlaceholder:"Field for WHERE clause",escapeValues:"Escape Values",batchInsert:"Batch Insert"},features:{conversion:{title:"Smart Conversion",description:"Automatically convert JSON arrays to Excel, CSV, or SQL format with proper data type handling."},formatting:{title:"Excel Formatting",description:"Generate properly formatted Excel files with headers, auto-sized columns, and multiple sheets."},batch:{title:"Batch Processing",description:"Handle large datasets efficiently with preview and bulk download capabilities."}},errors:{emptyInput:"Please provide JSON data to convert",invalidJson:"Invalid JSON format. Please check your input.",notArray:"Input must be a JSON array",emptyArray:"JSON array cannot be empty",conversionFailed:"Failed to convert JSON",emptyTableName:"Please provide a table name",emptyWhereField:"Please provide a WHERE field for UPDATE statements"},success:{conversionComplete:"JSON converted to Excel successfully!",csvConversionComplete:"JSON converted to CSV successfully!",sqlConversionComplete:"SQL statements generated successfully!"}},excelToJson:{title:"Excel to JSON Converter",description:"Convert Excel files to JSON format with flexible parsing options",inputTitle:"Upload Excel File",outputTitle:"JSON Output",uploadDescription:"Select an Excel file to convert to JSON",selectFile:"Select Excel File",supportedFormats:"Supports .xlsx, .xls, .csv, and .ods files",noResults:"No conversion results yet. Please upload an Excel file.",conversionComplete:"Conversion completed successfully!",recordsCount:"{count} records converted",convert:"Convert to JSON",fileSelected:"File selected successfully",options:{title:"Conversion Options",firstRowAsHeaders:"First row as headers",skipEmptyRows:"Skip empty rows",sheetIndex:"Sheet"},errors:{conversionFailed:"Failed to convert Excel file",noFileSelected:"Please select an Excel file to convert",xlsxRequired:"XLSX library is required for Excel file parsing. Please install: npm install xlsx"},features:{parsing:{title:"Excel Parsing",description:"Parse Excel files with support for multiple sheets, formulas, and data types."},conversion:{title:"Flexible Conversion",description:"Convert with options for headers, empty rows, and specific sheet selection."},options:{title:"Conversion Options",description:"Customize output with header handling, empty row skipping, and sheet selection."}}},jsonToSql:{title:"JSON to SQL Converter",description:"Generate SQL INSERT, UPDATE, or CREATE TABLE statements from JSON data",inputTitle:"Input JSON Data",outputTitle:"SQL Output",inputPlaceholder:"Paste your JSON array here...",noResults:"No SQL statements generated yet. Please input JSON data and configure options.",conversionComplete:"SQL generation completed successfully!",statementsGenerated:"{count} SQL statements generated",convert:"Generate SQL",options:{title:"SQL Options",tableName:"Table Name",tableNamePlaceholder:"Enter table name",sqlType:"SQL Type",whereField:"WHERE Field",whereFieldPlaceholder:"Field for WHERE clause",escapeValues:"Escape Values",batchInsert:"Batch Insert"},features:{insertion:{title:"Multiple SQL Types",description:"Generate INSERT, UPDATE, or CREATE TABLE statements from JSON data."},customization:{title:"Customization",description:"Configure table names, SQL types, and field mappings for your database."},security:{title:"SQL Security",description:"Automatic value escaping to prevent SQL injection vulnerabilities."}},errors:{emptyInput:"Please provide JSON data to convert",emptyTableName:"Please provide a table name",emptyWhereField:"Please provide a WHERE field for UPDATE statements",invalidJson:"Invalid JSON format. Please check your input.",notArray:"Input must be a JSON array",emptyArray:"JSON array cannot be empty",conversionFailed:"Failed to generate SQL statements"},success:{conversionComplete:"SQL statements generated successfully!"}},fileRenamer:{title:"File Renamer Tool",description:"Batch rename files with multiple modes - local processing for privacy",uploadArea:{title:"Drag & Drop Files Here",subtitle:"or click to select files",selectFiles:"Select Files"},fileCount:"Total files: {count}",totalSize:"Total size: {size}",tabs:{sequential:"Sequential",replace:"Find & Replace",case:"Case Transform",insert:"Insert Text",truncate:"Truncate",script:"Generate Script"},sequential:{prefix:"Prefix",prefixPlaceholder:"e.g., photo_",startNumber:"Start Number",padding:"Number Padding"},replace:{findText:"Find Text",findPlaceholder:"Text to find",replaceText:"Replace With",replacePlaceholder:"Replacement text",caseSensitive:"Case Sensitive"},case:{transformation:"Case Transformation",uppercase:"UPPERCASE",lowercase:"lowercase",capitalize:"Capitalize Words"},insert:{text:"Text to Insert",textPlaceholder:"Text to insert",position:"Position",prefix:"At Beginning",suffix:"At End",atIndex:"At Index",index:"Insert Index"},truncate:{startIndex:"Start Index",endIndex:"End Index",description:"Extract substring from start index to end index (0-based)"},script:{scriptType:"Script Type",windows:"Windows Batch (.bat)",linux:"Linux Shell (.sh)",autoGenerated:"Auto-generated script",scriptPreview:"Script Preview",downloadScript:"Download Script",copyScript:"Copy Script",noContent:"No script content available. Add files and apply renaming options to generate script.",instructions:{title:"Instructions",description:'This tool generates scripts to rename your files. The script is automatically generated based on your files and renaming options. Click "Download Script" to download it. Place the script in the directory with your files and run it to perform the renaming operation.'}},actions:{preview:"Preview",apply:"Apply Rename",download:"Download ZIP",clear:"Clear Files"},sorting:{title:"Sorting",natural:"Natural Sort",filename:"Filename Order",modifiedTime:"Modified Time",modifiedTimeDesc:"Modified Time (Descending)",random:"Random",reverse:"Reverse Current Order",manual:"Manual Sort (Drag & Drop)"},fileList:{title:"File List",drag:"Drag",originalName:"Original Name",newName:"New Name",size:"Size",type:"Type",dragHint:"Drag files to reorder them manually"},messages:{filesAdded:"{count} file(s) added successfully!",renameApplied:"Rename applied successfully!",downloadStarted:"Download started! Please check your downloads folder.",downloadError:"Download failed! Please try again.",filesCleared:"All files cleared!",noFilesToProcess:"No files to process! Please add files first.",noScriptToDownload:"No script to download! Please generate a script first.",noScriptToCopy:"No script to copy! Please generate a script first.",scriptDownloaded:'Script "{fileName}" downloaded successfully!',scriptCopied:"Script copied to clipboard successfully!",scriptCopyFailed:"Failed to copy script to clipboard!"}},imageCompressor:{title:"Image Compressor Master",description:"Efficient online image compression tool with batch processing and local privacy",settings:"Compression Settings",quality:"Quality",smaller:"Smaller",larger:"Larger",outputFormat:"Output Format",keepOriginal:"Keep Original Format",maxWidth:"Max Width",uploadTitle:"Drag & Drop Images or Click to Select",uploadDescription:"Support multiple images, local processing, no upload to server",supportedFormats:"Supported formats",selectFiles:"Select Files",imageList:"Image List",compressing:"Compressing...",compressAll:"Compress All",downloadAll:"Download All",compress:"Compress",remove:"Remove",originalSize:"Original Size",compressedSize:"Compressed Size",spaceSaved:"Space Saved",original:"Original",compressed:"Compressed",imagePreview:"Image Preview",originalImage:"Original Image",compressedImage:"Compressed Image",size:"Size",dimensions:"Dimensions",saved:"Saved",status:{pending:"Pending",compressing:"Processing",completed:"Completed",error:"Failed"},features:{efficient:{title:"High Efficiency",description:"Advanced compression algorithms maintain image quality while reducing file size by 40-80%."},secure:{title:"Privacy Protection",description:"All processing happens locally in your browser. Images never uploaded to any server."},batch:{title:"Batch Processing",description:"Process multiple images simultaneously with progress tracking and batch download."}},errors:{noValidImages:"No valid image files found",compressionFailed:"Failed to compress {filename}"},success:{compressionComplete:"All images compressed successfully!",downloadComplete:"Batch download completed!",pasteSuccess:"Images pasted successfully!"}},imageToGifConverter:{title:"Image to GIF Converter",description:"Convert multiple images to animated GIFs with customizable timing and settings",howToUse:{title:"How to Use",step1:'Upload multiple image files by clicking "Select Image Files" or dragging and dropping',step2:"Adjust GIF settings (width, quality, frame rate)",step3:"Set individual frame delays and reorder images as needed",step4:'Click "Generate GIF" to create your animated GIF'},tips:{title:"Tips for Best Results",tip1:"For best results, use images with similar dimensions",tip2:"Lower frame rates (1-5 FPS) create smoother animations",tip3:"Smaller GIF widths (200-400px) load faster and consume less memory",tip4:"Use medium quality for a good balance between file size and image quality"},upload:{title:"Upload Images",dragDrop:"Drag & drop your images here",selectFile:"Select Image Files",supportedFormats:"Supports JPG, PNG, WebP and other image formats"},settings:{width:"GIF Width (px)",quality:"Quality",fps:"Frame Rate (FPS)",loopCount:"Loop Count",infinite:"Infinite",qualityOptions:{high:"High Quality",medium:"Medium Quality",low:"Low Quality (Smaller File)"}},preview:{title:"Image Preview & Controls",selectedImages:"Selected Images",moveUp:"Move First to End",moveDown:"Move Last to Start",reverse:"Reverse Order",shuffle:"Shuffle Images"},actions:{generateGif:"Generate GIF"},processing:{title:"Processing Images",description:"Converting your images to GIF. This may take a moment...",preview:"Preview"},result:{title:"Generated GIF",download:"Download GIF",createNew:"Create New GIF"},features:{conversion:{title:"Image Conversion",description:"Convert multiple images to high-quality animated GIFs with customizable frame rate and dimensions."},customization:{title:"Full Customization",description:"Control every aspect including quality, size, timing, and loop count for perfect results."},animation:{title:"Animation Control",description:"Set individual frame delays, reorder images, and control animation loop behavior."}},errors:{noImages:"Please select valid image files.",processingFailed:"Failed to process images. Please try again.",noImagesSelected:"Please select image files first.",fileProcessing:"Failed to process selected files."},messages:{filesAdded:"{count} image(s) added successfully!",gifGenerated:"GIF generated successfully!",filesPasted:"{count} image(s) pasted from clipboard!",cleared:"All images cleared!"}},gifEditor:{title:"GIF Editor",description:"Split, edit and modify GIF frames with custom timing and settings",howToUse:{title:"How to Use",step1:'Upload a GIF file by clicking "Select GIF File" or dragging and dropping',step2:"Adjust GIF settings (width, quality, frame rate)",step3:"Modify individual frame delays, reorder frames or delete unwanted frames",step4:'Click "Generate GIF" to create your edited GIF'},tips:{title:"Tips for Best Results",tip1:"For best results, use GIFs with consistent frame dimensions",tip2:"Frame delays below 20ms may not display correctly in some browsers",tip3:"Smaller GIF widths (200-400px) load faster and consume less memory",tip4:"Medium quality provides a good balance between file size and image quality"},upload:{title:"Upload GIF",dragDrop:"Drag GIF here",selectFile:"Select GIF File",supportedFormats:"GIF format only (max: 50MB)"},settings:{width:"GIF Width (pixels)",quality:"Quality",fps:"Frame Rate (FPS)",preserveOriginal:"Preserved from original GIF",qualityOptions:{high:"High Quality",medium:"Medium Quality",low:"Low Quality (smaller file)"}},preview:{title:"GIF Preview and Controls",originalGif:"Original GIF",frames:"Frames",frame:"Frame",delay:"Delay",dimensions:"Dimensions",pixels:"pixels",moveUp:"Move First to End",moveDown:"Move Last to Beginning",reverse:"Reverse Order",shuffle:"Shuffle Frames"},actions:{generateGif:"Generate GIF"},processing:{title:"Processing GIF",description:"Editing your GIF frames. This may take some time...",preview:"Preview"},result:{title:"Generated GIF",download:"Download GIF",createNew:"Edit Another GIF"},features:{frameEditing:{title:"Frame Editing",description:"Split GIF into individual frames and modify delay, order or delete unwanted frames."},customization:{title:"Full Customization",description:"Control quality, size, timing, frame order and all aspects for perfect results."},animation:{title:"Animation Control",description:"Set individual frame delays, reorder frames and control animation loop behavior."}},errors:{noGif:"Please select a valid GIF file.",invalidFile:"Please select a valid GIF file.",fileTooLarge:"File size must be less than 50MB.",processingFailed:"Failed to process GIF, please try again.",noFrames:"No frames to process. Please upload a GIF file first.",fileProcessing:"Failed to process selected file.",frameParsingFailed:"Failed to parse GIF frames."},messages:{fileLoaded:"GIF file loaded successfully!",gifGenerated:"GIF generated successfully!",filePasted:"Pasted GIF file from clipboard!"}},svgEditor:{title:"SVG Editor",description:"Edit SVG code with real-time preview and visual editor",howToUse:{title:"How to Use",step1:"Edit SVG code directly in the editor or use the visual editor",step2:"See real-time preview of your SVG",step3:"Load tutorials to learn SVG basics",step4:"Download your SVG when finished"},tips:{title:"Tips for Best Results",tip1:"Use the visual editor to quickly create basic shapes",tip2:"Load tutorials to learn advanced SVG techniques",tip3:"Copy SVG code to clipboard for use in other applications",tip4:"Download SVG files for use in web projects"},editor:{title:"SVG Code Editor",loadExample:"Load Example",clear:"Clear",placeholder:"Enter your SVG code here...",lines:"Lines",copy:"Copy to Clipboard"},preview:{title:"SVG Preview",empty:"No SVG code to display. Add some SVG code to see the preview.",dimensions:"Dimensions",download:"Download SVG"},visualEditor:{title:"Visual Editor",shapes:"Shapes",history:"History",tools:"Tools",delete:"Delete Selected",clear:"Clear Canvas",undo:"Undo",redo:"Redo",properties:"Properties",fill:"Fill Color",stroke:"Stroke Color",strokeWidth:"Stroke Width",rotation:"Rotation",transparent:"Transparent",noSelection:"No shape selected"},shapes:{rectangle:"Rectangle",circle:"Circle",ellipse:"Ellipse",line:"Line",triangle:"Triangle",path:"Path",polygon:"Polygon",star:"Star",heart:"Heart",quadraticCurve:"Quadratic Curve",cubicCurve:"Cubic Curve",arcCurve:"Arc Curve"},tutorials:{title:"SVG Tutorials",viewTutorial:"View Tutorial",basicShapes:"Basic Shapes",basicShapesDesc:"Learn to create rectangles, circles, and ellipses",paths:"Paths and Polylines",pathsDesc:"Draw custom shapes using paths",gradients:"Gradients and Patterns",gradientsDesc:"Add gradients and patterns to your SVGs"},errors:{copyFailed:"Failed to copy code to clipboard",noSvg:"No SVG code to download"},messages:{exampleLoaded:"Example SVG loaded successfully!",editorCleared:"Editor cleared!",codeCopied:"SVG code copied to clipboard!",svgDownloaded:"SVG downloaded successfully!",tutorialLoaded:"Tutorial loaded!",shapeDeleted:"Selected shape deleted!",canvasCleared:"Canvas cleared!"}},imageWatermark:{title:"Image Watermark Master",description:"Add text or image watermarks to your photos with customizable styles and positions",settings:"Watermark Settings",watermarkType:"Watermark Type",textWatermark:"Text Watermark",textWatermarkDesc:"Add text watermark to images",imageWatermark:"Image Watermark",imageWatermarkDesc:"Add image watermark to images",combinedWatermark:"Combined Watermark",combinedWatermarkDesc:"Add both text and image watermarks",textSettings:"Text Settings",watermarkText:"Watermark Text",textPlaceholder:"Enter watermark text",fontSize:"Font Size",fontColor:"Font Color",fontFamily:"Font Family",imageSettings:"Image Settings",watermarkImage:"Watermark Image",uploadWatermark:"Upload Watermark Image",watermarkPreview:"Watermark Preview",removeWatermark:"Remove Watermark",imageWidth:"Image Width",imageOpacity:"Image Opacity",positionSettings:"Position Settings",position:"Position",margin:"Margin",advancedSettings:"Advanced Settings",opacity:"Opacity",rotation:"Rotation",scale:"Scale",topLeft:"Top Left",topCenter:"Top Center",topRight:"Top Right",centerLeft:"Center Left",center:"Center",centerRight:"Center Right",bottomLeft:"Bottom Left",bottomCenter:"Bottom Center",bottomRight:"Bottom Right",uploadTitle:"Drag & Drop Images or Click to Select",uploadDescription:"Support multiple images, local processing, no upload to server",supportedFormats:"Supported formats",selectFiles:"Select Files",imageList:"Image List",processing:"Processing...",processAll:"Process All",downloadAll:"Download All",process:"Process",remove:"Remove",originalSize:"Original Size",processedSize:"Processed Size",processed:"Processed",original:"Original",imagePreview:"Image Preview",originalImage:"Original Image",processedImage:"Processed Image",size:"Size",dimensions:"Dimensions",gifWarning:"Watermark will be applied to all frames of the animated GIF",status:{pending:"Pending",processing:"Processing",completed:"Completed",error:"Failed"},features:{watermark:{title:"Multiple Watermark Types",description:"Add text watermarks, image watermarks, or combine both with full customization options."},batch:{title:"Batch Processing",description:"Process multiple images simultaneously with progress tracking and batch download."},customization:{title:"Full Customization",description:"Adjust watermark position, opacity, rotation, scale, font properties, and more."}},errors:{noValidImages:"No valid image files found",invalidWatermark:"Please select a valid image file for watermark",noWatermarkImage:"Please upload a watermark image",noWatermarkText:"Please enter watermark text",watermarkProcessingFailed:"Failed to process watermark image",processingFailed:"Failed to process {filename}"},success:{processingComplete:"All images processed successfully!",downloadComplete:"Batch download completed!",pasteSuccess:"Images pasted successfully!"}},faviconGenerator:{title:"Favicon Generator",description:"Generate professional favicons in multiple sizes and formats from any image",uploadSection:"Upload Image",uploadTitle:"Drag & Drop Image or Click to Select",uploadDescription:"Upload any image to create favicons for your website",supportedFormats:"Supported formats",selectImage:"Select Image",cropImage:"Crop Image",originalImage:"Original Image",originalImageDescription:"Full resolution image preview - this is your source image",imageSize:"Image Size",cropPreview:"Crop Preview",selectAnother:"Select Another",cropInstruction:"Drag the crop area to move it, or drag the corner handles to resize. The selected square area will be used to generate favicons.",cropInstructionAdvanced:"Drag to move the crop area, resize by dragging corners, or use mouse wheel to zoom. The selected square area will be used for favicon generation.",outputFormat:"Output Format",sizes:"Favicon Sizes",generate:"Generate Favicons",generating:"Generating...",generatedFavicons:"Generated Favicons",downloadAll:"Download All as ZIP",usageInstructions:"How to Use Favicons",htmlUsage:"HTML Implementation",tips:"Best Practices",tip1:"Use simple, recognizable designs that work at small sizes",tip2:"Ensure good contrast for visibility across different backgrounds",tip3:"Test your favicon on various devices and browsers",tip4:"Place favicon.ico in your website root directory for automatic detection",pasteHint:"Tip: You can also paste images directly from your clipboard",features:{cropping:{title:"Smart Cropping",description:"Interactive crop tool to select the perfect square area from your image with real-time preview."},multiSize:{title:"Multiple Sizes",description:"Generate favicons in all standard sizes (16px to 128px) for optimal compatibility across devices."},formats:{title:"Multiple Formats",description:"Export in ICO, PNG, or JPG formats to meet different browser and platform requirements."}},errors:{invalidFile:"Please select a valid image file",generationFailed:"Failed to generate favicons",downloadFailed:"Failed to download files",imageLoadFailed:"Failed to load image",fileReadFailed:"Failed to read file"},success:{imageLoaded:"Image loaded successfully!",generationComplete:"Favicons generated successfully!",downloadComplete:"Download completed!"},messages:{pasteSuccess:"Image pasted from clipboard and processing started!"}},cookieToJson:{title:"Cookie ↔ JSON Converter",description:"Convert between cookie strings and JSON objects in real-time with bidirectional support",inputTitle:"Cookie String",outputTitle:"JSON Object",format:"Format",formatButton:"Format",convertLeft:"Convert to Cookie",convertRight:"Convert to JSON",inputNote:"Paste cookie string in the format:",inputPlaceholder:`Paste your cookie string here, e.g.:
sessionId=abc123; userId=12345; theme=dark; lang=en-US

Supported formats:
- Standard cookie format: name1=value1; name2=value2
- URL-encoded values are automatically decoded
- Handles cookies without values (flags)`,parseOptions:"Parse Options",noResults:"No conversion results yet. Please input data to convert.",error:"Parse Error",success:"Parsing Successful",conversionComplete:"Conversion Complete",cookiesFound:"{count} cookies found",statistics:"{total} total cookies, {nonEmpty} with values",options:{decodeValues:"Decode URL-encoded Values",removeEmpty:"Remove Empty Values",formatOutput:"Format JSON Output"},features:{parsing:{title:"Cookie Parsing",description:"Automatically parse cookie strings with support for standard HTTP cookie format and URL decoding."},conversion:{title:"Bidirectional Conversion",description:"Convert between cookie strings and JSON objects in both directions with real-time conversion."},export:{title:"Export Options",description:"Copy to clipboard or download as JSON file with statistics and validation feedback."}},errors:{noCookies:"No valid cookies found in the input string",parseError:"Failed to parse cookie string: {error}",conversionFailed:"Conversion failed. Please check your input format.",unsupportedFormat:"Unsupported format selected",invalidJson:"Invalid JSON format. Please check your input.",formatFailed:"Format failed. Please check your input format."},messages:{copied:"Content copied to clipboard successfully!",copyFailed:"Failed to copy to clipboard",downloaded:"JSON file downloaded successfully!",downloadFailed:"Failed to download file"}},universalConverter:{title:"Universal Format Converter",description:"Convert between JSON, XML, and HTTP query parameters in real-time",inputTitle:"Input",outputTitle:"Output",format:"Format",formatButton:"Format",conversionDirection:"Conversion Direction",conversionDirectionDescription:"Choose which direction to convert or swap panels",swap:"Swap Panels",convertLeft:"Convert to Left",convertRight:"Convert to Right",features:{bidirectional:{title:"Bidirectional Conversion",description:"Convert between any of the supported formats in both directions"},realtime:{title:"Real-time Conversion",description:"See conversions happen instantly as you type with automatic detection"},validation:{title:"Format Validation",description:"Built-in validation for all supported formats with detailed error messages"}},errors:{conversionFailed:"Conversion failed. Please check your input format.",unsupportedFormat:"Unsupported format selected",invalidJson:"Invalid JSON format. Please check your input.",invalidXml:"Invalid XML format. Please check your input.",invalidQuery:"Invalid query parameter format. Please check your input.",xmlGenerationFailed:"Failed to generate XML output",queryGenerationFailed:"Failed to generate query parameter output",formatFailed:"Format failed. Please check your input format."}},qrCodeTool:{title:"QR Code Generator & Recognizer",description:"Generate QR codes from text and recognize QR codes from images with batch processing support",tabs:{generate:"Generate",recognize:"Recognize"},generate:{inputTitle:"Generate QR Code",textInputLabel:"Text to encode",textInputPlaceholder:`Enter text to generate QR code...
For batch mode, enter one text per line`,modeLabel:"Generation Mode",singleMode:"Single QR Code",batchMode:"Batch QR Codes",singleModeHint:"Generates one QR code from all the text",batchModeHint:"Generates multiple QR codes, one for each line of text",generateSingleButton:"Generate QR Code",generateBatchButton:"Generate Batch QR Codes",singleGeneratedTitle:"Generated QR Code",batchGeneratedTitle:"Generated QR Codes ({count})",downloadAll:"Download All as ZIP"},recognize:{uploadTitle:"Recognize QR Codes",uploadInstruction:"Upload QR Code Images",uploadDescription:"Drag and drop images here or click to select files. Supports JPG, PNG, WebP formats.",pasteHint:"Tip: You can also paste images directly from your clipboard",selectFiles:"Select Files",resultsTitle:"Recognition Results",copyAll:"Copy All Results",recognitionFailed:"Failed to recognize QR code"},features:{batch:{title:"Batch Processing",description:"Generate multiple QR codes at once or recognize QR codes from multiple images simultaneously."},generate:{title:"QR Generation",description:"Create high-quality QR codes from any text input with customizable options."},recognize:{title:"QR Recognition",description:"Extract data from QR codes in images with support for various image formats."}},messages:{generateSuccess:"QR code generated successfully!",batchGenerateSuccess:"Generated {count} QR codes successfully!",downloadAllSuccess:"All QR codes downloaded as ZIP file!",copySuccess:"QR code copied to clipboard!",copyAllSuccess:"All recognition results copied to clipboard!",recognitionComplete:"QR code recognition completed!",pasteSuccess:"Image pasted from clipboard and processing started!"},errors:{generateFailed:"Failed to generate QR code. Please try again.",batchGenerateFailed:"Failed to generate batch QR codes. Please check your input.",emptyBatch:"Please enter at least one text to generate QR codes.",downloadAllFailed:"Failed to download all QR codes. Please try again.",copyFailed:"Failed to copy QR code to clipboard.",copyAllFailed:"Failed to copy recognition results to clipboard.",noValidImages:"Please select valid image files.",noQRCodeFound:"No QR code found in the image.",noResultsToCopy:"No successful recognition results to copy."}},webRtcFileTransfer:{title:"WebRTC File Transfer",description:"Transfer files directly between devices on the same network without intermediaries",deviceId:"Device ID",status:{disconnected:"Disconnected",connecting:"Connecting",connected:"Connected"},discovery:{title:"Device Discovery",search:"Search for Devices",searching:"Searching...",foundDevices:"Found Devices",noDevices:"No devices found on the network",unknownDevice:"Unknown Device",connect:"Connect",demoDevice1:"Demo Device 1",demoDevice2:"Demo Device 2",connected:"Connected to signaling server",disconnected:"Not connected to signaling server"},connectionRequests:{title:"Connection Requests",accept:"Accept",reject:"Reject"},transfer:{title:"File Transfer",selectFile:"Select File to Send",send:"Send File",sending:"Sending...",progress:"Transfer Progress",receivedFiles:"Received Files",download:"Download",connectFirst:"Please connect to a device first"},logs:{title:"Activity Log",startDiscovery:"Starting device discovery",devicesFound:"Found {count} devices",connectingToDevice:"Connecting to device {deviceId}",connectionEstablished:"Connection established successfully",fileSelected:"Selected file: {name} ({size})",sendingFile:"Sending file: {name}",fileSent:"File {name} sent successfully",receivingFile:"Receiving file: {name} ({size})",fileReceived:"Received file: {name}",unexpectedDataChunk:"Received unexpected data chunk",initialized:"WebRTC file transfer initialized",webRTCInitialized:"WebRTC initialized successfully",webRTCInitFailed:"Failed to initialize WebRTC: {error}",dataChannelOpened:"Data channel opened",dataChannelClosed:"Data channel closed",dataChannelError:"Data channel error: {error}",messageReceived:"Message received of type: {type}",iceCandidateGenerated:"ICE candidate generated",iceCandidateSent:"ICE candidate sent to signaling server",iceCandidateAdded:"ICE candidate added",connectionStateChange:"Connection state changed to: {state}",channelNotReady:"Data channel is not ready for sending",sendFileFailed:"Failed to send file: {error}",signalServerConnected:"Connected to signaling server",signalServerDisconnected:"Disconnected from signaling server",signalServerConnectionFailed:"Failed to connect to signaling server: {error}",signalServerError:"Signaling server error: {error}",connectingToSignalServer:"Connecting to signaling server on port {port}",notConnectedToSignalServer:"Not connected to signaling server",receivedDeviceId:"Received device ID: {id}",deviceDiscovered:"New device discovered: {id}",deviceDisconnected:"Device disconnected: {id}",messageParseError:"Failed to parse message from server: {error}",offerSent:"Offer sent to target device",answerSent:"Answer sent to source device",offerHandlingFailed:"Failed to handle offer: {error}",answerHandlingFailed:"Failed to handle answer: {error}",iceCandidateFailed:"Failed to add ICE candidate: {error}",connectionRequestReceived:"Connection request received from device {id}",unexpectedOffer:"Unexpected offer received from device {id}",connectionTimeout:"Connection timed out, resetting connection",connectionReset:"Connection reset to initial state",acceptingConnection:"Accepting connection from device {id}",connectionRequestRejected:"Connection request from device {id} rejected"}},textSteganography:{title:"Text Steganography",description:"Hide secret messages within plain text using invisible Unicode characters",encryptionTitle:"Encryption",decryptionTitle:"Decryption",visibleText:"Visible Text",visibleTextPlaceholder:"Enter the text that will be visible",hiddenText:"Hidden Text",hiddenTextPlaceholder:"Enter the secret message to hide",steganographyText:"Steganography Text",steganographyTextPlaceholder:"Paste the steganography text here to decode",steganographyResult:"Steganography Result",decodedText:"Decoded Text",generateSteganography:"Generate Steganography Text",features:{encryption:{title:"Text Encryption",description:"Hide secret messages within plain text using invisible Unicode characters"},decryption:{title:"Text Decryption",description:"Extract hidden messages from steganography text"},security:{title:"Secure Communication",description:"Share sensitive information discreetly through seemingly normal text"}},errors:{decodingFailed:"Failed to decode the steganography text"}},imageSteganography:{title:"Image Steganography",description:"Hide secret images within other images using LSB (Least Significant Bit) steganography technique",canvasTitle:"Image Canvas",decodedImageTitle:"Decoded Image",operationsTitle:"Operations",decodingTitle:"Decoding",canvasPlaceholder:"Select an image to begin",decodedImagePlaceholder:"Select an image to decode",exportImage:"Export Image",exportDecodedImage:"Export Decoded Image",modeToggle:{encode:"Encode",decode:"Decode"},step1:"Step 1: Select Image to Hide",step1Desc:"Choose the image you want to hide within another image",step2:"Step 2: Save Hidden Image Data",step2Desc:"Save the pixel data of the hidden image for steganography",step3:"Step 3: Select Target Image",step3Desc:"Choose the image in which you want to hide the secret image",step4:"Step 4: Start Encryption",step4Desc:"Embed the hidden image data into the target image",selectHiddenImage:"Select Hidden Image",saveHiddenData:"Save Hidden Image Data",selectTargetImage:"Select Target Image",startEncryption:"Start Encryption",decodeStep1:"Step 1: Select Image to Decode",decodeStep1Desc:"Choose the image containing hidden data to decode",decodeStep2:"Step 2: Start Decoding",decodeStep2Desc:"Extract the hidden image from the selected image",selectImageToDecode:"Select Image to Decode",startDecoding:"Start Decoding",decodedImagePreview:"Decoded Image Preview",features:{encryption:{title:"Image Encryption",description:"Hide secret images within other images using advanced steganography techniques"},decryption:{title:"Image Decryption",description:"Extract hidden images from steganography images"},steganography:{title:"LSB Steganography",description:"Utilize Least Significant Bit technique to embed data invisibly"},extraction:{title:"Data Extraction",description:"Recover hidden data from steganography images"},export:{title:"Export Results",description:"Download the steganography result as a PNG image file"},result:{title:"Decoded Result",description:"View and export the extracted hidden image"}},messages:{imageLoaded:"Image loaded successfully",dataSaved:"Hidden image data saved successfully",encryptionComplete:"Encryption completed successfully",decryptionComplete:"Decryption completed successfully",imageExported:"Image exported successfully",canvasCleared:"Canvas cleared"},errors:{imageLoadFailed:"Failed to load image",dataSaveFailed:"Failed to save hidden image data",encryptionFailed:"Failed to encrypt image",decryptionFailed:"Failed to decrypt image",exportFailed:"Failed to export image"}},textProcessor:{title:"Text Processor",description:"Process text with URL encoding/decoding, Base64 encoding/decoding, and hashing functions",inputTitle:"Input Text",outputTitle:"Output Text",inputPlaceholder:"Enter or paste your text here...",outputPlaceholder:"Processed text will appear here...",chars:"Characters",words:"Words",lines:"Lines",operations:"Text Operations",urlEncode:"URL Encode",urlDecode:"URL Decode",base64Encode:"Base64 Encode",base64Decode:"Base64 Decode",md5Hash:"MD5 Hash",sha256Hash:"SHA-256 Hash",exampleText:"Hello World! This is a sample text for processing. https://example.com/?param=value",features:{urlEncoding:{title:"URL Encoding",description:"Encode or decode URLs and URI components for safe transmission"},base64:{title:"Base64 Encoding",description:"Encode or decode data using Base64 encoding scheme"},hashing:{title:"Hash Functions",description:"Generate cryptographic hashes using MD5 or SHA-256 algorithms"}},errors:{encodingError:"Failed to encode text. Please check your input.",decodingError:"Failed to decode text. Please check your input.",hashingError:"Failed to generate hash. Please check your input."}},heartCollage:{title:"Shape Collage Generator",description:"Create beautiful collages in various shapes filled with your images",uploadTitle:"Upload Images",uploadDescription:"Drag and drop images here or click to select files",supportedFormats:"Supported formats",selectFiles:"Select Files",settings:"Collage Settings",canvasSize:"Canvas Size",shape:"Shape",imageShape:"Image Shape",arrangement:"Arrangement",random:"Random",grid:"Grid",fitAll:"Fit All Images",spacing:"Spacing",additionalOptions:"Additional Options",backgroundColor:"Background Color",borderOptions:"Border Options",showBorder:"Show Border",small:"Small",medium:"Medium",large:"Large",heart:"Heart",square:"Square",rectangle:"Rectangle",circle:"Circle",star:"Star",rounded:"Rounded",canvas:"Collage Canvas",images:"images",autoArrange:"Auto Arrange",downloadCollage:"Download Collage",selectedImages:"Selected Images",dragInstructions:"Drag images to reposition them within the shape. Drag corners to resize.",features:{collage:{title:"Shape Collage",description:"Create beautiful collages in various shapes filled with your images"},customization:{title:"Customization Options",description:"Customize canvas size, shapes, image shapes, spacing, and more"},export:{title:"Export Results",description:"Download your collage as a high-quality PNG image"}},messages:{filesAdded:"{count} file(s) added successfully",arranged:"Images arranged within the shape",downloadSuccess:"Collage downloaded successfully!",cleared:"All images cleared"},errors:{noImages:"Please select valid image files",fileProcessing:"Failed to process selected files",noImagesSelected:"Please select at least one image to arrange",downloadFailed:"Failed to download collage"}},jsonNumberToText:{title:"JSON Number to Text Converter",description:"Convert numeric values to text strings in JSON objects and arrays",inputTitle:"Input JSON",inputPlaceholder:"Paste your JSON here...",conversionOptions:"Conversion Options",conversionMode:"Conversion Mode",targetFields:"Target Fields",preserveDecimals:"Preserve Decimal Places",decimalPlaces:"Decimal Places",addQuotes:"Add Quotes Around Numbers",convertAll:"Convert All Numbers",specificFields:"Specific Fields Only",integersOnly:"Integers Only",decimalsOnly:"Decimals Only",analysisResults:"Analysis Results",numbersFound:"Numbers found",fieldsToConvert:"Fields to convert",numericFields:"Numeric fields",convertButton:"Convert Numbers to Text",convertedJson:"Converted JSON",conversionComplete:"Conversion Complete",noResults:"No results yet. Input JSON to convert numbers to text.",features:{typeConversion:{title:"Type Conversion",description:"Convert numeric values to text while preserving JSON structure"},selectiveProcessing:{title:"Selective Processing",description:"Choose specific fields or convert all numeric values"},formatOptions:{title:"Format Options",description:"Control decimal places and number formatting"}},errors:{invalidJson:"Invalid JSON format:"}},jsonMissingKeyFinder:{title:"JSON Missing Key Finder",description:"Find missing keys across JSON objects in arrays and detect inconsistencies",inputTitle:"Input JSON Array",inputPlaceholder:"Paste your JSON array here...",analysisOptions:"Analysis Options",ignoreNullValues:"Ignore Null Values",deepAnalysis:"Deep Analysis (Nested Objects)",caseSensitive:"Case Sensitive Key Comparison",analyzeButton:"Analyze Missing Keys",analysisResults:"Analysis Results",totalObjects:"Total objects",uniqueKeys:"Unique keys found",objectsWithMissing:"Objects with missing keys",missingKeysReport:"Missing Keys by Object",noResults:"No analysis yet. Input a JSON array to find missing keys.",features:{keyAnalysis:{title:"Key Analysis",description:"Analyze all objects to find missing keys and inconsistencies"},detailedReport:{title:"Detailed Report",description:"Get comprehensive reports on missing keys per object"},exportResults:{title:"Export Results",description:"Export findings and generate complete object templates"}},errors:{invalidJson:"Invalid JSON format:",invalidArray:"Input must be a JSON array"}},jsonArraySlicer:{title:"JSON Array Slicer",description:"Extract specific portions of JSON arrays using index ranges or conditions",inputTitle:"Input JSON Array",inputPlaceholder:"Paste your JSON array here...",slicingOptions:"Slicing Options",method:"Method",indexRange:"Index Range",conditionalFilter:"Conditional Filter",randomSample:"Random Sample",startIndex:"Start",endIndex:"End",conditionField:"Field",operator:"Operator",conditionValue:"Value",sampleCount:"Count",preserveOrder:"Preserve Original Order",arrayInfo:"Array Information",totalElements:"Total elements",willExtract:"Will extract",matchingElements:"Matching elements",willSample:"Will sample",sliceButton:"Slice Array",slicedArray:"Sliced Array",noResults:"No results yet. Input a JSON array to slice.",features:{indexSlicing:{title:"Index Slicing",description:"Extract array elements by start/end index positions"},conditionalSlicing:{title:"Conditional Slicing",description:"Filter elements based on field values and conditions"},smartPreview:{title:"Smart Preview",description:"Preview sliced results with detailed statistics"}},errors:{invalidJson:"Invalid JSON format:",invalidArray:"Input must be a JSON array"},operators:{equals:"Equals",notEquals:"Not Equals",greater:"Greater Than",less:"Less Than",contains:"Contains"}},unifiedExcelToJson:{title:"Excel to JSON Converter",description:"Convert Excel files and text data to JSON format",modes:{file:"File Mode",text:"Text Mode"},tabs:{unifiedExcelToJson:"Excel to JSON"}},jsonArrayDeduplicator:{title:"JSON Array Deduplicator",description:"Remove duplicate elements from JSON arrays with various comparison methods",inputTitle:"Input JSON Array",inputPlaceholder:"Paste your JSON array here...",deduplicationOptions:"Deduplication Options",comparisonMethod:"Comparison Method",compareField:"Compare Field",keepOccurrence:"Keep Occurrence",caseSensitive:"Case Sensitive Comparison",showDuplicates:"Show Found Duplicates",deepComparison:"Deep Comparison (Entire Object)",shallowComparison:"Shallow Comparison (Reference)",fieldComparison:"Specific Field Comparison",stringComparison:"JSON String Comparison",firstOccurrence:"First Occurrence",lastOccurrence:"Last Occurrence",analysisResults:"Analysis Results",totalElements:"Total elements",uniqueElements:"Unique elements",duplicatesFound:"Duplicates found",willRemove:"Will remove",deduplicateButton:"Remove Duplicates",deduplicatedArray:"Deduplicated Array",deduplicationComplete:"Deduplication Complete",noResults:"No results yet. Input a JSON array to remove duplicates.",features:{smartDetection:{title:"Smart Detection",description:"Detect duplicates using deep comparison or specific field matching"},flexibleOptions:{title:"Flexible Options",description:"Choose comparison methods and specify which occurrence to keep"},detailedStats:{title:"Detailed Stats",description:"Get comprehensive statistics about duplicates found and removed"}},errors:{invalidJson:"Invalid JSON format:",invalidArray:"Input must be a JSON array"}},colorPicker:{title:"Color Picker",description:"Advanced color picker with support for multiple color formats and image color extraction",colorPicker:"Color Picker",imagePicker:"Image Color Picker",dropImage:"Drop an image here or click to select",selectImage:"Select Image",imagePreview:"Image Preview",pickColor:"Pick Color",cancelPick:"Cancel Pick",clickToPick:"Click on the image to pick a color",keepPickingUntilCancel:"Keep picking colors until you click Cancel Pick",commonColors:"Common Colors",conversions:"Color Conversions",preview:"Preview",onLight:"On Light Background",onDark:"On Dark Background",colorPicked:"Color picked successfully!",colorUpdated:"Color updated successfully!",colorPickError:"Failed to pick color from image",noImageInClipboard:"No image found in clipboard",pasteFailed:"Failed to paste image from clipboard",pasteImage:"Paste Image",invalidHex:"Invalid HEX color format",invalidRgb:"Invalid RGB color format",invalidRgbRange:"RGB values must be between 0 and 255",invalidRgba:"Invalid RGBA color format",invalidRgbaRange:"RGBA values must be between 0-255 for colors and 0-1 for alpha",invalidHsl:"Invalid HSL color format",invalidHslRange:"HSL values must be H: 0-360, S: 0-100%, L: 0-100%",invalidHsv:"Invalid HSV color format",invalidHsvRange:"HSV values must be H: 0-360, S: 0-100%, V: 0-100%",invalidCmyk:"Invalid CMYK color format",invalidCmykRange:"CMYK values must be between 0-100%",hexPlaceholder:"Enter HEX color (#RRGGBB)",features:{title:"Key Features",conversions:{title:"Multiple Format Support",description:"Convert between HEX, RGB, RGBA, HSL, HSV, and CMYK color formats with real-time updates"},imagePicker:{title:"Image Color Extraction",description:"Upload an image and click anywhere to extract exact colors from the image"},commonColors:{title:"Common Color Palette",description:"Quickly select from a palette of commonly used colors"}}},pdfViewer:{title:"PDF Viewer",description:"Preview and basic editing of PDF documents",uploadSection:"Upload PDF",uploadTitle:"Upload PDF File",uploadDescription:"Drag and drop your PDF file here or click to browse",supportedFormats:"Supported formats",selectPdf:"Select PDF File",preview:"PDF Preview",download:"Download PDF",selectAnother:"Select Another PDF",documentInfo:"Document Information",fileName:"File Name",fileSize:"File Size",pageCount:"Page Count",zoom:"Zoom",page:"Page",of:"of"}}},e1={common:{clear:"清除",copy:"复制",close:"关闭",download:"下载",loadExample:"加载示例",loadObjectExample:"加载对象示例",selectAll:"全选",clearSelection:"清除选择",extract:"提取",results:"结果",options:"选项",input:"输入",preview:"预览",statistics:"统计",fields:"字段",items:"项",found:"找到",extracted:"提取",with:"包含",total:"总计",unique:"唯一",nonEmpty:"非空",loading:"加载中...",remove:"移除",more:"更多"},navigation:{tools:"工具",title:"专业Web开发者工具",language:"语言",categories:"工具分类",menu:"菜单",close:"关闭",search:"搜索工具...",noResults:"没有找到匹配的工具。",noToolsInCategory:"该分类中没有可用的工具。",home:"首页"},homepage:{title:"开发者工具集合",subtitle:"为开发者、设计师和内容创作者提供强大的在线工具。提升您的工作效率。",recommendedTools:"推荐工具",exploreCategories:"浏览分类",stats:{totalTools:"工具总数",activeTools:"可用工具",categories:"分类数量",comingSoon:"即将推出"}},notFound:{title:"页面未找到",description:"您要查找的工具或页面不存在或已被移动。",backToHome:"返回首页",goBack:"返回上页",popularTools:"热门工具",helpText:"如果您需要帮助寻找特定工具，请查看侧边栏中的分类。"},toast:{success:"成功",error:"错误",warning:"警告",info:"信息",copied:"结果已复制到剪贴板！",copyFailed:"复制到剪贴板失败",downloadSuccess:"文件下载成功！"},footer:{madeWith:"用",by:"制作"},categories:{webTools:{name:"网页工具",description:"网页开发和分析工具"},jsonTools:{name:"JSON工具",description:"全面的JSON处理和转换实用程序"},dataTools:{name:"数据工具",description:"数据处理和操作工具"},imageTools:{name:"图片工具",description:"图片处理和管理工具"},converters:{name:"转换器",description:"格式转换实用工具"},generators:{name:"生成器",description:"代码和内容生成器"}},pagination:{previous:"上一页",next:"下一页",page:"第",of:"页，共"},status:{active:"可用","coming-soon":"即将推出"},tools:{htmlExtractor:{title:"HTML 内容提取器",description:"一键从 HTML 代码中提取图片、视频、链接和其他资源",contentTypes:"内容类型",baseUrl:"基础 URL",inputPlaceholder:"请在此处粘贴您的 HTML 代码...",extractionResults:"转换结果",noResults:"暂无提取结果。请输入 HTML 代码并选择要提取的内容类型。",features:{imageExtraction:{title:"图片提取",description:"自动从 HTML 中提取所有图片 URL，包括 img 标签和 CSS 背景图片。支持相对路径转绝对路径，方便使用。"},mediaProcessing:{title:"媒体处理",description:"批量提取视频和音频文件链接，支持多种格式（MP4、WebM、Ogg、MP3 等）。自动识别 video 和 audio 标签中的源文件。"},linkAnalysis:{title:"链接分析",description:"提取页面中的所有超链接，包括 a 标签的 href 属性。支持筛选内部和外部链接，帮助分析网站结构。"}},options:{uniqueOnly:"仅显示唯一结果",absoluteUrls:"转换为绝对 URL"},types:{images:"图片",videos:"视频",audio:"音频",links:"链接",css:"CSS",javascript:"JavaScript",iframes:"内嵌框架",metadata:"元数据",forms:"表单"}},jsonKeysExtractor:{title:"JSON 键值提取器",description:"从 JSON 对象和数组中提取所有唯一键或值",inputTitle:"输入 JSON",inputPlaceholder:"在此粘贴您的 JSON 数据...",extractedKeys:"提取的键",extractedValues:"提取的值",noResults:"暂无提取结果。请输入 JSON 进行分析。",extractionOptions:"提取选项",includeNested:"包含嵌套键（使用点符号）",sortResults:"按字母顺序排序结果",includeArrayIndices:"包含数组索引",outputFormat:"输出格式",formatOptions:{array:"JSON 数组",list:"行分隔列表",tree:"树形结构"},modeToggle:{keys:"提取键",values:"提取值"},features:{keyDiscovery:{title:"键发现",description:"自动从复杂的 JSON 结构中发现所有键"},valueExtraction:{title:"值提取",description:"从复杂的 JSON 结构中提取所有值"},nestedSupport:{title:"嵌套支持",description:"使用路径符号处理嵌套对象的深层结构"},valueTypes:{title:"值类型",description:"提取所有类型的值（字符串、数字、布尔值等）"},exportOptions:{title:"多种格式",description:"导出为数组、列表或树形结构"}},errors:{invalidJson:"无效的 JSON 格式："}},jsonToExcel:{title:"JSON 转 Excel/CSV/SQL 转换器",description:"将 JSON 数据转换为 Excel、CSV 或 SQL 格式，支持自定义选项",inputTitle:"输入 JSON 数据",outputTitle:"Excel 输出结果",csvOutputTitle:"CSV 输出结果",sqlOutputTitle:"SQL 输出结果",inputPlaceholder:"在此粘贴您的 JSON 数组...",noResults:"暂无转换结果。请输入 JSON 数据以进行转换。",conversionComplete:"转换成功完成！",readyForDownload:"Excel 文件已准备就绪，可立即下载。",csvReadyForDownload:"CSV 文件已准备就绪，可立即下载。",sqlReadyForDownload:"SQL 文件已准备就绪，可立即下载。",previewTitle:"数据预览",convertToExcel:"转换为 Excel",convertToCsv:"转换为 CSV",convertToSql:"生成 SQL",showingRows:"显示 {shown} 行，共 {total} 行",options:{conversionType:"转换类型",includeHeaders:"包含表头",autoFitColumns:"自动适配列宽",sheetName:"工作表名称",sheetNamePlaceholder:"输入工作表名称",delimiter:"分隔符",quoteChar:"引号字符",flattenNested:"展开嵌套对象",comma:"逗号",semicolon:"分号",tab:"制表符",pipe:"管道符",doubleQuote:"双引号",singleQuote:"单引号",none:"无",sqlOptions:"SQL 选项",tableName:"表名",tableNamePlaceholder:"输入表名",sqlType:"SQL 类型",whereField:"WHERE 字段",whereFieldPlaceholder:"WHERE 子句的字段",escapeValues:"转义值",batchInsert:"批量插入"},features:{conversion:{title:"智能转换",description:"自动将 JSON 数组转换为 Excel、CSV 或 SQL 格式，并对数据类型进行妥善处理。"},formatting:{title:"Excel 格式优化",description:"生成格式规范的 Excel 文件，包含表头、自动调整列宽及多工作表支持。"},batch:{title:"批量处理",description:"支持高效处理大型数据集，提供数据预览与批量下载功能。"}},errors:{emptyInput:"请提供待转换的 JSON 数据",invalidJson:"JSON 格式无效，请检查您的输入内容。",notArray:"输入内容必须是 JSON 数组",emptyArray:"JSON 数组不能为空",conversionFailed:"JSON 转换失败",emptyTableName:"请提供表名",emptyWhereField:"请为 UPDATE 语句提供 WHERE 字段"},success:{conversionComplete:"JSON 成功转换为 Excel！",csvConversionComplete:"JSON 成功转换为 CSV！",sqlConversionComplete:"SQL 语句生成成功！"}},excelToJson:{title:"Excel 转 JSON 转换器 ",description:" 将 Excel 文件转换为 JSON 格式，支持灵活的解析选项 ",inputTitle:" 上传 Excel 文件 ",outputTitle:"JSON 输出结果 ",uploadDescription:" 选择需转换为 JSON 的 Excel 文件 ",selectFile:" 选择 Excel 文件 ",supportedFormats:" 支持 .xlsx、.xls、.csv 及 .ods 格式文件 ",noResults:" 暂无转换结果。请上传 Excel 文件。",conversionComplete:" 转换成功完成！",recordsCount:" 已转换 {count} 条记录 ",convert:" 转换为 JSON",fileSelected:" 文件选择成功 ",options:{title:"转换选项",firstRowAsHeaders:"第一行作为标题",skipEmptyRows:"跳过空行",sheetIndex:"工作表"},errors:{conversionFailed:"Excel 文件转换失败",noFileSelected:"请选择一个 Excel 文件进行转换",xlsxRequired:"Excel 文件解析需依赖 XLSX 库。请安装：npm install xlsx"},features:{parsing:{title:"Excel 解析 ",description:" 解析 Excel 文件，支持多工作表、公式及多种数据类型。"},conversion:{title:" 灵活转换 ",description:" 转换时可配置表头、空行处理及特定工作表选择等选项。"},options:{title:" 转换选项配置 ",description:" 通过表头处理、空行跳过及工作表选择等功能自定义输出结果。"}}},jsonExtractor:{title:"JSON 字段提取器",description:"一键从 JSON 数组数据中提取指定字段",availableFields:"可用字段",inputTitle:"输入 JSON 数组",inputNote:"请粘贴格式为以下的 JSON 数组数据：",inputDescription:"工具将自动解析 JSON 并列出所有可选择的字段。",inputPlaceholder:"请在此处粘贴您的 JSON 数据",extractedData:"提取的数据",fieldStatistics:"字段统计",noResults:"暂无提取结果。请输入 JSON 数组数据并选择要提取的字段。",options:{preserveStructure:"保持对象结构",removeEmpty:"移除空值",flattenNested:"展平嵌套对象"},features:{fieldExtraction:{title:"字段提取",description:"自动解析 JSON 数组并提取选定字段。支持嵌套对象并保持数据类型以确保准确提取。"},smartFiltering:{title:"智能过滤",description:"选择要包含在输出中的特定字段。可选择移除空值并保持原始对象结构以获得清晰的结果。"},exportOptions:{title:"导出选项",description:"将提取的数据复制到剪贴板或下载为 JSON 文件。包括字段统计和数据分析，便于更好地理解您的数据集。"}},errors:{invalidFormat:"输入必须是 JSON 数组格式：[{},{},...]",emptyArray:"JSON 数组不能为空",noFields:"请选择至少一个字段进行提取",invalidJson:"无效的 JSON 格式：",noData:"请提供要提取的 JSON 数据"}},imageListProcessor:{title:"图片列表处理器",description:"输入图片URL列表并以可视化图库格式显示",inputTitle:"输入图片URL",inputNote:"请在下方粘贴图片URL，每行一个：",inputPlaceholder:`在此粘贴图片URL，每行一个...

示例：
https://example.com/image1.jpg
https://example.com/image2.png
https://example.com/image3.webp`,imagePreview:"图片图库",noResults:"未找到有效的图片URL。请输入有效的图片URL。",imageError:"加载失败",emptyState:{title:"没有图片可显示",description:"在上方输入一些图片URL以在下方图库中查看。"},features:{simple:{title:"简单输入",description:"只需逐行粘贴图片URL - 无需复杂格式。"},gallery:{title:"增强图库",description:"在清洁4列布局中查看所有图片，支持缩放、平移和键盘导航的全功能灯箱预览。"},fast:{title:"专业预览",description:"高级图片查看器，支持缩放、滚动、拖拽和全屏功能，便于详细检查。"}}},videoToGifConverter:{title:"视频转GIF工具",description:"将视频转换为动态GIF，支持自定义文字叠加和时间控制",howToUse:{title:"使用方法",step1:'通过点击"选择视频文件"或拖拽来上传视频文件',step2:"调整GIF设置（宽度、质量、帧率）",step3:"设置GIF的时间范围，如果需要可以添加文字叠加",step4:'点击"生成GIF"来创建您的动画GIF'},tips:{title:"获得最佳效果的技巧",tip1:"为获得最佳效果，请使用较短的视频片段（10秒以内）",tip2:"较低的帧率（10-15 FPS）可创建较小的文件大小",tip3:"较小的GIF宽度（200-400px）加载更快且消耗更少内存",tip4:"使用中等质量可在文件大小和图像质量之间取得良好平衡"},loadingVideo:"正在加载视频...",upload:{title:"上传视频",dragDrop:"拖拽视频文件到此处",selectFile:"选择视频文件",supportedFormats:"支持 MP4、AVI、MOV、WebM 等视频格式（最大：100MB）"},settings:{width:"GIF宽度（像素）",quality:"质量",fps:"帧率（FPS）",qualityOptions:{high:"高质量",medium:"中等质量",low:"低质量（文件更小）"}},preview:{title:"视频预览和控制"},actions:{startCapture:"开始取图",stopCapture:"取图完成",generateGif:"生成GIF"},timeRange:{title:"时间范围选择",start:"开始",end:"结束",setStart:"设置开始时间",setEnd:"设置结束时间"},textOverlay:{title:"文字叠加",add:"添加文字",text:"文字",placeholder:"输入叠加文字...",startTime:"开始时间（秒）",endTime:"结束时间（秒）",fontSize:"字体大小",color:"颜色",position:"位置",positions:{top:"顶部",center:"居中",bottom:"底部"}},processing:{title:"处理视频中",description:"正在将您的视频转换为GIF并添加文字叠加。这可能需要一些时间...",preview:"预览"},result:{title:"生成的GIF",download:"下载GIF",createNew:"创建新的GIF"},features:{conversion:{title:"视频转换",description:"将视频转换为高质量的动态GIF，支持自定义帧率和尺寸。"},textOverlay:{title:"文字叠加",description:"添加多个文字叠加，支持精确时间控制、自定义颜色、字体和位置。"},customization:{title:"全面自定义",description:"控制质量、大小、时间和文字外观等各个方面，获得完美效果。"}},errors:{invalidFile:"请选择有效的视频文件。",fileTooLarge:"文件大小必须小于100MB。",noVideoSelected:"请先选择一个视频文件。",invalidTimeRange:"时间范围无效。结束时间必须大于开始时间。",processingFailed:"视频处理失败，请重试。"},messages:{fileLoaded:"视频文件加载成功！",gifGenerated:"GIF生成成功！",filePasted:"已从剪贴板粘贴视频文件！"}},apngGenerator:{title:"APNG动图生成器",description:"将多张静态图片制作成动态PNG文件，支持自定义动画设置",uploadTitle:"上传图片帧",uploadDescription:"拖拽多张图片或点击选择动画帧",selectFiles:"选择图片文件",supportedFormats:"支持格式",settings:"动画设置",frameDelay:"帧延迟",loopCount:"循环次数",infinite:"无限循环",outputWidth:"输出宽度",outputHeight:"输出高度",advancedOptions:"高级选项",maintainAspectRatio:"保持宽高比",optimizeSize:"优化文件大小",frameList:"动画帧列表",generateAPNG:"生成APNG",generating:"生成中...",preview:"预览动画",animationPreview:"动画预览",downloadAPNG:"下载APNG",reorderHint:"帧将按上述顺序播放动画。您可以通过点击×按钮删除不需要的帧。",features:{title:"主要功能",highQuality:{title:"高质量输出",description:"生成无损动态PNG文件，支持透明度和24位色彩"},customizable:{title:"完全可定制",description:"控制帧时间、循环次数、尺寸和优化设置"},easyToUse:{title:"易于使用",description:"简单的拖拽界面，实时预览和即时下载"}}},backgroundRemover:{title:"在线抠图工具",description:"使用AI技术自动去除图片背景",features:{aiPowered:{title:"AI智能识别",description:"先进的机器学习算法，精确检测和去除背景"},fastProcessing:{title:"快速处理",description:"秒级背景去除，高质量效果"},highQuality:{title:"高质量输出",description:"保持图像质量和细节，干净地去除背景"}},upload:{title:"上传图片",dragDrop:"拖拽图片到此处",supportedFormats:"支持JPG、PNG、GIF等图片格式",selectFile:"选择图片"},preview:{original:"原始图片",originalAlt:"原始图片",processed:"去背景后",processedAlt:"去除背景后的图片"},options:{title:"输出选项",model:"AI模型",outputFormat:"输出格式",transparent:"透明背景",whiteBackground:"白色背景",backgroundColor:"背景颜色",quality:"输出质量"},models:{small:"小型（快速）",medium:"中型（平衡）",large:"大型（最佳质量）"},actions:{remove:"去除背景"},processing:{inProgress:"处理中...",analyzing:"正在分析图片并去除背景...",pleaseWait:"这可能需要几秒钟"},result:{title:"处理结果",noResult:"暂无处理结果。请上传图片以去除背景。",complete:"背景去除完成",ready:"您的图片已准备好下载"},imageInfo:{size:"文件大小",format:"格式"},tips:{title:"获得最佳效果的技巧",tip1:"使用主体边界清晰的高分辨率图片以获得最佳效果",tip2:"主体与背景对比度好的图片效果更佳",tip3:"避免使用复杂背景或与主体颜色相似的图片",tip4:"PNG格式保持透明度，JPG格式使用白色背景",tip5:"使用对比视图并排查看处理前后的结果"},comparison:{before:"处理前",after:"处理后"}},csvtojson:{title:"CSV转JSON工具",description:"将CSV数据转换为JSON格式，支持自定义解析选项",introduction:{title:"工具介绍",description:"在线CSV转JSON工具，用于将固定符号分隔的CSV格式数据转换为JSON格式数据。",usage:"默认是以制表符(\\t)为数据字段分隔符，如是其它符号，直接在分隔符输入框中填写即可。支持将CSV转换为JSON对象或JSON数组。"},example:{title:"示例",input:"CSV输入：",output:"JSON输出："},input:{title:"输入CSV数据",placeholder:`在此粘贴您的CSV数据...

示例：
name,age,score
李华,25,89
小明,22,85`,fileUpload:"上传CSV文件"},options:{title:"解析选项",delimiter:"分隔符",outputFormat:"输出格式",hasHeaders:"首行作为标题",skipEmptyLines:"跳过空行",autoDetectNumbers:"自动检测数字",autoDetectBooleans:"自动检测布尔值"},delimiters:{comma:"逗号 (,)",semicolon:"分号 (;)",tab:"制表符 (\\t)",pipe:"管道符 (|)",space:"空格"},formats:{jsonObject:"JSON对象",jsonArray:"JSON数组"},preview:{title:"数据预览",firstRows:"前{count}行",rowsDetected:"检测到{count}行"},convert:"转换为JSON",output:{title:"JSON输出",complete:"转换完成",recordsConverted:"已转换{count}条记录",noOutput:"暂无JSON输出。请输入CSV数据进行转换。"}},excelTextToJson:{title:"Excel文本转JSON",description:"将Excel剪贴板数据直接转换为JSON格式",introduction:{title:"工具介绍",description:"在线Excel文本转JSON工具，用于将制表符分隔的Excel数据转换为JSON格式。",usage:"从Excel中复制数据并粘贴到此处。默认分隔符为制表符(\\t)。对象格式需要第一行包含标题。"},example:{title:"示例",input:"Excel输入：",output:"JSON输出："},input:{title:"输入Excel数据",placeholder:`在此粘贴您的Excel数据...

示例：
name	age	score
李华	25	89
小明	22	85`,fileUpload:"上传文本文件"},options:{title:"解析选项",delimiter:"分隔符",outputFormat:"输出格式",hasHeaders:"首行作为标题",skipEmptyLines:"跳过空行",autoDetectNumbers:"自动检测数字",autoDetectBooleans:"自动检测布尔值"},delimiters:{comma:"逗号 (,)",semicolon:"分号 (;)",tab:"制表符 (\\t)",pipe:"管道符 (|)",space:"空格"},formats:{jsonObject:"JSON对象",jsonArray:"JSON数组"},preview:{title:"数据预览",firstRows:"前{count}行",rowsDetected:"检测到{count}行"},convert:"转换为JSON",output:{title:"JSON输出",complete:"转换完成",recordsConverted:"已转换{count}条记录",noOutput:"暂无JSON输出。请输入Excel数据进行转换。"}},jsonPathExtractor:{title:"JSON 路径提取器",description:"使用 JSONPath 表达式从 JSON 中提取数据，支持高级过滤功能",extractButton:"提取数据",mode:{path:"路径模式",field:"字段模式"},tabs:{path:"路径提取器",formatter:"JSON 格式化",excelTojson:"Excel 转 JSON",jsonMerge:"JSON 合并",excelTextToJson:"Excel 文本转 JSON",jsonToExcel:"JSON 转 Excel"},features:{pathExtraction:{title:"路径提取",description:"使用 JSONPath 表达式从复杂的 JSON 结构中精准提取数据，支持点记号和数组索引。"},filtering:{title:"高级过滤",description:"支持通配符、数组切片和条件过滤，精确提取您需要的数据。"},export:{title:"导出结果",description:"将提取的数据复制到剪贴板或下载为 JSON 文件，包含格式化输出和统计信息。"}},syntaxGuide:{title:"JSONPath 语法指南",basicSyntax:"基本语法",examples:"常用示例",rootSymbol:"根对象或元素",currentSymbol:"当前对象或元素",childOperator:"子元素操作符",recursiveMatch:"递归匹配所有子元素",wildcard:"通配符。匹配所有对象或元素",subscriptOperator:"下标运算符，JSONPath索引从0开始",unionOperator:"连接运算符，将多个结果拼成数组返回，JSONPath允许使用别名",arraySlice:"数组切片运算符",filterExpression:"过滤器（脚本）表达式",scriptExpression:"脚本表达式",exampleDesc1:"获取第一本书的标题",exampleDesc2:"获取所有书的作者",exampleDesc3:"递归查找所有作者",exampleDesc4:"查找价格小于10的书籍",tutorialTitle:"JSONPath 教程",tutorialDesc:"了解更多关于 JSONPath 语法和用法："},inputSection:{title:"JSON 数据和路径",jsonData:"JSON 数据",jsonPath:"JSONPath 表达式",jsonPlaceholder:"在此粘贴您的 JSON 数据...",pathPlaceholder:"输入 JSONPath 表达式（例如：$.users[*].name）",quickPaths:"快速路径模板"},outputSection:{title:"提取结果",noResults:"暂无提取结果。请输入 JSON 数据和 JSONPath 表达式。",extractedData:"提取的数据"},quickPaths:{root:"根元素",allProperties:"所有属性",firstArrayItem:"第一个数组项",allArrayItems:"所有数组项",lastArrayItem:"最后一个数组项",arraySlice:"数组切片 (0-2)",recursiveMatch:"递归匹配所有作者",filterExpression:"过滤价格小于10的书籍",unionOperator:"连接运算符用于多个项目",scriptExpression:"脚本表达式获取最后一项"},success:{validJson:"有效的 JSON 格式",extracted:"数据提取成功",arrayResults:"找到 {count} 个数组项",objectResults:"找到包含 {count} 个属性的对象",primitiveResult:"找到 {type} 类型的值"},errors:{invalidJson:"无效的 JSON 格式",pathError:"JSONPath 表达式错误",noMatches:"没有数据匹配指定的路径"},messages:{copied:"提取的数据已成功复制到剪贴板！",copyFailed:"复制到剪贴板失败",downloaded:"JSON 文件下载成功！",downloadFailed:"文件下载失败"}},jsonFormatter:{title:"JSON 格式化工具",description:"美化、验证和格式化 JSON 数据，支持多种格式选项",inputTitle:"输入 JSON",outputTitle:"格式化结果",inputPlaceholder:"请输入要格式化的 JSON 数据...",noResults:"暂无格式化结果。请输入有效的 JSON 数据进行格式化。",validJson:"有效的 JSON",invalidJson:"无效的 JSON",formattingComplete:"格式化完成",formatOptions:"格式选项",indent:"缩进",outputFormat:"输出格式",prettyFormat:"美化格式",compactFormat:"压缩格式",sortKeys:"排序键",escapeUnicode:"转义 Unicode",formatJson:"格式化 JSON",spaces2:"2 个空格",spaces4:"4 个空格",tab:"制表符",lines:"行数",characters:"字符数",keys:"键数量",depth:"嵌套深度",caseOptions:"大小写选项",keyCase:"键大小写",valueCase:"值大小写",preserveCase:"保持原始大小写",toUpperCase:"转换为大写",toLowerCase:"转换为小写",features:{prettyFormat:{title:"美化格式",description:"使用适当的缩进和间距自动格式化和美化 JSON"},validation:{title:"验证",description:"实时 JSON 验证，包含详细的错误消息和行号"},customization:{title:"自定义",description:"选择缩进大小、排序和紧凑格式选项"}},messages:{formatSuccess:"JSON 格式化成功！",formatError:"格式化 JSON 失败：",provideData:"请提供要格式化的 JSON 数据",copied:"JSON 已成功复制到剪贴板！",copyFailed:"复制到剪贴板失败",downloaded:"JSON 文件下载成功！",downloadFailed:"文件下载失败"}},jsonMerge:{title:"JSON 文件合并工具",description:"将多个 JSON 文件合并成一个文件",introduction:{title:"工具介绍",description:"在线 JSON 文件合并工具，将多个 JSON 文件合并成一个大的 JSON 文件。",usage:"JSON 文件将按导入顺序依次合并，如对顺序有要求，请注意文件顺序。"},fileUpload:{title:"上传 JSON 文件",description:"选择多个 JSON 文件进行合并。文件将按以下顺序处理。",selectFiles:"选择 JSON 文件",supportedFormats:"支持 .json 文件",noFiles:"尚未选择文件。请选择要合并的 JSON 文件。"},filePreview:{title:"文件预览",fileName:"文件名",fileSize:"文件大小",jsonStructure:"JSON 结构",arrayItems:"{count} 个数组项",object:"JSON 对象",remove:"移除",moveUp:"上移",moveDown:"下移"},options:{title:"合并选项",outputFileName:"输出文件名",outputFileNamePlaceholder:"输入输出文件名（不含扩展名）",defaultFileName:"合并的JSON"},actions:{merge:"合并 JSON 文件",clear:"清空所有文件",download:"下载合并的 JSON"},output:{title:"合并的 JSON 输出",noOutput:"暂无合并输出。请上传 JSON 文件并点击合并。",complete:"合并完成",itemsMerged:"已合并 {count} 项",downloadReady:"合并的 JSON 文件已准备好下载。"},features:{multipleFiles:{title:"多文件支持",description:"通过拖放支持上传和合并多个 JSON 文件。"},orderControl:{title:"顺序控制",description:"合并前重新排序文件以控制输出顺序。"},preview:{title:"文件预览",description:"合并前预览文件结构和内容。"}},errors:{noFiles:"请至少选择一个 JSON 文件进行合并",invalidJson:"文件中的 JSON 无效：{fileName}",mergeFailed:"合并 JSON 文件失败：{error}",emptyArray:"JSON 文件的根级别必须包含一个数组"},success:{filesAdded:"已成功添加 {count} 个文件",mergeComplete:"JSON 文件合并成功！"}},cookieToJson:{title:"Cookie ↔ JSON 转换器",description:"在 Cookie 字符串和 JSON 对象之间实时转换，支持双向转换",inputTitle:"Cookie 字符串",outputTitle:"JSON 对象",format:"格式",formatButton:"格式化",convertLeft:"转换为 Cookie",convertRight:"转换为 JSON",inputNote:"粘贴如下格式的 Cookie 字符串：",inputPlaceholder:`请在此粘贴您的 Cookie 字符串，例如：
sessionId=abc123; userId=12345; theme=dark; lang=zh-CN

支持的格式：
- 标准 Cookie 格式：name1=value1; name2=value2
- 自动解码 URL 编码的值
- 处理无值的 Cookie（标志位）`,parseOptions:"解析选项",noResults:"暂无转换结果。请输入数据进行转换。",error:"解析错误",success:"解析成功",conversionComplete:"转换完成",cookiesFound:"找到 {count} 个 Cookie",statistics:"共 {total} 个 Cookie，{nonEmpty} 个有值",options:{decodeValues:"解码 URL 编码值",removeEmpty:"移除空值",formatOutput:"格式化 JSON 输出"},features:{parsing:{title:"Cookie 解析",description:"自动解析 Cookie 字符串，支持标准 HTTP Cookie 格式和 URL 解码。"},conversion:{title:"双向转换",description:"在 Cookie 字符串和 JSON 对象之间双向转换，支持实时转换。"},export:{title:"导出选项",description:"复制到剪贴板或下载为 JSON 文件，包含统计信息和验证反馈。"}},errors:{noCookies:"输入字符串中未找到有效的 Cookie",parseError:"解析 Cookie 字符串失败：{error}",conversionFailed:"转换失败。请检查您的输入格式。",unsupportedFormat:"选择了不支持的格式",invalidJson:"JSON 格式无效。请检查您的输入。",formatFailed:"格式化失败。请检查您的输入格式。"},messages:{copied:"内容已成功复制到剪贴板！",copyFailed:"复制到剪贴板失败",downloaded:"JSON 文件下载成功！",downloadFailed:"文件下载失败"}},fileRenamer:{title:"文件重命名工具",description:"多模式批量重命名文件 - 本地处理保护隐私",uploadArea:{title:"拖放文件到此处",subtitle:"或点击选择文件",selectFiles:"选择文件"},fileCount:"文件总数：{count}",totalSize:"总大小：{size}",tabs:{sequential:"顺序编号",replace:"查找替换",case:"大小写转换",insert:"插入文本",truncate:"截取文本",script:"生成脚本"},sequential:{prefix:"前缀",prefixPlaceholder:"例如：photo_",startNumber:"起始数字",padding:"数字补位"},replace:{findText:"查找文本",findPlaceholder:"要查找的文本",replaceText:"替换为",replacePlaceholder:"替换文本",caseSensitive:"区分大小写"},case:{transformation:"大小写转换",uppercase:"全部大写",lowercase:"全部小写",capitalize:"首字母大写"},insert:{text:"插入文本",textPlaceholder:"要插入的文本",position:"插入位置",prefix:"文件名开头",suffix:"文件名结尾",atIndex:"指定位置",index:"插入索引"},truncate:{startIndex:"开始索引",endIndex:"结束索引",description:"从开始索引到结束索引提取子字符串（从0开始计数）"},script:{scriptType:"脚本类型",windows:"Windows 批处理 (.bat)",linux:"Linux Shell (.sh)",autoGenerated:"自动生成脚本",scriptPreview:"脚本预览",downloadScript:"下载脚本",copyScript:"复制脚本",noContent:"暂无脚本内容。添加文件并应用重命名选项以生成脚本。",instructions:{title:"使用说明",description:'此工具生成用于重命名文件的脚本。脚本会根据您的文件和重命名选项自动生成。点击"下载脚本"进行下载。将脚本放置在包含文件的目录中，然后运行它来执行重命名操作。'}},actions:{preview:"预览",apply:"应用重命名",download:"下载ZIP",clear:"清空文件"},sorting:{title:"排序",natural:"自然排序",filename:"文件名顺序",modifiedTime:"文件修改时间顺序",modifiedTimeDesc:"修改时间倒序",random:"随机排序",reverse:"反转当前排序",manual:"手动排序（拖拽）"},fileList:{title:"文件列表",drag:"拖拽",originalName:"原始名称",newName:"新名称",size:"大小",type:"类型",dragHint:"拖拽文件以手动重新排序"},messages:{filesAdded:"成功添加 {count} 个文件！",renameApplied:"重命名应用成功！",downloadStarted:"下载已开始！请检查您的下载文件夹。",downloadError:"下载失败！请重试。",filesCleared:"所有文件已清空！",noFilesToProcess:"没有要处理的文件！请先添加文件。",noScriptToDownload:"没有可下载的脚本！请先生成脚本。",noScriptToCopy:"没有可复制的脚本！请先生成脚本。",scriptDownloaded:'脚本 "{fileName}" 下载成功！',scriptCopied:"脚本已成功复制到剪贴板！",scriptCopyFailed:"复制脚本到剪贴板失败！"}},imageCompressor:{title:"压图大师",description:"高效的在线图片压缩工具，支持批量处理和本地隐私保护",settings:"压缩设置",quality:"质量",smaller:"更小",larger:"更大",outputFormat:"输出格式",keepOriginal:"保持原格式",maxWidth:"最大宽度",uploadTitle:"拖放图片或点击选择",uploadDescription:"支持多张图片，本地处理，不上传到服务器",supportedFormats:"支持格式",selectFiles:"选择文件",imageList:"图片列表",compressing:"压缩中...",compressAll:"全部压缩",downloadAll:"下载全部",compress:"压缩",remove:"移除",originalSize:"原始大小",compressedSize:"压缩后大小",spaceSaved:"节省空间",original:"原始",compressed:"压缩后",imagePreview:"图片预览",originalImage:"原始图片",compressedImage:"压缩后图片",size:"大小",dimensions:"尺寸",saved:"节省",status:{pending:"等待中",compressing:"处理中",completed:"已完成",error:"失败"},features:{efficient:{title:"高效压缩",description:"先进的压缩算法在保持图片质量的同时减少文件大小40-80%。"},secure:{title:"隐私保护",description:"所有处理都在您的浏览器本地进行。图片永远不会上传到任何服务器。"},batch:{title:"批量处理",description:"同时处理多张图片，具有进度跟踪和批量下载功能。"}},errors:{noValidImages:"未找到有效的图片文件",compressionFailed:"压缩 {filename} 失败"},success:{compressionComplete:"所有图片压缩完成！",downloadComplete:"批量下载完成！",pasteSuccess:"图片粘贴成功！"}},imageWatermark:{title:"图片水印大师",description:"为您的照片添加文字或图片水印，支持自定义样式和位置",settings:"水印设置",watermarkType:"水印类型",textWatermark:"文字水印",textWatermarkDesc:"为图片添加文字水印",imageWatermark:"图片水印",imageWatermarkDesc:"为图片添加图片水印",combinedWatermark:"组合水印",combinedWatermarkDesc:"同时添加文字和图片水印",textSettings:"文字设置",watermarkText:"水印文字",textPlaceholder:"输入水印文字",fontSize:"字体大小",fontColor:"字体颜色",fontFamily:"字体",imageSettings:"图片设置",watermarkImage:"水印图片",uploadWatermark:"上传水印图片",watermarkPreview:"水印预览",removeWatermark:"移除水印",imageWidth:"图片宽度",imageOpacity:"图片透明度",positionSettings:"位置设置",position:"位置",margin:"边距",advancedSettings:"高级设置",opacity:"透明度",rotation:"旋转",scale:"缩放",topLeft:"左上角",topCenter:"顶部居中",topRight:"右上角",centerLeft:"左侧居中",center:"居中",centerRight:"右侧居中",bottomLeft:"左下角",bottomCenter:"底部居中",bottomRight:"右下角",uploadTitle:"拖放图片或点击选择",uploadDescription:"支持多张图片，本地处理，不上传到服务器",supportedFormats:"支持格式",selectFiles:"选择文件",imageList:"图片列表",processing:"处理中...",processAll:"全部处理",downloadAll:"下载全部",process:"处理",remove:"移除",originalSize:"原始大小",processedSize:"处理后大小",processed:"已处理",original:"原始",imagePreview:"图片预览",originalImage:"原始图片",processedImage:"处理后图片",size:"大小",dimensions:"尺寸",gifWarning:"水印将应用于动图的所有帧",status:{pending:"等待中",processing:"处理中",completed:"已完成",error:"失败"},features:{watermark:{title:"多种水印类型",description:"添加文字水印、图片水印或组合水印，支持完全自定义。"},batch:{title:"批量处理",description:"同时处理多张图片，具有进度跟踪和批量下载功能。"},customization:{title:"完全自定义",description:"调整水印位置、透明度、旋转、缩放、字体属性等。"}},errors:{noValidImages:"未找到有效的图片文件",invalidWatermark:"请选择有效的图片文件作为水印",noWatermarkImage:"请上传水印图片",noWatermarkText:"请输入水印文字",watermarkProcessingFailed:"处理水印图片失败",processingFailed:"处理 {filename} 失败"},success:{processingComplete:"所有图片处理完成！",downloadComplete:"批量下载完成！",pasteSuccess:"图片粘贴成功！"}},faviconGenerator:{title:"网站图标生成器",description:"从任意图片生成多种尺寸和格式的专业网站图标",uploadSection:"上传图片",uploadTitle:"拖放图片或点击选择",uploadDescription:"上传任意图片来为您的网站创建图标",supportedFormats:"支持格式",selectImage:"选择图片",cropImage:"裁剪图片",originalImage:"原始图片",originalImageDescription:"完整分辨率图片预览 - 这是您的源图片",imageSize:"图片尺寸",cropPreview:"裁剪预览",selectAnother:"选择其他",cropInstruction:"拖动裁剪区域移动位置，或拖动角落手柄调整大小。选中的正方形区域将用于生成图标。",cropInstructionAdvanced:"拖动移动裁剪区域，拖动角落调整大小，或使用鼠标滚轮缩放。选中的正方形区域将用于生成网站图标。",outputFormat:"输出格式",sizes:"图标尺寸",generate:"生成图标",generating:"生成中...",generatedFavicons:"生成的图标",downloadAll:"下载全部为ZIP",usageInstructions:"如何使用图标",htmlUsage:"HTML 实现",tips:"最佳实践",tip1:"使用简单、易识别的设计，确保在小尺寸下清晰可见",tip2:"确保良好的对比度，以便在不同背景下都能清晰显示",tip3:"在各种设备和浏览器上测试您的图标",tip4:"将 favicon.ico 放在网站根目录以便自动检测",pasteHint:"提示：您也可以直接从剪贴板粘贴图片",features:{cropping:{title:"智能裁剪",description:"交互式裁剪工具，从您的图片中选择完美的正方形区域，实时预览效果。"},multiSize:{title:"多种尺寸",description:"生成所有标准尺寸（16px到128px）的图标，确保在各种设备上的最佳兼容性。"},formats:{title:"多种格式",description:"支持导出ICO、PNG或JPG格式，满足不同浏览器和平台的要求。"}},errors:{invalidFile:"请选择有效的图片文件",generationFailed:"生成图标失败",downloadFailed:"下载文件失败",imageLoadFailed:"图片加载失败",fileReadFailed:"文件读取失败"},success:{imageLoaded:"图片加载成功！",generationComplete:"图标生成成功！",downloadComplete:"下载完成！"},messages:{pasteSuccess:"已从剪贴板粘贴图片并开始处理！"}},jsonToSql:{title:"JSON 转 SQL 转换器",description:"从 JSON 数据生成 SQL INSERT、UPDATE 或 CREATE TABLE 语句",inputTitle:"输入 JSON 数据",outputTitle:"SQL 输出",inputPlaceholder:"在此粘贴您的 JSON 数组...",noResults:"暂无生成的 SQL 语句。请输入 JSON 数据并配置选项。",conversionComplete:"SQL 生成完成！",statementsGenerated:"已生成 {count} 条 SQL 语句",convert:"生成 SQL",options:{title:"SQL 选项",tableName:"表名",tableNamePlaceholder:"输入表名",sqlType:"SQL 类型",whereField:"WHERE 字段",whereFieldPlaceholder:"WHERE 子句的字段",escapeValues:"转义值",batchInsert:"批量插入"},features:{insertion:{title:"多种 SQL 类型",description:"从 JSON 数据生成 INSERT、UPDATE 或 CREATE TABLE 语句。"},customization:{title:"自定义选项",description:"配置表名、SQL 类型和字段映射以适应您的数据库。"},security:{title:"SQL 安全",description:"自动值转义以防止 SQL 注入漏洞。"}},errors:{emptyInput:"请提供要转换的 JSON 数据",emptyTableName:"请提供表名",emptyWhereField:"请为 UPDATE 语句提供 WHERE 字段",invalidJson:"JSON 格式无效。请检查您的输入。",notArray:"输入必须是 JSON 数组",emptyArray:"JSON 数组不能为空",conversionFailed:"生成 SQL 语句失败"},success:{conversionComplete:"SQL 语句生成成功！"}},universalConverter:{title:"通用格式转换器",description:"在 JSON、XML 和 HTTP 查询参数之间实时转换",inputTitle:"输入",outputTitle:"输出",format:"格式",formatButton:"格式化",conversionDirection:"转换方向",conversionDirectionDescription:"选择转换方向或交换面板",swap:"交换面板",convertLeft:"转换到左侧",convertRight:"转换到右侧",features:{bidirectional:{title:"双向转换",description:"支持任意格式之间的双向转换"},realtime:{title:"实时转换",description:"输入时即时转换，自动检测格式"},validation:{title:"格式验证",description:"内置所有支持格式的验证功能，提供详细的错误信息"}},errors:{conversionFailed:"转换失败。请检查您的输入格式。",unsupportedFormat:"选择了不支持的格式",invalidJson:"JSON 格式无效。请检查您的输入。",invalidXml:"XML 格式无效。请检查您的输入。",invalidQuery:"查询参数格式无效。请检查您的输入。",xmlGenerationFailed:"生成 XML 输出失败",queryGenerationFailed:"生成查询参数输出失败",formatFailed:"格式化失败。请检查您的输入格式。"}},qrCodeTool:{title:"二维码生成与识别工具",description:"从文本生成二维码并从图像中识别二维码，支持批量处理",tabs:{generate:"生成",recognize:"识别"},generate:{inputTitle:"生成二维码",textInputLabel:"要编码的文本",textInputPlaceholder:`输入要生成二维码的文本...
批量模式下，每行输入一个文本`,modeLabel:"生成模式",singleMode:"单个二维码",batchMode:"批量二维码",singleModeHint:"从所有文本生成一个二维码",batchModeHint:"为每行文本生成一个单独的二维码",generateSingleButton:"生成二维码",generateBatchButton:"批量生成二维码",singleGeneratedTitle:"生成的二维码",batchGeneratedTitle:"生成的二维码 ({count} 个)",downloadAll:"全部下载为ZIP"},recognize:{uploadTitle:"识别二维码",uploadInstruction:"上传二维码图片",uploadDescription:"将图片拖放到此处或点击选择文件。支持 JPG、PNG、WebP 格式。",pasteHint:"提示：您也可以直接从剪贴板粘贴图片",selectFiles:"选择文件",resultsTitle:"识别结果",copyAll:"复制所有结果",recognitionFailed:"识别二维码失败"},features:{batch:{title:"批量处理",description:"一次生成多个二维码或同时从多个图像中识别二维码。"},generate:{title:"二维码生成",description:"从任意文本输入创建高质量的二维码，支持自定义选项。"},recognize:{title:"二维码识别",description:"从图像中提取二维码数据，支持多种图像格式。"}},messages:{generateSuccess:"二维码生成成功！",batchGenerateSuccess:"成功生成 {count} 个二维码！",downloadAllSuccess:"所有二维码已下载为 ZIP 文件！",copySuccess:"二维码已复制到剪贴板！",copyAllSuccess:"所有识别结果已复制到剪贴板！",recognitionComplete:"二维码识别完成！",pasteSuccess:"已从剪贴板粘贴图片并开始处理！"},errors:{generateFailed:"生成二维码失败。请重试。",batchGenerateFailed:"批量生成二维码失败。请检查您的输入。",emptyBatch:"请输入至少一个文本来生成二维码。",downloadAllFailed:"下载所有二维码失败。请重试。",copyFailed:"复制二维码到剪贴板失败。",copyAllFailed:"复制识别结果到剪贴板失败。",noValidImages:"请选择有效的图像文件。",noQRCodeFound:"图像中未找到二维码。",noResultsToCopy:"没有可复制的成功识别结果。"}},webRtcFileTransfer:{title:"WebRTC 文件传输",description:"在同一网络上的设备之间直接传输文件，无需中介服务器",deviceId:"设备ID",status:{disconnected:"未连接",connecting:"连接中",connected:"已连接"},discovery:{title:"设备发现",search:"搜索设备",searching:"搜索中...",foundDevices:"发现的设备",noDevices:"在网络中未找到设备",unknownDevice:"未知设备",connect:"连接",demoDevice1:"演示设备1",demoDevice2:"演示设备2",connected:"已连接到信令服务器",disconnected:"未连接到信令服务器"},connectionRequests:{title:"连接请求",accept:"接受",reject:"拒绝"},transfer:{title:"文件传输",selectFile:"选择要发送的文件",send:"发送文件",sending:"发送中...",progress:"传输进度",receivedFiles:"接收的文件",download:"下载",connectFirst:"请先连接到设备"},logs:{title:"活动日志",startDiscovery:"开始设备发现",devicesFound:"发现 {count} 个设备",connectingToDevice:"正在连接到设备 {deviceId}",connectionEstablished:"连接建立成功",fileSelected:"选择的文件: {name} ({size})",sendingFile:"正在发送文件: {name}",fileSent:"文件 {name} 发送成功",receivingFile:"正在接收文件: {name} ({size})",fileReceived:"接收到文件: {name}",unexpectedDataChunk:"收到意外的数据块",initialized:"WebRTC 文件传输初始化",webRTCInitialized:"WebRTC 初始化成功",webRTCInitFailed:"WebRTC 初始化失败: {error}",dataChannelOpened:"数据通道已打开",dataChannelClosed:"数据通道已关闭",dataChannelError:"数据通道错误: {error}",messageReceived:"收到消息，类型: {type}",iceCandidateGenerated:"生成 ICE 候选",iceCandidateSent:"ICE 候选已发送到信令服务器",iceCandidateAdded:"ICE 候选已添加",connectionStateChange:"连接状态变更为: {state}",channelNotReady:"数据通道未准备好发送",sendFileFailed:"发送文件失败: {error}",signalServerConnected:"已连接到信令服务器",signalServerDisconnected:"与信令服务器断开连接",signalServerConnectionFailed:"连接信令服务器失败: {error}",signalServerError:"信令服务器错误: {error}",connectingToSignalServer:"正在连接到端口 {port} 上的信令服务器",notConnectedToSignalServer:"未连接到信令服务器",receivedDeviceId:"收到设备 ID: {id}",deviceDiscovered:"发现新设备: {id}",deviceDisconnected:"设备断开连接: {id}",messageParseError:"解析服务器消息失败: {error}",offerSent:"已向目标设备发送 Offer",answerSent:"已向源设备发送 Answer",offerHandlingFailed:"处理 Offer 失败: {error}",answerHandlingFailed:"处理 Answer 失败: {error}",iceCandidateFailed:"添加 ICE 候选失败: {error}",connectionRequestReceived:"收到来自设备 {id} 的连接请求",unexpectedOffer:"收到来自设备 {id} 的意外连接请求",connectionTimeout:"连接超时，正在重置连接",connectionReset:"连接已重置为初始状态",acceptingConnection:"正在接受来自设备 {id} 的连接",connectionRequestRejected:"已拒绝来自设备 {id} 的连接请求"}},textSteganography:{title:"文本隐写术",description:"使用不可见的Unicode字符在普通文本中隐藏秘密信息",encryptionTitle:"加密",decryptionTitle:"解密",visibleText:"可见文本",visibleTextPlaceholder:"输入将可见的文本",hiddenText:"隐藏文本",hiddenTextPlaceholder:"输入要隐藏的秘密信息",steganographyText:"隐写文本",steganographyTextPlaceholder:"在此粘贴隐写文本来解码",steganographyResult:"隐写结果",decodedText:"解码文本",generateSteganography:"生成隐写文本",features:{encryption:{title:"文本加密",description:"使用不可见的Unicode字符在普通文本中隐藏秘密信息"},decryption:{title:"文本解密",description:"从隐写文本中提取隐藏信息"},security:{title:"安全通信",description:"通过看似正常的文本隐秘分享敏感信息"}},errors:{decodingFailed:"解码隐写文本失败"}},imageSteganography:{title:"图片隐写术",description:"使用LSB（最低有效位）隐写技术将秘密图片隐藏在其他图片中",canvasTitle:"图片画布",decodedImageTitle:"解码图片",operationsTitle:"操作步骤",decodingTitle:"解码",canvasPlaceholder:"请选择一张图片开始",decodedImagePlaceholder:"请选择一张图片进行解码",exportImage:"导出图片",exportDecodedImage:"导出解码图片",modeToggle:{encode:"编码",decode:"解码"},step1:"步骤1：选择要隐藏的图片",step1Desc:"选择您想要隐藏在另一张图片中的图片",step2:"步骤2：保存隐藏图片数据",step2Desc:"保存隐藏图片的像素数据用于隐写",step3:"步骤3：选择目标图片",step3Desc:"选择您想要隐藏秘密图片的图片",step4:"步骤4：开始加密",step4Desc:"将隐藏图片数据嵌入到目标图片中",selectHiddenImage:"选择隐藏图片",saveHiddenData:"保存隐藏图片数据",selectTargetImage:"选择目标图片",startEncryption:"开始加密",decodeStep1:"步骤1：选择要解码的图片",decodeStep1Desc:"选择包含隐藏数据的图片进行解码",decodeStep2:"步骤2：开始解码",decodeStep2Desc:"从选中的图片中提取隐藏图片",selectImageToDecode:"选择要解码的图片",startDecoding:"开始解码",decodedImagePreview:"解码图片预览",features:{encryption:{title:"图片加密",description:"使用先进的隐写技术将秘密图片隐藏在其他图片中"},decryption:{title:"图片解密",description:"从隐写图片中提取隐藏图片"},steganography:{title:"LSB隐写",description:"利用最低有效位技术无形地嵌入数据"},extraction:{title:"数据提取",description:"从隐写图片中恢复隐藏数据"},export:{title:"导出结果",description:"将隐写结果下载为PNG图片文件"},result:{title:"解码结果",description:"查看和导出提取的隐藏图片"}},messages:{imageLoaded:"图片加载成功",dataSaved:"隐藏图片数据保存成功",encryptionComplete:"加密完成",decryptionComplete:"解码完成",imageExported:"图片导出成功",canvasCleared:"画布已清空"},errors:{imageLoadFailed:"图片加载失败",dataSaveFailed:"隐藏图片数据保存失败",encryptionFailed:"图片加密失败",decryptionFailed:"图片解码失败",exportFailed:"图片导出失败"}},imageToGifConverter:{title:"图片转GIF工具",description:"将多张图片转换为动态GIF，支持自定义时间和设置",howToUse:{title:"使用方法",step1:'通过点击"选择图片文件"或拖拽来上传多张图片',step2:"调整GIF设置（宽度、质量、帧率）",step3:"设置单独的帧延迟并根据需要重新排序图片",step4:'点击"生成GIF"来创建您的动画GIF'},tips:{title:"获得最佳效果的技巧",tip1:"为获得最佳效果，请使用尺寸相似的图片",tip2:"较低的帧率（1-5 FPS）可创建更流畅的动画",tip3:"较小的GIF宽度（200-400px）加载更快且消耗更少内存",tip4:"使用中等质量可在文件大小和图像质量之间取得良好平衡"},upload:{title:"上传图片",dragDrop:"拖拽图片到此处",selectFile:"选择图片文件",supportedFormats:"支持JPG、PNG、WebP等图片格式"},settings:{width:"GIF宽度（像素）",quality:"质量",fps:"帧率（FPS）",preserveOriginal:"保持原始GIF尺寸",qualityOptions:{high:"高质量",medium:"中等质量",low:"低质量（文件更小）"}},preview:{title:"图片预览和控制",selectedImages:"已选图片",moveUp:"首个移至末尾",moveDown:"末个移至开头",reverse:"反转顺序",shuffle:"随机排序"},actions:{generateGif:"生成GIF"},processing:{title:"处理图片中",description:"正在将您的图片转换为GIF。这可能需要一些时间...",preview:"预览"},result:{title:"生成的GIF",download:"下载GIF",createNew:"创建新的GIF"},features:{conversion:{title:"图片转换",description:"将多张图片转换为高质量的动态GIF，支持自定义帧率和尺寸。"},customization:{title:"全面自定义",description:"控制质量、大小、时间、循环次数等各个方面，获得完美效果。"},animation:{title:"动画控制",description:"设置单独的帧延迟、重新排序图片并控制动画循环行为。"}},errors:{noImages:"请选择有效的图片文件。",processingFailed:"处理图片失败，请重试。",noImagesSelected:"请先选择图片文件。",fileProcessing:"处理选中的文件失败。"},messages:{filesAdded:"成功添加 {count} 个文件！",gifGenerated:"GIF生成成功！",filesPasted:"从剪贴板粘贴了 {count} 个文件！",cleared:"所有图片已清除！"}},gifEditor:{title:"GIF编辑器",description:"拆分、编辑和修改GIF帧，支持自定义时间和设置",howToUse:{title:"使用方法",step1:'通过点击"选择GIF文件"或拖拽来上传GIF文件',step2:"调整GIF设置（宽度、质量、帧率）",step3:"修改单独的帧延迟、重新排序帧或删除不需要的帧",step4:'点击"生成GIF"来创建您编辑的GIF'},tips:{title:"获得最佳效果的技巧",tip1:"为获得最佳效果，请使用帧尺寸一致的GIF",tip2:"低于20毫秒的帧延迟在某些浏览器中可能无法正确显示",tip3:"较小的GIF宽度（200-400px）加载更快且消耗更少内存",tip4:"使用中等质量可在文件大小和图像质量之间取得良好平衡"},upload:{title:"上传GIF",dragDrop:"拖拽GIF到此处",selectFile:"选择GIF文件",supportedFormats:"仅支持GIF格式（最大：50MB）"},settings:{width:"GIF宽度（像素）",quality:"质量",fps:"帧率（FPS）",preserveOriginal:"保持原始GIF尺寸",qualityOptions:{high:"高质量",medium:"中等质量",low:"低质量（文件更小）"}},preview:{title:"GIF预览和控制",originalGif:"原始GIF",frames:"帧",frame:"帧",delay:"延迟",dimensions:"尺寸",pixels:"像素",moveUp:"首个移至末尾",moveDown:"末个移至开头",reverse:"反转顺序",shuffle:"随机排序帧"},actions:{generateGif:"生成GIF"},processing:{title:"处理GIF中",description:"正在编辑您的GIF帧。这可能需要一些时间...",preview:"预览"},result:{title:"生成的GIF",download:"下载GIF",createNew:"编辑另一个GIF"},features:{frameEditing:{title:"帧编辑",description:"将GIF拆分为单独的帧并修改每帧的延迟、顺序或删除不需要的帧。"},customization:{title:"全面自定义",description:"控制质量、大小、时间、帧顺序等各个方面，获得完美效果。"},animation:{title:"动画控制",description:"设置单独的帧延迟、重新排序帧并控制动画循环行为。"}},errors:{noGif:"请选择有效的GIF文件。",invalidFile:"请选择有效的GIF文件。",fileTooLarge:"文件大小必须小于50MB。",processingFailed:"处理GIF失败，请重试。",noFrames:"没有要处理的帧。请先上传GIF文件。",fileProcessing:"处理选中的文件失败。",frameParsingFailed:"解析GIF帧失败。"},messages:{fileLoaded:"GIF文件加载成功！",gifGenerated:"GIF生成成功！",filePasted:"从剪贴板粘贴了GIF文件！"}},svgEditor:{title:"SVG编辑器",description:"编辑SVG代码并实时预览，还提供可视化编辑器",howToUse:{title:"使用方法",step1:"在编辑器中直接编辑SVG代码或使用可视化编辑器",step2:"实时预览您的SVG",step3:"加载教程学习SVG基础知识",step4:"完成后下载您的SVG"},tips:{title:"获得最佳效果的技巧",tip1:"使用可视化编辑器快速创建基本形状",tip2:"加载教程学习高级SVG技术",tip3:"复制SVG代码到剪贴板以在其他应用程序中使用",tip4:"下载SVG文件用于Web项目"},editor:{title:"SVG代码编辑器",loadExample:"加载示例",clear:"清除",placeholder:"在此处输入您的SVG代码...",lines:"行数",copy:"复制到剪贴板"},preview:{title:"SVG预览",empty:"没有SVG代码可显示。添加一些SVG代码以查看预览。",dimensions:"尺寸",download:"下载SVG"},visualEditor:{title:"可视化编辑器",shapes:"形状",history:"历史记录",tools:"工具",delete:"删除选中项",clear:"清空画布",undo:"撤销",redo:"重做",properties:"属性",fill:"填充颜色",stroke:"描边颜色",strokeWidth:"描边宽度",rotation:"旋转",transparent:"透明",noSelection:"未选择形状"},shapes:{rectangle:"矩形",circle:"圆形",ellipse:"椭圆",line:"直线",triangle:"三角形",path:"路径",polygon:"多边形",star:"星形",heart:"心形",quadraticCurve:"二次曲线",cubicCurve:"三次曲线",arcCurve:"弧线"},tutorials:{title:"SVG教程",viewTutorial:"查看教程",basicShapes:"基本形状",basicShapesDesc:"学习创建矩形、圆形和椭圆",paths:"路径和折线",pathsDesc:"使用路径绘制自定义形状",gradients:"渐变和图案",gradientsDesc:"为您的SVG添加渐变和图案"},errors:{copyFailed:"复制代码到剪贴板失败",noSvg:"没有SVG代码可下载"},messages:{exampleLoaded:"示例SVG加载成功！",editorCleared:"编辑器已清除！",codeCopied:"SVG代码已复制到剪贴板！",svgDownloaded:"SVG下载成功！",tutorialLoaded:"教程已加载！",shapeDeleted:"选中的形状已删除！",canvasCleared:"画布已清空！"}},textProcessor:{title:"文本处理器",description:"使用URL编码/解码、Base64编码/解码和哈希函数处理文本",inputTitle:"输入文本",outputTitle:"输出文本",inputPlaceholder:"在此输入或粘贴您的文本...",outputPlaceholder:"处理后的文本将显示在这里...",chars:"字符",words:"单词",lines:"行数",operations:"文本操作",urlEncode:"URL 编码",urlDecode:"URL 解码",base64Encode:"Base64 编码",base64Decode:"Base64 解码",md5Hash:"MD5 哈希",sha256Hash:"SHA-256 哈希",exampleText:"你好世界！这是用于处理的示例文本。https://example.com/?param=value",features:{urlEncoding:{title:"URL 编码",description:"编码或解码 URL 和 URI 组件以确保安全传输"},base64:{title:"Base64 编码",description:"使用 Base64 编码方案编码或解码数据"},hashing:{title:"哈希函数",description:"使用 MD5 或 SHA-256 算法生成加密哈希"}},errors:{encodingError:"文本编码失败。请检查您的输入。",decodingError:"文本解码失败。请检查您的输入。",hashingError:"哈希生成失败。请检查您的输入。"}},heartCollage:{title:"形状拼贴生成器",description:"用您的图片创建各种形状的美丽拼贴",uploadTitle:"上传图片",uploadDescription:"将图片拖放到此处或点击选择文件",supportedFormats:"支持的格式",selectFiles:"选择文件",settings:"拼贴设置",canvasSize:"画布大小",shape:"形状",imageShape:"图片形状",arrangement:"排列方式",random:"随机",grid:"网格",fitAll:"适应所有图片",spacing:"间距",additionalOptions:"附加选项",backgroundColor:"背景颜色",borderOptions:"边框选项",showBorder:"显示边框",small:"小",medium:"中",large:"大",heart:"心形",square:"方形",rectangle:"矩形",circle:"圆形",star:"星形",rounded:"圆角",canvas:"拼贴画布",images:"图片",autoArrange:"自动排列",downloadCollage:"下载拼贴",selectedImages:"已选图片",dragInstructions:"拖动图片以在形状内重新定位。拖动角落可调整大小。",features:{collage:{title:"形状拼贴",description:"用您的图片创建各种形状的美丽拼贴"},customization:{title:"自定义选项",description:"自定义画布大小、形状、图片形状、间距等"},export:{title:"导出结果",description:"将拼贴下载为高质量PNG图片"}},messages:{filesAdded:"成功添加 {count} 个文件",arranged:"图片已在形状内排列",downloadSuccess:"拼贴下载成功！",cleared:"所有图片已清除"},errors:{noImages:"请选择有效的图片文件",fileProcessing:"处理选中的文件失败",noImagesSelected:"请至少选择一张图片来排列",downloadFailed:"下载拼贴失败"}},jsonNumberToText:{title:"JSON 数字转文本转换器",description:"将 JSON 对象和数组中的数值转换为文本字符串",inputTitle:"输入 JSON",inputPlaceholder:"在此粘贴您的 JSON 数据...",conversionOptions:"转换选项",conversionMode:"转换模式",targetFields:"目标字段",preserveDecimals:"保留小数位数",decimalPlaces:"小数位数",addQuotes:"在数字周围添加引号",convertAll:"转换所有数字",specificFields:"仅特定字段",integersOnly:"仅整数",decimalsOnly:"仅小数",analysisResults:"分析结果",numbersFound:"找到的数字",fieldsToConvert:"要转换的字段",numericFields:"数值字段",convertButton:"将数字转换为文本",convertedJson:"转换后的 JSON",conversionComplete:"转换完成",noResults:"暂无结果。请输入 JSON 数据以将数字转换为文本。",features:{typeConversion:{title:"类型转换",description:"在保持 JSON 结构的同时将数值转换为文本"},selectiveProcessing:{title:"选择性处理",description:"选择特定字段或转换所有数值"},formatOptions:{title:"格式选项",description:"控制小数位数和数字格式"}},errors:{invalidJson:"无效的 JSON 格式："}},jsonMissingKeyFinder:{title:"JSON 缺失键查找器",description:"查找 JSON 数组中对象的缺失键并检测不一致性",inputTitle:"输入 JSON 数组",inputPlaceholder:"在此粘贴您的 JSON 数组...",analysisOptions:"分析选项",ignoreNullValues:"忽略空值",deepAnalysis:"深度分析（嵌套对象）",caseSensitive:"区分大小写键比较",analyzeButton:"分析缺失键",analysisResults:"分析结果",totalObjects:"对象总数",uniqueKeys:"找到的唯一键",objectsWithMissing:"有缺失键的对象",missingKeysReport:"按对象列出的缺失键",noResults:"暂无分析结果。请输入 JSON 数组以查找缺失键。",features:{keyAnalysis:{title:"键分析",description:"分析所有对象以查找缺失键和不一致性"},detailedReport:{title:"详细报告",description:"获取每个对象缺失键的综合报告"},exportResults:{title:"导出结果",description:"导出发现结果并生成完整的对象模板"}},errors:{invalidJson:"无效的 JSON 格式：",invalidArray:"输入必须是 JSON 数组"}},jsonArraySlicer:{title:"JSON 数组切片器",description:"使用索引范围或条件提取 JSON 数组的特定部分",inputTitle:"输入 JSON 数组",inputPlaceholder:"在此粘贴您的 JSON 数组...",slicingOptions:"切片选项",method:"方法",indexRange:"索引范围",conditionalFilter:"条件过滤器",randomSample:"随机采样",startIndex:"开始",endIndex:"结束",conditionField:"字段",operator:"操作符",conditionValue:"值",sampleCount:"数量",preserveOrder:"保持原始顺序",arrayInfo:"数组信息",totalElements:"元素总数",willExtract:"将提取",matchingElements:"匹配元素",willSample:"将采样",sliceButton:"切片数组",slicedArray:"切片数组",noResults:"暂无结果。请输入 JSON 数组进行切片。",features:{indexSlicing:{title:"索引切片",description:"通过起始/结束索引位置提取数组元素"},conditionalSlicing:{title:"条件切片",description:"基于字段值和条件过滤元素"},smartPreview:{title:"智能预览",description:"预览切片结果并显示详细统计信息"}},errors:{invalidJson:"无效的 JSON 格式：",invalidArray:"输入必须是 JSON 数组"},operators:{equals:"等于",notEquals:"不等于",greater:"大于",less:"小于",contains:"包含"}},unifiedExcelToJson:{title:"Excel 转 JSON 转换器",description:"将 Excel 文件和文本数据转换为 JSON 格式",modes:{file:"文件模式",text:"文本模式"},tabs:{unifiedExcelToJson:"Excel 转 JSON"}},jsonArrayDeduplicator:{title:"JSON 数组去重器",description:"使用各种比较方法从 JSON 数组中删除重复元素",inputTitle:"输入 JSON 数组",inputPlaceholder:"在此粘贴您的 JSON 数组...",deduplicationOptions:"去重选项",comparisonMethod:"比较方法",compareField:"比较字段",keepOccurrence:"保留出现次数",caseSensitive:"区分大小写比较",showDuplicates:"显示找到的重复项",deepComparison:"深度比较（整个对象）",shallowComparison:"浅比较（引用）",fieldComparison:"特定字段比较",stringComparison:"JSON 字符串比较",firstOccurrence:"第一次出现",lastOccurrence:"最后一次出现",analysisResults:"分析结果",totalElements:"元素总数",uniqueElements:"唯一元素",duplicatesFound:"找到的重复项",willRemove:"将移除",deduplicateButton:"删除重复项",deduplicatedArray:"去重数组",deduplicationComplete:"去重完成",noResults:"暂无结果。请输入 JSON 数组以删除重复项。",features:{smartDetection:{title:"智能检测",description:"使用深度比较或特定字段匹配检测重复项"},flexibleOptions:{title:"灵活选项",description:"选择比较方法并指定保留哪次出现"},detailedStats:{title:"详细统计",description:"获取关于找到和删除的重复项的综合统计信息"}},errors:{invalidJson:"无效的 JSON 格式：",invalidArray:"输入必须是 JSON 数组"}},colorPicker:{title:"颜色选择器",description:"支持多种颜色格式和图像颜色提取的高级颜色选择器",colorPicker:"颜色选择器",imagePicker:"图像颜色选择器",dropImage:"将图像拖放到此处或点击选择",selectImage:"选择图像",imagePreview:"图像预览",pickColor:"拾取颜色",cancelPick:"取消拾取",clickToPick:"点击图像选择颜色",keepPickingUntilCancel:"持续选择颜色，直到点击取消拾取",commonColors:"常用颜色",conversions:"颜色转换",preview:"预览",onLight:"浅色背景上",onDark:"深色背景上",colorPicked:"颜色选择成功！",colorUpdated:"颜色更新成功！",colorPickError:"从图像中选择颜色失败",noImageInClipboard:"剪贴板中未找到图像",pasteFailed:"从剪贴板粘贴图像失败",pasteImage:"粘贴图像",invalidHex:"HEX颜色格式无效",invalidRgb:"RGB颜色格式无效",invalidRgbRange:"RGB值必须在0-255之间",invalidRgba:"RGBA颜色格式无效",invalidRgbaRange:"RGBA值必须为颜色0-255，透明度0-1",invalidHsl:"HSL颜色格式无效",invalidHslRange:"HSL值必须为H: 0-360, S: 0-100%, L: 0-100%",invalidHsv:"HSV颜色格式无效",invalidHsvRange:"HSV值必须为H: 0-360, S: 0-100%, V: 0-100%",invalidCmyk:"CMYK颜色格式无效",invalidCmykRange:"CMYK值必须在0-100%之间",hexPlaceholder:"输入HEX颜色 (#RRGGBB)",features:{title:"主要功能",conversions:{title:"多格式支持",description:"在 HEX、RGB、RGBA、HSL、HSV 和 CMYK 颜色格式之间转换，实时更新"},imagePicker:{title:"图像颜色提取",description:"上传图像并点击任意位置提取图像中的精确颜色"},commonColors:{title:"常用颜色调色板",description:"从常用颜色调色板中快速选择"}}},pdfViewer:{title:"PDF 查看器",description:"预览和基本编辑 PDF 文档",uploadSection:"上传 PDF",uploadTitle:"上传 PDF 文件",uploadDescription:"将您的 PDF 文件拖放到此处或点击浏览",supportedFormats:"支持的格式",selectPdf:"选择 PDF 文件",preview:"PDF 预览",download:"下载 PDF",selectAnother:"选择其他 PDF",documentInfo:"文档信息",fileName:"文件名",fileSize:"文件大小",pageCount:"页数",zoom:"缩放",page:"第",of:"页，共"}}},t1={en:Zb,zh:e1},vi=ky({legacy:!1,locale:localStorage.getItem("locale")||"zh",fallbackLocale:"zh",messages:t1}),n1=vd.flatMap(e=>e.children?.map(t=>({...t,path:`/${e.path}/${t.path}`}))||[]),Xd=cv({history:Gh("/"),routes:[{path:"/",name:"landing",children:[{path:"",name:"homepage",component:Lb},...n1]},{path:"/:pathMatch(.*)*",name:"not-found",component:Xb}]});Xd.beforeEach((e,t,n)=>{e.name==="homepage"?document.title=vi.global.t("navigation.title"):document.title=vi.global.t(`tools.${e.name}.title`),n()});function xo(e,t){t===void 0&&(t={});var n=t.insertAt;if(e&&typeof document<"u"){var o=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",n==="top"&&o.firstChild?o.insertBefore(r,o.firstChild):o.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}}xo(".vel-fade-enter-active,.vel-fade-leave-active{-webkit-transition:all .3s ease;transition:all .3s ease}.vel-fade-enter-from,.vel-fade-leave-to{opacity:0}.vel-img-swiper{display:block;position:relative}.vel-modal{background:rgba(0,0,0,.5);bottom:0;left:0;margin:0;position:fixed;right:0;top:0;z-index:9998}.vel-img-wrapper{left:50%;margin:0;position:absolute;top:50%;-webkit-transform:translate(-50% -50%);transform:translate(-50% -50%);-webkit-transition:.3s linear;transition:.3s linear;will-change:transform opacity}.vel-img,.vel-img-wrapper{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.vel-img{background-color:rgba(0,0,0,.7);-webkit-box-shadow:0 5px 20px 2px rgba(0,0,0,.7);box-shadow:0 5px 20px 2px rgba(0,0,0,.7);display:block;max-height:80vh;max-width:80vw;position:relative;-webkit-transition:-webkit-transform .3s ease-in-out;transition:-webkit-transform .3s ease-in-out;transition:transform .3s ease-in-out;transition:transform .3s ease-in-out,-webkit-transform .3s ease-in-out}@media (max-width:750px){.vel-img{max-height:95vh;max-width:85vw}}.vel-btns-wrapper{position:static}.vel-btns-wrapper .btn__close,.vel-btns-wrapper .btn__next,.vel-btns-wrapper .btn__prev{-webkit-tap-highlight-color:transparent;color:#fff;cursor:pointer;font-size:32px;opacity:.6;outline:none;position:absolute;top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);-webkit-transition:.15s linear;transition:.15s linear;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.vel-btns-wrapper .btn__close:hover,.vel-btns-wrapper .btn__next:hover,.vel-btns-wrapper .btn__prev:hover{opacity:1}.vel-btns-wrapper .btn__close.disable,.vel-btns-wrapper .btn__close.disable:hover,.vel-btns-wrapper .btn__next.disable,.vel-btns-wrapper .btn__next.disable:hover,.vel-btns-wrapper .btn__prev.disable,.vel-btns-wrapper .btn__prev.disable:hover{cursor:default;opacity:.2}.vel-btns-wrapper .btn__next{right:12px}.vel-btns-wrapper .btn__prev{left:12px}.vel-btns-wrapper .btn__close{right:10px;top:24px}@media (max-width:750px){.vel-btns-wrapper .btn__next,.vel-btns-wrapper .btn__prev{font-size:20px}.vel-btns-wrapper .btn__close{font-size:24px}.vel-btns-wrapper .btn__next{right:4px}.vel-btns-wrapper .btn__prev{left:4px}}.vel-modal.is-rtl .vel-btns-wrapper .btn__next{left:12px;right:auto}.vel-modal.is-rtl .vel-btns-wrapper .btn__prev{left:auto;right:12px}@media (max-width:750px){.vel-modal.is-rtl .vel-btns-wrapper .btn__next{left:4px;right:auto}.vel-modal.is-rtl .vel-btns-wrapper .btn__prev{left:auto;right:4px}}.vel-modal.is-rtl .vel-img-title{direction:rtl}");xo('.vel-loading{left:50%;position:absolute;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.vel-loading .ring{display:inline-block;height:64px;width:64px}.vel-loading .ring:after{-webkit-animation:ring 1.2s linear infinite;animation:ring 1.2s linear infinite;border-color:hsla(0,0%,100%,.7) transparent;border-radius:50%;border-style:solid;border-width:5px;content:" ";display:block;height:46px;margin:1px;width:46px}@-webkit-keyframes ring{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes ring{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}');xo(".vel-on-error{left:50%;position:absolute;top:50%;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%)}.vel-on-error .icon{color:#aaa;font-size:80px}");xo(".vel-img-title{bottom:60px;color:#ccc;cursor:default;font-size:12px;left:50%;line-height:1;max-width:80%;opacity:.8;overflow:hidden;position:absolute;text-align:center;text-overflow:ellipsis;-webkit-transform:translate(-50%);transform:translate(-50%);-webkit-transition:opacity .15s;transition:opacity .15s;white-space:nowrap}.vel-img-title:hover{opacity:1}");xo(".vel-icon{fill:currentColor;height:1em;overflow:hidden;vertical-align:-.15em;width:1em}");xo(".vel-toolbar{border-radius:4px;bottom:8px;display:-webkit-box;display:-ms-flexbox;display:flex;left:50%;opacity:.9;overflow:hidden;padding:0;position:absolute;-webkit-transform:translate(-50%);transform:translate(-50%)}.vel-toolbar,.vel-toolbar .toolbar-btn{background-color:#2d2d2d;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.vel-toolbar .toolbar-btn{-ms-flex-negative:0;-webkit-tap-highlight-color:transparent;color:#fff;cursor:pointer;flex-shrink:0;font-size:20px;outline:none;padding:6px 10px}.vel-toolbar .toolbar-btn:active,.vel-toolbar .toolbar-btn:hover{background-color:#3d3d3d}");const bt="vel",Nt=nt({name:"SvgIcon",props:{type:{type:String,default:""}},setup:e=>()=>B("svg",{class:`${bt}-icon icon`,"aria-hidden":"true"},[B("use",{"xlink:href":`#icon-${e.type}`},null)])}),gs=typeof window<"u",Zn=()=>{};let Zd=!1;if(gs)try{const e={};Object.defineProperty(e,"passive",{get(){Zd=!0}}),window.addEventListener("test-passive",Zn,e)}catch{}const Wl=function(e,t,n){let o=arguments.length>3&&arguments[3]!==void 0&&arguments[3];gs&&e.addEventListener(t,n,!!Zd&&{capture:!1,passive:o})},zl=(e,t,n)=>{gs&&e.removeEventListener(t,n)},o1=e=>{e.preventDefault()},r1=Object.prototype.toString,na=e=>t=>r1.call(t).slice(8,-1)===e,s1=e=>!!e&&na("Object")(e),Kl=e=>!!e&&na("String")(e);function i1(e){return e!=null}const a1=nt({name:"Toolbar",props:{zoomIn:{type:Function,default:Zn},zoomOut:{type:Function,default:Zn},rotateLeft:{type:Function,default:Zn},rotateRight:{type:Function,default:Zn},resize:{type:Function,default:Zn},rotateDisabled:{type:Boolean,default:!1},zoomDisabled:{type:Boolean,default:!1}},setup:e=>()=>B("div",{class:`${bt}-toolbar`},[!e.zoomDisabled&&B(Ce,null,[B("div",{role:"button","aria-label":"zoom in button",class:"toolbar-btn toolbar-btn__zoomin",onClick:e.zoomIn},[B(Nt,{type:"zoomin"},null)]),B("div",{role:"button","aria-label":"zoom out button",class:"toolbar-btn toolbar-btn__zoomout",onClick:e.zoomOut},[B(Nt,{type:"zoomout"},null)])]),B("div",{role:"button","aria-label":"resize image button",class:"toolbar-btn toolbar-btn__resize",onClick:e.resize},[B(Nt,{type:"resize"},null)]),!e.rotateDisabled&&B(Ce,null,[B("div",{role:"button","aria-label":"image rotate left button",class:"toolbar-btn toolbar-btn__rotate",onClick:e.rotateLeft},[B(Nt,{type:"rotate-left"},null)]),B("div",{role:"button","aria-label":"image rotate right button",class:"toolbar-btn toolbar-btn__rotate",onClick:e.rotateRight},[B(Nt,{type:"rotate-right"},null)])])])}),l1=()=>B("div",{class:`${bt}-loading`},[B("div",{class:"ring"},null)]),c1=()=>B("div",{class:`${bt}-on-error`},[B("div",{class:"ring"},null),B(Nt,{type:"img-broken"},null)]),u1=(e,t)=>{let{slots:n}=t;return B("div",{class:`${bt}-img-title`},[n.default?n.default():""])},d1=nt({name:"DefaultIcons",setup:()=>()=>B("svg",{"aria-hidden":!0,style:"position: absolute; width: 0; height: 0; overflow: hidden; visibility: hidden;"},[B("symbol",{id:"icon-rotate-right",viewBox:"0 0 1024 1024"},[B("path",{d:"M275.199914 450.496179v20.031994c0.384-38.079988 12.543996-67.423979 36.479989-87.967973 22.431993-20.351994 49.215985-30.55999 80.319975-30.55999 32.06399 0 59.295981 10.175997 81.759974 30.55999 22.815993 20.543994 34.591989 49.887984 35.359989 87.967973v123.935961c-0.768 37.887988-12.543996 67.135979-35.359989 87.679973-22.431993 20.351994-49.695984 30.75199-81.759974 31.10399a120.255962 120.255962 0 0 1-72.991978-24.895992c-21.503993-15.839995-35.359989-38.751988-41.567987-68.735979h60.831981c9.247997 23.007993 27.167992 34.495989 53.759983 34.49599 37.535988-0.384 56.863982-21.407993 57.983982-63.071981v-38.751988c-28.095991 8.863997-54.303983 13.119996-78.623975 12.735996a91.263971 91.263971 0 0 1-68.447979-27.711991c-18.847994-18.303994-28.095991-47.231985-27.711991-86.847973z m62.55998 24.863992c7.103998 24.799992 25.215992 37.343988 54.271983 37.663989 27.103992-0.288 44.703986-11.327996 52.831984-33.11999 3.135999-8.383997 2.655999-29.599991-1.28-38.559988-8.607997-19.615994-25.791992-29.695991-51.551984-30.20799-28.383991 0.576-46.303986 12.639996-53.759983 36.159988a58.719982 58.719982 0 0 0-0.512 28.063991z m390.335878 115.711964v-116.895963c-1.12-41.311987-20.447994-62.335981-57.983981-63.07198-37.727988 0.768-56.959982 21.791993-57.695982 63.07198v116.895963c0.768 41.663987 19.999994 62.68798 57.695982 63.071981 37.535988-0.384 56.863982-21.407993 57.983981-63.071981z m-174.815945 3.391999v-123.935961c0.384-38.079988 12.543996-67.423979 36.479989-87.967973 22.431993-20.351994 49.215985-30.55999 80.319975-30.55999 32.06399 0 59.295981 10.175997 81.759974 30.55999 22.815993 20.543994 34.591989 49.887984 35.359989 87.967973v123.935961c-0.768 37.887988-12.543996 67.135979-35.359989 87.679973-22.431993 20.351994-49.695984 30.75199-81.759974 31.10399-31.10399-0.384-57.887982-10.751997-80.319975-31.10399-23.935993-20.543994-36.127989-49.791984-36.479989-87.679973z m282.559912-479.07185A509.887841 509.887841 0 0 0 511.99984 0.00032C229.215928 0.00032 0 229.216248 0 512.00016s229.215928 511.99984 511.99984 511.99984 511.99984-229.215928 511.99984-511.99984c0-3.743999-0.032-7.455998-0.128-11.167997-1.631999-11.295996-8.159997-27.103992-31.87199-27.103991-27.487991 0-31.67999 21.247993-32.03199 32.06399l0.032 4.127999a30.62399 30.62399 0 0 0 0.16 2.079999H959.9997c0 247.423923-200.575937 447.99986-447.99986 447.99986S63.99998 759.424083 63.99998 512.00016 264.575917 64.0003 511.99984 64.0003a446.079861 446.079861 0 0 1 277.439913 96.22397l-94.91197 91.679971c-25.439992 24.607992-17.439995 44.991986 17.887994 45.599986l188.031942 3.295999a64.31998 64.31998 0 0 0 65.055979-62.84798l3.295999-188.127942C969.407697 15.040315 949.311703 5.792318 923.871711 30.368311l-87.999972 85.023973z",fill:""},null)]),B("symbol",{id:"icon-rotate-left",viewBox:"0 0 1024 1024"},[B("path",{d:"M275.199914 450.496179v20.031994c0.384-38.079988 12.543996-67.423979 36.479989-87.967973 22.431993-20.351994 49.215985-30.55999 80.319975-30.55999 32.06399 0 59.295981 10.175997 81.759974 30.55999 22.815993 20.543994 34.591989 49.887984 35.359989 87.967973v123.935961c-0.768 37.887988-12.543996 67.135979-35.359989 87.679973-22.431993 20.351994-49.695984 30.75199-81.759974 31.10399a120.255962 120.255962 0 0 1-72.991978-24.895992c-21.503993-15.839995-35.359989-38.751988-41.567987-68.735979h60.831981c9.247997 23.007993 27.167992 34.495989 53.759983 34.49599 37.535988-0.384 56.863982-21.407993 57.983982-63.071981v-38.751988c-28.095991 8.863997-54.303983 13.119996-78.623975 12.735996a91.263971 91.263971 0 0 1-68.447979-27.711991c-18.847994-18.303994-28.095991-47.231985-27.711991-86.847973z m62.55998 24.863992c7.103998 24.799992 25.215992 37.343988 54.271983 37.663989 27.103992-0.288 44.703986-11.327996 52.831984-33.11999 3.135999-8.383997 2.655999-29.599991-1.28-38.559988-8.607997-19.615994-25.791992-29.695991-51.551984-30.20799-28.383991 0.576-46.303986 12.639996-53.759983 36.159988a58.719982 58.719982 0 0 0-0.512 28.063991z m390.335878 115.711964v-116.895963c-1.12-41.311987-20.447994-62.335981-57.983981-63.07198-37.727988 0.768-56.959982 21.791993-57.695982 63.07198v116.895963c0.768 41.663987 19.999994 62.68798 57.695982 63.071981 37.535988-0.384 56.863982-21.407993 57.983981-63.071981z m-174.815945 3.391999v-123.935961c0.384-38.079988 12.543996-67.423979 36.479989-87.967973 22.431993-20.351994 49.215985-30.55999 80.319975-30.55999 32.06399 0 59.295981 10.175997 81.759974 30.55999 22.815993 20.543994 34.591989 49.887984 35.359989 87.967973v123.935961c-0.768 37.887988-12.543996 67.135979-35.359989 87.679973-22.431993 20.351994-49.695984 30.75199-81.759974 31.10399-31.10399-0.384-57.887982-10.751997-80.319975-31.10399-23.935993-20.543994-36.127989-49.791984-36.479989-87.679973zM188.159941 115.392284A509.887841 509.887841 0 0 1 511.99984 0.00032c282.783912 0 511.99984 229.215928 511.99984 511.99984s-229.215928 511.99984-511.99984 511.99984S0 794.784072 0 512.00016c0-3.743999 0.032-7.455998 0.128-11.167997 1.631999-11.295996 8.159997-27.103992 31.87199-27.103991 27.487991 0 31.67999 21.247993 32.03199 32.06399L63.99998 509.920161a30.62399 30.62399 0 0 1-0.16 2.079999H63.99998c0 247.423923 200.575937 447.99986 447.99986 447.99986s447.99986-200.575937 447.99986-447.99986S759.423763 64.0003 511.99984 64.0003a446.079861 446.079861 0 0 0-277.439913 96.22397l94.91197 91.679971c25.439992 24.607992 17.439995 44.991986-17.887994 45.599986L123.551961 300.800226a64.31998 64.31998 0 0 1-65.055979-62.84798l-3.295999-188.127942C54.591983 15.040315 74.687977 5.792318 100.127969 30.368311l87.999972 85.023973z",fill:""},null)]),B("symbol",{id:"icon-resize",viewBox:"0 0 1024 1024"},[B("path",{d:"M456.036919 791.8108 270.553461 791.8108 460.818829 601.572038l-39.593763-39.567157L231.314785 751.915162l0.873903-183.953615c0-15.465227-12.515035-27.981285-27.981285-27.981285s-27.981285 12.515035-27.981285 27.981285l0 251.829516c0 8.3072 3.415796 14.975063 8.826016 19.564591 5.082762 5.192256 12.132318 8.416693 19.947308 8.416693l251.036453 0c15.46625 0 27.981285-12.514012 27.981285-27.981285C484.018204 804.325835 471.504192 791.8108 456.036919 791.8108zM838.945819 184.644347c-5.082762-5.191232-12.132318-8.416693-19.947308-8.416693L567.961034 176.227654c-15.46625 0-27.981285 12.515035-27.981285 27.981285 0 15.46625 12.514012 27.981285 27.981285 27.981285l185.483458 0L563.206754 422.427962l39.567157 39.567157 189.910281-189.910281-0.873903 183.953615c0 15.46625 12.514012 27.981285 27.981285 27.981285s27.981285-12.514012 27.981285-27.981285L847.772858 204.208938C847.771835 195.902762 844.356039 189.234899 838.945819 184.644347zM847.771835 64.303538 176.227142 64.303538c-61.809741 0-111.924115 50.115398-111.924115 111.924115l0 671.544693c0 61.809741 50.114374 111.924115 111.924115 111.924115l671.544693 0c61.809741 0 111.924115-50.114374 111.924115-111.924115l0-671.544693C959.69595 114.418936 909.581576 64.303538 847.771835 64.303538zM903.733381 847.772346c0 30.878265-25.056676 55.962569-55.962569 55.962569L176.227142 903.734916c-30.90487 0-55.962569-25.084305-55.962569-55.962569l0-671.544693c0-30.9325 25.056676-55.962569 55.962569-55.962569l671.544693 0c30.90487 0 55.962569 25.03007 55.962569 55.962569L903.734404 847.772346z"},null)]),B("symbol",{id:"icon-img-broken",viewBox:"0 0 1024 1024"},[B("path",{d:"M810.666667 128H213.333333c-46.933333 0-85.333333 38.4-85.333333 85.333333v597.333334c0 46.933333 38.4 85.333333 85.333333 85.333333h597.333334c46.933333 0 85.333333-38.4 85.333333-85.333333V213.333333c0-46.933333-38.4-85.333333-85.333333-85.333333z m0 682.666667H213.333333v-195.413334l42.24 42.24 170.666667-170.666666 170.666667 170.666666 170.666666-170.24L810.666667 530.346667V810.666667z m0-401.493334l-43.093334-43.093333-170.666666 171.093333-170.666667-170.666666-170.666667 170.666666-42.24-42.666666V213.333333h597.333334v195.84z"},null)]),B("symbol",{id:"icon-prev",viewBox:"0 0 1024 1024"},[B("path",{d:"M784.652701 955.6957 346.601985 517.644983c-2.822492-2.822492-2.822492-7.902977 0-11.289967l439.179713-439.179713c6.77398-6.77398 10.725469-16.370452 10.725469-25.966924L796.507166 36.692393c0-20.32194-16.370452-36.692393-36.692393-36.692393l-4.515987 0c-9.596472 0-19.192944 3.951488-25.966924 10.725469L250.072767 489.420066c-12.418964 12.418964-12.418964 32.740904 0 45.159868l477.565601 477.565601c7.338479 7.338479 17.499449 11.854465 28.224917 11.854465l0 0c22.015436 0 40.079383-18.063947 40.079383-40.079383l0 0C796.507166 973.759647 791.99118 963.598677 784.652701 955.6957z"},null)]),B("symbol",{id:"icon-next",viewBox:"0 0 1024 1024"},[B("path",{d:"M246.121279 955.6957l438.050717-438.050717c2.822492-2.822492 2.822492-7.902977 0-11.289967L244.992282 67.175303c-6.77398-6.77398-10.725469-16.370452-10.725469-25.966924L234.266814 36.692393C234.266814 16.370452 250.637266 0 270.959206 0l4.515987 0c9.596472 0 19.192944 3.951488 25.966924 10.725469l478.694598 478.694598c12.418964 12.418964 12.418964 32.740904 0 45.159868l-477.565601 477.565601c-7.338479 7.338479-17.499449 11.854465-28.224917 11.854465l0 0c-22.015436 0-40.079383-18.063947-40.079383-40.079383l0 0C234.266814 973.759647 238.7828 963.598677 246.121279 955.6957z"},null)]),B("symbol",{id:"icon-zoomin",viewBox:"0 0 1024 1024"},[B("path",{d:"M725.504 652.864c46.4-61.44 71.744-136.448 71.744-218.752C797.248 230.464 632.768 64 430.656 64S64 230.464 64 434.112C64 639.36 228.48 805.76 430.656 805.76c86.656 0 164.48-30.144 227.52-81.088L889.984 960 960 891.264l-234.496-238.4z m-294.848 67.456c-155.776 0-282.624-128.896-282.624-286.208s126.848-286.208 282.624-286.208 282.624 128.896 282.624 286.208-126.912 286.208-282.624 286.208z"},null),B("path",{d:"M235.712 369.92h390.72v127.104H235.712z"},null),B("path",{d:"M367.488 238.144h127.104v390.72H367.488z"},null)]),B("symbol",{id:"icon-close",viewBox:"0 0 1024 1024"},[B("path",{d:"M570.24 512l259.2 259.2-58.88 58.24L512 570.24l-261.12 261.12-58.24-58.24L453.76 512 194.56 252.8l58.24-58.24L512 453.76l261.12-261.12 58.24 58.24z"},null)]),B("symbol",{id:"icon-zoomout",viewBox:"0 0 1024 1024"},[B("path",{d:"M725.504 652.864c46.4-61.44 71.744-136.448 71.744-218.752C797.248 230.464 632.768 64 430.656 64S64 230.464 64 434.112C64 639.36 228.48 805.76 430.656 805.76c86.656 0 164.48-30.144 227.52-81.088L889.984 960 960 891.264l-234.496-238.4z m-294.848 67.456c-155.776 0-282.624-128.896-282.624-286.208s126.848-286.208 282.624-286.208 282.624 128.896 282.624 286.208-126.912 286.208-282.624 286.208z"},null),B("path",{d:"M235.712 369.92h390.72v127.104H235.712z"},null)])])}),Uo=gs?window:global;let ql=Date.now();function f1(e){const t=Date.now(),n=Math.max(0,16-(t-ql)),o=setTimeout(e,n);return ql=t+n,o}function Ds(e){return(Uo.requestAnimationFrame||f1).call(Uo,e)}function Yl(e){(Uo.cancelAnimationFrame||Uo.clearTimeout).call(Uo,e)}function Ql(e,t){const n=e.clientX-t.clientX,o=e.clientY-t.clientY;return Math.sqrt(n*n+o*o)}function Ms(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!$t(e)}var Gs=nt({name:"VueEasyLightbox",props:{imgs:{type:[Array,String],default:()=>""},visible:{type:Boolean,default:!1},index:{type:Number,default:0},scrollDisabled:{type:Boolean,default:!0},escDisabled:{type:Boolean,default:!1},moveDisabled:{type:Boolean,default:!1},titleDisabled:{type:Boolean,default:!1},maskClosable:{type:Boolean,default:!0},teleport:{type:[String,Object],default:null},swipeTolerance:{type:Number,default:50},loop:{type:Boolean,default:!1},rtl:{type:Boolean,default:!1},zoomScale:{type:Number,default:.12},maxZoom:{type:Number,default:3},minZoom:{type:Number,default:.1},rotateDisabled:{type:Boolean,default:!1},zoomDisabled:{type:Boolean,default:!1},pinchDisabled:{type:Boolean,default:!1},dblclickDisabled:{type:Boolean,default:!1}},emits:{hide:()=>!0,"on-error":e=>!0,"on-prev":(e,t)=>!0,"on-next":(e,t)=>!0,"on-prev-click":(e,t)=>!0,"on-next-click":(e,t)=>!0,"on-index-change":(e,t)=>!0,"on-rotate":e=>!0},setup(e,t){let{emit:n,slots:o}=t;const{imgRef:r,imgState:s,setImgSize:i}=(()=>{const b=Fe(),R=vn({width:0,height:0,maxScale:1});return{imgRef:b,imgState:R,setImgSize:()=>{if(b.value){const{width:k,height:J,naturalWidth:z}=b.value;R.maxScale=z/k,R.width=k,R.height=J}}}})(),a=Fe(e.index),l=Fe(""),c=vn({scale:1,lastScale:1,rotateDeg:0,top:0,left:0,initX:0,initY:0,lastX:0,lastY:0,touches:[]}),u=vn({loadError:!1,loading:!1,dragging:!1,gesturing:!1,wheeling:!1}),d=pe((()=>{return b=e.imgs,na("Array")(b)?e.imgs.map((R=>typeof R=="string"?{src:R}:(function(k){return s1(k)&&Kl(k.src)})(R)?R:void 0)).filter(i1):Kl(e.imgs)?[{src:e.imgs}]:[];var b})),f=pe((()=>d.value[a.value])),y=pe((()=>d.value[a.value]?.src)),C=pe((()=>d.value[a.value]?.title)),w=pe((()=>d.value[a.value]?.alt)),L=pe((()=>({cursor:u.loadError?"default":e.moveDisabled?u.dragging?"grabbing":"grab":"move",top:`calc(50% + ${c.top}px)`,left:`calc(50% + ${c.left}px)`,transition:u.dragging||u.gesturing?"none":"",transform:`translate(-50%, -50%) scale(${c.scale}) rotate(${c.rotateDeg}deg)`}))),A=()=>{n("hide")},I=()=>{c.scale=1,c.lastScale=1,c.rotateDeg=0,c.top=0,c.left=0,u.loadError=!1,u.dragging=!1,u.loading=!0},p=(b,R)=>{const k=a.value;I(),a.value=b,d.value[a.value]===d.value[b]&&Jn((()=>{u.loading=!1})),e.visible&&k!==b&&(R&&R(k,b),n("on-index-change",k,b))},h=()=>{const b=a.value,R=e.loop?(b+1)%d.value.length:b+1;!e.loop&&R>d.value.length-1||p(R,((k,J)=>{n("on-next",k,J),n("on-next-click",k,J)}))},S=()=>{const b=a.value;let R=b-1;if(b===0){if(!e.loop)return;R=d.value.length-1}p(R,((k,J)=>{n("on-prev",k,J),n("on-prev-click",k,J)}))},E=b=>{Math.abs(1-b)<.05?b=1:Math.abs(s.maxScale-b)<.05&&(b=s.maxScale),c.lastScale=c.scale,c.scale=b},F=()=>{const b=c.scale+e.zoomScale;b<s.maxScale*e.maxZoom&&E(b)},M=()=>{const b=c.scale-e.zoomScale;b>e.minZoom&&E(b)},N=()=>{const b=c.rotateDeg%360;n("on-rotate",Math.abs(b<0?b+360:b))},G=()=>{c.rotateDeg-=90,N()},$=()=>{c.rotateDeg+=90,N()},T=()=>{c.scale=1,c.top=0,c.left=0},W=function(){let b=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return!e.moveDisabled&&b===0},{onMouseDown:se,onMouseMove:ee,onMouseUp:q}=((b,R,k)=>{let J,z=!1;return{onMouseDown:m=>{b.initX=b.lastX=m.clientX,b.initY=b.lastY=m.clientY,R.dragging=!0,z=!1,m.stopPropagation()},onMouseUp:m=>{k(m.button)&&Yl(J),R.dragging=!1,z=!1},onMouseMove:m=>{if(R.dragging)if(k(m.button)){if(z)return;z=!0,J=Ds((()=>{const{top:v,left:x,lastY:U,lastX:j}=b;b.top=v-U+m.clientY,b.left=x-j+m.clientX,b.lastX=m.clientX,b.lastY=m.clientY,z=!1}))}else b.lastX=m.clientX,b.lastY=m.clientY;m.stopPropagation()}}})(c,u,W),{onTouchStart:ie,onTouchMove:X,onTouchEnd:be}=((b,R,k,J,z)=>{let m,v=!1;return{onTouchStart:x=>{const{touches:U}=x;U.length>1&&z()?(k.gesturing=!0,R.touches=U):(R.initX=R.lastX=U[0].clientX,R.initY=R.lastY=U[0].clientY,k.dragging=!0),x.stopPropagation()},onTouchMove:x=>{if(v)return;const{touches:U}=x,{lastX:j,lastY:H,left:O,top:D,scale:oe}=R;if(!k.gesturing&&k.dragging){if(!U[0])return;const{clientX:ne,clientY:_e}=U[0];J()?m=Ds((()=>{R.lastX=ne,R.lastY=_e,R.top=D-H+_e,R.left=O-j+ne,v=!1})):(R.lastX=ne,R.lastY=_e)}else k.gesturing&&R.touches.length>1&&U.length>1&&z()&&(m=Ds((()=>{const ne=(Ql(R.touches[0],R.touches[1])-Ql(U[0],U[1]))/b.width;R.touches=U;const _e=oe-1.3*ne;_e>.5&&_e<1.5*b.maxScale&&(R.scale=_e),v=!1})))},onTouchEnd:()=>{Yl(m),k.dragging=!1,k.gesturing=!1,v=!1}}})(s,c,u,W,(()=>!e.pinchDisabled)),Ye=()=>{e.dblclickDisabled||(c.scale!==s.maxScale?(c.lastScale=c.scale,c.scale=s.maxScale):c.scale=c.lastScale)},Ae=b=>{u.loadError||u.gesturing||u.loading||u.dragging||u.wheeling||!e.scrollDisabled||e.zoomDisabled||(u.wheeling=!0,setTimeout((()=>{u.wheeling=!1}),80),b.deltaY<0?F():M())},Ie=b=>{const R=b;e.visible&&(!e.escDisabled&&R.key==="Escape"&&e.visible&&A(),R.key==="ArrowLeft"&&(e.rtl?h():S()),R.key==="ArrowRight"&&(e.rtl?S():h()))},lt=()=>{e.maskClosable&&A()},ot=()=>{i()},yt=()=>{u.loading=!1},Ge=b=>{u.loading=!1,u.loadError=!0,n("on-error",b)},V=()=>{e.visible&&i()};ft((()=>e.index),(b=>{b<0||b>=d.value.length||p(b)})),ft((()=>u.dragging),((b,R)=>{const k=!b&&R;if(!W()&&k){const J=c.lastX-c.initX,z=c.lastY-c.initY,m=e.swipeTolerance;Math.abs(J)>Math.abs(z)&&(J<-1*m?h():J>m&&S())}})),ft((()=>e.visible),(b=>{if(b){I();const R=d.value.length;if(R===0)return a.value=0,u.loading=!1,void Jn((()=>u.loadError=!0));a.value=e.index>=R?R-1:e.index<0?0:e.index,e.scrollDisabled&&Y()}else e.scrollDisabled&&K()}));const Y=()=>{document&&(l.value=document.body.style.overflowY,document.body.style.overflowY="hidden")},K=()=>{document&&(document.body.style.overflowY=l.value)};ln((()=>{Wl(document,"keydown",Ie),Wl(window,"resize",V)})),cr((()=>{zl(document,"keydown",Ie),zl(window,"resize",V),e.scrollDisabled&&K()}));const Z=()=>u.loading?o.loading?o.loading({key:"loading"}):B(l1,{key:"img-loading"},null):u.loadError?o.onerror?o.onerror({key:"onerror"}):B(c1,{key:"img-on-error"},null):B("div",{class:`${bt}-img-wrapper`,style:L.value,key:"img-wrapper"},[B("img",{alt:w.value,ref:r,draggable:"false",class:`${bt}-img`,src:y.value,onMousedown:se,onMouseup:q,onMousemove:ee,onTouchstart:ie,onTouchmove:X,onTouchend:be,onLoad:ot,onDblclick:Ye,onDragstart:b=>{b.preventDefault()}},null)]),ue=()=>{if(o["prev-btn"])return o["prev-btn"]({prev:S});if(d.value.length<=1)return;const b=!e.loop&&a.value<=0;return B("div",{role:"button","aria-label":"previous image button",class:"btn__prev "+(b?"disable":""),onClick:S},[e.rtl?B(Nt,{type:"next"},null):B(Nt,{type:"prev"},null)])},he=()=>{if(o["next-btn"])return o["next-btn"]({next:h});if(d.value.length<=1)return;const b=!e.loop&&a.value>=d.value.length-1;return B("div",{role:"button","aria-label":"next image button",class:"btn__next "+(b?"disable":""),onClick:h},[e.rtl?B(Nt,{type:"prev"},null):B(Nt,{type:"next"},null)])},_=()=>{if(!(e.titleDisabled||u.loading||u.loadError))return o.title?o.title({currentImg:f.value}):C.value?B(u1,null,{default:()=>[C.value]}):void 0},g=()=>{let b;if(e.visible)return B("div",{onTouchmove:o1,class:[`${bt}-modal`,e.rtl?"is-rtl":""],onClick:Qu(lt,["self"]),onWheel:Ae},[B(d1,null,null),B(io,{name:`${bt}-fade`,mode:"out-in"},Ms(b=Z())?b:{default:()=>[b]}),B("img",{style:"display:none;",src:y.value,onError:Ge,onLoad:yt},null),B("div",{class:`${bt}-btns-wrapper`},[ue(),he(),_(),o["close-btn"]?o["close-btn"]({close:A}):B("div",{role:"button","aria-label":"close image preview button",class:"btn__close",onClick:A},[B(Nt,{type:"close"},null)]),o.toolbar?o.toolbar({toolbarMethods:{zoomIn:F,zoomOut:M,rotate:G,rotateLeft:G,rotateRight:$,resize:T},zoomIn:F,zoomOut:M,rotate:G,rotateLeft:G,rotateRight:$,resize:T}):B(a1,{zoomIn:F,zoomOut:M,resize:T,rotateLeft:G,rotateRight:$,rotateDisabled:e.rotateDisabled,zoomDisabled:e.zoomDisabled},null)])])};return()=>{let b;if(e.teleport){let R;return B(Jc,{to:e.teleport},{default:()=>[B(io,{name:`${bt}-fade`},Ms(R=g())?R:{default:()=>[R]})]})}return B(io,{name:`${bt}-fade`},Ms(b=g())?b:{default:()=>[b]})}}});const p1=Object.assign(Gs,{install:e=>{e.component(Gs.name,Gs)}}),hs=Hr(V_);hs.use(Xd);hs.use(vi);hs.use(p1);hs.mount("#app");export{Vu as $,te as A,vo as B,kn as C,wf as D,fe as E,Ce as F,Cf as G,Tf as H,Of as I,ec as J,pt as K,gf as L,g1 as M,gt as N,m1 as O,lo as P,Gt as Q,Qu as R,St as S,Bi as T,B as U,Ut as V,Qy as W,im as X,qo as Y,om as Z,ke as _,P as a,Ou as a0,ln as a1,bo as a2,bn as a3,vn as a4,Ni as a5,oh as a6,Jn as a7,io as a8,Jc as a9,So as aa,Hr as ab,tg as b,we as c,nt as d,Lc as e,Dt as f,Xo as g,Vn as h,pe as i,ji as j,Dn as k,Oe as l,Ee as m,yi as n,me as o,wt as p,tf as q,Fe as r,yo as s,re as t,Bn as u,zu as v,ft as w,je as x,xe as y,no as z};
