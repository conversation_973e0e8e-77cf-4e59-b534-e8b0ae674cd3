import{d as Z,u as ee,r as b,a1 as te,a2 as se,c as v,a as o,t as a,f as O,S as I,F as J,k as A,a3 as ne,o as g}from"./index-CkZTMFXG.js";const oe={class:"p-4 max-w-4xl mx-auto"},re={class:"text-2xl font-bold mb-6"},le={class:"mb-6 p-4 rounded-lg bg-blue-50 dark:bg-blue-900"},ae={class:"flex items-center space-x-4"},ie={class:"flex items-center"},ce={key:0,class:"text-sm text-gray-600 dark:text-gray-300"},de={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ue={class:"border rounded-lg p-4"},fe={class:"text-xl font-semibold mb-4"},ve={class:"mb-4"},ge=["disabled"],be={key:0,class:"mt-4"},we={class:"font-medium mb-2"},he={class:"space-y-2"},me={class:"font-medium"},ye={class:"text-sm text-gray-500"},pe=["onClick","disabled"],Fe={key:1,class:"text-gray-500 italic"},Te={class:"border rounded-lg p-4"},Re={class:"text-xl font-semibold mb-4"},_e={key:0,class:"space-y-4"},Se={class:"block mb-2 font-medium"},Ce=["disabled"],ke=["disabled"],xe={key:0,class:"mt-4"},De={class:"flex justify-between mb-1"},$e={class:"w-full bg-gray-200 rounded-full h-2.5"},ze={key:1,class:"mt-4"},Me={class:"font-medium mb-2"},Be={class:"space-y-2"},Ne={class:"font-medium"},Oe={class:"text-sm text-gray-500"},Ie=["href","download"],Je={key:1,class:"text-gray-500 italic"},Ae={class:"mt-6 border rounded-lg p-4"},Le={class:"text-xl font-semibold mb-4"},Ee={class:"bg-gray-100 dark:bg-gray-800 p-3 rounded h-40 overflow-y-auto font-mono text-sm"},Pe=Z({__name:"WebRtcFileTransfer",setup(qe){const{t:s}=ee(),w=b("disconnected"),k=b(!1),h=b(!1),R=b(""),p=b([]),f=b(null),T=b(!1),F=b(0),z=b([]),x=b([]),M=b(null);let i=null,c=null,d=null,D="",u=null;const B=b([]),N=e=>{if(e===0)return"0 Bytes";const t=1024,r=["Bytes","KB","MB","GB"],l=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,l)).toFixed(2))+" "+r[l]},n=(e,t="info")=>{const l=new Date().toTimeString().split(" ")[0];x.value.push({timestamp:l,message:e,type:t}),x.value.length>100&&x.value.shift()},P=()=>{const e=[3e3,3001,3002,3003];let t=0;const r=()=>{if(t>=e.length){n(s("tools.webRtcFileTransfer.logs.signalServerConnectionFailed",{error:"No available ports"}),"error");return}const l=e[t];n(s("tools.webRtcFileTransfer.logs.connectingToSignalServer",{port:l}),"info");try{d=new WebSocket(`wss://tools.codeemo.cn/webrtc:${l}`),d.onopen=()=>{h.value=!0,n(s("tools.webRtcFileTransfer.logs.signalServerConnected"),"success"),j()},d.onmessage=y=>{H(y.data)},d.onclose=()=>{h.value&&(h.value=!1,n(s("tools.webRtcFileTransfer.logs.signalServerDisconnected"),"info"))},d.onerror=y=>{h.value=!1,n(s("tools.webRtcFileTransfer.logs.signalServerError",{error:y.type||y.toString()}),"error"),t++,setTimeout(r,1e3)}}catch(y){h.value=!1,n(s("tools.webRtcFileTransfer.logs.signalServerConnectionFailed",{error:y.toString()}),"error"),t++,setTimeout(r,1e3)}};r()},H=e=>{try{const t=JSON.parse(e);switch(t.type){case"id":R.value=t.id,n(s("tools.webRtcFileTransfer.logs.receivedDeviceId",{id:t.id}),"info");break;case"devices":p.value=t.devices,k.value=!1,n(s("tools.webRtcFileTransfer.logs.devicesFound",{count:t.devices.length}),"success");break;case"device-discovered":p.value.some(r=>r.id===t.id)||(p.value.push({id:t.id,name:t.name}),n(s("tools.webRtcFileTransfer.logs.deviceDiscovered",{id:t.id}),"info"));break;case"device-disconnected":p.value=p.value.filter(r=>r.id!==t.id),n(s("tools.webRtcFileTransfer.logs.deviceDisconnected",{id:t.id}),"info");break;case"offer":U(t.source,t.sdp);break;case"answer":W(t.sdp);break;case"ice-candidate":V(t.candidate);break;case"connection-request":B.value.push({id:t.source,name:t.name||t.source}),n(s("tools.webRtcFileTransfer.logs.connectionRequestReceived",{id:t.source}),"info");break}}catch(t){n(s("tools.webRtcFileTransfer.logs.messageParseError",{error:t.toString()}),"error")}},U=async(e,t)=>{const r=B.value.findIndex(l=>l.id===e);if(r===-1){n(s("tools.webRtcFileTransfer.logs.unexpectedOffer",{id:e}),"warning");return}B.value.splice(r,1),i||await L(!1);try{await i?.setRemoteDescription(new RTCSessionDescription(t));const l=await i?.createAnswer();await i?.setLocalDescription(l),d?.send(JSON.stringify({type:"answer",target:e,sdp:l})),n(s("tools.webRtcFileTransfer.logs.answerSent"),"info")}catch(l){n(s("tools.webRtcFileTransfer.logs.offerHandlingFailed",{error:l.toString()}),"error")}},W=async e=>{try{await i?.setRemoteDescription(new RTCSessionDescription(e)),n(s("tools.webRtcFileTransfer.logs.connectionEstablished"),"success"),u&&(clearTimeout(u),u=null)}catch(t){n(s("tools.webRtcFileTransfer.logs.answerHandlingFailed",{error:t.toString()}),"error"),_()}},V=async e=>{try{await i?.addIceCandidate(new RTCIceCandidate(e)),n(s("tools.webRtcFileTransfer.logs.iceCandidateAdded"),"info")}catch(t){n(s("tools.webRtcFileTransfer.logs.iceCandidateFailed",{error:t.toString()}),"error")}},L=async(e=!0)=>{try{if(i=new RTCPeerConnection({iceServers:[{urls:["turn:turn.codeemo.cn"],username:"codeemo",credential:"codeemo"}]}),e&&(c=i.createDataChannel("fileTransfer",{ordered:!0,maxRetransmits:5}),E()),i.onicecandidate=t=>{t.candidate&&d&&(d.send(JSON.stringify({type:"ice-candidate",target:D,candidate:t.candidate})),n(s("tools.webRtcFileTransfer.logs.iceCandidateSent"),"info"))},i.onconnectionstatechange=()=>{n(s("tools.webRtcFileTransfer.logs.connectionStateChange",{state:i?.connectionState}),"info"),i?.connectionState==="connected"?(w.value="connected",u&&(clearTimeout(u),u=null)):(i?.connectionState==="disconnected"||i?.connectionState==="failed")&&(w.value="disconnected",_())},i.ondatachannel=t=>{c=t.channel,E()},n(s("tools.webRtcFileTransfer.logs.webRTCInitialized"),"success"),e&&i&&d){const t=await i.createOffer();await i.setLocalDescription(t),d.send(JSON.stringify({type:"offer",target:D,sdp:t})),u=window.setTimeout(()=>{n(s("tools.webRtcFileTransfer.logs.connectionTimeout"),"warning"),_()},3e4),n(s("tools.webRtcFileTransfer.logs.offerSent"),"info")}}catch(t){n(s("tools.webRtcFileTransfer.logs.webRTCInitFailed",{error:t.toString()}),"error"),_()}},E=()=>{c&&(c.onopen=()=>{w.value="connected",n(s("tools.webRtcFileTransfer.logs.dataChannelOpened"),"success"),u&&(clearTimeout(u),u=null)},c.onclose=()=>{w.value="disconnected",n(s("tools.webRtcFileTransfer.logs.dataChannelClosed"),"info")},c.onerror=e=>{n(s("tools.webRtcFileTransfer.logs.dataChannelError",{error:e.toString()}),"error"),_()},c.onmessage=e=>{G(e.data)})},_=()=>{w.value="disconnected",D="",u&&(clearTimeout(u),u=null),i&&(i.close(),i=null),c&&(c.close(),c=null),n(s("tools.webRtcFileTransfer.logs.connectionReset"),"info")},G=e=>{try{if(e instanceof ArrayBuffer)q(e);else{const t=JSON.parse(e);t.type==="file-metadata"&&K(t)}}catch{e instanceof ArrayBuffer?q(e):n(s("tools.webRtcFileTransfer.logs.messageReceived",{type:typeof e}),"info")}};let m=null,$=[],S=0;const K=e=>{m=e,$=[],S=0,n(s("tools.webRtcFileTransfer.logs.receivingFile",{name:e.name,size:N(e.size)}),"info")},q=e=>{if(!m){n(s("tools.webRtcFileTransfer.logs.unexpectedDataChunk"),"warning");return}$.push(e),S+=e.byteLength;const t=Math.floor(S/m.size*100);if(F.value=t,S>=m.size){const r=new Blob($,{type:m.mimeType}),l=URL.createObjectURL(r);z.value.push({name:m.name,size:m.size,url:l}),m=null,$=[],S=0,F.value=0,n(s("tools.webRtcFileTransfer.logs.fileReceived",{name:m?.name||"unknown"}),"success")}},j=async()=>{if(!h.value||!d){n(s("tools.webRtcFileTransfer.logs.notConnectedToSignalServer"),"error");return}k.value=!0,p.value=[],n(s("tools.webRtcFileTransfer.logs.startDiscovery"),"info"),d.send(JSON.stringify({type:"discover"}))},Q=async e=>{D=e,w.value="connecting",n(s("tools.webRtcFileTransfer.logs.connectingToDevice",{deviceId:e}),"info"),d?.send(JSON.stringify({type:"connection-request",target:e,source:R.value,name:"Device "+R.value})),await L(!0)},X=e=>{const t=e.target;t.files&&t.files.length>0&&(f.value=t.files[0],n(s("tools.webRtcFileTransfer.logs.fileSelected",{name:f.value.name,size:N(f.value.size)}),"info"))},Y=async()=>{if(!f.value||!c||c.readyState!=="open"){n(s("tools.webRtcFileTransfer.logs.channelNotReady"),"error");return}T.value=!0,F.value=0,n(s("tools.webRtcFileTransfer.logs.sendingFile",{name:f.value.name}),"info");try{const e={type:"file-metadata",name:f.value.name,size:f.value.size,mimeType:f.value.type};c.send(JSON.stringify(e));const t=new FileReader,r=16384;let l=0;const y=()=>{if(l>=f.value.size){T.value=!1,n(s("tools.webRtcFileTransfer.logs.fileSent",{name:f.value.name}),"success"),M.value&&(M.value.value=""),f.value=null;return}const C=f.value.slice(l,l+r);t.readAsArrayBuffer(C)};t.onload=C=>{if(c&&c.readyState==="open")c.send(C.target.result);else throw new Error("Data channel is not open");l+=r,F.value=Math.min(100,Math.floor(l/f.value.size*100)),setTimeout(y,0)},t.onerror=C=>{throw C},y()}catch(e){T.value=!1,n(s("tools.webRtcFileTransfer.logs.sendFileFailed",{error:e.toString()}),"error")}};return te(()=>{P(),n(s("tools.webRtcFileTransfer.logs.initialized"),"success")}),se(()=>{i&&i.close(),d&&d.close(),c&&c.close(),u&&clearTimeout(u)}),(e,t)=>(g(),v("div",oe,[o("h1",re,a(e.$t("tools.webRtcFileTransfer.title")),1),o("div",le,[o("div",ae,[o("div",ie,[o("div",{class:I(["w-3 h-3 rounded-full mr-2",w.value==="connected"?"bg-green-500":w.value==="connecting"?"bg-yellow-500":"bg-red-500"])},null,2),o("span",null,a(e.$t(`tools.webRtcFileTransfer.status.${w.value}`)),1)]),R.value?(g(),v("div",ce,a(e.$t("tools.webRtcFileTransfer.deviceId"))+": "+a(R.value),1)):O("",!0)])]),o("div",de,[o("div",ue,[o("h2",fe,a(e.$t("tools.webRtcFileTransfer.discovery.title")),1),o("div",ve,[o("button",{onClick:j,disabled:k.value||!h.value,class:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"},a(k.value?e.$t("tools.webRtcFileTransfer.discovery.searching"):e.$t("tools.webRtcFileTransfer.discovery.search")),9,ge),o("div",{class:I(["mt-2 text-sm",h.value?"text-green-600":"text-red-600"])},a(h.value?e.$t("tools.webRtcFileTransfer.discovery.connected"):e.$t("tools.webRtcFileTransfer.discovery.disconnected")),3)]),p.value.length>0?(g(),v("div",be,[o("h3",we,a(e.$t("tools.webRtcFileTransfer.discovery.foundDevices"))+": ",1),o("ul",he,[(g(!0),v(J,null,A(p.value,r=>(g(),v("li",{key:r.id,class:"flex justify-between items-center p-2 border rounded hover:bg-gray-50 dark:hover:bg-gray-800"},[o("div",null,[o("div",me,a(r.name||e.$t("tools.webRtcFileTransfer.discovery.unknownDevice")),1),o("div",ye,a(r.id),1)]),o("button",{onClick:l=>Q(r.id),disabled:w.value==="connecting",class:"px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600 disabled:opacity-50"},a(e.$t("tools.webRtcFileTransfer.discovery.connect")),9,pe)]))),128))])])):(g(),v("div",Fe,a(e.$t("tools.webRtcFileTransfer.discovery.noDevices")),1))]),o("div",Te,[o("h2",Re,a(e.$t("tools.webRtcFileTransfer.transfer.title")),1),w.value==="connected"?(g(),v("div",_e,[o("div",null,[o("label",Se,a(e.$t("tools.webRtcFileTransfer.transfer.selectFile")),1),o("input",{type:"file",onChange:X,ref_key:"fileInput",ref:M,class:"w-full p-2 border rounded",disabled:T.value},null,40,Ce)]),o("div",null,[o("button",{onClick:Y,disabled:!f.value||T.value,class:"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"},a(T.value?e.$t("tools.webRtcFileTransfer.transfer.sending"):e.$t("tools.webRtcFileTransfer.transfer.send")),9,ke)]),F.value>0?(g(),v("div",xe,[o("div",De,[o("span",null,a(e.$t("tools.webRtcFileTransfer.transfer.progress")),1),o("span",null,a(F.value)+"%",1)]),o("div",$e,[o("div",{class:"bg-blue-600 h-2.5 rounded-full",style:ne({width:F.value+"%"})},null,4)])])):O("",!0),z.value.length>0?(g(),v("div",ze,[o("h3",Me,a(e.$t("tools.webRtcFileTransfer.transfer.receivedFiles"))+": ",1),o("ul",Be,[(g(!0),v(J,null,A(z.value,(r,l)=>(g(),v("li",{key:l,class:"flex justify-between items-center p-2 border rounded"},[o("div",null,[o("div",Ne,a(r.name),1),o("div",Oe,a(N(r.size)),1)]),o("a",{href:r.url,download:r.name,class:"px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"},a(e.$t("tools.webRtcFileTransfer.transfer.download")),9,Ie)]))),128))])])):O("",!0)])):(g(),v("div",Je,a(e.$t("tools.webRtcFileTransfer.transfer.connectFirst")),1))])]),o("div",Ae,[o("h2",Le,a(e.$t("tools.webRtcFileTransfer.logs.title")),1),o("div",Ee,[(g(!0),v(J,null,A(x.value,(r,l)=>(g(),v("div",{key:l,class:I({"text-red-500":r.type==="error","text-yellow-500":r.type==="warning","text-green-500":r.type==="success","text-blue-500":r.type==="info"})}," ["+a(r.timestamp)+"] "+a(r.message),3))),128))])])]))}});export{Pe as default};
