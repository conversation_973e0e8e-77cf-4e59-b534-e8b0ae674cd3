export default {
  common: {
    clear: '清除',
    copy: '复制',
    close: '关闭',
    download: '下载',
    loadExample: '加载示例',
    loadObjectExample: '加载对象示例',
    selectAll: '全选',
    clearSelection: '清除选择',
    extract: '提取',
    results: '结果',
    options: '选项',
    input: '输入',
    preview: '预览',
    statistics: '统计',
    fields: '字段',
    items: '项',
    found: '找到',
    extracted: '提取',
    with: '包含',
    total: '总计',
    unique: '唯一',
    nonEmpty: '非空',
    loading: '加载中...',
    remove: '移除',
    more: '更多',
  },
  navigation: {
    tools: '工具',
    title: '专业Web开发者工具',
    language: '语言',
    categories: '工具分类',
    menu: '菜单',
    close: '关闭',
    search: '搜索工具...',
    noResults: '没有找到匹配的工具。',
    noToolsInCategory: '该分类中没有可用的工具。',
    home: '首页',
  },
  homepage: {
    title: '开发者工具集合',
    subtitle: '为开发者、设计师和内容创作者提供强大的在线工具。提升您的工作效率。',
    recommendedTools: '推荐工具',
    exploreCategories: '浏览分类',
    stats: {
      totalTools: '工具总数',
      activeTools: '可用工具',
      categories: '分类数量',
      comingSoon: '即将推出',
    },
  },
  notFound: {
    title: '页面未找到',
    description: '您要查找的工具或页面不存在或已被移动。',
    backToHome: '返回首页',
    goBack: '返回上页',
    popularTools: '热门工具',
    helpText: '如果您需要帮助寻找特定工具，请查看侧边栏中的分类。',
  },
  toast: {
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
    copied: '结果已复制到剪贴板！',
    copyFailed: '复制到剪贴板失败',
    downloadSuccess: '文件下载成功！',
  },
  footer: {
    madeWith: '用',
    by: '制作',
  },
  categories: {
    webTools: {
      name: '网页工具',
      description: '网页开发和分析工具',
    },
    jsonTools: {
      name: 'JSON工具',
      description: '全面的JSON处理和转换实用程序',
    },
    dataTools: {
      name: '数据工具',
      description: '数据处理和操作工具',
    },
    imageTools: {
      name: '图片工具',
      description: '图片处理和管理工具',
    },
    converters: {
      name: '转换器',
      description: '格式转换实用工具',
    },
    generators: {
      name: '生成器',
      description: '代码和内容生成器',
    },
  },
  pagination: {
    previous: '上一页',
    next: '下一页',
    page: '第',
    of: '页，共',
  },
  status: {
    active: '可用',
    'coming-soon': '即将推出',
  },
  tools: {
    htmlExtractor: {
      title: 'HTML 内容提取器',
      description: '一键从 HTML 代码中提取图片、视频、链接和其他资源',
      contentTypes: '内容类型',
      baseUrl: '基础 URL',
      inputPlaceholder: '请在此处粘贴您的 HTML 代码...',
      extractionResults: '转换结果',
      noResults: '暂无提取结果。请输入 HTML 代码并选择要提取的内容类型。',
      features: {
        imageExtraction: {
          title: '图片提取',
          description:
            '自动从 HTML 中提取所有图片 URL，包括 img 标签和 CSS 背景图片。支持相对路径转绝对路径，方便使用。',
        },
        mediaProcessing: {
          title: '媒体处理',
          description:
            '批量提取视频和音频文件链接，支持多种格式（MP4、WebM、Ogg、MP3 等）。自动识别 video 和 audio 标签中的源文件。',
        },
        linkAnalysis: {
          title: '链接分析',
          description:
            '提取页面中的所有超链接，包括 a 标签的 href 属性。支持筛选内部和外部链接，帮助分析网站结构。',
        },
      },
      options: {
        uniqueOnly: '仅显示唯一结果',
        absoluteUrls: '转换为绝对 URL',
      },
      types: {
        images: '图片',
        videos: '视频',
        audio: '音频',
        links: '链接',
        css: 'CSS',
        javascript: 'JavaScript',
        iframes: '内嵌框架',
        metadata: '元数据',
        forms: '表单',
      },
    },
    jsonKeysExtractor: {
      title: 'JSON 键值提取器',
      description: '从 JSON 对象和数组中提取所有唯一键或值',
      inputTitle: '输入 JSON',
      inputPlaceholder: '在此粘贴您的 JSON 数据...',
      extractedKeys: '提取的键',
      extractedValues: '提取的值',
      noResults: '暂无提取结果。请输入 JSON 进行分析。',
      extractionOptions: '提取选项',
      includeNested: '包含嵌套键（使用点符号）',
      sortResults: '按字母顺序排序结果',
      includeArrayIndices: '包含数组索引',
      outputFormat: '输出格式',
      formatOptions: {
        array: 'JSON 数组',
        list: '行分隔列表',
        tree: '树形结构',
      },
      modeToggle: {
        keys: '提取键',
        values: '提取值',
      },
      features: {
        keyDiscovery: {
          title: '键发现',
          description: '自动从复杂的 JSON 结构中发现所有键',
        },
        valueExtraction: {
          title: '值提取',
          description: '从复杂的 JSON 结构中提取所有值',
        },
        nestedSupport: {
          title: '嵌套支持',
          description: '使用路径符号处理嵌套对象的深层结构',
        },
        valueTypes: {
          title: '值类型',
          description: '提取所有类型的值（字符串、数字、布尔值等）',
        },
        exportOptions: {
          title: '多种格式',
          description: '导出为数组、列表或树形结构',
        },
      },
      errors: {
        invalidJson: '无效的 JSON 格式：',
      },
    },
    jsonToExcel: {
      title: 'JSON 转 Excel/CSV/SQL 转换器',
      description: '将 JSON 数据转换为 Excel、CSV 或 SQL 格式，支持自定义选项',
      inputTitle: '输入 JSON 数据',
      outputTitle: 'Excel 输出结果',
      csvOutputTitle: 'CSV 输出结果',
      sqlOutputTitle: 'SQL 输出结果',
      inputPlaceholder: '在此粘贴您的 JSON 数组...',
      noResults: '暂无转换结果。请输入 JSON 数据以进行转换。',
      conversionComplete: '转换成功完成！',
      readyForDownload: 'Excel 文件已准备就绪，可立即下载。',
      csvReadyForDownload: 'CSV 文件已准备就绪，可立即下载。',
      sqlReadyForDownload: 'SQL 文件已准备就绪，可立即下载。',
      previewTitle: '数据预览',
      convertToExcel: '转换为 Excel',
      convertToCsv: '转换为 CSV',
      convertToSql: '生成 SQL',
      showingRows: '显示 {shown} 行，共 {total} 行',
      options: {
        conversionType: '转换类型',
        includeHeaders: '包含表头',
        autoFitColumns: '自动适配列宽',
        sheetName: '工作表名称',
        sheetNamePlaceholder: '输入工作表名称',
        delimiter: '分隔符',
        quoteChar: '引号字符',
        flattenNested: '展开嵌套对象',
        comma: '逗号',
        semicolon: '分号',
        tab: '制表符',
        pipe: '管道符',
        doubleQuote: '双引号',
        singleQuote: '单引号',
        none: '无',
        sqlOptions: 'SQL 选项',
        tableName: '表名',
        tableNamePlaceholder: '输入表名',
        sqlType: 'SQL 类型',
        whereField: 'WHERE 字段',
        whereFieldPlaceholder: 'WHERE 子句的字段',
        escapeValues: '转义值',
        batchInsert: '批量插入',
      },
      features: {
        conversion: {
          title: '智能转换',
          description: '自动将 JSON 数组转换为 Excel、CSV 或 SQL 格式，并对数据类型进行妥善处理。',
        },
        formatting: {
          title: 'Excel 格式优化',
          description: '生成格式规范的 Excel 文件，包含表头、自动调整列宽及多工作表支持。',
        },
        batch: {
          title: '批量处理',
          description: '支持高效处理大型数据集，提供数据预览与批量下载功能。',
        },
      },
      errors: {
        emptyInput: '请提供待转换的 JSON 数据',
        invalidJson: 'JSON 格式无效，请检查您的输入内容。',
        notArray: '输入内容必须是 JSON 数组',
        emptyArray: 'JSON 数组不能为空',
        conversionFailed: 'JSON 转换失败',
        emptyTableName: '请提供表名',
        emptyWhereField: '请为 UPDATE 语句提供 WHERE 字段',
      },
      success: {
        conversionComplete: 'JSON 成功转换为 Excel！',
        csvConversionComplete: 'JSON 成功转换为 CSV！',
        sqlConversionComplete: 'SQL 语句生成成功！',
      },
    },
    excelToJson: {
      title: 'Excel 转 JSON 转换器 ',
      description: ' 将 Excel 文件转换为 JSON 格式，支持灵活的解析选项 ',
      inputTitle: ' 上传 Excel 文件 ',
      outputTitle: 'JSON 输出结果 ',
      uploadDescription: ' 选择需转换为 JSON 的 Excel 文件 ',
      selectFile: ' 选择 Excel 文件 ',
      supportedFormats: ' 支持 .xlsx、.xls、.csv 及 .ods 格式文件 ',
      noResults: ' 暂无转换结果。请上传 Excel 文件。',
      conversionComplete: ' 转换成功完成！',
      recordsCount: ' 已转换 {count} 条记录 ',
      convert: ' 转换为 JSON',
      fileSelected: ' 文件选择成功 ',
      options: {
        title: '转换选项',
        firstRowAsHeaders: '第一行作为标题',
        skipEmptyRows: '跳过空行',
        sheetIndex: '工作表',
      },
      errors: {
        conversionFailed: 'Excel 文件转换失败',
        noFileSelected: '请选择一个 Excel 文件进行转换',
        xlsxRequired: 'Excel 文件解析需依赖 XLSX 库。请安装：npm install xlsx',
      },
      features: {
        parsing: {
          title: 'Excel 解析 ',
          description: ' 解析 Excel 文件，支持多工作表、公式及多种数据类型。',
        },
        conversion: {
          title: ' 灵活转换 ',
          description: ' 转换时可配置表头、空行处理及特定工作表选择等选项。',
        },
        options: {
          title: ' 转换选项配置 ',
          description: ' 通过表头处理、空行跳过及工作表选择等功能自定义输出结果。',
        },
      },
    },
    jsonExtractor: {
      title: 'JSON 字段提取器',
      description: '一键从 JSON 数组数据中提取指定字段',
      availableFields: '可用字段',
      inputTitle: '输入 JSON 数组',
      inputNote: '请粘贴格式为以下的 JSON 数组数据：',
      inputDescription: '工具将自动解析 JSON 并列出所有可选择的字段。',
      inputPlaceholder: '请在此处粘贴您的 JSON 数据',
      extractedData: '提取的数据',
      fieldStatistics: '字段统计',
      noResults: '暂无提取结果。请输入 JSON 数组数据并选择要提取的字段。',
      options: {
        preserveStructure: '保持对象结构',
        removeEmpty: '移除空值',
        flattenNested: '展平嵌套对象',
      },
      features: {
        fieldExtraction: {
          title: '字段提取',
          description:
            '自动解析 JSON 数组并提取选定字段。支持嵌套对象并保持数据类型以确保准确提取。',
        },
        smartFiltering: {
          title: '智能过滤',
          description:
            '选择要包含在输出中的特定字段。可选择移除空值并保持原始对象结构以获得清晰的结果。',
        },
        exportOptions: {
          title: '导出选项',
          description:
            '将提取的数据复制到剪贴板或下载为 JSON 文件。包括字段统计和数据分析，便于更好地理解您的数据集。',
        },
      },
      errors: {
        invalidFormat: '输入必须是 JSON 数组格式：[{},{},...]',
        emptyArray: 'JSON 数组不能为空',
        noFields: '请选择至少一个字段进行提取',
        invalidJson: '无效的 JSON 格式：',
        noData: '请提供要提取的 JSON 数据',
      },
    },
    imageListProcessor: {
      title: '图片列表处理器',
      description: '输入图片URL列表并以可视化图库格式显示',
      inputTitle: '输入图片URL',
      inputNote: '请在下方粘贴图片URL，每行一个：',
      inputPlaceholder:
        '在此粘贴图片URL，每行一个...\n\n示例：\nhttps://example.com/image1.jpg\nhttps://example.com/image2.png\nhttps://example.com/image3.webp',
      imagePreview: '图片图库',
      noResults: '未找到有效的图片URL。请输入有效的图片URL。',
      imageError: '加载失败',
      emptyState: {
        title: '没有图片可显示',
        description: '在上方输入一些图片URL以在下方图库中查看。',
      },
      features: {
        simple: {
          title: '简单输入',
          description: '只需逐行粘贴图片URL - 无需复杂格式。',
        },
        gallery: {
          title: '增强图库',
          description: '在清洁4列布局中查看所有图片，支持缩放、平移和键盘导航的全功能灯箱预览。',
        },
        fast: {
          title: '专业预览',
          description: '高级图片查看器，支持缩放、滚动、拖拽和全屏功能，便于详细检查。',
        },
      },
    },
    videoToGifConverter: {
      title: '视频转GIF工具',
      description: '将视频转换为动态GIF，支持自定义文字叠加和时间控制',
      howToUse: {
        title: '使用方法',
        step1: '通过点击"选择视频文件"或拖拽来上传视频文件',
        step2: '调整GIF设置（宽度、质量、帧率）',
        step3: '设置GIF的时间范围，如果需要可以添加文字叠加',
        step4: '点击"生成GIF"来创建您的动画GIF',
      },
      tips: {
        title: '获得最佳效果的技巧',
        tip1: '为获得最佳效果，请使用较短的视频片段（10秒以内）',
        tip2: '较低的帧率（10-15 FPS）可创建较小的文件大小',
        tip3: '较小的GIF宽度（200-400px）加载更快且消耗更少内存',
        tip4: '使用中等质量可在文件大小和图像质量之间取得良好平衡',
      },
      loadingVideo: '正在加载视频...',
      upload: {
        title: '上传视频',
        dragDrop: '拖拽视频文件到此处',
        selectFile: '选择视频文件',
        supportedFormats: '支持 MP4、AVI、MOV、WebM 等视频格式（最大：100MB）',
      },
      settings: {
        width: 'GIF宽度（像素）',
        quality: '质量',
        fps: '帧率（FPS）',
        qualityOptions: {
          high: '高质量',
          medium: '中等质量',
          low: '低质量（文件更小）',
        },
      },
      preview: {
        title: '视频预览和控制',
      },
      actions: {
        startCapture: '开始取图',
        stopCapture: '取图完成',
        generateGif: '生成GIF',
      },
      timeRange: {
        title: '时间范围选择',
        start: '开始',
        end: '结束',
        setStart: '设置开始时间',
        setEnd: '设置结束时间',
      },
      textOverlay: {
        title: '文字叠加',
        add: '添加文字',
        text: '文字',
        placeholder: '输入叠加文字...',
        startTime: '开始时间（秒）',
        endTime: '结束时间（秒）',
        fontSize: '字体大小',
        color: '颜色',
        position: '位置',
        positions: {
          top: '顶部',
          center: '居中',
          bottom: '底部',
        },
      },
      processing: {
        title: '处理视频中',
        description: '正在将您的视频转换为GIF并添加文字叠加。这可能需要一些时间...',
        preview: '预览',
      },
      result: {
        title: '生成的GIF',
        download: '下载GIF',
        createNew: '创建新的GIF',
      },
      features: {
        conversion: {
          title: '视频转换',
          description: '将视频转换为高质量的动态GIF，支持自定义帧率和尺寸。',
        },
        textOverlay: {
          title: '文字叠加',
          description: '添加多个文字叠加，支持精确时间控制、自定义颜色、字体和位置。',
        },
        customization: {
          title: '全面自定义',
          description: '控制质量、大小、时间和文字外观等各个方面，获得完美效果。',
        },
      },
      errors: {
        invalidFile: '请选择有效的视频文件。',
        fileTooLarge: '文件大小必须小于100MB。',
        noVideoSelected: '请先选择一个视频文件。',
        invalidTimeRange: '时间范围无效。结束时间必须大于开始时间。',
        processingFailed: '视频处理失败，请重试。',
      },
      messages: {
        fileLoaded: '视频文件加载成功！',
        gifGenerated: 'GIF生成成功！',
        filePasted: '已从剪贴板粘贴视频文件！',
      },
    },
    apngGenerator: {
      title: 'APNG动图生成器',
      description: '将多张静态图片制作成动态PNG文件，支持自定义动画设置',
      uploadTitle: '上传图片帧',
      uploadDescription: '拖拽多张图片或点击选择动画帧',
      selectFiles: '选择图片文件',
      supportedFormats: '支持格式',
      settings: '动画设置',
      frameDelay: '帧延迟',
      loopCount: '循环次数',
      infinite: '无限循环',
      outputWidth: '输出宽度',
      outputHeight: '输出高度',
      advancedOptions: '高级选项',
      maintainAspectRatio: '保持宽高比',
      optimizeSize: '优化文件大小',
      frameList: '动画帧列表',
      generateAPNG: '生成APNG',
      generating: '生成中...',
      preview: '预览动画',
      animationPreview: '动画预览',
      downloadAPNG: '下载APNG',
      reorderHint: '帧将按上述顺序播放动画。您可以通过点击×按钮删除不需要的帧。',
      features: {
        title: '主要功能',
        highQuality: {
          title: '高质量输出',
          description: '生成无损动态PNG文件，支持透明度和24位色彩',
        },
        customizable: {
          title: '完全可定制',
          description: '控制帧时间、循环次数、尺寸和优化设置',
        },
        easyToUse: {
          title: '易于使用',
          description: '简单的拖拽界面，实时预览和即时下载',
        },
      },
    },
    backgroundRemover: {
      title: '在线抠图工具',
      description: '使用AI技术自动去除图片背景',
      features: {
        aiPowered: {
          title: 'AI智能识别',
          description: '先进的机器学习算法，精确检测和去除背景',
        },
        fastProcessing: {
          title: '快速处理',
          description: '秒级背景去除，高质量效果',
        },
        highQuality: {
          title: '高质量输出',
          description: '保持图像质量和细节，干净地去除背景',
        },
      },
      upload: {
        title: '上传图片',
        dragDrop: '拖拽图片到此处',
        supportedFormats: '支持JPG、PNG、GIF等图片格式',
        selectFile: '选择图片',
      },
      preview: {
        original: '原始图片',
        originalAlt: '原始图片',
        processed: '去背景后',
        processedAlt: '去除背景后的图片',
      },
      options: {
        title: '输出选项',
        model: 'AI模型',
        outputFormat: '输出格式',
        transparent: '透明背景',
        whiteBackground: '白色背景',
        backgroundColor: '背景颜色',
        quality: '输出质量',
      },
      models: {
        small: '小型（快速）',
        medium: '中型（平衡）',
        large: '大型（最佳质量）',
      },
      actions: {
        remove: '去除背景',
      },
      processing: {
        inProgress: '处理中...',
        analyzing: '正在分析图片并去除背景...',
        pleaseWait: '这可能需要几秒钟',
      },
      result: {
        title: '处理结果',
        noResult: '暂无处理结果。请上传图片以去除背景。',
        complete: '背景去除完成',
        ready: '您的图片已准备好下载',
      },
      imageInfo: {
        size: '文件大小',
        format: '格式',
      },
      tips: {
        title: '获得最佳效果的技巧',
        tip1: '使用主体边界清晰的高分辨率图片以获得最佳效果',
        tip2: '主体与背景对比度好的图片效果更佳',
        tip3: '避免使用复杂背景或与主体颜色相似的图片',
        tip4: 'PNG格式保持透明度，JPG格式使用白色背景',
        tip5: '使用对比视图并排查看处理前后的结果',
      },
      comparison: {
        before: '处理前',
        after: '处理后',
      },
    },
    csvtojson: {
      title: 'CSV转JSON工具',
      description: '将CSV数据转换为JSON格式，支持自定义解析选项',
      introduction: {
        title: '工具介绍',
        description: '在线CSV转JSON工具，用于将固定符号分隔的CSV格式数据转换为JSON格式数据。',
        usage:
          '默认是以制表符(\\t)为数据字段分隔符，如是其它符号，直接在分隔符输入框中填写即可。支持将CSV转换为JSON对象或JSON数组。',
      },
      example: {
        title: '示例',
        input: 'CSV输入：',
        output: 'JSON输出：',
      },
      input: {
        title: '输入CSV数据',
        placeholder: '在此粘贴您的CSV数据...\n\n示例：\nname,age,score\n李华,25,89\n小明,22,85',
        fileUpload: '上传CSV文件',
      },
      options: {
        title: '解析选项',
        delimiter: '分隔符',
        outputFormat: '输出格式',
        hasHeaders: '首行作为标题',
        skipEmptyLines: '跳过空行',
        autoDetectNumbers: '自动检测数字',
        autoDetectBooleans: '自动检测布尔值',
      },
      delimiters: {
        comma: '逗号 (,)',
        semicolon: '分号 (;)',
        tab: '制表符 (\\t)',
        pipe: '管道符 (|)',
        space: '空格',
      },
      formats: {
        jsonObject: 'JSON对象',
        jsonArray: 'JSON数组',
      },
      preview: {
        title: '数据预览',
        firstRows: '前{count}行',
        rowsDetected: '检测到{count}行',
      },
      convert: '转换为JSON',
      output: {
        title: 'JSON输出',
        complete: '转换完成',
        recordsConverted: '已转换{count}条记录',
        noOutput: '暂无JSON输出。请输入CSV数据进行转换。',
      },
    },
    excelTextToJson: {
      title: 'Excel文本转JSON',
      description: '将Excel剪贴板数据直接转换为JSON格式',
      introduction: {
        title: '工具介绍',
        description: '在线Excel文本转JSON工具，用于将制表符分隔的Excel数据转换为JSON格式。',
        usage:
          '从Excel中复制数据并粘贴到此处。默认分隔符为制表符(\\t)。对象格式需要第一行包含标题。',
      },
      example: {
        title: '示例',
        input: 'Excel输入：',
        output: 'JSON输出：',
      },
      input: {
        title: '输入Excel数据',
        placeholder:
          '在此粘贴您的Excel数据...\n\n示例：\nname\tage\tscore\n李华\t25\t89\n小明\t22\t85',
        fileUpload: '上传文本文件',
      },
      options: {
        title: '解析选项',
        delimiter: '分隔符',
        outputFormat: '输出格式',
        hasHeaders: '首行作为标题',
        skipEmptyLines: '跳过空行',
        autoDetectNumbers: '自动检测数字',
        autoDetectBooleans: '自动检测布尔值',
      },
      delimiters: {
        comma: '逗号 (,)',
        semicolon: '分号 (;)',
        tab: '制表符 (\\t)',
        pipe: '管道符 (|)',
        space: '空格',
      },
      formats: {
        jsonObject: 'JSON对象',
        jsonArray: 'JSON数组',
      },
      preview: {
        title: '数据预览',
        firstRows: '前{count}行',
        rowsDetected: '检测到{count}行',
      },
      convert: '转换为JSON',
      output: {
        title: 'JSON输出',
        complete: '转换完成',
        recordsConverted: '已转换{count}条记录',
        noOutput: '暂无JSON输出。请输入Excel数据进行转换。',
      },
    },
    jsonPathExtractor: {
      title: 'JSON 路径提取器',
      description: '使用 JSONPath 表达式从 JSON 中提取数据，支持高级过滤功能',
      extractButton: '提取数据',
      mode: {
        path: '路径模式',
        field: '字段模式',
      },
      tabs: {
        path: '路径提取器',
        formatter: 'JSON 格式化',
        excelTojson: 'Excel 转 JSON',
        jsonMerge: 'JSON 合并',
        excelTextToJson: 'Excel 文本转 JSON',
        jsonToExcel: 'JSON 转 Excel',
      },
      features: {
        pathExtraction: {
          title: '路径提取',
          description:
            '使用 JSONPath 表达式从复杂的 JSON 结构中精准提取数据，支持点记号和数组索引。',
        },
        filtering: {
          title: '高级过滤',
          description: '支持通配符、数组切片和条件过滤，精确提取您需要的数据。',
        },
        export: {
          title: '导出结果',
          description: '将提取的数据复制到剪贴板或下载为 JSON 文件，包含格式化输出和统计信息。',
        },
      },
      syntaxGuide: {
        title: 'JSONPath 语法指南',
        basicSyntax: '基本语法',
        examples: '常用示例',
        rootSymbol: '根对象或元素',
        currentSymbol: '当前对象或元素',
        childOperator: '子元素操作符',
        recursiveMatch: '递归匹配所有子元素',
        wildcard: '通配符。匹配所有对象或元素',
        subscriptOperator: '下标运算符，JSONPath索引从0开始',
        unionOperator: '连接运算符，将多个结果拼成数组返回，JSONPath允许使用别名',
        arraySlice: '数组切片运算符',
        filterExpression: '过滤器（脚本）表达式',
        scriptExpression: '脚本表达式',
        exampleDesc1: '获取第一本书的标题',
        exampleDesc2: '获取所有书的作者',
        exampleDesc3: '递归查找所有作者',
        exampleDesc4: '查找价格小于10的书籍',
        tutorialTitle: 'JSONPath 教程',
        tutorialDesc: '了解更多关于 JSONPath 语法和用法：',
      },
      inputSection: {
        title: 'JSON 数据和路径',
        jsonData: 'JSON 数据',
        jsonPath: 'JSONPath 表达式',
        jsonPlaceholder: '在此粘贴您的 JSON 数据...',
        pathPlaceholder: '输入 JSONPath 表达式（例如：$.users[*].name）',
        quickPaths: '快速路径模板',
      },
      outputSection: {
        title: '提取结果',
        noResults: '暂无提取结果。请输入 JSON 数据和 JSONPath 表达式。',
        extractedData: '提取的数据',
      },
      quickPaths: {
        root: '根元素',
        allProperties: '所有属性',
        firstArrayItem: '第一个数组项',
        allArrayItems: '所有数组项',
        lastArrayItem: '最后一个数组项',
        arraySlice: '数组切片 (0-2)',
        recursiveMatch: '递归匹配所有作者',
        filterExpression: '过滤价格小于10的书籍',
        unionOperator: '连接运算符用于多个项目',
        scriptExpression: '脚本表达式获取最后一项',
      },
      success: {
        validJson: '有效的 JSON 格式',
        extracted: '数据提取成功',
        arrayResults: '找到 {count} 个数组项',
        objectResults: '找到包含 {count} 个属性的对象',
        primitiveResult: '找到 {type} 类型的值',
      },
      errors: {
        invalidJson: '无效的 JSON 格式',
        pathError: 'JSONPath 表达式错误',
        noMatches: '没有数据匹配指定的路径',
      },
      messages: {
        copied: '提取的数据已成功复制到剪贴板！',
        copyFailed: '复制到剪贴板失败',
        downloaded: 'JSON 文件下载成功！',
        downloadFailed: '文件下载失败',
      },
    },
    jsonFormatter: {
      title: 'JSON 格式化工具',
      description: '美化、验证和格式化 JSON 数据，支持多种格式选项',
      inputTitle: '输入 JSON',
      outputTitle: '格式化结果',
      inputPlaceholder: '请输入要格式化的 JSON 数据...',
      noResults: '暂无格式化结果。请输入有效的 JSON 数据进行格式化。',
      validJson: '有效的 JSON',
      invalidJson: '无效的 JSON',
      formattingComplete: '格式化完成',
      formatOptions: '格式选项',
      indent: '缩进',
      outputFormat: '输出格式',
      prettyFormat: '美化格式',
      compactFormat: '压缩格式',
      sortKeys: '排序键',
      escapeUnicode: '转义 Unicode',
      formatJson: '格式化 JSON',
      spaces2: '2 个空格',
      spaces4: '4 个空格',
      tab: '制表符',
      lines: '行数',
      characters: '字符数',
      keys: '键数量',
      depth: '嵌套深度',
      caseOptions: '大小写选项',
      keyCase: '键大小写',
      valueCase: '值大小写',
      preserveCase: '保持原始大小写',
      toUpperCase: '转换为大写',
      toLowerCase: '转换为小写',
      features: {
        prettyFormat: {
          title: '美化格式',
          description: '使用适当的缩进和间距自动格式化和美化 JSON',
        },
        validation: {
          title: '验证',
          description: '实时 JSON 验证，包含详细的错误消息和行号',
        },
        customization: {
          title: '自定义',
          description: '选择缩进大小、排序和紧凑格式选项',
        },
      },
      messages: {
        formatSuccess: 'JSON 格式化成功！',
        formatError: '格式化 JSON 失败：',
        provideData: '请提供要格式化的 JSON 数据',
        copied: 'JSON 已成功复制到剪贴板！',
        copyFailed: '复制到剪贴板失败',
        downloaded: 'JSON 文件下载成功！',
        downloadFailed: '文件下载失败',
      },
    },
    jsonMerge: {
      title: 'JSON 文件合并工具',
      description: '将多个 JSON 文件合并成一个文件',
      introduction: {
        title: '工具介绍',
        description: '在线 JSON 文件合并工具，将多个 JSON 文件合并成一个大的 JSON 文件。',
        usage: 'JSON 文件将按导入顺序依次合并，如对顺序有要求，请注意文件顺序。',
      },
      fileUpload: {
        title: '上传 JSON 文件',
        description: '选择多个 JSON 文件进行合并。文件将按以下顺序处理。',
        selectFiles: '选择 JSON 文件',
        supportedFormats: '支持 .json 文件',
        noFiles: '尚未选择文件。请选择要合并的 JSON 文件。',
      },
      filePreview: {
        title: '文件预览',
        fileName: '文件名',
        fileSize: '文件大小',
        jsonStructure: 'JSON 结构',
        arrayItems: '{count} 个数组项',
        object: 'JSON 对象',
        remove: '移除',
        moveUp: '上移',
        moveDown: '下移',
      },
      options: {
        title: '合并选项',
        outputFileName: '输出文件名',
        outputFileNamePlaceholder: '输入输出文件名（不含扩展名）',
        defaultFileName: '合并的JSON',
      },
      actions: {
        merge: '合并 JSON 文件',
        clear: '清空所有文件',
        download: '下载合并的 JSON',
      },
      output: {
        title: '合并的 JSON 输出',
        noOutput: '暂无合并输出。请上传 JSON 文件并点击合并。',
        complete: '合并完成',
        itemsMerged: '已合并 {count} 项',
        downloadReady: '合并的 JSON 文件已准备好下载。',
      },
      features: {
        multipleFiles: {
          title: '多文件支持',
          description: '通过拖放支持上传和合并多个 JSON 文件。',
        },
        orderControl: {
          title: '顺序控制',
          description: '合并前重新排序文件以控制输出顺序。',
        },
        preview: {
          title: '文件预览',
          description: '合并前预览文件结构和内容。',
        },
      },
      errors: {
        noFiles: '请至少选择一个 JSON 文件进行合并',
        invalidJson: '文件中的 JSON 无效：{fileName}',
        mergeFailed: '合并 JSON 文件失败：{error}',
        emptyArray: 'JSON 文件的根级别必须包含一个数组',
      },
      success: {
        filesAdded: '已成功添加 {count} 个文件',
        mergeComplete: 'JSON 文件合并成功！',
      },
    },
    cookieToJson: {
      title: 'Cookie ↔ JSON 转换器',
      description: '在 Cookie 字符串和 JSON 对象之间实时转换，支持双向转换',
      inputTitle: 'Cookie 字符串',
      outputTitle: 'JSON 对象',
      format: '格式',
      formatButton: '格式化',
      convertLeft: '转换为 Cookie',
      convertRight: '转换为 JSON',
      inputNote: '粘贴如下格式的 Cookie 字符串：',
      inputPlaceholder:
        '请在此粘贴您的 Cookie 字符串，例如：\nsessionId=abc123; userId=12345; theme=dark; lang=zh-CN\n\n支持的格式：\n- 标准 Cookie 格式：name1=value1; name2=value2\n- 自动解码 URL 编码的值\n- 处理无值的 Cookie（标志位）',
      parseOptions: '解析选项',
      noResults: '暂无转换结果。请输入数据进行转换。',
      error: '解析错误',
      success: '解析成功',
      conversionComplete: '转换完成',
      cookiesFound: '找到 {count} 个 Cookie',
      statistics: '共 {total} 个 Cookie，{nonEmpty} 个有值',
      options: {
        decodeValues: '解码 URL 编码值',
        removeEmpty: '移除空值',
        formatOutput: '格式化 JSON 输出',
      },
      features: {
        parsing: {
          title: 'Cookie 解析',
          description: '自动解析 Cookie 字符串，支持标准 HTTP Cookie 格式和 URL 解码。',
        },
        conversion: {
          title: '双向转换',
          description: '在 Cookie 字符串和 JSON 对象之间双向转换，支持实时转换。',
        },
        export: {
          title: '导出选项',
          description: '复制到剪贴板或下载为 JSON 文件，包含统计信息和验证反馈。',
        },
      },
      errors: {
        noCookies: '输入字符串中未找到有效的 Cookie',
        parseError: '解析 Cookie 字符串失败：{error}',
        conversionFailed: '转换失败。请检查您的输入格式。',
        unsupportedFormat: '选择了不支持的格式',
        invalidJson: 'JSON 格式无效。请检查您的输入。',
        formatFailed: '格式化失败。请检查您的输入格式。',
      },
      messages: {
        copied: '内容已成功复制到剪贴板！',
        copyFailed: '复制到剪贴板失败',
        downloaded: 'JSON 文件下载成功！',
        downloadFailed: '文件下载失败',
      },
    },
    fileRenamer: {
      title: '文件重命名工具',
      description: '多模式批量重命名文件 - 本地处理保护隐私',
      uploadArea: {
        title: '拖放文件到此处',
        subtitle: '或点击选择文件',
        selectFiles: '选择文件',
      },
      fileCount: '文件总数：{count}',
      totalSize: '总大小：{size}',
      tabs: {
        sequential: '顺序编号',
        replace: '查找替换',
        case: '大小写转换',
        insert: '插入文本',
        truncate: '截取文本',
        script: '生成脚本',
      },
      sequential: {
        prefix: '前缀',
        prefixPlaceholder: '例如：photo_',
        startNumber: '起始数字',
        padding: '数字补位',
      },
      replace: {
        findText: '查找文本',
        findPlaceholder: '要查找的文本',
        replaceText: '替换为',
        replacePlaceholder: '替换文本',
        caseSensitive: '区分大小写',
      },
      case: {
        transformation: '大小写转换',
        uppercase: '全部大写',
        lowercase: '全部小写',
        capitalize: '首字母大写',
      },
      insert: {
        text: '插入文本',
        textPlaceholder: '要插入的文本',
        position: '插入位置',
        prefix: '文件名开头',
        suffix: '文件名结尾',
        atIndex: '指定位置',
        index: '插入索引',
      },
      truncate: {
        startIndex: '开始索引',
        endIndex: '结束索引',
        description: '从开始索引到结束索引提取子字符串（从0开始计数）',
      },
      script: {
        scriptType: '脚本类型',
        windows: 'Windows 批处理 (.bat)',
        linux: 'Linux Shell (.sh)',
        autoGenerated: '自动生成脚本',
        scriptPreview: '脚本预览',
        downloadScript: '下载脚本',
        copyScript: '复制脚本',
        noContent: '暂无脚本内容。添加文件并应用重命名选项以生成脚本。',
        instructions: {
          title: '使用说明',
          description:
            '此工具生成用于重命名文件的脚本。脚本会根据您的文件和重命名选项自动生成。点击"下载脚本"进行下载。将脚本放置在包含文件的目录中，然后运行它来执行重命名操作。',
        },
      },
      actions: {
        preview: '预览',
        apply: '应用重命名',
        download: '下载ZIP',
        clear: '清空文件',
      },
      sorting: {
        title: '排序',
        natural: '自然排序',
        filename: '文件名顺序',
        modifiedTime: '文件修改时间顺序',
        modifiedTimeDesc: '修改时间倒序',
        random: '随机排序',
        reverse: '反转当前排序',
        manual: '手动排序（拖拽）',
      },
      fileList: {
        title: '文件列表',
        drag: '拖拽',
        originalName: '原始名称',
        newName: '新名称',
        size: '大小',
        type: '类型',
        dragHint: '拖拽文件以手动重新排序',
      },
      messages: {
        filesAdded: '成功添加 {count} 个文件！',
        renameApplied: '重命名应用成功！',
        downloadStarted: '下载已开始！请检查您的下载文件夹。',
        downloadError: '下载失败！请重试。',
        filesCleared: '所有文件已清空！',
        noFilesToProcess: '没有要处理的文件！请先添加文件。',
        noScriptToDownload: '没有可下载的脚本！请先生成脚本。',
        noScriptToCopy: '没有可复制的脚本！请先生成脚本。',
        scriptDownloaded: '脚本 "{fileName}" 下载成功！',
        scriptCopied: '脚本已成功复制到剪贴板！',
        scriptCopyFailed: '复制脚本到剪贴板失败！',
      },
    },
    imageCompressor: {
      title: '压图大师',
      description: '高效的在线图片压缩工具，支持批量处理和本地隐私保护',
      settings: '压缩设置',
      quality: '质量',
      smaller: '更小',
      larger: '更大',
      outputFormat: '输出格式',
      keepOriginal: '保持原格式',
      maxWidth: '最大宽度',
      uploadTitle: '拖放图片或点击选择',
      uploadDescription: '支持多张图片，本地处理，不上传到服务器',
      supportedFormats: '支持格式',
      selectFiles: '选择文件',
      imageList: '图片列表',
      compressing: '压缩中...',
      compressAll: '全部压缩',
      downloadAll: '下载全部',
      compress: '压缩',
      remove: '移除',
      originalSize: '原始大小',
      compressedSize: '压缩后大小',
      spaceSaved: '节省空间',
      original: '原始',
      compressed: '压缩后',
      imagePreview: '图片预览',
      originalImage: '原始图片',
      compressedImage: '压缩后图片',
      size: '大小',
      dimensions: '尺寸',
      saved: '节省',
      status: {
        pending: '等待中',
        compressing: '处理中',
        completed: '已完成',
        error: '失败',
      },
      features: {
        efficient: {
          title: '高效压缩',
          description: '先进的压缩算法在保持图片质量的同时减少文件大小40-80%。',
        },
        secure: {
          title: '隐私保护',
          description: '所有处理都在您的浏览器本地进行。图片永远不会上传到任何服务器。',
        },
        batch: {
          title: '批量处理',
          description: '同时处理多张图片，具有进度跟踪和批量下载功能。',
        },
      },
      errors: {
        noValidImages: '未找到有效的图片文件',
        compressionFailed: '压缩 {filename} 失败',
      },
      success: {
        compressionComplete: '所有图片压缩完成！',
        downloadComplete: '批量下载完成！',
        pasteSuccess: '图片粘贴成功！',
      },
    },
    imageWatermark: {
      title: '图片水印大师',
      description: '为您的照片添加文字或图片水印，支持自定义样式和位置',
      settings: '水印设置',
      watermarkType: '水印类型',
      textWatermark: '文字水印',
      textWatermarkDesc: '为图片添加文字水印',
      imageWatermark: '图片水印',
      imageWatermarkDesc: '为图片添加图片水印',
      combinedWatermark: '组合水印',
      combinedWatermarkDesc: '同时添加文字和图片水印',
      textSettings: '文字设置',
      watermarkText: '水印文字',
      textPlaceholder: '输入水印文字',
      fontSize: '字体大小',
      fontColor: '字体颜色',
      fontFamily: '字体',
      imageSettings: '图片设置',
      watermarkImage: '水印图片',
      uploadWatermark: '上传水印图片',
      watermarkPreview: '水印预览',
      removeWatermark: '移除水印',
      imageWidth: '图片宽度',
      imageOpacity: '图片透明度',
      positionSettings: '位置设置',
      position: '位置',
      margin: '边距',
      advancedSettings: '高级设置',
      opacity: '透明度',
      rotation: '旋转',
      scale: '缩放',
      topLeft: '左上角',
      topCenter: '顶部居中',
      topRight: '右上角',
      centerLeft: '左侧居中',
      center: '居中',
      centerRight: '右侧居中',
      bottomLeft: '左下角',
      bottomCenter: '底部居中',
      bottomRight: '右下角',
      uploadTitle: '拖放图片或点击选择',
      uploadDescription: '支持多张图片，本地处理，不上传到服务器',
      supportedFormats: '支持格式',
      selectFiles: '选择文件',
      imageList: '图片列表',
      processing: '处理中...',
      processAll: '全部处理',
      downloadAll: '下载全部',
      process: '处理',
      remove: '移除',
      originalSize: '原始大小',
      processedSize: '处理后大小',
      processed: '已处理',
      original: '原始',
      imagePreview: '图片预览',
      originalImage: '原始图片',
      processedImage: '处理后图片',
      size: '大小',
      dimensions: '尺寸',
      gifWarning: '水印将应用于动图的所有帧',
      status: {
        pending: '等待中',
        processing: '处理中',
        completed: '已完成',
        error: '失败',
      },
      features: {
        watermark: {
          title: '多种水印类型',
          description: '添加文字水印、图片水印或组合水印，支持完全自定义。',
        },
        batch: {
          title: '批量处理',
          description: '同时处理多张图片，具有进度跟踪和批量下载功能。',
        },
        customization: {
          title: '完全自定义',
          description: '调整水印位置、透明度、旋转、缩放、字体属性等。',
        },
      },
      errors: {
        noValidImages: '未找到有效的图片文件',
        invalidWatermark: '请选择有效的图片文件作为水印',
        noWatermarkImage: '请上传水印图片',
        noWatermarkText: '请输入水印文字',
        watermarkProcessingFailed: '处理水印图片失败',
        processingFailed: '处理 {filename} 失败',
      },
      success: {
        processingComplete: '所有图片处理完成！',
        downloadComplete: '批量下载完成！',
        pasteSuccess: '图片粘贴成功！',
      },
    },
    faviconGenerator: {
      title: '网站图标生成器',
      description: '从任意图片生成多种尺寸和格式的专业网站图标',
      uploadSection: '上传图片',
      uploadTitle: '拖放图片或点击选择',
      uploadDescription: '上传任意图片来为您的网站创建图标',
      supportedFormats: '支持格式',
      selectImage: '选择图片',
      cropImage: '裁剪图片',
      originalImage: '原始图片',
      originalImageDescription: '完整分辨率图片预览 - 这是您的源图片',
      imageSize: '图片尺寸',
      cropPreview: '裁剪预览',
      selectAnother: '选择其他',
      cropInstruction:
        '拖动裁剪区域移动位置，或拖动角落手柄调整大小。选中的正方形区域将用于生成图标。',
      cropInstructionAdvanced:
        '拖动移动裁剪区域，拖动角落调整大小，或使用鼠标滚轮缩放。选中的正方形区域将用于生成网站图标。',
      outputFormat: '输出格式',
      sizes: '图标尺寸',
      generate: '生成图标',
      generating: '生成中...',
      generatedFavicons: '生成的图标',
      downloadAll: '下载全部为ZIP',
      usageInstructions: '如何使用图标',
      htmlUsage: 'HTML 实现',
      tips: '最佳实践',
      tip1: '使用简单、易识别的设计，确保在小尺寸下清晰可见',
      tip2: '确保良好的对比度，以便在不同背景下都能清晰显示',
      tip3: '在各种设备和浏览器上测试您的图标',
      tip4: '将 favicon.ico 放在网站根目录以便自动检测',
      pasteHint: '提示：您也可以直接从剪贴板粘贴图片',
      features: {
        cropping: {
          title: '智能裁剪',
          description: '交互式裁剪工具，从您的图片中选择完美的正方形区域，实时预览效果。',
        },
        multiSize: {
          title: '多种尺寸',
          description: '生成所有标准尺寸（16px到128px）的图标，确保在各种设备上的最佳兼容性。',
        },
        formats: {
          title: '多种格式',
          description: '支持导出ICO、PNG或JPG格式，满足不同浏览器和平台的要求。',
        },
      },
      errors: {
        invalidFile: '请选择有效的图片文件',
        generationFailed: '生成图标失败',
        downloadFailed: '下载文件失败',
        imageLoadFailed: '图片加载失败',
        fileReadFailed: '文件读取失败',
      },
      success: {
        imageLoaded: '图片加载成功！',
        generationComplete: '图标生成成功！',
        downloadComplete: '下载完成！',
      },
      messages: {
        pasteSuccess: '已从剪贴板粘贴图片并开始处理！',
      },
    },
    jsonToSql: {
      title: 'JSON 转 SQL 转换器',
      description: '从 JSON 数据生成 SQL INSERT、UPDATE 或 CREATE TABLE 语句',
      inputTitle: '输入 JSON 数据',
      outputTitle: 'SQL 输出',
      inputPlaceholder: '在此粘贴您的 JSON 数组...',
      noResults: '暂无生成的 SQL 语句。请输入 JSON 数据并配置选项。',
      conversionComplete: 'SQL 生成完成！',
      statementsGenerated: '已生成 {count} 条 SQL 语句',
      convert: '生成 SQL',
      options: {
        title: 'SQL 选项',
        tableName: '表名',
        tableNamePlaceholder: '输入表名',
        sqlType: 'SQL 类型',
        whereField: 'WHERE 字段',
        whereFieldPlaceholder: 'WHERE 子句的字段',
        escapeValues: '转义值',
        batchInsert: '批量插入',
      },
      features: {
        insertion: {
          title: '多种 SQL 类型',
          description: '从 JSON 数据生成 INSERT、UPDATE 或 CREATE TABLE 语句。',
        },
        customization: {
          title: '自定义选项',
          description: '配置表名、SQL 类型和字段映射以适应您的数据库。',
        },
        security: {
          title: 'SQL 安全',
          description: '自动值转义以防止 SQL 注入漏洞。',
        },
      },
      errors: {
        emptyInput: '请提供要转换的 JSON 数据',
        emptyTableName: '请提供表名',
        emptyWhereField: '请为 UPDATE 语句提供 WHERE 字段',
        invalidJson: 'JSON 格式无效。请检查您的输入。',
        notArray: '输入必须是 JSON 数组',
        emptyArray: 'JSON 数组不能为空',
        conversionFailed: '生成 SQL 语句失败',
      },
      success: {
        conversionComplete: 'SQL 语句生成成功！',
      },
    },
    universalConverter: {
      title: '通用格式转换器',
      description: '在 JSON、XML 和 HTTP 查询参数之间实时转换',
      inputTitle: '输入',
      outputTitle: '输出',
      format: '格式',
      formatButton: '格式化',
      conversionDirection: '转换方向',
      conversionDirectionDescription: '选择转换方向或交换面板',
      swap: '交换面板',
      convertLeft: '转换到左侧',
      convertRight: '转换到右侧',
      features: {
        bidirectional: {
          title: '双向转换',
          description: '支持任意格式之间的双向转换',
        },
        realtime: {
          title: '实时转换',
          description: '输入时即时转换，自动检测格式',
        },
        validation: {
          title: '格式验证',
          description: '内置所有支持格式的验证功能，提供详细的错误信息',
        },
      },
      errors: {
        conversionFailed: '转换失败。请检查您的输入格式。',
        unsupportedFormat: '选择了不支持的格式',
        invalidJson: 'JSON 格式无效。请检查您的输入。',
        invalidXml: 'XML 格式无效。请检查您的输入。',
        invalidQuery: '查询参数格式无效。请检查您的输入。',
        xmlGenerationFailed: '生成 XML 输出失败',
        queryGenerationFailed: '生成查询参数输出失败',
        formatFailed: '格式化失败。请检查您的输入格式。',
      },
    },
    qrCodeTool: {
      title: '二维码生成与识别工具',
      description: '从文本生成二维码并从图像中识别二维码，支持批量处理',
      tabs: {
        generate: '生成',
        recognize: '识别',
      },
      generate: {
        inputTitle: '生成二维码',
        textInputLabel: '要编码的文本',
        textInputPlaceholder: '输入要生成二维码的文本...\n批量模式下，每行输入一个文本',
        modeLabel: '生成模式',
        singleMode: '单个二维码',
        batchMode: '批量二维码',
        singleModeHint: '从所有文本生成一个二维码',
        batchModeHint: '为每行文本生成一个单独的二维码',
        generateSingleButton: '生成二维码',
        generateBatchButton: '批量生成二维码',
        singleGeneratedTitle: '生成的二维码',
        batchGeneratedTitle: '生成的二维码 ({count} 个)',
        downloadAll: '全部下载为ZIP',
      },
      recognize: {
        uploadTitle: '识别二维码',
        uploadInstruction: '上传二维码图片',
        uploadDescription: '将图片拖放到此处或点击选择文件。支持 JPG、PNG、WebP 格式。',
        pasteHint: '提示：您也可以直接从剪贴板粘贴图片',
        selectFiles: '选择文件',
        resultsTitle: '识别结果',
        copyAll: '复制所有结果',
        recognitionFailed: '识别二维码失败',
      },
      features: {
        batch: {
          title: '批量处理',
          description: '一次生成多个二维码或同时从多个图像中识别二维码。',
        },
        generate: {
          title: '二维码生成',
          description: '从任意文本输入创建高质量的二维码，支持自定义选项。',
        },
        recognize: {
          title: '二维码识别',
          description: '从图像中提取二维码数据，支持多种图像格式。',
        },
      },
      messages: {
        generateSuccess: '二维码生成成功！',
        batchGenerateSuccess: '成功生成 {count} 个二维码！',
        downloadAllSuccess: '所有二维码已下载为 ZIP 文件！',
        copySuccess: '二维码已复制到剪贴板！',
        copyAllSuccess: '所有识别结果已复制到剪贴板！',
        recognitionComplete: '二维码识别完成！',
        pasteSuccess: '已从剪贴板粘贴图片并开始处理！',
      },
      errors: {
        generateFailed: '生成二维码失败。请重试。',
        batchGenerateFailed: '批量生成二维码失败。请检查您的输入。',
        emptyBatch: '请输入至少一个文本来生成二维码。',
        downloadAllFailed: '下载所有二维码失败。请重试。',
        copyFailed: '复制二维码到剪贴板失败。',
        copyAllFailed: '复制识别结果到剪贴板失败。',
        noValidImages: '请选择有效的图像文件。',
        noQRCodeFound: '图像中未找到二维码。',
        noResultsToCopy: '没有可复制的成功识别结果。',
      },
    },
    webRtcChatRoom: {
      title: 'WebRTC 聊天室',
      description: '实时聊天室，支持视频、音频和文件分享功能',
      userName: '您的姓名',
      userNamePlaceholder: '请输入您的姓名',
      or: '或者',
      status: {
        disconnected: '未连接',
        connecting: '连接中...',
        connected: '已连接',
      },
      room: {
        title: '聊天室',
        createRoom: '创建新房间',
        joinRoom: '加入房间',
        roomIdPlaceholder: '输入房间ID',
        leave: '离开',
        you: '你',
        created: '房间已创建，ID: {roomId}',
        joined: '已加入房间: {roomId}',
        left: '已离开房间',
        error: '房间错误: {error}',
        participantJoined: '参与者加入: {id}',
        participantLeft: '参与者离开: {id}',
      },
      participants: {
        title: '参与者',
        online: '在线',
        host: '房主',
        member: '成员',
      },
      video: {
        localVideo: '您的视频',
        remoteVideos: '参与者',
        noRemoteVideo: '无远程视频流',
      },
      controls: {
        camera: '摄像头',
        cameraOff: '摄像头关闭',
        mic: '麦克风',
        micOff: '麦克风关闭',
        shareScreen: '分享屏幕',
        stopScreen: '停止分享',
        mute: '静音',
        kick: '踢出',
        record: '录制',
        stopRecord: '停止录制',
      },
      chat: {
        placeholder: '输入消息...',
        send: '发送',
        sendFile: '发送文件',
        download: '下载',
        roomCreated: '房间已创建: {roomId}',
        roomJoined: '已加入房间: {roomId}',
        participantJoined: '{name} 加入了房间',
        participantLeft: '{name} 离开了房间',
        fileReceived: '收到文件: {fileName}',
        fileSent: '文件已发送: {fileName}',
      },
      logs: {
        title: '调试日志',
        showLogs: '显示调试日志',
      },
    },
    webRtcMeeting: {
      title: 'WebRTC 会议',
      subtitle: '专业视频会议，支持实时协作',
      displayName: '显示名称',
      displayNamePlaceholder: '请输入您的显示名称',
      or: '或者',
      you: '您',
      status: {
        disconnected: '未连接',
        connecting: '连接中...',
        connected: '已连接',
      },
      meeting: {
        title: '会议',
        createMeeting: '创建新会议',
        joinMeeting: '加入会议',
        meetingIdPlaceholder: '输入会议ID',
        leave: '离开会议',
        info: '会议信息',
        copyLink: '复制会议链接',
        created: '会议已创建，ID: {meetingId}',
        joined: '已加入会议: {meetingId}',
        left: '已离开会议',
        error: '会议错误: {error}',
      },
      participants: {
        title: '参会者',
        count: '位参会者',
        host: '主持人',
        member: '成员',
        togglePanel: '切换参会者面板',
      },
      video: {
        sharingScreen: '正在共享屏幕',
        localVideo: '您的视频',
        remoteVideos: '参会者',
        noRemoteVideo: '无远程视频流',
      },
      controls: {
        muteMic: '静音麦克风',
        unmuteMic: '取消静音',
        turnOffCamera: '关闭摄像头',
        turnOnCamera: '开启摄像头',
        startScreenShare: '开始屏幕共享',
        stopScreenShare: '停止屏幕共享',
        mute: '静音参会者',
        remove: '移除参会者',
        muteAll: '全体静音',
        moreOptions: '更多选项',
      },
      recording: {
        start: '开始录制',
        stop: '停止录制',
        recording: '录制中',
      },
      chat: {
        title: '聊天',
        toggleChat: '切换聊天面板',
        placeholder: '输入消息...',
        send: '发送',
        sendFile: '发送文件',
        download: '下载',
        meetingCreated: '会议已创建: {meetingId}',
        meetingJoined: '已加入会议: {meetingId}',
        participantJoined: '{name} 加入了会议',
        participantLeft: '{name} 离开了会议',
        fileReceived: '收到文件: {fileName}',
        fileSent: '文件已发送: {fileName}',
      },
      logs: {
        title: '调试日志',
        showLogs: '显示调试日志',
      },
      av: {
        title: '音视频通话',
        localVideo: '你的摄像头',
        remoteVideos: '参与者',
        noRemoteVideo: '房间中没有其他人',
        enableCamera: '启用摄像头',
        disableCamera: '禁用摄像头',
        enableMic: '启用麦克风',
        disableMic: '禁用麦克风',
        startScreenShare: '共享屏幕',
        stopScreenShare: '停止共享',
        localStreamStarted: '本地流已启动',
        localStreamError: '启动本地流失败: {error}',
        remoteStreamAdded: '远程流已添加',
        cameraEnabled: '摄像头已启用',
        cameraDisabled: '摄像头已禁用',
        microphoneEnabled: '麦克风已启用',
        microphoneDisabled: '麦克风已禁用',
        screenShareStarted: '屏幕共享已启动',
        screenShareStopped: '屏幕共享已停止',
        screenShareStoppedNote: '注意：您可能需要重新协商连接以恢复摄像头流',
        screenShareError: '启动屏幕共享失败: {error}',
      },
      danmaku: {
        title: '弹幕',
        placeholder: '输入你的弹幕...',
        send: '发送',
      },
      discovery: {
        title: '设备发现',
        search: '搜索设备',
        searching: '搜索中...',
        foundDevices: '发现的设备',
        noDevices: '在网络中未找到设备',
        unknownDevice: '未知设备',
        connect: '连接',
        demoDevice1: '演示设备1',
        demoDevice2: '演示设备2',
        connected: '已连接到信令服务器',
        disconnected: '未连接到信令服务器',
      },
      connectionRequests: {
        title: '连接请求',
        accept: '接受',
        reject: '拒绝',
      },
      transfer: {
        title: '文件传输',
        selectFile: '选择要发送的文件',
        send: '发送文件',
        sending: '发送中...',
        progress: '传输进度',
        receivedFiles: '接收的文件',
        download: '下载',
        connectFirst: '请先连接到设备',
      },
      logs: {
        title: '活动日志',
        startDiscovery: '开始设备发现',
        devicesFound: '发现 {count} 个设备',
        connectingToDevice: '正在连接到设备 {deviceId}',
        connectionEstablished: '连接建立成功',
        fileSelected: '选择的文件: {name} ({size})',
        sendingFile: '正在发送文件: {name}',
        fileSent: '文件 {name} 发送成功',
        receivingFile: '正在接收文件: {name} ({size})',
        fileReceived: '接收到文件: {name}',
        unexpectedDataChunk: '收到意外的数据块',
        initialized: 'WebRTC 文件传输初始化',
        webRTCInitialized: 'WebRTC 初始化成功',
        webRTCInitFailed: 'WebRTC 初始化失败: {error}',
        dataChannelOpened: '数据通道已打开',
        dataChannelClosed: '数据通道已关闭',
        dataChannelError: '数据通道错误: {error}',
        messageReceived: '收到消息，类型: {type}',
        iceCandidateGenerated: '生成 ICE 候选',
        iceCandidateSent: 'ICE 候选已发送到信令服务器',
        iceCandidateAdded: 'ICE 候选已添加',
        connectionStateChange: '连接状态变更为: {state}',
        channelNotReady: '数据通道未准备好发送',
        sendFileFailed: '发送文件失败: {error}',
        signalServerConnected: '已连接到信令服务器',
        signalServerDisconnected: '与信令服务器断开连接',
        signalServerConnectionFailed: '连接信令服务器失败: {error}',
        signalServerError: '信令服务器错误: {error}',
        connectingToSignalServer: '正在连接到端口 {port} 上的信令服务器',
        notConnectedToSignalServer: '未连接到信令服务器',
        receivedDeviceId: '收到设备 ID: {id}',
        deviceDiscovered: '发现新设备: {id}',
        deviceDisconnected: '设备断开连接: {id}',
        messageParseError: '解析服务器消息失败: {error}',
        offerSent: '已向目标设备发送 Offer',
        answerSent: '已向源设备发送 Answer',
        offerHandlingFailed: '处理 Offer 失败: {error}',
        answerHandlingFailed: '处理 Answer 失败: {error}',
        iceCandidateFailed: '添加 ICE 候选失败: {error}',
        connectionRequestReceived: '收到来自设备 {id} 的连接请求',
        unexpectedOffer: '收到来自设备 {id} 的意外连接请求',
        connectionTimeout: '连接超时，正在重置连接',
        connectionReset: '连接已重置为初始状态',
        acceptingConnection: '正在接受来自设备 {id} 的连接',
        connectionRequestRejected: '已拒绝来自设备 {id} 的连接请求',
      },
    },
    textSteganography: {
      title: '文本隐写术',
      description: '使用不可见的Unicode字符在普通文本中隐藏秘密信息',
      encryptionTitle: '加密',
      decryptionTitle: '解密',
      visibleText: '可见文本',
      visibleTextPlaceholder: '输入将可见的文本',
      hiddenText: '隐藏文本',
      hiddenTextPlaceholder: '输入要隐藏的秘密信息',
      steganographyText: '隐写文本',
      steganographyTextPlaceholder: '在此粘贴隐写文本来解码',
      steganographyResult: '隐写结果',
      decodedText: '解码文本',
      generateSteganography: '生成隐写文本',
      features: {
        encryption: {
          title: '文本加密',
          description: '使用不可见的Unicode字符在普通文本中隐藏秘密信息',
        },
        decryption: {
          title: '文本解密',
          description: '从隐写文本中提取隐藏信息',
        },
        security: {
          title: '安全通信',
          description: '通过看似正常的文本隐秘分享敏感信息',
        },
      },
      errors: {
        decodingFailed: '解码隐写文本失败',
      },
    },
    imageSteganography: {
      title: '图片隐写术',
      description: '使用LSB（最低有效位）隐写技术将秘密图片隐藏在其他图片中',
      canvasTitle: '图片画布',
      decodedImageTitle: '解码图片',
      operationsTitle: '操作步骤',
      decodingTitle: '解码',
      canvasPlaceholder: '请选择一张图片开始',
      decodedImagePlaceholder: '请选择一张图片进行解码',
      exportImage: '导出图片',
      exportDecodedImage: '导出解码图片',
      modeToggle: {
        encode: '编码',
        decode: '解码',
      },
      step1: '步骤1：选择要隐藏的图片',
      step1Desc: '选择您想要隐藏在另一张图片中的图片',
      step2: '步骤2：保存隐藏图片数据',
      step2Desc: '保存隐藏图片的像素数据用于隐写',
      step3: '步骤3：选择目标图片',
      step3Desc: '选择您想要隐藏秘密图片的图片',
      step4: '步骤4：开始加密',
      step4Desc: '将隐藏图片数据嵌入到目标图片中',
      selectHiddenImage: '选择隐藏图片',
      saveHiddenData: '保存隐藏图片数据',
      selectTargetImage: '选择目标图片',
      startEncryption: '开始加密',
      decodeStep1: '步骤1：选择要解码的图片',
      decodeStep1Desc: '选择包含隐藏数据的图片进行解码',
      decodeStep2: '步骤2：开始解码',
      decodeStep2Desc: '从选中的图片中提取隐藏图片',
      selectImageToDecode: '选择要解码的图片',
      startDecoding: '开始解码',
      decodedImagePreview: '解码图片预览',
      features: {
        encryption: {
          title: '图片加密',
          description: '使用先进的隐写技术将秘密图片隐藏在其他图片中',
        },
        decryption: {
          title: '图片解密',
          description: '从隐写图片中提取隐藏图片',
        },
        steganography: {
          title: 'LSB隐写',
          description: '利用最低有效位技术无形地嵌入数据',
        },
        extraction: {
          title: '数据提取',
          description: '从隐写图片中恢复隐藏数据',
        },
        export: {
          title: '导出结果',
          description: '将隐写结果下载为PNG图片文件',
        },
        result: {
          title: '解码结果',
          description: '查看和导出提取的隐藏图片',
        },
      },
      messages: {
        imageLoaded: '图片加载成功',
        dataSaved: '隐藏图片数据保存成功',
        encryptionComplete: '加密完成',
        decryptionComplete: '解码完成',
        imageExported: '图片导出成功',
        canvasCleared: '画布已清空',
      },
      errors: {
        imageLoadFailed: '图片加载失败',
        dataSaveFailed: '隐藏图片数据保存失败',
        encryptionFailed: '图片加密失败',
        decryptionFailed: '图片解码失败',
        exportFailed: '图片导出失败',
      },
    },
    imageToGifConverter: {
      title: '图片转GIF工具',
      description: '将多张图片转换为动态GIF，支持自定义时间和设置',
      howToUse: {
        title: '使用方法',
        step1: '通过点击"选择图片文件"或拖拽来上传多张图片',
        step2: '调整GIF设置（宽度、质量、帧率）',
        step3: '设置单独的帧延迟并根据需要重新排序图片',
        step4: '点击"生成GIF"来创建您的动画GIF',
      },
      tips: {
        title: '获得最佳效果的技巧',
        tip1: '为获得最佳效果，请使用尺寸相似的图片',
        tip2: '较低的帧率（1-5 FPS）可创建更流畅的动画',
        tip3: '较小的GIF宽度（200-400px）加载更快且消耗更少内存',
        tip4: '使用中等质量可在文件大小和图像质量之间取得良好平衡',
      },
      upload: {
        title: '上传图片',
        dragDrop: '拖拽图片到此处',
        selectFile: '选择图片文件',
        supportedFormats: '支持JPG、PNG、WebP等图片格式',
      },
      settings: {
        width: 'GIF宽度（像素）',
        quality: '质量',
        fps: '帧率（FPS）',
        preserveOriginal: '保持原始GIF尺寸',
        qualityOptions: {
          high: '高质量',
          medium: '中等质量',
          low: '低质量（文件更小）',
        },
      },
      preview: {
        title: '图片预览和控制',
        selectedImages: '已选图片',
        moveUp: '首个移至末尾',
        moveDown: '末个移至开头',
        reverse: '反转顺序',
        shuffle: '随机排序',
      },
      actions: {
        generateGif: '生成GIF',
      },
      processing: {
        title: '处理图片中',
        description: '正在将您的图片转换为GIF。这可能需要一些时间...',
        preview: '预览',
      },
      result: {
        title: '生成的GIF',
        download: '下载GIF',
        createNew: '创建新的GIF',
      },
      features: {
        conversion: {
          title: '图片转换',
          description: '将多张图片转换为高质量的动态GIF，支持自定义帧率和尺寸。',
        },
        customization: {
          title: '全面自定义',
          description: '控制质量、大小、时间、循环次数等各个方面，获得完美效果。',
        },
        animation: {
          title: '动画控制',
          description: '设置单独的帧延迟、重新排序图片并控制动画循环行为。',
        },
      },
      errors: {
        noImages: '请选择有效的图片文件。',
        processingFailed: '处理图片失败，请重试。',
        noImagesSelected: '请先选择图片文件。',
        fileProcessing: '处理选中的文件失败。',
      },
      messages: {
        filesAdded: '成功添加 {count} 个文件！',
        gifGenerated: 'GIF生成成功！',
        filesPasted: '从剪贴板粘贴了 {count} 个文件！',
        cleared: '所有图片已清除！',
      },
    },
    gifEditor: {
      title: 'GIF编辑器',
      description: '拆分、编辑和修改GIF帧，支持自定义时间和设置',
      howToUse: {
        title: '使用方法',
        step1: '通过点击"选择GIF文件"或拖拽来上传GIF文件',
        step2: '调整GIF设置（宽度、质量、帧率）',
        step3: '修改单独的帧延迟、重新排序帧或删除不需要的帧',
        step4: '点击"生成GIF"来创建您编辑的GIF',
      },
      tips: {
        title: '获得最佳效果的技巧',
        tip1: '为获得最佳效果，请使用帧尺寸一致的GIF',
        tip2: '低于20毫秒的帧延迟在某些浏览器中可能无法正确显示',
        tip3: '较小的GIF宽度（200-400px）加载更快且消耗更少内存',
        tip4: '使用中等质量可在文件大小和图像质量之间取得良好平衡',
      },
      upload: {
        title: '上传GIF',
        dragDrop: '拖拽GIF到此处',
        selectFile: '选择GIF文件',
        supportedFormats: '仅支持GIF格式（最大：50MB）',
      },
      settings: {
        width: 'GIF宽度（像素）',
        quality: '质量',
        fps: '帧率（FPS）',
        preserveOriginal: '保持原始GIF尺寸',
        qualityOptions: {
          high: '高质量',
          medium: '中等质量',
          low: '低质量（文件更小）',
        },
      },
      preview: {
        title: 'GIF预览和控制',
        originalGif: '原始GIF',
        frames: '帧',
        frame: '帧',
        delay: '延迟',
        dimensions: '尺寸',
        pixels: '像素',
        moveUp: '首个移至末尾',
        moveDown: '末个移至开头',
        reverse: '反转顺序',
        shuffle: '随机排序帧',
      },
      actions: {
        generateGif: '生成GIF',
      },
      processing: {
        title: '处理GIF中',
        description: '正在编辑您的GIF帧。这可能需要一些时间...',
        preview: '预览',
      },
      result: {
        title: '生成的GIF',
        download: '下载GIF',
        createNew: '编辑另一个GIF',
      },
      features: {
        frameEditing: {
          title: '帧编辑',
          description: '将GIF拆分为单独的帧并修改每帧的延迟、顺序或删除不需要的帧。',
        },
        customization: {
          title: '全面自定义',
          description: '控制质量、大小、时间、帧顺序等各个方面，获得完美效果。',
        },
        animation: {
          title: '动画控制',
          description: '设置单独的帧延迟、重新排序帧并控制动画循环行为。',
        },
      },
      errors: {
        noGif: '请选择有效的GIF文件。',
        invalidFile: '请选择有效的GIF文件。',
        fileTooLarge: '文件大小必须小于50MB。',
        processingFailed: '处理GIF失败，请重试。',
        noFrames: '没有要处理的帧。请先上传GIF文件。',
        fileProcessing: '处理选中的文件失败。',
        frameParsingFailed: '解析GIF帧失败。',
      },
      messages: {
        fileLoaded: 'GIF文件加载成功！',
        gifGenerated: 'GIF生成成功！',
        filePasted: '从剪贴板粘贴了GIF文件！',
      },
    },
    svgEditor: {
      title: 'SVG编辑器',
      description: '编辑SVG代码并实时预览，还提供可视化编辑器',
      howToUse: {
        title: '使用方法',
        step1: '在编辑器中直接编辑SVG代码或使用可视化编辑器',
        step2: '实时预览您的SVG',
        step3: '加载教程学习SVG基础知识',
        step4: '完成后下载您的SVG',
      },
      tips: {
        title: '获得最佳效果的技巧',
        tip1: '使用可视化编辑器快速创建基本形状',
        tip2: '加载教程学习高级SVG技术',
        tip3: '复制SVG代码到剪贴板以在其他应用程序中使用',
        tip4: '下载SVG文件用于Web项目',
      },
      editor: {
        title: 'SVG代码编辑器',
        loadExample: '加载示例',
        clear: '清除',
        placeholder: '在此处输入您的SVG代码...',
        lines: '行数',
        copy: '复制到剪贴板',
      },
      preview: {
        title: 'SVG预览',
        empty: '没有SVG代码可显示。添加一些SVG代码以查看预览。',
        dimensions: '尺寸',
        download: '下载SVG',
      },
      visualEditor: {
        title: '可视化编辑器',
        shapes: '形状',
        history: '历史记录',
        tools: '工具',
        delete: '删除选中项',
        clear: '清空画布',
        undo: '撤销',
        redo: '重做',
        properties: '属性',
        fill: '填充颜色',
        stroke: '描边颜色',
        strokeWidth: '描边宽度',
        rotation: '旋转',
        transparent: '透明',
        noSelection: '未选择形状',
      },
      shapes: {
        rectangle: '矩形',
        circle: '圆形',
        ellipse: '椭圆',
        line: '直线',
        triangle: '三角形',
        path: '路径',
        polygon: '多边形',
        star: '星形',
        heart: '心形',
        quadraticCurve: '二次曲线',
        cubicCurve: '三次曲线',
        arcCurve: '弧线',
      },
      tutorials: {
        title: 'SVG教程',
        viewTutorial: '查看教程',
        basicShapes: '基本形状',
        basicShapesDesc: '学习创建矩形、圆形和椭圆',
        paths: '路径和折线',
        pathsDesc: '使用路径绘制自定义形状',
        gradients: '渐变和图案',
        gradientsDesc: '为您的SVG添加渐变和图案',
      },
      errors: {
        copyFailed: '复制代码到剪贴板失败',
        noSvg: '没有SVG代码可下载',
      },
      messages: {
        exampleLoaded: '示例SVG加载成功！',
        editorCleared: '编辑器已清除！',
        codeCopied: 'SVG代码已复制到剪贴板！',
        svgDownloaded: 'SVG下载成功！',
        tutorialLoaded: '教程已加载！',
        shapeDeleted: '选中的形状已删除！',
        canvasCleared: '画布已清空！',
      },
    },
    textProcessor: {
      title: '文本处理器',
      description: '使用URL编码/解码、Base64编码/解码和哈希函数处理文本',
      inputTitle: '输入文本',
      outputTitle: '输出文本',
      inputPlaceholder: '在此输入或粘贴您的文本...',
      outputPlaceholder: '处理后的文本将显示在这里...',
      chars: '字符',
      words: '单词',
      lines: '行数',
      operations: '文本操作',
      urlEncode: 'URL 编码',
      urlDecode: 'URL 解码',
      base64Encode: 'Base64 编码',
      base64Decode: 'Base64 解码',
      md5Hash: 'MD5 哈希',
      sha256Hash: 'SHA-256 哈希',
      exampleText: '你好世界！这是用于处理的示例文本。https://example.com/?param=value',
      features: {
        urlEncoding: {
          title: 'URL 编码',
          description: '编码或解码 URL 和 URI 组件以确保安全传输',
        },
        base64: {
          title: 'Base64 编码',
          description: '使用 Base64 编码方案编码或解码数据',
        },
        hashing: {
          title: '哈希函数',
          description: '使用 MD5 或 SHA-256 算法生成加密哈希',
        },
      },
      errors: {
        encodingError: '文本编码失败。请检查您的输入。',
        decodingError: '文本解码失败。请检查您的输入。',
        hashingError: '哈希生成失败。请检查您的输入。',
      },
    },
    heartCollage: {
      title: '形状拼贴生成器',
      description: '用您的图片创建各种形状的美丽拼贴',
      uploadTitle: '上传图片',
      uploadDescription: '将图片拖放到此处或点击选择文件',
      supportedFormats: '支持的格式',
      selectFiles: '选择文件',
      settings: '拼贴设置',
      canvasSize: '画布大小',
      shape: '形状',
      imageShape: '图片形状',
      arrangement: '排列方式',
      random: '随机',
      grid: '网格',
      fitAll: '适应所有图片',
      spacing: '间距',
      additionalOptions: '附加选项',
      backgroundColor: '背景颜色',
      borderOptions: '边框选项',
      showBorder: '显示边框',
      small: '小',
      medium: '中',
      large: '大',
      heart: '心形',
      square: '方形',
      rectangle: '矩形',
      circle: '圆形',
      star: '星形',
      rounded: '圆角',
      canvas: '拼贴画布',
      images: '图片',
      autoArrange: '自动排列',
      downloadCollage: '下载拼贴',
      selectedImages: '已选图片',
      dragInstructions: '拖动图片以在形状内重新定位。拖动角落可调整大小。',
      features: {
        collage: {
          title: '形状拼贴',
          description: '用您的图片创建各种形状的美丽拼贴',
        },
        customization: {
          title: '自定义选项',
          description: '自定义画布大小、形状、图片形状、间距等',
        },
        export: {
          title: '导出结果',
          description: '将拼贴下载为高质量PNG图片',
        },
      },
      messages: {
        filesAdded: '成功添加 {count} 个文件',
        arranged: '图片已在形状内排列',
        downloadSuccess: '拼贴下载成功！',
        cleared: '所有图片已清除',
      },
      errors: {
        noImages: '请选择有效的图片文件',
        fileProcessing: '处理选中的文件失败',
        noImagesSelected: '请至少选择一张图片来排列',
        downloadFailed: '下载拼贴失败',
      },
    },
    jsonNumberToText: {
      title: 'JSON 数字转文本转换器',
      description: '将 JSON 对象和数组中的数值转换为文本字符串',
      inputTitle: '输入 JSON',
      inputPlaceholder: '在此粘贴您的 JSON 数据...',
      conversionOptions: '转换选项',
      conversionMode: '转换模式',
      targetFields: '目标字段',
      preserveDecimals: '保留小数位数',
      decimalPlaces: '小数位数',
      addQuotes: '在数字周围添加引号',
      convertAll: '转换所有数字',
      specificFields: '仅特定字段',
      integersOnly: '仅整数',
      decimalsOnly: '仅小数',
      analysisResults: '分析结果',
      numbersFound: '找到的数字',
      fieldsToConvert: '要转换的字段',
      numericFields: '数值字段',
      convertButton: '将数字转换为文本',
      convertedJson: '转换后的 JSON',
      conversionComplete: '转换完成',
      noResults: '暂无结果。请输入 JSON 数据以将数字转换为文本。',
      features: {
        typeConversion: {
          title: '类型转换',
          description: '在保持 JSON 结构的同时将数值转换为文本',
        },
        selectiveProcessing: {
          title: '选择性处理',
          description: '选择特定字段或转换所有数值',
        },
        formatOptions: {
          title: '格式选项',
          description: '控制小数位数和数字格式',
        },
      },
      errors: {
        invalidJson: '无效的 JSON 格式：',
      },
    },
    jsonMissingKeyFinder: {
      title: 'JSON 缺失键查找器',
      description: '查找 JSON 数组中对象的缺失键并检测不一致性',
      inputTitle: '输入 JSON 数组',
      inputPlaceholder: '在此粘贴您的 JSON 数组...',
      analysisOptions: '分析选项',
      ignoreNullValues: '忽略空值',
      deepAnalysis: '深度分析（嵌套对象）',
      caseSensitive: '区分大小写键比较',
      analyzeButton: '分析缺失键',
      analysisResults: '分析结果',
      totalObjects: '对象总数',
      uniqueKeys: '找到的唯一键',
      objectsWithMissing: '有缺失键的对象',
      missingKeysReport: '按对象列出的缺失键',
      noResults: '暂无分析结果。请输入 JSON 数组以查找缺失键。',
      features: {
        keyAnalysis: {
          title: '键分析',
          description: '分析所有对象以查找缺失键和不一致性',
        },
        detailedReport: {
          title: '详细报告',
          description: '获取每个对象缺失键的综合报告',
        },
        exportResults: {
          title: '导出结果',
          description: '导出发现结果并生成完整的对象模板',
        },
      },
      errors: {
        invalidJson: '无效的 JSON 格式：',
        invalidArray: '输入必须是 JSON 数组',
      },
    },
    jsonArraySlicer: {
      title: 'JSON 数组切片器',
      description: '使用索引范围或条件提取 JSON 数组的特定部分',
      inputTitle: '输入 JSON 数组',
      inputPlaceholder: '在此粘贴您的 JSON 数组...',
      slicingOptions: '切片选项',
      method: '方法',
      indexRange: '索引范围',
      conditionalFilter: '条件过滤器',
      randomSample: '随机采样',
      startIndex: '开始',
      endIndex: '结束',
      conditionField: '字段',
      operator: '操作符',
      conditionValue: '值',
      sampleCount: '数量',
      preserveOrder: '保持原始顺序',
      arrayInfo: '数组信息',
      totalElements: '元素总数',
      willExtract: '将提取',
      matchingElements: '匹配元素',
      willSample: '将采样',
      sliceButton: '切片数组',
      slicedArray: '切片数组',
      noResults: '暂无结果。请输入 JSON 数组进行切片。',
      features: {
        indexSlicing: {
          title: '索引切片',
          description: '通过起始/结束索引位置提取数组元素',
        },
        conditionalSlicing: {
          title: '条件切片',
          description: '基于字段值和条件过滤元素',
        },
        smartPreview: {
          title: '智能预览',
          description: '预览切片结果并显示详细统计信息',
        },
      },
      errors: {
        invalidJson: '无效的 JSON 格式：',
        invalidArray: '输入必须是 JSON 数组',
      },
      operators: {
        equals: '等于',
        notEquals: '不等于',
        greater: '大于',
        less: '小于',
        contains: '包含',
      },
    },
    unifiedExcelToJson: {
      title: 'Excel 转 JSON 转换器',
      description: '将 Excel 文件和文本数据转换为 JSON 格式',
      modes: {
        file: '文件模式',
        text: '文本模式',
      },
      tabs: {
        unifiedExcelToJson: 'Excel 转 JSON',
      },
    },
    jsonArrayDeduplicator: {
      title: 'JSON 数组去重器',
      description: '使用各种比较方法从 JSON 数组中删除重复元素',
      inputTitle: '输入 JSON 数组',
      inputPlaceholder: '在此粘贴您的 JSON 数组...',
      deduplicationOptions: '去重选项',
      comparisonMethod: '比较方法',
      compareField: '比较字段',
      keepOccurrence: '保留出现次数',
      caseSensitive: '区分大小写比较',
      showDuplicates: '显示找到的重复项',
      deepComparison: '深度比较（整个对象）',
      shallowComparison: '浅比较（引用）',
      fieldComparison: '特定字段比较',
      stringComparison: 'JSON 字符串比较',
      firstOccurrence: '第一次出现',
      lastOccurrence: '最后一次出现',
      analysisResults: '分析结果',
      totalElements: '元素总数',
      uniqueElements: '唯一元素',
      duplicatesFound: '找到的重复项',
      willRemove: '将移除',
      deduplicateButton: '删除重复项',
      deduplicatedArray: '去重数组',
      deduplicationComplete: '去重完成',
      noResults: '暂无结果。请输入 JSON 数组以删除重复项。',
      features: {
        smartDetection: {
          title: '智能检测',
          description: '使用深度比较或特定字段匹配检测重复项',
        },
        flexibleOptions: {
          title: '灵活选项',
          description: '选择比较方法并指定保留哪次出现',
        },
        detailedStats: {
          title: '详细统计',
          description: '获取关于找到和删除的重复项的综合统计信息',
        },
      },
      errors: {
        invalidJson: '无效的 JSON 格式：',
        invalidArray: '输入必须是 JSON 数组',
      },
    },
    colorPicker: {
      title: '颜色选择器',
      description: '支持多种颜色格式和图像颜色提取的高级颜色选择器',
      colorPicker: '颜色选择器',
      imagePicker: '图像颜色选择器',
      dropImage: '将图像拖放到此处或点击选择',
      selectImage: '选择图像',
      imagePreview: '图像预览',
      pickColor: '拾取颜色',
      cancelPick: '取消拾取',
      clickToPick: '点击图像选择颜色',
      keepPickingUntilCancel: '持续选择颜色，直到点击取消拾取',
      commonColors: '常用颜色',
      conversions: '颜色转换',
      preview: '预览',
      onLight: '浅色背景上',
      onDark: '深色背景上',
      colorPicked: '颜色选择成功！',
      colorUpdated: '颜色更新成功！',
      colorPickError: '从图像中选择颜色失败',
      noImageInClipboard: '剪贴板中未找到图像',
      pasteFailed: '从剪贴板粘贴图像失败',
      pasteImage: '粘贴图像',
      invalidHex: 'HEX颜色格式无效',
      invalidRgb: 'RGB颜色格式无效',
      invalidRgbRange: 'RGB值必须在0-255之间',
      invalidRgba: 'RGBA颜色格式无效',
      invalidRgbaRange: 'RGBA值必须为颜色0-255，透明度0-1',
      invalidHsl: 'HSL颜色格式无效',
      invalidHslRange: 'HSL值必须为H: 0-360, S: 0-100%, L: 0-100%',
      invalidHsv: 'HSV颜色格式无效',
      invalidHsvRange: 'HSV值必须为H: 0-360, S: 0-100%, V: 0-100%',
      invalidCmyk: 'CMYK颜色格式无效',
      invalidCmykRange: 'CMYK值必须在0-100%之间',
      hexPlaceholder: '输入HEX颜色 (#RRGGBB)',
      features: {
        title: '主要功能',
        conversions: {
          title: '多格式支持',
          description: '在 HEX、RGB、RGBA、HSL、HSV 和 CMYK 颜色格式之间转换，实时更新',
        },
        imagePicker: {
          title: '图像颜色提取',
          description: '上传图像并点击任意位置提取图像中的精确颜色',
        },
        commonColors: {
          title: '常用颜色调色板',
          description: '从常用颜色调色板中快速选择',
        },
      },
    },
    pdfViewer: {
      title: 'PDF 查看器',
      description: '预览和基本编辑 PDF 文档',
      uploadSection: '上传 PDF',
      uploadTitle: '上传 PDF 文件',
      uploadDescription: '将您的 PDF 文件拖放到此处或点击浏览',
      supportedFormats: '支持的格式',
      selectPdf: '选择 PDF 文件',
      preview: 'PDF 预览',
      download: '下载 PDF',
      selectAnother: '选择其他 PDF',
      documentInfo: '文档信息',
      fileName: '文件名',
      fileSize: '文件大小',
      pageCount: '页数',
      zoom: '缩放',
      page: '第',
      of: '页，共',
    },
  },
}
