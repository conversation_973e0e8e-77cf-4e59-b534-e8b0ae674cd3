var Qs=c=>{throw TypeError(c)};var us=(c,t,e)=>t.has(c)||Qs("Cannot "+e);var st=(c,t,e)=>(us(c,t,"read from private field"),e?e.call(c):t.get(c)),Lt=(c,t,e)=>t.has(c)?Qs("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(c):t.set(c,e),_t=(c,t,e,s)=>(us(c,t,"write to private field"),s?s.call(c,e):t.set(c,e),e),Dt=(c,t,e)=>(us(c,t,"access private method"),e);var Zs=(c,t,e,s)=>({set _(i){_t(c,t,i,e)},get _(){return st(c,t,s)}});import{r as X,i as Xt,a5 as pt,d as Re,c as F,a as S,f as tt,t as R,S as Ke,R as Ai,e as sn,g as nn,o as O,W as Me,w as xe,a6 as rn,F as ce,k as de,a1 as an,a2 as _i,a3 as fs,h as be,U as Be,V as $,a7 as Js}from"./index-CkZTMFXG.js";const yt=typeof process=="object"&&process+""=="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&process.type!=="browser"),_s=[.001,0,0,.001,0,0],ps=1.35,Ct={ANY:1,DISPLAY:2,PRINT:4,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,IS_EDITING:128,OPLIST:256},Wt={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},Si="pdfjs_internal_editor_",z={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15,SIGNATURE:101,COMMENT:102},W={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_THICKNESS:32,HIGHLIGHT_FREE:33,HIGHLIGHT_SHOW_ALL:34,DRAW_STEP:41},on={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},bt={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4},qe={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},lt={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26},re={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5},es={ERRORS:0,WARNINGS:1,INFOS:5},Qe={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91,setStrokeTransparent:92,setFillTransparent:93,rawFillPath:94},$e={moveTo:0,lineTo:1,curveTo:2,closePath:3},ln={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let ss=es.WARNINGS;function hn(c){Number.isInteger(c)&&(ss=c)}function cn(){return ss}function is(c){ss>=es.INFOS&&console.log(`Info: ${c}`)}function V(c){ss>=es.WARNINGS&&console.log(`Warning: ${c}`)}function Q(c){throw new Error(c)}function ct(c,t){c||Q(t)}function dn(c){switch(c?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function Ei(c,t=null,e=null){if(!c)return null;if(e&&typeof c=="string"&&(e.addDefaultProtocol&&c.startsWith("www.")&&c.match(/\./g)?.length>=2&&(c=`http://${c}`),e.tryConvertEncoding))try{c=mn(c)}catch{}const s=t?URL.parse(c,t):URL.parse(c);return dn(s)?s:null}function Ci(c,t,e=!1){const s=URL.parse(c);return s?(s.hash=t,s.href):e&&Ei(c,"http://example.com")?c.split("#",1)[0]+`${t?`#${t}`:""}`:""}function j(c,t,e,s=!1){return Object.defineProperty(c,t,{value:e,enumerable:!s,configurable:!0,writable:!1}),e}const ie=(function(){function t(e,s){this.message=e,this.name=s}return t.prototype=new Error,t.constructor=t,t})();class ti extends ie{constructor(t,e){super(t,"PasswordException"),this.code=e}}class gs extends ie{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}class Ss extends ie{constructor(t){super(t,"InvalidPDFException")}}class Ze extends ie{constructor(t,e,s){super(t,"ResponseException"),this.status=e,this.missing=s}}class un extends ie{constructor(t){super(t,"FormatError")}}class Yt extends ie{constructor(t){super(t,"AbortException")}}function xi(c){(typeof c!="object"||c?.length===void 0)&&Q("Invalid argument for bytesToString");const t=c.length,e=8192;if(t<e)return String.fromCharCode.apply(null,c);const s=[];for(let i=0;i<t;i+=e){const n=Math.min(i+e,t),r=c.subarray(i,n);s.push(String.fromCharCode.apply(null,r))}return s.join("")}function Ie(c){typeof c!="string"&&Q("Invalid argument for stringToBytes");const t=c.length,e=new Uint8Array(t);for(let s=0;s<t;++s)e[s]=c.charCodeAt(s)&255;return e}function fn(c){return String.fromCharCode(c>>24&255,c>>16&255,c>>8&255,c&255)}function pn(){const c=new Uint8Array(4);return c[0]=1,new Uint32Array(c.buffer,0,1)[0]===1}function gn(){try{return new Function(""),!0}catch{return!1}}class mt{static get isLittleEndian(){return j(this,"isLittleEndian",pn())}static get isEvalSupported(){return j(this,"isEvalSupported",gn())}static get isOffscreenCanvasSupported(){return j(this,"isOffscreenCanvasSupported",typeof OffscreenCanvas<"u")}static get isImageDecoderSupported(){return j(this,"isImageDecoderSupported",typeof ImageDecoder<"u")}static get platform(){const{platform:t,userAgent:e}=navigator;return j(this,"platform",{isAndroid:e.includes("Android"),isLinux:t.includes("Linux"),isMac:t.includes("Mac"),isWindows:t.includes("Win"),isFirefox:e.includes("Firefox")})}static get isCSSRoundSupported(){return j(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const ms=Array.from(Array(256).keys(),c=>c.toString(16).padStart(2,"0"));class D{static makeHexColor(t,e,s){return`#${ms[t]}${ms[e]}${ms[s]}`}static scaleMinMax(t,e){let s;t[0]?(t[0]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[0],e[2]*=t[0],t[3]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[3],e[3]*=t[3]):(s=e[0],e[0]=e[1],e[1]=s,s=e[2],e[2]=e[3],e[3]=s,t[1]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[1],e[3]*=t[1],t[2]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[2],e[2]*=t[2]),e[0]+=t[4],e[1]+=t[5],e[2]+=t[4],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e,s=0){const i=t[s],n=t[s+1];t[s]=i*e[0]+n*e[2]+e[4],t[s+1]=i*e[1]+n*e[3]+e[5]}static applyTransformToBezier(t,e,s=0){const i=e[0],n=e[1],r=e[2],a=e[3],o=e[4],h=e[5];for(let l=0;l<6;l+=2){const d=t[s+l],u=t[s+l+1];t[s+l]=d*i+u*r+o,t[s+l+1]=d*n+u*a+h}}static applyInverseTransform(t,e){const s=t[0],i=t[1],n=e[0]*e[3]-e[1]*e[2];t[0]=(s*e[3]-i*e[2]+e[2]*e[5]-e[4]*e[3])/n,t[1]=(-s*e[1]+i*e[0]+e[4]*e[1]-e[5]*e[0])/n}static axialAlignedBoundingBox(t,e,s){const i=e[0],n=e[1],r=e[2],a=e[3],o=e[4],h=e[5],l=t[0],d=t[1],u=t[2],f=t[3];let g=i*l+o,p=g,b=i*u+o,m=b,_=a*d+h,y=_,w=a*f+h,v=w;if(n!==0||r!==0){const E=n*l,A=n*u,T=r*d,k=r*f;g+=T,m+=T,b+=k,p+=k,_+=E,v+=E,w+=A,y+=A}s[0]=Math.min(s[0],g,b,p,m),s[1]=Math.min(s[1],_,w,y,v),s[2]=Math.max(s[2],g,b,p,m),s[3]=Math.max(s[3],_,w,y,v)}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t,e){const s=t[0],i=t[1],n=t[2],r=t[3],a=s**2+i**2,o=s*n+i*r,h=n**2+r**2,l=(a+h)/2,d=Math.sqrt(l**2-(a*h-o**2));e[0]=Math.sqrt(l+d||1),e[1]=Math.sqrt(l-d||1)}static normalizeRect(t){const e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){const s=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),i=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(s>i)return null;const n=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),r=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return n>r?null:[s,n,i,r]}static pointBoundingBox(t,e,s){s[0]=Math.min(s[0],t),s[1]=Math.min(s[1],e),s[2]=Math.max(s[2],t),s[3]=Math.max(s[3],e)}static rectBoundingBox(t,e,s,i,n){n[0]=Math.min(n[0],t,s),n[1]=Math.min(n[1],e,i),n[2]=Math.max(n[2],t,s),n[3]=Math.max(n[3],e,i)}static#t(t,e,s,i,n,r,a,o,h,l){if(h<=0||h>=1)return;const d=1-h,u=h*h,f=u*h,g=d*(d*(d*t+3*h*e)+3*u*s)+f*i,p=d*(d*(d*n+3*h*r)+3*u*a)+f*o;l[0]=Math.min(l[0],g),l[1]=Math.min(l[1],p),l[2]=Math.max(l[2],g),l[3]=Math.max(l[3],p)}static#e(t,e,s,i,n,r,a,o,h,l,d,u){if(Math.abs(h)<1e-12){Math.abs(l)>=1e-12&&this.#t(t,e,s,i,n,r,a,o,-d/l,u);return}const f=l**2-4*d*h;if(f<0)return;const g=Math.sqrt(f),p=2*h;this.#t(t,e,s,i,n,r,a,o,(-l+g)/p,u),this.#t(t,e,s,i,n,r,a,o,(-l-g)/p,u)}static bezierBoundingBox(t,e,s,i,n,r,a,o,h){h[0]=Math.min(h[0],t,a),h[1]=Math.min(h[1],e,o),h[2]=Math.max(h[2],t,a),h[3]=Math.max(h[3],e,o),this.#e(t,s,n,a,e,i,r,o,3*(-t+3*(s-n)+a),6*(t-2*s+n),3*(s-t),h),this.#e(t,s,n,a,e,i,r,o,3*(-e+3*(i-r)+o),6*(e-2*i+r),3*(i-e),h)}}function mn(c){return decodeURIComponent(escape(c))}let bs=null,ei=null;function bn(c){return bs||(bs=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,ei=new Map([["ﬅ","ſt"]])),c.replaceAll(bs,(t,e,s)=>e?e.normalize("NFKC"):ei.get(s))}function Ti(){if(typeof crypto.randomUUID=="function")return crypto.randomUUID();const c=new Uint8Array(32);return crypto.getRandomValues(c),xi(c)}const Ds="pdfjs_internal_id_";function vn(c,t,e){if(!Array.isArray(e)||e.length<2)return!1;const[s,i,...n]=e;if(!c(s)&&!Number.isInteger(s)||!t(i))return!1;const r=n.length;let a=!0;switch(i.name){case"XYZ":if(r<2||r>3)return!1;break;case"Fit":case"FitB":return r===0;case"FitH":case"FitBH":case"FitV":case"FitBV":if(r>1)return!1;break;case"FitR":if(r!==4)return!1;a=!1;break;default:return!1}for(const o of n)if(!(typeof o=="number"||a&&o===null))return!1;return!0}function wt(c,t,e){return Math.min(Math.max(c,t),e)}function ki(c){return Uint8Array.prototype.toBase64?c.toBase64():btoa(xi(c))}function yn(c){return Uint8Array.fromBase64?Uint8Array.fromBase64(c):Ie(atob(c))}typeof Promise.try!="function"&&(Promise.try=function(c,...t){return new Promise(e=>{e(c(...t))})});typeof Math.sumPrecise!="function"&&(Math.sumPrecise=function(c){return c.reduce((t,e)=>t+e,0)});const Vt="http://www.w3.org/2000/svg";class ge{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}async function Le(c,t="text"){if(_e(c,document.baseURI)){const e=await fetch(c);if(!e.ok)throw new Error(e.statusText);switch(t){case"arraybuffer":return e.arrayBuffer();case"blob":return e.blob();case"json":return e.json()}return e.text()}return new Promise((e,s)=>{const i=new XMLHttpRequest;i.open("GET",c,!0),i.responseType=t,i.onreadystatechange=()=>{if(i.readyState===XMLHttpRequest.DONE){if(i.status===200||i.status===0){switch(t){case"arraybuffer":case"blob":case"json":e(i.response);return}e(i.responseText);return}s(new Error(i.statusText))}},i.send(null)})}class De{constructor({viewBox:t,userUnit:e,scale:s,rotation:i,offsetX:n=0,offsetY:r=0,dontFlip:a=!1}){this.viewBox=t,this.userUnit=e,this.scale=s,this.rotation=i,this.offsetX=n,this.offsetY=r,s*=e;const o=(t[2]+t[0])/2,h=(t[3]+t[1])/2;let l,d,u,f;switch(i%=360,i<0&&(i+=360),i){case 180:l=-1,d=0,u=0,f=1;break;case 90:l=0,d=1,u=1,f=0;break;case 270:l=0,d=-1,u=-1,f=0;break;case 0:l=1,d=0,u=0,f=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}a&&(u=-u,f=-f);let g,p,b,m;l===0?(g=Math.abs(h-t[1])*s+n,p=Math.abs(o-t[0])*s+r,b=(t[3]-t[1])*s,m=(t[2]-t[0])*s):(g=Math.abs(o-t[0])*s+n,p=Math.abs(h-t[1])*s+r,b=(t[2]-t[0])*s,m=(t[3]-t[1])*s),this.transform=[l*s,d*s,u*s,f*s,g-l*s*o-u*s*h,p-d*s*o-f*s*h],this.width=b,this.height=m}get rawDims(){const t=this.viewBox;return j(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:s=this.offsetX,offsetY:i=this.offsetY,dontFlip:n=!1}={}){return new De({viewBox:this.viewBox.slice(),userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:n})}convertToViewportPoint(t,e){const s=[t,e];return D.applyTransform(s,this.transform),s}convertToViewportRectangle(t){const e=[t[0],t[1]];D.applyTransform(e,this.transform);const s=[t[2],t[3]];return D.applyTransform(s,this.transform),[e[0],e[1],s[0],s[1]]}convertToPdfPoint(t,e){const s=[t,e];return D.applyInverseTransform(s,this.transform),s}}class Ns extends ie{constructor(t,e=0){super(t,"RenderingCancelledException"),this.extraDelay=e}}function ns(c){const t=c.length;let e=0;for(;e<t&&c[e].trim()==="";)e++;return c.substring(e,e+5).toLowerCase()==="data:"}function Fs(c){return typeof c=="string"&&/\.pdf$/i.test(c)}function wn(c){return[c]=c.split(/[#?]/,1),c.substring(c.lastIndexOf("/")+1)}function An(c,t="document.pdf"){if(typeof c!="string")return t;if(ns(c))return V('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),t;const s=(a=>{try{return new URL(a)}catch{try{return new URL(decodeURIComponent(a))}catch{try{return new URL(a,"https://foo.bar")}catch{try{return new URL(decodeURIComponent(a),"https://foo.bar")}catch{return null}}}}})(c);if(!s)return t;const i=a=>{try{let o=decodeURIComponent(a);return o.includes("/")?(o=o.split("/").at(-1),o.test(/^\.pdf$/i)?o:a):o}catch{return a}},n=/\.pdf$/i,r=s.pathname.split("/").at(-1);if(n.test(r))return i(r);if(s.searchParams.size>0){const a=Array.from(s.searchParams.values()).reverse();for(const h of a)if(n.test(h))return i(h);const o=Array.from(s.searchParams.keys()).reverse();for(const h of o)if(n.test(h))return i(h)}if(s.hash){const o=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i.exec(s.hash);if(o)return i(o[0])}return t}class si{started=Object.create(null);times=[];time(t){t in this.started&&V(`Timer is already running for ${t}`),this.started[t]=Date.now()}timeEnd(t){t in this.started||V(`Timer has not been started for ${t}`),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){const t=[];let e=0;for(const{name:s}of this.times)e=Math.max(s.length,e);for(const{name:s,start:i,end:n}of this.times)t.push(`${s.padEnd(e)} ${n-i}ms
`);return t.join("")}}function _e(c,t){const e=t?URL.parse(c,t):URL.parse(c);return e?.protocol==="http:"||e?.protocol==="https:"}function Pt(c){c.preventDefault()}function ht(c){c.preventDefault(),c.stopPropagation()}function _n(c){console.log("Deprecated API usage: "+c)}class Es{static#t;static toDateObject(t){if(t instanceof Date)return t;if(!t||typeof t!="string")return null;this.#t||=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?");const e=this.#t.exec(t);if(!e)return null;const s=parseInt(e[1],10);let i=parseInt(e[2],10);i=i>=1&&i<=12?i-1:0;let n=parseInt(e[3],10);n=n>=1&&n<=31?n:1;let r=parseInt(e[4],10);r=r>=0&&r<=23?r:0;let a=parseInt(e[5],10);a=a>=0&&a<=59?a:0;let o=parseInt(e[6],10);o=o>=0&&o<=59?o:0;const h=e[7]||"Z";let l=parseInt(e[8],10);l=l>=0&&l<=23?l:0;let d=parseInt(e[9],10)||0;return d=d>=0&&d<=59?d:0,h==="-"?(r+=l,a+=d):h==="+"&&(r-=l,a-=d),new Date(Date.UTC(s,i,n,r,a,o))}}function Sn(c,{scale:t=1,rotation:e=0}){const{width:s,height:i}=c.attributes.style,n=[0,0,parseInt(s),parseInt(i)];return new De({viewBox:n,userUnit:1,scale:t,rotation:e})}function rs(c){if(c.startsWith("#")){const t=parseInt(c.slice(1),16);return[(t&16711680)>>16,(t&65280)>>8,t&255]}return c.startsWith("rgb(")?c.slice(4,-1).split(",").map(t=>parseInt(t)):c.startsWith("rgba(")?c.slice(5,-1).split(",").map(t=>parseInt(t)).slice(0,3):(V(`Not a valid color format: "${c}"`),[0,0,0])}function En(c){const t=document.createElement("span");t.style.visibility="hidden",t.style.colorScheme="only light",document.body.append(t);for(const e of c.keys()){t.style.color=e;const s=window.getComputedStyle(t).color;c.set(e,rs(s))}t.remove()}function nt(c){const{a:t,b:e,c:s,d:i,e:n,f:r}=c.getTransform();return[t,e,s,i,n,r]}function Nt(c){const{a:t,b:e,c:s,d:i,e:n,f:r}=c.getTransform().invertSelf();return[t,e,s,i,n,r]}function ee(c,t,e=!1,s=!0){if(t instanceof De){const{pageWidth:i,pageHeight:n}=t.rawDims,{style:r}=c,a=mt.isCSSRoundSupported,o=`var(--total-scale-factor) * ${i}px`,h=`var(--total-scale-factor) * ${n}px`,l=a?`round(down, ${o}, var(--scale-round-x))`:`calc(${o})`,d=a?`round(down, ${h}, var(--scale-round-y))`:`calc(${h})`;!e||t.rotation%180===0?(r.width=l,r.height=d):(r.width=d,r.height=l)}s&&c.setAttribute("data-main-rotation",t.rotation)}class $t{constructor(){const{pixelRatio:t}=$t;this.sx=t,this.sy=t}get scaled(){return this.sx!==1||this.sy!==1}get symmetric(){return this.sx===this.sy}limitCanvas(t,e,s,i,n=-1){let r=1/0,a=1/0,o=1/0;s=$t.capPixels(s,n),s>0&&(r=Math.sqrt(s/(t*e))),i!==-1&&(a=i/t,o=i/e);const h=Math.min(r,a,o);return this.sx>h||this.sy>h?(this.sx=h,this.sy=h,!0):!1}static get pixelRatio(){return globalThis.devicePixelRatio||1}static capPixels(t,e){if(e>=0){const s=Math.ceil(window.screen.availWidth*window.screen.availHeight*this.pixelRatio**2*(1+e/100));return t>0?Math.min(t,s):s}return t}}const Cs=["image/apng","image/avif","image/bmp","image/gif","image/jpeg","image/png","image/svg+xml","image/webp","image/x-icon"];class Ce{#t=null;#e=null;#s;#i=null;#r=null;#n=null;#a=null;static#o=null;constructor(t){this.#s=t,Ce.#o||=Object.freeze({freetext:"pdfjs-editor-remove-freetext-button",highlight:"pdfjs-editor-remove-highlight-button",ink:"pdfjs-editor-remove-ink-button",stamp:"pdfjs-editor-remove-stamp-button",signature:"pdfjs-editor-remove-signature-button"})}render(){const t=this.#t=document.createElement("div");t.classList.add("editToolbar","hidden"),t.setAttribute("role","toolbar");const e=this.#s._uiManager._signal;t.addEventListener("contextmenu",Pt,{signal:e}),t.addEventListener("pointerdown",Ce.#c,{signal:e});const s=this.#i=document.createElement("div");s.className="buttons",t.append(s);const i=this.#s.toolbarPosition;if(i){const{style:n}=t,r=this.#s._uiManager.direction==="ltr"?1-i[0]:i[0];n.insetInlineEnd=`${100*r}%`,n.top=`calc(${100*i[1]}% + var(--editor-toolbar-vert-offset))`}return t}get div(){return this.#t}static#c(t){t.stopPropagation()}#h(t){this.#s._focusEventsAllowed=!1,ht(t)}#u(t){this.#s._focusEventsAllowed=!0,ht(t)}#l(t){const e=this.#s._uiManager._signal;t.addEventListener("focusin",this.#h.bind(this),{capture:!0,signal:e}),t.addEventListener("focusout",this.#u.bind(this),{capture:!0,signal:e}),t.addEventListener("contextmenu",Pt,{signal:e})}hide(){this.#t.classList.add("hidden"),this.#e?.hideDropdown()}show(){this.#t.classList.remove("hidden"),this.#r?.shown(),this.#n?.shown()}addDeleteButton(){const{editorType:t,_uiManager:e}=this.#s,s=document.createElement("button");s.className="delete",s.tabIndex=0,s.setAttribute("data-l10n-id",Ce.#o[t]),this.#l(s),s.addEventListener("click",i=>{e.delete()},{signal:e._signal}),this.#i.append(s)}get#p(){const t=document.createElement("div");return t.className="divider",t}async addAltText(t){const e=await t.render();this.#l(e),this.#i.append(e,this.#p),this.#r=t}addComment(t){if(this.#n)return;const e=t.render();e&&(this.#l(e),this.#i.prepend(e,this.#p),this.#n=t,t.toolbar=this)}addColorPicker(t){if(this.#e)return;this.#e=t;const e=t.renderButton();this.#l(e),this.#i.append(e,this.#p)}async addEditSignatureButton(t){const e=this.#a=await t.renderEditButton(this.#s);this.#l(e),this.#i.append(e,this.#p)}async addButton(t,e){switch(t){case"colorPicker":this.addColorPicker(e);break;case"altText":await this.addAltText(e);break;case"editSignature":await this.addEditSignatureButton(e);break;case"delete":this.addDeleteButton();break;case"comment":this.addComment(e);break}}updateEditSignatureButton(t){this.#a&&(this.#a.title=t)}remove(){this.#t.remove(),this.#e?.destroy(),this.#e=null}}class Cn{#t=null;#e=null;#s;constructor(t){this.#s=t}#i(){const t=this.#e=document.createElement("div");t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",Pt,{signal:this.#s._signal});const e=this.#t=document.createElement("div");return e.className="buttons",t.append(e),this.#n(),t}#r(t,e){let s=0,i=0;for(const n of t){const r=n.y+n.height;if(r<s)continue;const a=n.x+(e?n.width:0);if(r>s){i=a,s=r;continue}e?a>i&&(i=a):a<i&&(i=a)}return[e?1-i:i,s]}show(t,e,s){const[i,n]=this.#r(e,s),{style:r}=this.#e||=this.#i();t.append(this.#e),r.insetInlineEnd=`${100*i}%`,r.top=`calc(${100*n}% + var(--editor-toolbar-vert-offset))`}hide(){this.#e.remove()}#n(){const t=document.createElement("button");t.className="highlightButton",t.tabIndex=0,t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e),e.className="visuallyHidden",e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const s=this.#s._signal;t.addEventListener("contextmenu",Pt,{signal:s}),t.addEventListener("click",()=>{this.#s.highlightSelection("floating_button")},{signal:s}),this.#t.append(t)}}function Pi(c,t,e){for(const s of e)t.addEventListener(s,c[s].bind(c))}class xn{#t=0;get id(){return`${Si}${this.#t++}`}}class Os{#t=Ti();#e=0;#s=null;static get _isSVGFittingCanvas(){const t='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',s=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),i=new Image;i.src=t;const n=i.decode().then(()=>(s.drawImage(i,0,0,1,1,0,0,1,3),new Uint32Array(s.getImageData(0,0,1,1).data.buffer)[0]===0));return j(this,"_isSVGFittingCanvas",n)}async#i(t,e){this.#s||=new Map;let s=this.#s.get(t);if(s===null)return null;if(s?.bitmap)return s.refCounter+=1,s;try{s||={bitmap:null,id:`image_${this.#t}_${this.#e++}`,refCounter:0,isSvg:!1};let i;if(typeof e=="string"?(s.url=e,i=await Le(e,"blob")):e instanceof File?i=s.file=e:e instanceof Blob&&(i=e),i.type==="image/svg+xml"){const n=Os._isSVGFittingCanvas,r=new FileReader,a=new Image,o=new Promise((h,l)=>{a.onload=()=>{s.bitmap=a,s.isSvg=!0,h()},r.onload=async()=>{const d=s.svgUrl=r.result;a.src=await n?`${d}#svgView(preserveAspectRatio(none))`:d},a.onerror=r.onerror=l});r.readAsDataURL(i),await o}else s.bitmap=await createImageBitmap(i);s.refCounter=1}catch(i){V(i),s=null}return this.#s.set(t,s),s&&this.#s.set(s.id,s),s}async getFromFile(t){const{lastModified:e,name:s,size:i,type:n}=t;return this.#i(`${e}_${s}_${i}_${n}`,t)}async getFromUrl(t){return this.#i(t,t)}async getFromBlob(t,e){const s=await e;return this.#i(t,s)}async getFromId(t){this.#s||=new Map;const e=this.#s.get(t);if(!e)return null;if(e.bitmap)return e.refCounter+=1,e;if(e.file)return this.getFromFile(e.file);if(e.blobPromise){const{blobPromise:s}=e;return delete e.blobPromise,this.getFromBlob(e.id,s)}return this.getFromUrl(e.url)}getFromCanvas(t,e){this.#s||=new Map;let s=this.#s.get(t);if(s?.bitmap)return s.refCounter+=1,s;const i=new OffscreenCanvas(e.width,e.height);return i.getContext("2d").drawImage(e,0,0),s={bitmap:i.transferToImageBitmap(),id:`image_${this.#t}_${this.#e++}`,refCounter:1,isSvg:!1},this.#s.set(t,s),this.#s.set(s.id,s),s}getSvgUrl(t){const e=this.#s.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#s||=new Map;const e=this.#s.get(t);if(!e||(e.refCounter-=1,e.refCounter!==0))return;const{bitmap:s}=e;if(!e.url&&!e.file){const i=new OffscreenCanvas(s.width,s.height);i.getContext("bitmaprenderer").transferFromImageBitmap(s),e.blobPromise=i.convertToBlob()}s.close?.(),e.bitmap=null}isValidId(t){return t.startsWith(`image_${this.#t}_`)}}class Tn{#t=[];#e=!1;#s;#i=-1;constructor(t=128){this.#s=t}add({cmd:t,undo:e,post:s,mustExec:i,type:n=NaN,overwriteIfSameType:r=!1,keepUndo:a=!1}){if(i&&t(),this.#e)return;const o={cmd:t,undo:e,post:s,type:n};if(this.#i===-1){this.#t.length>0&&(this.#t.length=0),this.#i=0,this.#t.push(o);return}if(r&&this.#t[this.#i].type===n){a&&(o.undo=this.#t[this.#i].undo),this.#t[this.#i]=o;return}const h=this.#i+1;h===this.#s?this.#t.splice(0,1):(this.#i=h,h<this.#t.length&&this.#t.splice(h)),this.#t.push(o)}undo(){if(this.#i===-1)return;this.#e=!0;const{undo:t,post:e}=this.#t[this.#i];t(),e?.(),this.#e=!1,this.#i-=1}redo(){if(this.#i<this.#t.length-1){this.#i+=1,this.#e=!0;const{cmd:t,post:e}=this.#t[this.#i];t(),e?.(),this.#e=!1}}hasSomethingToUndo(){return this.#i!==-1}hasSomethingToRedo(){return this.#i<this.#t.length-1}cleanType(t){if(this.#i!==-1){for(let e=this.#i;e>=0;e--)if(this.#t[e].type!==t){this.#t.splice(e+1,this.#i-e),this.#i=e;return}this.#t.length=0,this.#i=-1}}destroy(){this.#t=null}}class Ne{constructor(t){this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:e}=mt.platform;for(const[s,i,n={}]of t)for(const r of s){const a=r.startsWith("mac+");e&&a?(this.callbacks.set(r.slice(4),{callback:i,options:n}),this.allKeys.add(r.split("+").at(-1))):!e&&!a&&(this.callbacks.set(r,{callback:i,options:n}),this.allKeys.add(r.split("+").at(-1)))}}#t(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);const e=this.buffer.join("+");return this.buffer.length=0,e}exec(t,e){if(!this.allKeys.has(e.key))return;const s=this.callbacks.get(this.#t(e));if(!s)return;const{callback:i,options:{bubbles:n=!1,args:r=[],checker:a=null}}=s;a&&!a(t,e)||(i.bind(t,...r,e)(),n||ht(e))}}class Bs{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);return En(t),j(this,"_colors",t)}convert(t){const e=rs(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[s,i]of this._colors)if(i.every((n,r)=>n===e[r]))return Bs._colorsMapping.get(s);return e}getHexCode(t){const e=this._colors.get(t);return e?D.makeHexColor(...e):t}}class Kt{#t=new AbortController;#e=null;#s=new Map;#i=new Map;#r=null;#n=null;#a=null;#o=new Tn;#c=null;#h=null;#u=null;#l=0;#p=new Set;#m=null;#f=null;#d=new Set;_editorUndoBar=null;#g=!1;#y=!1;#b=!1;#A=null;#_=null;#v=null;#E=null;#P=!1;#S=null;#R=new xn;#k=!1;#T=!1;#x=null;#I=null;#O=null;#L=null;#z=null;#C=z.NONE;#w=new Set;#B=null;#$=null;#V=null;#G=null;#W={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1};#U=[0,0];#D=null;#N=null;#j=null;#K=null;#F=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){const t=Kt.prototype,e=r=>r.#N.contains(document.activeElement)&&document.activeElement.tagName!=="BUTTON"&&r.hasSomethingToControl(),s=(r,{target:a})=>{if(a instanceof HTMLInputElement){const{type:o}=a;return o!=="text"&&o!=="number"}return!0},i=this.TRANSLATE_SMALL,n=this.TRANSLATE_BIG;return j(this,"_keyboardManager",new Ne([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:s}],[["ctrl+z","mac+meta+z"],t.undo,{checker:s}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:s}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:s}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(r,{target:a})=>!(a instanceof HTMLButtonElement)&&r.#N.contains(a)&&!r.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(r,{target:a})=>!(a instanceof HTMLButtonElement)&&r.#N.contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-n,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[n,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-n],checker:e}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,n],checker:e}]]))}constructor(t,e,s,i,n,r,a,o,h,l,d,u,f,g,p,b){const m=this._signal=this.#t.signal;this.#N=t,this.#j=e,this.#K=s,this.#r=i,this.#c=n,this.#$=r,this._eventBus=a,a._on("editingaction",this.onEditingAction.bind(this),{signal:m}),a._on("pagechanging",this.onPageChanging.bind(this),{signal:m}),a._on("scalechanging",this.onScaleChanging.bind(this),{signal:m}),a._on("rotationchanging",this.onRotationChanging.bind(this),{signal:m}),a._on("setpreference",this.onSetPreference.bind(this),{signal:m}),a._on("switchannotationeditorparams",_=>this.updateParams(_.type,_.value),{signal:m}),this.#rt(),this.#ht(),this.#Z(),this.#n=o.annotationStorage,this.#A=o.filterFactory,this.#V=h,this.#E=l||null,this.#g=d,this.#y=u,this.#b=f,this.#z=g||null,this.viewParameters={realScale:ge.PDF_TO_CSS_UNITS,rotation:0},this.isShiftKeyDown=!1,this._editorUndoBar=p||null,this._supportsPinchToZoom=b!==!1}destroy(){this.#F?.resolve(),this.#F=null,this.#t?.abort(),this.#t=null,this._signal=null;for(const t of this.#i.values())t.destroy();this.#i.clear(),this.#s.clear(),this.#d.clear(),this.#L?.clear(),this.#e=null,this.#w.clear(),this.#o.destroy(),this.#r?.destroy(),this.#c?.destroy(),this.#$?.destroy(),this.#S?.hide(),this.#S=null,this.#O?.destroy(),this.#O=null,this.#_&&(clearTimeout(this.#_),this.#_=null),this.#D&&(clearTimeout(this.#D),this.#D=null),this._editorUndoBar?.destroy()}combinedSignal(t){return AbortSignal.any([this._signal,t.signal])}get mlManager(){return this.#z}get useNewAltTextFlow(){return this.#y}get useNewAltTextWhenAddingImage(){return this.#b}get hcmFilter(){return j(this,"hcmFilter",this.#V?this.#A.addHCMFilter(this.#V.foreground,this.#V.background):"none")}get direction(){return j(this,"direction",getComputedStyle(this.#N).direction)}get _highlightColors(){return j(this,"_highlightColors",this.#E?new Map(this.#E.split(",").map(t=>(t=t.split("=").map(e=>e.trim()),t[1]=t[1].toUpperCase(),t))):null)}get highlightColors(){const{_highlightColors:t}=this;if(!t)return j(this,"highlightColors",null);const e=new Map,s=!!this.#V;for(const[i,n]of t){const r=i.endsWith("_HCM");if(s&&r){e.set(i.replace("_HCM",""),n);continue}!s&&!r&&e.set(i,n)}return j(this,"highlightColors",e)}get highlightColorNames(){return j(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,t=>t.reverse())):null)}getNonHCMColor(t){if(!this._highlightColors)return t;const e=this.highlightColorNames.get(t);return this._highlightColors.get(e)||t}getNonHCMColorName(t){return this.highlightColorNames.get(t)||t}setCurrentDrawingSession(t){t?(this.unselectAll(),this.disableUserSelect(!0)):this.disableUserSelect(!1),this.#u=t}setMainHighlightColorPicker(t){this.#O=t}editAltText(t,e=!1){this.#r?.editAltText(this,t,e)}hasCommentManager(){return!!this.#c}editComment(t,e){this.#c?.open(this,t,e)}getSignature(t){this.#$?.getSignature({uiManager:this,editor:t})}get signatureManager(){return this.#$}switchToMode(t,e){this._eventBus.on("annotationeditormodechanged",e,{once:!0,signal:this._signal}),this._eventBus.dispatch("showannotationeditorui",{source:this,mode:t})}setPreference(t,e){this._eventBus.dispatch("setpreference",{source:this,name:t,value:e})}onSetPreference({name:t,value:e}){switch(t){case"enableNewAltTextWhenAddingImage":this.#b=e;break}}onPageChanging({pageNumber:t}){this.#l=t-1}focusMainContainer(){this.#N.focus()}findParent(t,e){for(const s of this.#i.values()){const{x:i,y:n,width:r,height:a}=s.div.getBoundingClientRect();if(t>=i&&t<=i+r&&e>=n&&e<=n+a)return s}return null}disableUserSelect(t=!1){this.#j.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#d.add(t)}removeShouldRescale(t){this.#d.delete(t)}onScaleChanging({scale:t}){this.commitOrRemove(),this.viewParameters.realScale=t*ge.PDF_TO_CSS_UNITS;for(const e of this.#d)e.onScaleChanging();this.#u?.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove(),this.viewParameters.rotation=t}#X({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t}#Q(t){const{currentLayer:e}=this;if(e.hasTextLayer(t))return e;for(const s of this.#i.values())if(s.hasTextLayer(t))return s;return null}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:s,anchorOffset:i,focusNode:n,focusOffset:r}=e,a=e.toString(),h=this.#X(e).closest(".textLayer"),l=this.getSelectionBoxes(h);if(!l)return;e.empty();const d=this.#Q(h),u=this.#C===z.NONE,f=()=>{d?.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:l,anchorNode:s,anchorOffset:i,focusNode:n,focusOffset:r,text:a}),u&&this.showAllEditors("highlight",!0,!0)};if(u){this.switchToMode(z.HIGHLIGHT,f);return}f()}#it(){const t=document.getSelection();if(!t||t.isCollapsed)return;const s=this.#X(t).closest(".textLayer"),i=this.getSelectionBoxes(s);i&&(this.#S||=new Cn(this),this.#S.show(s,i,this.direction==="ltr"))}addToAnnotationStorage(t){!t.isEmpty()&&this.#n&&!this.#n.has(t.id)&&this.#n.setValue(t.id,t)}a11yAlert(t,e=null){const s=this.#K;s&&(s.setAttribute("data-l10n-id",t),e?s.setAttribute("data-l10n-args",JSON.stringify(e)):s.removeAttribute("data-l10n-args"))}#nt(){const t=document.getSelection();if(!t||t.isCollapsed){this.#B&&(this.#S?.hide(),this.#B=null,this.#M({hasSelectedText:!1}));return}const{anchorNode:e}=t;if(e===this.#B)return;const i=this.#X(t).closest(".textLayer");if(!i){this.#B&&(this.#S?.hide(),this.#B=null,this.#M({hasSelectedText:!1}));return}if(this.#S?.hide(),this.#B=e,this.#M({hasSelectedText:!0}),!(this.#C!==z.HIGHLIGHT&&this.#C!==z.NONE)&&(this.#C===z.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0),this.#P=this.isShiftKeyDown,!this.isShiftKeyDown)){const n=this.#C===z.HIGHLIGHT?this.#Q(i):null;n?.toggleDrawing();const r=new AbortController,a=this.combinedSignal(r),o=h=>{h.type==="pointerup"&&h.button!==0||(r.abort(),n?.toggleDrawing(!0),h.type==="pointerup"&&this.#Y("main_toolbar"))};window.addEventListener("pointerup",o,{signal:a}),window.addEventListener("blur",o,{signal:a})}}#Y(t=""){this.#C===z.HIGHLIGHT?this.highlightSelection(t):this.#g&&this.#it()}#rt(){document.addEventListener("selectionchange",this.#nt.bind(this),{signal:this._signal})}#at(){if(this.#v)return;this.#v=new AbortController;const t=this.combinedSignal(this.#v);window.addEventListener("focus",this.focus.bind(this),{signal:t}),window.addEventListener("blur",this.blur.bind(this),{signal:t})}#ot(){this.#v?.abort(),this.#v=null}blur(){if(this.isShiftKeyDown=!1,this.#P&&(this.#P=!1,this.#Y("main_toolbar")),!this.hasSelection)return;const{activeElement:t}=document;for(const e of this.#w)if(e.div.contains(t)){this.#I=[e,t],e._focusEventsAllowed=!1;break}}focus(){if(!this.#I)return;const[t,e]=this.#I;this.#I=null,e.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:this._signal}),e.focus()}#Z(){if(this.#x)return;this.#x=new AbortController;const t=this.combinedSignal(this.#x);window.addEventListener("keydown",this.keydown.bind(this),{signal:t}),window.addEventListener("keyup",this.keyup.bind(this),{signal:t})}#lt(){this.#x?.abort(),this.#x=null}#J(){if(this.#h)return;this.#h=new AbortController;const t=this.combinedSignal(this.#h);document.addEventListener("copy",this.copy.bind(this),{signal:t}),document.addEventListener("cut",this.cut.bind(this),{signal:t}),document.addEventListener("paste",this.paste.bind(this),{signal:t})}#tt(){this.#h?.abort(),this.#h=null}#ht(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t}),document.addEventListener("drop",this.drop.bind(this),{signal:t})}addEditListeners(){this.#Z(),this.#J()}removeEditListeners(){this.#lt(),this.#tt()}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const s of this.#f)if(s.isHandlingMimeForPasting(e)){t.dataTransfer.dropEffect="copy",t.preventDefault();return}}drop(t){for(const e of t.dataTransfer.items)for(const s of this.#f)if(s.isHandlingMimeForPasting(e.type)){s.paste(e,this.currentLayer),t.preventDefault();return}}copy(t){if(t.preventDefault(),this.#e?.commitOrRemove(),!this.hasSelection)return;const e=[];for(const s of this.#w){const i=s.serialize(!0);i&&e.push(i)}e.length!==0&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t),this.delete()}async paste(t){t.preventDefault();const{clipboardData:e}=t;for(const n of e.items)for(const r of this.#f)if(r.isHandlingMimeForPasting(n.type)){r.paste(n,this.currentLayer);return}let s=e.getData("application/pdfjs");if(!s)return;try{s=JSON.parse(s)}catch(n){V(`paste: "${n.message}".`);return}if(!Array.isArray(s))return;this.unselectAll();const i=this.currentLayer;try{const n=[];for(const o of s){const h=await i.deserialize(o);if(!h)return;n.push(h)}const r=()=>{for(const o of n)this.#et(o);this.#st(n)},a=()=>{for(const o of n)o.remove()};this.addCommands({cmd:r,undo:a,mustExec:!0})}catch(n){V(`paste: "${n.message}".`)}}keydown(t){!this.isShiftKeyDown&&t.key==="Shift"&&(this.isShiftKeyDown=!0),this.#C!==z.NONE&&!this.isEditorHandlingKeyboard&&Kt._keyboardManager.exec(this,t)}keyup(t){this.isShiftKeyDown&&t.key==="Shift"&&(this.isShiftKeyDown=!1,this.#P&&(this.#P=!1,this.#Y("main_toolbar")))}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu");break}}#M(t){Object.entries(t).some(([s,i])=>this.#W[s]!==i)&&(this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#W,t)}),this.#C===z.HIGHLIGHT&&t.hasSelectedEditor===!1&&this.#H([[W.HIGHLIGHT_FREE,!0]]))}#H(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){t?(this.#at(),this.#J(),this.#M({isEditing:this.#C!==z.NONE,isEmpty:this.#q(),hasSomethingToUndo:this.#o.hasSomethingToUndo(),hasSomethingToRedo:this.#o.hasSomethingToRedo(),hasSelectedEditor:!1})):(this.#ot(),this.#tt(),this.#M({isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!this.#f){this.#f=t;for(const e of this.#f)this.#H(e.defaultPropertiesToUpdate)}}getId(){return this.#R.id}get currentLayer(){return this.#i.get(this.#l)}getLayer(t){return this.#i.get(t)}get currentPageIndex(){return this.#l}addLayer(t){this.#i.set(t.pageIndex,t),this.#k?t.enable():t.disable()}removeLayer(t){this.#i.delete(t.pageIndex)}async updateMode(t,e=null,s=!1,i=!1,n=!1){if(this.#C!==t&&!(this.#F&&(await this.#F.promise,!this.#F))){if(this.#F=Promise.withResolvers(),this.#u?.commitOrRemove(),this.#C=t,t===z.NONE){this.setEditingState(!1),this.#dt(),this._editorUndoBar?.hide(),this.#F.resolve();return}t===z.SIGNATURE&&await this.#$?.loadSignatures(),this.setEditingState(!0),await this.#ct(),this.unselectAll();for(const r of this.#i.values())r.updateMode(t);if(!e){s&&this.addNewEditorFromKeyboard(),this.#F.resolve();return}for(const r of this.#s.values())r.annotationElementId===e||r.id===e?(this.setSelected(r),n?r.editComment():i&&r.enterInEditMode()):r.unselect();this.#F.resolve()}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t.mode!==this.#C&&this._eventBus.dispatch("switchannotationeditormode",{source:this,...t})}updateParams(t,e){if(this.#f){switch(t){case W.CREATE:this.currentLayer.addNewEditor(e);return;case W.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}}),(this.#G||=new Map).set(t,e),this.showAllEditors("highlight",e);break}if(this.hasSelection)for(const s of this.#w)s.updateParams(t,e);else for(const s of this.#f)s.updateDefaultParams(t,e)}}showAllEditors(t,e,s=!1){for(const n of this.#s.values())n.editorType===t&&n.show(e);(this.#G?.get(W.HIGHLIGHT_SHOW_ALL)??!0)!==e&&this.#H([[W.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(this.#T!==t){this.#T=t;for(const e of this.#i.values())t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}}async#ct(){if(!this.#k){this.#k=!0;const t=[];for(const e of this.#i.values())t.push(e.enable());await Promise.all(t);for(const e of this.#s.values())e.enable()}}#dt(){if(this.unselectAll(),this.#k){this.#k=!1;for(const t of this.#i.values())t.disable();for(const t of this.#s.values())t.disable()}}getEditors(t){const e=[];for(const s of this.#s.values())s.pageIndex===t&&e.push(s);return e}getEditor(t){return this.#s.get(t)}addEditor(t){this.#s.set(t.id,t)}removeEditor(t){t.div.contains(document.activeElement)&&(this.#_&&clearTimeout(this.#_),this.#_=setTimeout(()=>{this.focusMainContainer(),this.#_=null},0)),this.#s.delete(t.id),t.annotationElementId&&this.#L?.delete(t.annotationElementId),this.unselect(t),(!t.annotationElementId||!this.#p.has(t.annotationElementId))&&this.#n?.remove(t.id)}addDeletedAnnotationElement(t){this.#p.add(t.annotationElementId),this.addChangedExistingAnnotation(t),t.deleted=!0}isDeletedAnnotationElement(t){return this.#p.has(t)}removeDeletedAnnotationElement(t){this.#p.delete(t.annotationElementId),this.removeChangedExistingAnnotation(t),t.deleted=!1}#et(t){const e=this.#i.get(t.pageIndex);e?e.addOrRebuild(t):(this.addEditor(t),this.addToAnnotationStorage(t))}setActiveEditor(t){this.#e!==t&&(this.#e=t,t&&this.#H(t.propertiesToUpdate))}get#ut(){let t=null;for(t of this.#w);return t}updateUI(t){this.#ut===t&&this.#H(t.propertiesToUpdate)}updateUIForDefaultProperties(t){this.#H(t.defaultPropertiesToUpdate)}toggleSelected(t){if(this.#w.has(t)){this.#w.delete(t),t.unselect(),this.#M({hasSelectedEditor:this.hasSelection});return}this.#w.add(t),t.select(),this.#H(t.propertiesToUpdate),this.#M({hasSelectedEditor:!0})}setSelected(t){this.updateToolbar({mode:t.mode,editId:t.id}),this.#u?.commitOrRemove();for(const e of this.#w)e!==t&&e.unselect();this.#w.clear(),this.#w.add(t),t.select(),this.#H(t.propertiesToUpdate),this.#M({hasSelectedEditor:!0})}isSelected(t){return this.#w.has(t)}get firstSelectedEditor(){return this.#w.values().next().value}unselect(t){t.unselect(),this.#w.delete(t),this.#M({hasSelectedEditor:this.hasSelection})}get hasSelection(){return this.#w.size!==0}get isEnterHandled(){return this.#w.size===1&&this.firstSelectedEditor.isEnterHandled}undo(){this.#o.undo(),this.#M({hasSomethingToUndo:this.#o.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#q()}),this._editorUndoBar?.hide()}redo(){this.#o.redo(),this.#M({hasSomethingToUndo:!0,hasSomethingToRedo:this.#o.hasSomethingToRedo(),isEmpty:this.#q()})}addCommands(t){this.#o.add(t),this.#M({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#q()})}cleanUndoStack(t){this.#o.cleanType(t)}#q(){if(this.#s.size===0)return!0;if(this.#s.size===1)for(const t of this.#s.values())return t.isEmpty();return!1}delete(){this.commitOrRemove();const t=this.currentLayer?.endDrawingSession(!0);if(!this.hasSelection&&!t)return;const e=t?[t]:[...this.#w],s=()=>{this._editorUndoBar?.show(i,e.length===1?e[0].editorType:e.length);for(const n of e)n.remove()},i=()=>{for(const n of e)this.#et(n)};this.addCommands({cmd:s,undo:i,mustExec:!0})}commitOrRemove(){this.#e?.commitOrRemove()}hasSomethingToControl(){return this.#e||this.hasSelection}#st(t){for(const e of this.#w)e.unselect();this.#w.clear();for(const e of t)e.isEmpty()||(this.#w.add(e),e.select());this.#M({hasSelectedEditor:this.hasSelection})}selectAll(){for(const t of this.#w)t.commit();this.#st(this.#s.values())}unselectAll(){if(!(this.#e&&(this.#e.commitOrRemove(),this.#C!==z.NONE))&&!this.#u?.commitOrRemove()&&this.hasSelection){for(const t of this.#w)t.unselect();this.#w.clear(),this.#M({hasSelectedEditor:!1})}}translateSelectedEditors(t,e,s=!1){if(s||this.commitOrRemove(),!this.hasSelection)return;this.#U[0]+=t,this.#U[1]+=e;const[i,n]=this.#U,r=[...this.#w],a=1e3;this.#D&&clearTimeout(this.#D),this.#D=setTimeout(()=>{this.#D=null,this.#U[0]=this.#U[1]=0,this.addCommands({cmd:()=>{for(const o of r)this.#s.has(o.id)&&(o.translateInPage(i,n),o.translationDone())},undo:()=>{for(const o of r)this.#s.has(o.id)&&(o.translateInPage(-i,-n),o.translationDone())},mustExec:!1})},a);for(const o of r)o.translateInPage(t,e),o.translationDone()}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),this.#m=new Map;for(const t of this.#w)this.#m.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!this.#m)return!1;this.disableUserSelect(!1);const t=this.#m;this.#m=null;let e=!1;for(const[{x:i,y:n,pageIndex:r},a]of t)a.newX=i,a.newY=n,a.newPageIndex=r,e||=i!==a.savedX||n!==a.savedY||r!==a.savedPageIndex;if(!e)return!1;const s=(i,n,r,a)=>{if(this.#s.has(i.id)){const o=this.#i.get(a);o?i._setParentAndPosition(o,n,r):(i.pageIndex=a,i.x=n,i.y=r)}};return this.addCommands({cmd:()=>{for(const[i,{newX:n,newY:r,newPageIndex:a}]of t)s(i,n,r,a)},undo:()=>{for(const[i,{savedX:n,savedY:r,savedPageIndex:a}]of t)s(i,n,r,a)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(this.#m)for(const s of this.#m.keys())s.drag(t,e)}rebuild(t){if(t.parent===null){const e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||this.#w.size===1&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return this.#e===t}getActive(){return this.#e}getMode(){return this.#C}get imageManager(){return j(this,"imageManager",new Os)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let h=0,l=e.rangeCount;h<l;h++)if(!t.contains(e.getRangeAt(h).commonAncestorContainer))return null;const{x:s,y:i,width:n,height:r}=t.getBoundingClientRect();let a;switch(t.getAttribute("data-main-rotation")){case"90":a=(h,l,d,u)=>({x:(l-i)/r,y:1-(h+d-s)/n,width:u/r,height:d/n});break;case"180":a=(h,l,d,u)=>({x:1-(h+d-s)/n,y:1-(l+u-i)/r,width:d/n,height:u/r});break;case"270":a=(h,l,d,u)=>({x:1-(l+u-i)/r,y:(h-s)/n,width:u/r,height:d/n});break;default:a=(h,l,d,u)=>({x:(h-s)/n,y:(l-i)/r,width:d/n,height:u/r});break}const o=[];for(let h=0,l=e.rangeCount;h<l;h++){const d=e.getRangeAt(h);if(!d.collapsed)for(const{x:u,y:f,width:g,height:p}of d.getClientRects())g===0||p===0||o.push(a(u,f,g,p))}return o.length===0?null:o}addChangedExistingAnnotation({annotationElementId:t,id:e}){(this.#a||=new Map).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){this.#a?.delete(t)}renderAnnotationElement(t){const e=this.#a?.get(t.data.id);if(!e)return;const s=this.#n.getRawValue(e);s&&(this.#C===z.NONE&&!s.hasBeenModified||s.renderAnnotationElement(t))}setMissingCanvas(t,e,s){const i=this.#L?.get(t);i&&(i.setCanvas(e,s),this.#L.delete(t))}addMissingCanvas(t,e){(this.#L||=new Map).set(t,e)}}class Bt{#t=null;#e=!1;#s=null;#i=null;#r=null;#n=null;#a=!1;#o=null;#c=null;#h=null;#u=null;#l=!1;static#p=null;static _l10n=null;constructor(t){this.#c=t,this.#l=t._uiManager.useNewAltTextFlow,Bt.#p||=Object.freeze({added:"pdfjs-editor-new-alt-text-added-button","added-label":"pdfjs-editor-new-alt-text-added-button-label",missing:"pdfjs-editor-new-alt-text-missing-button","missing-label":"pdfjs-editor-new-alt-text-missing-button-label",review:"pdfjs-editor-new-alt-text-to-review-button","review-label":"pdfjs-editor-new-alt-text-to-review-button-label"})}static initialize(t){Bt._l10n??=t}async render(){const t=this.#s=document.createElement("button");t.className="altText",t.tabIndex="0";const e=this.#i=document.createElement("span");t.append(e),this.#l?(t.classList.add("new"),t.setAttribute("data-l10n-id",Bt.#p.missing),e.setAttribute("data-l10n-id",Bt.#p["missing-label"])):(t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button"),e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button-label"));const s=this.#c._uiManager._signal;t.addEventListener("contextmenu",Pt,{signal:s}),t.addEventListener("pointerdown",n=>n.stopPropagation(),{signal:s});const i=n=>{n.preventDefault(),this.#c._uiManager.editAltText(this.#c),this.#l&&this.#c._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_clicked",data:{label:this.#m}})};return t.addEventListener("click",i,{capture:!0,signal:s}),t.addEventListener("keydown",n=>{n.target===t&&n.key==="Enter"&&(this.#a=!0,i(n))},{signal:s}),await this.#f(),t}get#m(){return this.#t&&"added"||this.#t===null&&this.guessedText&&"review"||"missing"}finish(){this.#s&&(this.#s.focus({focusVisible:this.#a}),this.#a=!1)}isEmpty(){return this.#l?this.#t===null:!this.#t&&!this.#e}hasData(){return this.#l?this.#t!==null||!!this.#h:this.isEmpty()}get guessedText(){return this.#h}async setGuessedText(t){this.#t===null&&(this.#h=t,this.#u=await Bt._l10n.get("pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer",{generatedAltText:t}),this.#f())}toggleAltTextBadge(t=!1){if(!this.#l||this.#t){this.#o?.remove(),this.#o=null;return}if(!this.#o){const e=this.#o=document.createElement("div");e.className="noAltTextBadge",this.#c.div.append(e)}this.#o.classList.toggle("hidden",!t)}serialize(t){let e=this.#t;return!t&&this.#h===e&&(e=this.#u),{altText:e,decorative:this.#e,guessedText:this.#h,textWithDisclaimer:this.#u}}get data(){return{altText:this.#t,decorative:this.#e}}set data({altText:t,decorative:e,guessedText:s,textWithDisclaimer:i,cancel:n=!1}){s&&(this.#h=s,this.#u=i),!(this.#t===t&&this.#e===e)&&(n||(this.#t=t,this.#e=e),this.#f())}toggle(t=!1){this.#s&&(!t&&this.#n&&(clearTimeout(this.#n),this.#n=null),this.#s.disabled=!t)}shown(){this.#c._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_displayed",data:{label:this.#m}})}destroy(){this.#s?.remove(),this.#s=null,this.#i=null,this.#r=null,this.#o?.remove(),this.#o=null}async#f(){const t=this.#s;if(!t)return;if(this.#l){if(t.classList.toggle("done",!!this.#t),t.setAttribute("data-l10n-id",Bt.#p[this.#m]),this.#i?.setAttribute("data-l10n-id",Bt.#p[`${this.#m}-label`]),!this.#t){this.#r?.remove();return}}else{if(!this.#t&&!this.#e){t.classList.remove("done"),this.#r?.remove();return}t.classList.add("done"),t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-edit-button")}let e=this.#r;if(!e){this.#r=e=document.createElement("span"),e.className="tooltip",e.setAttribute("role","tooltip"),e.id=`alt-text-tooltip-${this.#c.id}`;const i=100,n=this.#c._uiManager._signal;n.addEventListener("abort",()=>{clearTimeout(this.#n),this.#n=null},{once:!0}),t.addEventListener("mouseenter",()=>{this.#n=setTimeout(()=>{this.#n=null,this.#r.classList.add("show"),this.#c._reportTelemetry({action:"alt_text_tooltip"})},i)},{signal:n}),t.addEventListener("mouseleave",()=>{this.#n&&(clearTimeout(this.#n),this.#n=null),this.#r?.classList.remove("show")},{signal:n})}this.#e?e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-decorative-tooltip"):(e.removeAttribute("data-l10n-id"),e.textContent=this.#t),e.parentNode||t.append(e),this.#c.getElementForAltText()?.setAttribute("aria-describedby",e.id)}}class He{#t=null;#e=!1;#s=null;#i=null;#r=null;#n=null;#a=!1;constructor(t){this.#s=t,this.toolbar=null}render(){if(!this.#s._uiManager.hasCommentManager())return null;const t=this.#t=document.createElement("button");t.className="comment",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-edit-comment-button");const e=this.#s._uiManager._signal;t.addEventListener("contextmenu",Pt,{signal:e}),t.addEventListener("pointerdown",i=>i.stopPropagation(),{signal:e});const s=i=>{i.preventDefault(),this.edit()};return t.addEventListener("click",s,{capture:!0,signal:e}),t.addEventListener("keydown",i=>{i.target===t&&i.key==="Enter"&&(this.#e=!0,s(i))},{signal:e}),t}edit(){const{bottom:t,left:e,right:s}=this.#s.getClientDimensions(),i={top:t};this.#s._uiManager.direction==="ltr"?i.right=s:i.left=e,this.#s._uiManager.editComment(this.#s,i)}finish(){this.#t&&(this.#t.focus({focusVisible:this.#e}),this.#e=!1)}isDeleted(){return this.#a||this.#r===""}hasBeenEdited(){return this.isDeleted()||this.#r!==this.#i}serialize(){return this.data}get data(){return{text:this.#r,date:this.#n,deleted:this.#a}}set data(t){if(t===null){this.#r="",this.#a=!0;return}this.#r=t,this.#n=new Date,this.#a=!1}setInitialText(t){this.#i=t,this.data=t}toggle(t=!1){this.#t&&(this.#t.disabled=!t)}shown(){}destroy(){this.#t?.remove(),this.#t=null,this.#r="",this.#n=null,this.#s=null,this.#e=!1,this.#a=!1}}class as{#t;#e=!1;#s=null;#i;#r;#n;#a;#o=null;#c;#h=null;#u;#l=null;constructor({container:t,isPinchingDisabled:e=null,isPinchingStopped:s=null,onPinchStart:i=null,onPinching:n=null,onPinchEnd:r=null,signal:a}){this.#t=t,this.#s=s,this.#i=e,this.#r=i,this.#n=n,this.#a=r,this.#u=new AbortController,this.#c=AbortSignal.any([a,this.#u.signal]),t.addEventListener("touchstart",this.#p.bind(this),{passive:!1,signal:this.#c})}get MIN_TOUCH_DISTANCE_TO_PINCH(){return 35/$t.pixelRatio}#p(t){if(this.#i?.())return;if(t.touches.length===1){if(this.#o)return;const i=this.#o=new AbortController,n=AbortSignal.any([this.#c,i.signal]),r=this.#t,a={capture:!0,signal:n,passive:!1},o=h=>{h.pointerType==="touch"&&(this.#o?.abort(),this.#o=null)};r.addEventListener("pointerdown",h=>{h.pointerType==="touch"&&(ht(h),o(h))},a),r.addEventListener("pointerup",o,a),r.addEventListener("pointercancel",o,a);return}if(!this.#l){this.#l=new AbortController;const i=AbortSignal.any([this.#c,this.#l.signal]),n=this.#t,r={signal:i,capture:!1,passive:!1};n.addEventListener("touchmove",this.#m.bind(this),r);const a=this.#f.bind(this);n.addEventListener("touchend",a,r),n.addEventListener("touchcancel",a,r),r.capture=!0,n.addEventListener("pointerdown",ht,r),n.addEventListener("pointermove",ht,r),n.addEventListener("pointercancel",ht,r),n.addEventListener("pointerup",ht,r),this.#r?.()}if(ht(t),t.touches.length!==2||this.#s?.()){this.#h=null;return}let[e,s]=t.touches;e.identifier>s.identifier&&([e,s]=[s,e]),this.#h={touch0X:e.screenX,touch0Y:e.screenY,touch1X:s.screenX,touch1Y:s.screenY}}#m(t){if(!this.#h||t.touches.length!==2)return;ht(t);let[e,s]=t.touches;e.identifier>s.identifier&&([e,s]=[s,e]);const{screenX:i,screenY:n}=e,{screenX:r,screenY:a}=s,o=this.#h,{touch0X:h,touch0Y:l,touch1X:d,touch1Y:u}=o,f=d-h,g=u-l,p=r-i,b=a-n,m=Math.hypot(p,b)||1,_=Math.hypot(f,g)||1;if(!this.#e&&Math.abs(_-m)<=as.MIN_TOUCH_DISTANCE_TO_PINCH)return;if(o.touch0X=i,o.touch0Y=n,o.touch1X=r,o.touch1Y=a,!this.#e){this.#e=!0;return}const y=[(i+r)/2,(n+a)/2];this.#n?.(y,_,m)}#f(t){t.touches.length>=2||(this.#l&&(this.#l.abort(),this.#l=null,this.#a?.()),this.#h&&(ht(t),this.#h=null,this.#e=!1))}destroy(){this.#u?.abort(),this.#u=null,this.#o?.abort(),this.#o=null}}class N{#t=null;#e=null;#s=null;#i=null;#r=!1;#n=null;#a="";#o=!1;#c=null;#h=null;#u=null;#l=null;#p="";#m=!1;#f=null;#d=!1;#g=!1;#y=!1;#b=null;#A=0;#_=0;#v=null;#E=null;isSelected=!1;_isCopy=!1;_editToolbar=null;_initialOptions=Object.create(null);_initialData=null;_isVisible=!0;_uiManager=null;_focusEventsAllowed=!0;static _l10n=null;static _l10nResizer=null;#P=!1;#S=N._zIndex++;static _borderLineWidth=-1;static _colorManager=new Bs;static _zIndex=1;static _telemetryTimeout=1e3;static get _resizerKeyboardManager(){const t=N.prototype._resizeWithKeyboard,e=Kt.TRANSLATE_SMALL,s=Kt.TRANSLATE_BIG;return j(this,"_resizerKeyboardManager",new Ne([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-s,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[s,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-s]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,s]}],[["Escape","mac+Escape"],N.prototype._stopResizingWithKeyboard]]))}constructor(t){this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null,this.annotationElementId=t.annotationElementId||null;const{rotation:e,rawDims:{pageWidth:s,pageHeight:i,pageX:n,pageY:r}}=this.parent.viewport;this.rotation=e,this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[s,i],this.pageTranslation=[n,r];const[a,o]=this.parentDimensions;this.x=t.x/a,this.y=t.y/o,this.isAttachedToDOM=!1,this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}get mode(){return Object.getPrototypeOf(this).constructor._editorType}static get isDrawer(){return!1}static get _defaultLineColor(){return j(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new kn({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(t,e){if(N._l10n??=t,N._l10nResizer||=Object.freeze({topLeft:"pdfjs-editor-resizer-top-left",topMiddle:"pdfjs-editor-resizer-top-middle",topRight:"pdfjs-editor-resizer-top-right",middleRight:"pdfjs-editor-resizer-middle-right",bottomRight:"pdfjs-editor-resizer-bottom-right",bottomMiddle:"pdfjs-editor-resizer-bottom-middle",bottomLeft:"pdfjs-editor-resizer-bottom-left",middleLeft:"pdfjs-editor-resizer-middle-left"}),N._borderLineWidth!==-1)return;const s=getComputedStyle(document.documentElement);N._borderLineWidth=parseFloat(s.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){Q("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#P}set _isDraggable(t){this.#P=t,this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(t*2),this.y+=this.width*t/(e*2);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(t*2),this.y-=this.width*t/(e*2);break;default:this.x-=this.width/2,this.y-=this.height/2;break}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#S}setParent(t){t!==null?(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions):this.#j(),this.parent=t}focusin(t){this._focusEventsAllowed&&(this.#m?this.#m=!1:this.parent.setSelected(this))}focusout(t){!this._focusEventsAllowed||!this.isAttachedToDOM||t.relatedTarget?.closest(`#${this.id}`)||(t.preventDefault(),this.parent?.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.isInEditMode()&&this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,s,i){const[n,r]=this.parentDimensions;[s,i]=this.screenToPageTranslation(s,i),this.x=(t+s)/n,this.y=(e+i)/r,this.fixAndSetPosition()}_moveAfterPaste(t,e){const[s,i]=this.parentDimensions;this.setAt(t*s,e*i,this.width*s,this.height*i),this._onTranslated()}#R([t,e],s,i){[s,i]=this.screenToPageTranslation(s,i),this.x+=s/t,this.y+=i/e,this._onTranslating(this.x,this.y),this.fixAndSetPosition()}translate(t,e){this.#R(this.parentDimensions,t,e)}translateInPage(t,e){this.#f||=[this.x,this.y,this.width,this.height],this.#R(this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}translationDone(){this._onTranslated(this.x,this.y)}drag(t,e){this.#f||=[this.x,this.y,this.width,this.height];const{div:s,parentDimensions:[i,n]}=this;if(this.x+=t/i,this.y+=e/n,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:d,y:u}=this.div.getBoundingClientRect();this.parent.findNewParent(this,d,u)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:r,y:a}=this;const[o,h]=this.getBaseTranslation();r+=o,a+=h;const{style:l}=s;l.left=`${(100*r).toFixed(2)}%`,l.top=`${(100*a).toFixed(2)}%`,this._onTranslating(r,a),s.scrollIntoView({block:"nearest"})}_onTranslating(t,e){}_onTranslated(t,e){}get _hasBeenMoved(){return!!this.#f&&(this.#f[0]!==this.x||this.#f[1]!==this.y)}get _hasBeenResized(){return!!this.#f&&(this.#f[2]!==this.width||this.#f[3]!==this.height)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:s}=N,i=s/t,n=s/e;switch(this.rotation){case 90:return[-i,n];case 180:return[i,n];case 270:return[i,-n];default:return[-i,-n]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const{div:{style:e},pageDimensions:[s,i]}=this;let{x:n,y:r,width:a,height:o}=this;if(a*=s,o*=i,n*=s,r*=i,this._mustFixPosition)switch(t){case 0:n=wt(n,0,s-a),r=wt(r,0,i-o);break;case 90:n=wt(n,0,s-o),r=wt(r,a,i);break;case 180:n=wt(n,a,s),r=wt(r,o,i);break;case 270:n=wt(n,o,s),r=wt(r,0,i-a);break}this.x=n/=s,this.y=r/=i;const[h,l]=this.getBaseTranslation();n+=h,r+=l,e.left=`${(100*n).toFixed(2)}%`,e.top=`${(100*r).toFixed(2)}%`,this.moveInDOM()}static#k(t,e,s){switch(s){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return N.#k(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return N.#k(t,e,360-this.parentRotation)}#T(t){switch(t){case 90:{const[e,s]=this.pageDimensions;return[0,-e/s,s/e,0]}case 180:return[-1,0,0,-1];case 270:{const[e,s]=this.pageDimensions;return[0,e/s,-s/e,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,s]}=this;return[e*t,s*t]}setDims(t,e){const[s,i]=this.parentDimensions,{style:n}=this.div;n.width=`${(100*t/s).toFixed(2)}%`,this.#o||(n.height=`${(100*e/i).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:s}=t,i=s.endsWith("%"),n=!this.#o&&e.endsWith("%");if(i&&n)return;const[r,a]=this.parentDimensions;i||(t.width=`${(100*parseFloat(s)/r).toFixed(2)}%`),!this.#o&&!n&&(t.height=`${(100*parseFloat(e)/a).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#x(){if(this.#c)return;this.#c=document.createElement("div"),this.#c.classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const s of t){const i=document.createElement("div");this.#c.append(i),i.classList.add("resizer",s),i.setAttribute("data-resizer-name",s),i.addEventListener("pointerdown",this.#I.bind(this,s),{signal:e}),i.addEventListener("contextmenu",Pt,{signal:e}),i.tabIndex=-1}this.div.prepend(this.#c)}#I(t,e){e.preventDefault();const{isMac:s}=mt.platform;if(e.button!==0||e.ctrlKey&&s)return;this.#s?.toggle(!1);const i=this._isDraggable;this._isDraggable=!1,this.#h=[e.screenX,e.screenY];const n=new AbortController,r=this._uiManager.combinedSignal(n);this.parent.togglePointerEvents(!1),window.addEventListener("pointermove",this.#z.bind(this,t),{passive:!0,capture:!0,signal:r}),window.addEventListener("touchmove",ht,{passive:!1,signal:r}),window.addEventListener("contextmenu",Pt,{signal:r}),this.#u={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const a=this.parent.div.style.cursor,o=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const h=()=>{n.abort(),this.parent.togglePointerEvents(!0),this.#s?.toggle(!0),this._isDraggable=i,this.parent.div.style.cursor=a,this.div.style.cursor=o,this.#L()};window.addEventListener("pointerup",h,{signal:r}),window.addEventListener("blur",h,{signal:r})}#O(t,e,s,i){this.width=s,this.height=i,this.x=t,this.y=e;const[n,r]=this.parentDimensions;this.setDims(n*s,r*i),this.fixAndSetPosition(),this._onResized()}_onResized(){}#L(){if(!this.#u)return;const{savedX:t,savedY:e,savedWidth:s,savedHeight:i}=this.#u;this.#u=null;const n=this.x,r=this.y,a=this.width,o=this.height;n===t&&r===e&&a===s&&o===i||this.addCommands({cmd:this.#O.bind(this,n,r,a,o),undo:this.#O.bind(this,t,e,s,i),mustExec:!0})}static _round(t){return Math.round(t*1e4)/1e4}#z(t,e){const[s,i]=this.parentDimensions,n=this.x,r=this.y,a=this.width,o=this.height,h=N.MIN_SIZE/s,l=N.MIN_SIZE/i,d=this.#T(this.rotation),u=(C,x)=>[d[0]*C+d[2]*x,d[1]*C+d[3]*x],f=this.#T(360-this.rotation),g=(C,x)=>[f[0]*C+f[2]*x,f[1]*C+f[3]*x];let p,b,m=!1,_=!1;switch(t){case"topLeft":m=!0,p=(C,x)=>[0,0],b=(C,x)=>[C,x];break;case"topMiddle":p=(C,x)=>[C/2,0],b=(C,x)=>[C/2,x];break;case"topRight":m=!0,p=(C,x)=>[C,0],b=(C,x)=>[0,x];break;case"middleRight":_=!0,p=(C,x)=>[C,x/2],b=(C,x)=>[0,x/2];break;case"bottomRight":m=!0,p=(C,x)=>[C,x],b=(C,x)=>[0,0];break;case"bottomMiddle":p=(C,x)=>[C/2,x],b=(C,x)=>[C/2,0];break;case"bottomLeft":m=!0,p=(C,x)=>[0,x],b=(C,x)=>[C,0];break;case"middleLeft":_=!0,p=(C,x)=>[0,x/2],b=(C,x)=>[C,x/2];break}const y=p(a,o),w=b(a,o);let v=u(...w);const E=N._round(n+v[0]),A=N._round(r+v[1]);let T=1,k=1,I,L;if(e.fromKeyboard)({deltaX:I,deltaY:L}=e);else{const{screenX:C,screenY:x}=e,[U,P]=this.#h;[I,L]=this.screenToPageTranslation(C-U,x-P),this.#h[0]=C,this.#h[1]=x}if([I,L]=g(I/s,L/i),m){const C=Math.hypot(a,o);T=k=Math.max(Math.min(Math.hypot(w[0]-y[0]-I,w[1]-y[1]-L)/C,1/a,1/o),h/a,l/o)}else _?T=wt(Math.abs(w[0]-y[0]-I),h,1)/a:k=wt(Math.abs(w[1]-y[1]-L),l,1)/o;const q=N._round(a*T),K=N._round(o*k);v=u(...b(q,K));const G=E-v[0],dt=A-v[1];this.#f||=[this.x,this.y,this.width,this.height],this.width=q,this.height=K,this.x=G,this.y=dt,this.setDims(s*q,i*K),this.fixAndSetPosition(),this._onResizing()}_onResizing(){}altTextFinish(){this.#s?.finish()}get toolbarButtons(){return null}async addEditToolbar(){if(this._editToolbar||this.#g)return this._editToolbar;this._editToolbar=new Ce(this),this.div.append(this._editToolbar.render()),this._editToolbar.addButton("comment",this.addCommentButton());const{toolbarButtons:t}=this;if(t)for(const[e,s]of t)await this._editToolbar.addButton(e,s);return this._editToolbar.addButton("delete"),this._editToolbar}removeEditToolbar(){this._editToolbar&&(this._editToolbar.remove(),this._editToolbar=null,this.#s?.destroy())}addContainer(t){const e=this._editToolbar?.div;e?e.before(t):this.div.append(t)}getClientDimensions(){return this.div.getBoundingClientRect()}createAltText(){return this.#s||(Bt.initialize(N._l10n),this.#s=new Bt(this),this.#t&&(this.#s.data=this.#t,this.#t=null)),this.#s}get altTextData(){return this.#s?.data}set altTextData(t){this.#s&&(this.#s.data=t)}get guessedAltText(){return this.#s?.guessedText}async setGuessedAltText(t){await this.#s?.setGuessedText(t)}serializeAltText(t){return this.#s?.serialize(t)}hasAltText(){return!!this.#s&&!this.#s.isEmpty()}hasAltTextData(){return this.#s?.hasData()??!1}addCommentButton(){return this.#i?this.#i:this.#i=new He(this)}get commentColor(){return null}get comment(){const t=this.#i;return{text:t.data.text,date:t.data.date,deleted:t.isDeleted(),color:this.commentColor}}set comment(t){this.#i||(this.#i=new He(this)),this.#i.data=t}setCommentData(t){this.#i||(this.#i=new He(this)),this.#i.setInitialText(t)}get hasEditedComment(){return this.#i?.hasBeenEdited()}async editComment(){this.#i||(this.#i=new He(this)),this.#i.edit()}addComment(t){this.hasEditedComment&&(t.popup={contents:this.comment.text,deleted:this.comment.deleted})}render(){const t=this.div=document.createElement("div");t.setAttribute("data-editor-rotation",(360-this.rotation)%360),t.className=this.name,t.setAttribute("id",this.id),t.tabIndex=this.#r?-1:0,t.setAttribute("role","application"),this.defaultL10nId&&t.setAttribute("data-l10n-id",this.defaultL10nId),this._isVisible||t.classList.add("hidden"),this.setInForeground(),this.#G();const[e,s]=this.parentDimensions;this.parentRotation%180!==0&&(t.style.maxWidth=`${(100*s/e).toFixed(2)}%`,t.style.maxHeight=`${(100*e/s).toFixed(2)}%`);const[i,n]=this.getInitialTranslation();return this.translate(i,n),Pi(this,t,["keydown","pointerdown","dblclick"]),this.isResizable&&this._uiManager._supportsPinchToZoom&&(this.#E||=new as({container:t,isPinchingDisabled:()=>!this.isSelected,onPinchStart:this.#C.bind(this),onPinching:this.#w.bind(this),onPinchEnd:this.#B.bind(this),signal:this._uiManager._signal})),this._uiManager._editorUndoBar?.hide(),t}#C(){this.#u={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height},this.#s?.toggle(!1),this.parent.togglePointerEvents(!1)}#w(t,e,s){let n=.7*(s/e)+1-.7;if(n===1)return;const r=this.#T(this.rotation),a=(E,A)=>[r[0]*E+r[2]*A,r[1]*E+r[3]*A],[o,h]=this.parentDimensions,l=this.x,d=this.y,u=this.width,f=this.height,g=N.MIN_SIZE/o,p=N.MIN_SIZE/h;n=Math.max(Math.min(n,1/u,1/f),g/u,p/f);const b=N._round(u*n),m=N._round(f*n);if(b===u&&m===f)return;this.#f||=[l,d,u,f];const _=a(u/2,f/2),y=N._round(l+_[0]),w=N._round(d+_[1]),v=a(b/2,m/2);this.x=y-v[0],this.y=w-v[1],this.width=b,this.height=m,this.setDims(o*b,h*m),this.fixAndSetPosition(),this._onResizing()}#B(){this.#s?.toggle(!0),this.parent.togglePointerEvents(!0),this.#L()}pointerdown(t){const{isMac:e}=mt.platform;if(t.button!==0||t.ctrlKey&&e){t.preventDefault();return}if(this.#m=!0,this._isDraggable){this.#V(t);return}this.#$(t)}#$(t){const{isMac:e}=mt.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}#V(t){const{isSelected:e}=this;this._uiManager.setUpDragSession();let s=!1;const i=new AbortController,n=this._uiManager.combinedSignal(i),r={capture:!0,passive:!1,signal:n},a=h=>{i.abort(),this.#n=null,this.#m=!1,this._uiManager.endDragSession()||this.#$(h),s&&this._onStopDragging()};e&&(this.#A=t.clientX,this.#_=t.clientY,this.#n=t.pointerId,this.#a=t.pointerType,window.addEventListener("pointermove",h=>{s||(s=!0,this._onStartDragging());const{clientX:l,clientY:d,pointerId:u}=h;if(u!==this.#n){ht(h);return}const[f,g]=this.screenToPageTranslation(l-this.#A,d-this.#_);this.#A=l,this.#_=d,this._uiManager.dragSelectedEditors(f,g)},r),window.addEventListener("touchmove",ht,r),window.addEventListener("pointerdown",h=>{h.pointerType===this.#a&&(this.#E||h.isPrimary)&&a(h),ht(h)},r));const o=h=>{if(!this.#n||this.#n===h.pointerId){a(h);return}ht(h)};window.addEventListener("pointerup",o,{signal:n}),window.addEventListener("blur",o,{signal:n})}_onStartDragging(){}_onStopDragging(){}moveInDOM(){this.#b&&clearTimeout(this.#b),this.#b=setTimeout(()=>{this.#b=null,this.parent?.moveEditorInDOM(this)},0)}_setParentAndPosition(t,e,s){t.changeParent(this),this.x=e,this.y=s,this.fixAndSetPosition(),this._onTranslated()}getRect(t,e,s=this.rotation){const i=this.parentScale,[n,r]=this.pageDimensions,[a,o]=this.pageTranslation,h=t/i,l=e/i,d=this.x*n,u=this.y*r,f=this.width*n,g=this.height*r;switch(s){case 0:return[d+h+a,r-u-l-g+o,d+h+f+a,r-u-l+o];case 90:return[d+l+a,r-u+h+o,d+l+g+a,r-u+h+f+o];case 180:return[d-h-f+a,r-u+l+o,d-h+a,r-u+l+g+o];case 270:return[d-l-g+a,r-u-h-f+o,d-l+a,r-u-h+o];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[s,i,n,r]=t,a=n-s,o=r-i;switch(this.rotation){case 0:return[s,e-r,a,o];case 90:return[s,e-i,o,a];case 180:return[n,e-i,a,o];case 270:return[n,e-r,o,a];default:throw new Error("Invalid rotation")}}onceAdded(t){}isEmpty(){return!1}enableEditMode(){return this.isInEditMode()?!1:(this.parent.setEditingState(!1),this.#g=!0,!0)}disableEditMode(){return this.isInEditMode()?(this.parent.setEditingState(!0),this.#g=!1,!0):!1}isInEditMode(){return this.#g}shouldGetKeyboardEvents(){return this.#y}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}get isOnScreen(){const{top:t,left:e,bottom:s,right:i}=this.getClientDimensions(),{innerHeight:n,innerWidth:r}=window;return e<r&&i>0&&t<n&&s>0}#G(){if(this.#l||!this.div)return;this.#l=new AbortController;const t=this._uiManager.combinedSignal(this.#l);this.div.addEventListener("focusin",this.focusin.bind(this),{signal:t}),this.div.addEventListener("focusout",this.focusout.bind(this),{signal:t})}rebuild(){this.#G()}rotate(t){}resize(){}serializeDeleted(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex,popupRef:this._initialData?.popupRef||""}}serialize(t=!1,e=null){Q("An editor must be serializable")}static async deserialize(t,e,s){const i=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:s,annotationElementId:t.annotationElementId});i.rotation=t.rotation,i.#t=t.accessibilityData,i._isCopy=t.isCopy||!1;const[n,r]=i.pageDimensions,[a,o,h,l]=i.getRectInCurrentCoords(t.rect,r);return i.x=a/n,i.y=o/r,i.width=h/n,i.height=l/r,i}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||this.serialize()!==null)}remove(){if(this.#l?.abort(),this.#l=null,this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),this.#b&&(clearTimeout(this.#b),this.#b=null),this.#j(),this.removeEditToolbar(),this.#v){for(const t of this.#v.values())clearTimeout(t);this.#v=null}this.parent=null,this.#E?.destroy(),this.#E=null}get isResizable(){return!1}makeResizable(){this.isResizable&&(this.#x(),this.#c.classList.remove("hidden"))}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||t.key!=="Enter")return;this._uiManager.setSelected(this),this.#u={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const e=this.#c.children;if(!this.#e){this.#e=Array.from(e);const r=this.#W.bind(this),a=this.#U.bind(this),o=this._uiManager._signal;for(const h of this.#e){const l=h.getAttribute("data-resizer-name");h.setAttribute("role","spinbutton"),h.addEventListener("keydown",r,{signal:o}),h.addEventListener("blur",a,{signal:o}),h.addEventListener("focus",this.#D.bind(this,l),{signal:o}),h.setAttribute("data-l10n-id",N._l10nResizer[l])}}const s=this.#e[0];let i=0;for(const r of e){if(r===s)break;i++}const n=(360-this.rotation+this.parentRotation)%360/90*(this.#e.length/4);if(n!==i){if(n<i)for(let a=0;a<i-n;a++)this.#c.append(this.#c.firstChild);else if(n>i)for(let a=0;a<n-i;a++)this.#c.firstChild.before(this.#c.lastChild);let r=0;for(const a of e){const h=this.#e[r++].getAttribute("data-resizer-name");a.setAttribute("data-l10n-id",N._l10nResizer[h])}}this.#N(0),this.#y=!0,this.#c.firstChild.focus({focusVisible:!0}),t.preventDefault(),t.stopImmediatePropagation()}#W(t){N._resizerKeyboardManager.exec(this,t)}#U(t){this.#y&&t.relatedTarget?.parentNode!==this.#c&&this.#j()}#D(t){this.#p=this.#y?t:""}#N(t){if(this.#e)for(const e of this.#e)e.tabIndex=t}_resizeWithKeyboard(t,e){this.#y&&this.#z(this.#p,{deltaX:t,deltaY:e,fromKeyboard:!0})}#j(){this.#y=!1,this.#N(-1),this.#L()}_stopResizingWithKeyboard(){this.#j(),this.div.focus()}select(){if(!(this.isSelected&&this._editToolbar)){if(this.isSelected=!0,this.makeResizable(),this.div?.classList.add("selectedEditor"),!this._editToolbar){this.addEditToolbar().then(()=>{this.div?.classList.contains("selectedEditor")&&this._editToolbar?.show()});return}this._editToolbar?.show(),this.#s?.toggleAltTextBadge(!1)}}unselect(){this.isSelected&&(this.isSelected=!1,this.#c?.classList.add("hidden"),this.div?.classList.remove("selectedEditor"),this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0}),this._editToolbar?.hide(),this.#s?.toggleAltTextBadge(!0))}updateParams(t,e){}disableEditing(){}enableEditing(){}get canChangeContent(){return!1}enterInEditMode(){this.canChangeContent&&(this.enableEditMode(),this.div.focus())}dblclick(t){this.enterInEditMode(),this.parent.updateToolbar({mode:this.constructor._editorType,editId:this.id})}getElementForAltText(){return this.div}get contentDiv(){return this.div}get isEditing(){return this.#d}set isEditing(t){this.#d=t,this.parent&&(t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(t,e){this.#o=!0;const s=t/e,{style:i}=this.div;i.aspectRatio=s,i.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){this.#v||=new Map;const{action:s}=t;let i=this.#v.get(s);i&&clearTimeout(i),i=setTimeout(()=>{this._reportTelemetry(t),this.#v.delete(s),this.#v.size===0&&(this.#v=null)},N._telemetryTimeout),this.#v.set(s,i);return}t.type||=this.editorType,this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}show(t=this._isVisible){this.div.classList.toggle("hidden",!t),this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0),this.#r=!1}disable(){this.div&&(this.div.tabIndex=-1),this.#r=!0}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(!e)e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.container.prepend(e);else if(e.nodeName==="CANVAS"){const s=e;e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),s.before(e)}return e}resetAnnotationElement(t){const{firstChild:e}=t.container;e?.nodeName==="DIV"&&e.classList.contains("annotationContent")&&e.remove()}}class kn extends N{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return this.serializeDeleted()}}const ii=3285377520,Tt=4294901760,Ft=65535;class Ri{constructor(t){this.h1=t?t&4294967295:ii,this.h2=t?t&4294967295:ii}update(t){let e,s;if(typeof t=="string"){e=new Uint8Array(t.length*2),s=0;for(let p=0,b=t.length;p<b;p++){const m=t.charCodeAt(p);m<=255?e[s++]=m:(e[s++]=m>>>8,e[s++]=m&255)}}else if(ArrayBuffer.isView(t))e=t.slice(),s=e.byteLength;else throw new Error("Invalid data format, must be a string or TypedArray.");const i=s>>2,n=s-i*4,r=new Uint32Array(e.buffer,0,i);let a=0,o=0,h=this.h1,l=this.h2;const d=3432918353,u=461845907,f=d&Ft,g=u&Ft;for(let p=0;p<i;p++)p&1?(a=r[p],a=a*d&Tt|a*f&Ft,a=a<<15|a>>>17,a=a*u&Tt|a*g&Ft,h^=a,h=h<<13|h>>>19,h=h*5+3864292196):(o=r[p],o=o*d&Tt|o*f&Ft,o=o<<15|o>>>17,o=o*u&Tt|o*g&Ft,l^=o,l=l<<13|l>>>19,l=l*5+3864292196);switch(a=0,n){case 3:a^=e[i*4+2]<<16;case 2:a^=e[i*4+1]<<8;case 1:a^=e[i*4],a=a*d&Tt|a*f&Ft,a=a<<15|a>>>17,a=a*u&Tt|a*g&Ft,i&1?h^=a:l^=a}this.h1=h,this.h2=l}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,t=t*3981806797&Tt|t*36045&Ft,e=e*4283543511&Tt|((e<<16|t>>>16)*2950163797&Tt)>>>16,t^=e>>>1,t=t*444984403&Tt|t*60499&Ft,e=e*3301882366&Tt|((e<<16|t>>>16)*3120437893&Tt)>>>16,t^=e>>>1,(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const xs=Object.freeze({map:null,hash:"",transfer:void 0});class $s{#t=!1;#e=null;#s=new Map;constructor(){this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){const s=this.#s.get(t);return s===void 0?e:Object.assign(e,s)}getRawValue(t){return this.#s.get(t)}remove(t){if(this.#s.delete(t),this.#s.size===0&&this.resetModified(),typeof this.onAnnotationEditor=="function"){for(const e of this.#s.values())if(e instanceof N)return;this.onAnnotationEditor(null)}}setValue(t,e){const s=this.#s.get(t);let i=!1;if(s!==void 0)for(const[n,r]of Object.entries(e))s[n]!==r&&(i=!0,s[n]=r);else i=!0,this.#s.set(t,e);i&&this.#i(),e instanceof N&&typeof this.onAnnotationEditor=="function"&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#s.has(t)}get size(){return this.#s.size}#i(){this.#t||(this.#t=!0,typeof this.onSetModified=="function"&&this.onSetModified())}resetModified(){this.#t&&(this.#t=!1,typeof this.onResetModified=="function"&&this.onResetModified())}get print(){return new Mi(this)}get serializable(){if(this.#s.size===0)return xs;const t=new Map,e=new Ri,s=[],i=Object.create(null);let n=!1;for(const[r,a]of this.#s){const o=a instanceof N?a.serialize(!1,i):a;o&&(t.set(r,o),e.update(`${r}:${JSON.stringify(o)}`),n||=!!o.bitmap)}if(n)for(const r of t.values())r.bitmap&&s.push(r.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:s}:xs}get editorStats(){let t=null;const e=new Map;for(const s of this.#s.values()){if(!(s instanceof N))continue;const i=s.telemetryFinalData;if(!i)continue;const{type:n}=i;e.has(n)||e.set(n,Object.getPrototypeOf(s).constructor),t||=Object.create(null);const r=t[n]||=new Map;for(const[a,o]of Object.entries(i)){if(a==="type")continue;let h=r.get(a);h||(h=new Map,r.set(a,h));const l=h.get(o)??0;h.set(o,l+1)}}for(const[s,i]of e)t[s]=i.computeTelemetryFinalData(t[s]);return t}resetModifiedIds(){this.#e=null}get modifiedIds(){if(this.#e)return this.#e;const t=[];for(const e of this.#s.values())!(e instanceof N)||!e.annotationElementId||!e.serialize()||t.push(e.annotationElementId);return this.#e={ids:new Set(t),hash:t.join(",")}}[Symbol.iterator](){return this.#s.entries()}}class Mi extends $s{#t;constructor(t){super();const{map:e,hash:s,transfer:i}=t.serializable,n=structuredClone(e,i?{transfer:i}:null);this.#t={map:n,hash:s,transfer:i}}get print(){Q("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#t}get modifiedIds(){return j(this,"modifiedIds",{ids:new Set,hash:""})}}class Pn{#t=new Set;constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){this._document=t,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),this.#t.clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont({systemFontInfo:t,disableFontFace:e,_inspectFont:s}){if(!(!t||this.#t.has(t.loadedName))){if(ct(!e,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:i,src:n,style:r}=t,a=new FontFace(i,n,r);this.addNativeFontFace(a);try{await a.load(),this.#t.add(i),s?.(t)}catch{V(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(a)}return}Q("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo){await this.loadSystemFont(t);return}if(this.isFontLoadingAPISupported){const s=t.createNativeFontFace();if(s){this.addNativeFontFace(s);try{await s.loaded}catch(i){throw V(`Failed to load font '${s.family}': '${i}'.`),t.disableFontFace=!0,i}}return}const e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise(s=>{const i=this._queueLoadingCallback(s);this._prepareFontLoadEvent(t,i)})}}get isFontLoadingAPISupported(){const t=!!this._document?.fonts;return j(this,"isFontLoadingAPISupported",t)}get isSyncFontLoadingSupported(){return j(this,"isSyncFontLoadingSupported",yt||mt.platform.isFirefox)}_queueLoadingCallback(t){function e(){for(ct(!i.done,"completeRequest() cannot be called twice."),i.done=!0;s.length>0&&s[0].done;){const n=s.shift();setTimeout(n.callback,0)}}const{loadingRequests:s}=this,i={done:!1,complete:e,callback:t};return s.push(i),i}get _loadTestFont(){const t=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return j(this,"_loadTestFont",t)}_prepareFontLoadEvent(t,e){function s(w,v){return w.charCodeAt(v)<<24|w.charCodeAt(v+1)<<16|w.charCodeAt(v+2)<<8|w.charCodeAt(v+3)&255}function i(w,v,E,A){const T=w.substring(0,v),k=w.substring(v+E);return T+A+k}let n,r;const a=this._document.createElement("canvas");a.width=1,a.height=1;const o=a.getContext("2d");let h=0;function l(w,v){if(++h>30){V("Load test font never loaded."),v();return}if(o.font="30px "+w,o.fillText(".",0,20),o.getImageData(0,0,1,1).data[3]>0){v();return}setTimeout(l.bind(null,w,v))}const d=`lt${Date.now()}${this.loadTestFontId++}`;let u=this._loadTestFont;u=i(u,976,d.length,d);const g=16,p=1482184792;let b=s(u,g);for(n=0,r=d.length-3;n<r;n+=4)b=b-p+s(d,n)|0;n<d.length&&(b=b-p+s(d+"XXX",n)|0),u=i(u,g,4,fn(b));const m=`url(data:font/opentype;base64,${btoa(u)});`,_=`@font-face {font-family:"${d}";src:${m}}`;this.insertRule(_);const y=this._document.createElement("div");y.style.visibility="hidden",y.style.width=y.style.height="10px",y.style.position="absolute",y.style.top=y.style.left="0px";for(const w of[t.loadedName,d]){const v=this._document.createElement("span");v.textContent="Hi",v.style.fontFamily=w,y.append(v)}this._document.body.append(y),l(d,()=>{y.remove(),e.complete()})}}class Rn{constructor(t,e=null){this.compiledGlyphs=Object.create(null);for(const s in t)this[s]=t[s];this._inspectFont=e}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(!this.cssFontInfo)t=new FontFace(this.loadedName,this.data,{});else{const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`),t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}return this._inspectFont?.(this),t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=`url(data:${this.mimetype};base64,${ki(this.data)});`;let e;if(!this.cssFontInfo)e=`@font-face {font-family:"${this.loadedName}";src:${t}}`;else{let s=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(s+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),e=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${s}src:${t}}`}return this._inspectFont?.(this,t),e}getPathGenerator(t,e){if(this.compiledGlyphs[e]!==void 0)return this.compiledGlyphs[e];const s=this.loadedName+"_path_"+e;let i;try{i=t.get(s)}catch(r){V(`getPathGenerator - ignoring character: "${r}".`)}const n=new Path2D(i||"");return this.fontExtraProperties||t.delete(s),this.compiledGlyphs[e]=n}}function Mn(c){if(c instanceof URL)return c.href;if(typeof c=="string"){if(yt)return c;const t=URL.parse(c,window.location);if(t)return t.href}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function In(c){if(yt&&typeof Buffer<"u"&&c instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(c instanceof Uint8Array&&c.byteLength===c.buffer.byteLength)return c;if(typeof c=="string")return Ie(c);if(c instanceof ArrayBuffer||ArrayBuffer.isView(c)||typeof c=="object"&&!isNaN(c?.length))return new Uint8Array(c);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}function ze(c){if(typeof c!="string")return null;if(c.endsWith("/"))return c;throw new Error(`Invalid factory url: "${c}" must include trailing slash.`)}const Ts=c=>typeof c=="object"&&Number.isInteger(c?.num)&&c.num>=0&&Number.isInteger(c?.gen)&&c.gen>=0,Ln=c=>typeof c=="object"&&typeof c?.name=="string",Dn=vn.bind(null,Ts,Ln);class Nn{#t=new Map;#e=Promise.resolve();postMessage(t,e){const s={data:structuredClone(t,e?{transfer:e}:null)};this.#e.then(()=>{for(const[i]of this.#t)i.call(this,s)})}addEventListener(t,e,s=null){let i=null;if(s?.signal instanceof AbortSignal){const{signal:n}=s;if(n.aborted){V("LoopbackPort - cannot use an `aborted` signal.");return}const r=()=>this.removeEventListener(t,e);i=()=>n.removeEventListener("abort",r),n.addEventListener("abort",r)}this.#t.set(e,i)}removeEventListener(t,e){this.#t.get(e)?.(),this.#t.delete(e)}terminate(){for(const[,t]of this.#t)t?.();this.#t.clear()}}const Ve={DATA:1,ERROR:2},ot={CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function ni(){}function St(c){if(c instanceof Yt||c instanceof Ss||c instanceof ti||c instanceof Ze||c instanceof gs)return c;switch(c instanceof Error||typeof c=="object"&&c!==null||Q('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),c.name){case"AbortException":return new Yt(c.message);case"InvalidPDFException":return new Ss(c.message);case"PasswordException":return new ti(c.message,c.code);case"ResponseException":return new Ze(c.message,c.status,c.missing);case"UnknownErrorException":return new gs(c.message,c.details)}return new gs(c.message,c.toString())}class Se{#t=new AbortController;constructor(t,e,s){this.sourceName=t,this.targetName=e,this.comObj=s,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),s.addEventListener("message",this.#e.bind(this),{signal:this.#t.signal})}#e({data:t}){if(t.targetName!==this.sourceName)return;if(t.stream){this.#i(t);return}if(t.callback){const s=t.callbackId,i=this.callbackCapabilities[s];if(!i)throw new Error(`Cannot resolve callback ${s}`);if(delete this.callbackCapabilities[s],t.callback===Ve.DATA)i.resolve(t.data);else if(t.callback===Ve.ERROR)i.reject(St(t.reason));else throw new Error("Unexpected callback case");return}const e=this.actionHandler[t.action];if(!e)throw new Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){const s=this.sourceName,i=t.sourceName,n=this.comObj;Promise.try(e,t.data).then(function(r){n.postMessage({sourceName:s,targetName:i,callback:Ve.DATA,callbackId:t.callbackId,data:r})},function(r){n.postMessage({sourceName:s,targetName:i,callback:Ve.ERROR,callbackId:t.callbackId,reason:St(r)})});return}if(t.streamId){this.#s(t);return}e(t.data)}on(t,e){const s=this.actionHandler;if(s[t])throw new Error(`There is already an actionName called "${t}"`);s[t]=e}send(t,e,s){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},s)}sendWithPromise(t,e,s){const i=this.callbackId++,n=Promise.withResolvers();this.callbackCapabilities[i]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:i,data:e},s)}catch(r){n.reject(r)}return n.promise}sendWithStream(t,e,s,i){const n=this.streamId++,r=this.sourceName,a=this.targetName,o=this.comObj;return new ReadableStream({start:h=>{const l=Promise.withResolvers();return this.streamControllers[n]={controller:h,startCall:l,pullCall:null,cancelCall:null,isClosed:!1},o.postMessage({sourceName:r,targetName:a,action:t,streamId:n,data:e,desiredSize:h.desiredSize},i),l.promise},pull:h=>{const l=Promise.withResolvers();return this.streamControllers[n].pullCall=l,o.postMessage({sourceName:r,targetName:a,stream:ot.PULL,streamId:n,desiredSize:h.desiredSize}),l.promise},cancel:h=>{ct(h instanceof Error,"cancel must have a valid reason");const l=Promise.withResolvers();return this.streamControllers[n].cancelCall=l,this.streamControllers[n].isClosed=!0,o.postMessage({sourceName:r,targetName:a,stream:ot.CANCEL,streamId:n,reason:St(h)}),l.promise}},s)}#s(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,n=this.comObj,r=this,a=this.actionHandler[t.action],o={enqueue(h,l=1,d){if(this.isCancelled)return;const u=this.desiredSize;this.desiredSize-=l,u>0&&this.desiredSize<=0&&(this.sinkCapability=Promise.withResolvers(),this.ready=this.sinkCapability.promise),n.postMessage({sourceName:s,targetName:i,stream:ot.ENQUEUE,streamId:e,chunk:h},d)},close(){this.isCancelled||(this.isCancelled=!0,n.postMessage({sourceName:s,targetName:i,stream:ot.CLOSE,streamId:e}),delete r.streamSinks[e])},error(h){ct(h instanceof Error,"error must have a valid reason"),!this.isCancelled&&(this.isCancelled=!0,n.postMessage({sourceName:s,targetName:i,stream:ot.ERROR,streamId:e,reason:St(h)}))},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};o.sinkCapability.resolve(),o.ready=o.sinkCapability.promise,this.streamSinks[e]=o,Promise.try(a,t.data,o).then(function(){n.postMessage({sourceName:s,targetName:i,stream:ot.START_COMPLETE,streamId:e,success:!0})},function(h){n.postMessage({sourceName:s,targetName:i,stream:ot.START_COMPLETE,streamId:e,reason:St(h)})})}#i(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,n=this.comObj,r=this.streamControllers[e],a=this.streamSinks[e];switch(t.stream){case ot.START_COMPLETE:t.success?r.startCall.resolve():r.startCall.reject(St(t.reason));break;case ot.PULL_COMPLETE:t.success?r.pullCall.resolve():r.pullCall.reject(St(t.reason));break;case ot.PULL:if(!a){n.postMessage({sourceName:s,targetName:i,stream:ot.PULL_COMPLETE,streamId:e,success:!0});break}a.desiredSize<=0&&t.desiredSize>0&&a.sinkCapability.resolve(),a.desiredSize=t.desiredSize,Promise.try(a.onPull||ni).then(function(){n.postMessage({sourceName:s,targetName:i,stream:ot.PULL_COMPLETE,streamId:e,success:!0})},function(h){n.postMessage({sourceName:s,targetName:i,stream:ot.PULL_COMPLETE,streamId:e,reason:St(h)})});break;case ot.ENQUEUE:if(ct(r,"enqueue should have stream controller"),r.isClosed)break;r.controller.enqueue(t.chunk);break;case ot.CLOSE:if(ct(r,"close should have stream controller"),r.isClosed)break;r.isClosed=!0,r.controller.close(),this.#r(r,e);break;case ot.ERROR:ct(r,"error should have stream controller"),r.controller.error(St(t.reason)),this.#r(r,e);break;case ot.CANCEL_COMPLETE:t.success?r.cancelCall.resolve():r.cancelCall.reject(St(t.reason)),this.#r(r,e);break;case ot.CANCEL:if(!a)break;const o=St(t.reason);Promise.try(a.onCancel||ni,o).then(function(){n.postMessage({sourceName:s,targetName:i,stream:ot.CANCEL_COMPLETE,streamId:e,success:!0})},function(h){n.postMessage({sourceName:s,targetName:i,stream:ot.CANCEL_COMPLETE,streamId:e,reason:St(h)})}),a.sinkCapability.reject(o),a.isCancelled=!0,delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async#r(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]),delete this.streamControllers[e]}destroy(){this.#t?.abort(),this.#t=null}}class Ii{#t=!1;constructor({enableHWA:t=!1}){this.#t=t}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const s=this._createCanvas(t,e);return{canvas:s,context:s.getContext("2d",{willReadFrequently:!this.#t})}}reset(t,e,s){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||s<=0)throw new Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=s}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){Q("Abstract method `_createCanvas` called.")}}class Fn extends Ii{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}){super({enableHWA:e}),this._document=t}_createCanvas(t,e){const s=this._document.createElement("canvas");return s.width=t,s.height=e,s}}class Li{constructor({baseUrl:t=null,isCompressed:e=!0}){this.baseUrl=t,this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error("Ensure that the `cMapUrl` and `cMapPacked` API parameters are provided.");if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":"");return this._fetch(e).then(s=>({cMapData:s,isCompressed:this.isCompressed})).catch(s=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)})}async _fetch(t){Q("Abstract method `_fetch` called.")}}class ri extends Li{async _fetch(t){const e=await Le(t,this.isCompressed?"arraybuffer":"text");return e instanceof ArrayBuffer?new Uint8Array(e):Ie(e)}}class Di{addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,s,i,n){return"none"}destroy(t=!1){}}class On extends Di{#t;#e;#s;#i;#r;#n;#a=0;constructor({docId:t,ownerDocument:e=globalThis.document}){super(),this.#i=t,this.#r=e}get#o(){return this.#e||=new Map}get#c(){return this.#n||=new Map}get#h(){if(!this.#s){const t=this.#r.createElement("div"),{style:e}=t;e.visibility="hidden",e.contain="strict",e.width=e.height=0,e.position="absolute",e.top=e.left=0,e.zIndex=-1;const s=this.#r.createElementNS(Vt,"svg");s.setAttribute("width",0),s.setAttribute("height",0),this.#s=this.#r.createElementNS(Vt,"defs"),t.append(s),s.append(this.#s),this.#r.body.append(t)}return this.#s}#u(t){if(t.length===1){const o=t[0],h=new Array(256);for(let d=0;d<256;d++)h[d]=o[d]/255;const l=h.join(",");return[l,l,l]}const[e,s,i]=t,n=new Array(256),r=new Array(256),a=new Array(256);for(let o=0;o<256;o++)n[o]=e[o]/255,r[o]=s[o]/255,a[o]=i[o]/255;return[n.join(","),r.join(","),a.join(",")]}#l(t){if(this.#t===void 0){this.#t="";const e=this.#r.URL;e!==this.#r.baseURI&&(ns(e)?V('#createUrl: ignore "data:"-URL for performance reasons.'):this.#t=Ci(e,""))}return`url(${this.#t}#${t})`}addFilter(t){if(!t)return"none";let e=this.#o.get(t);if(e)return e;const[s,i,n]=this.#u(t),r=t.length===1?s:`${s}${i}${n}`;if(e=this.#o.get(r),e)return this.#o.set(t,e),e;const a=`g_${this.#i}_transfer_map_${this.#a++}`,o=this.#l(a);this.#o.set(t,o),this.#o.set(r,o);const h=this.#f(a);return this.#g(s,i,n,h),o}addHCMFilter(t,e){const s=`${t}-${e}`,i="base";let n=this.#c.get(i);if(n?.key===s||(n?(n.filter?.remove(),n.key=s,n.url="none",n.filter=null):(n={key:s,url:"none",filter:null},this.#c.set(i,n)),!t||!e))return n.url;const r=this.#b(t);t=D.makeHexColor(...r);const a=this.#b(e);if(e=D.makeHexColor(...a),this.#h.style.color="",t==="#000000"&&e==="#ffffff"||t===e)return n.url;const o=new Array(256);for(let f=0;f<=255;f++){const g=f/255;o[f]=g<=.03928?g/12.92:((g+.055)/1.055)**2.4}const h=o.join(","),l=`g_${this.#i}_hcm_filter`,d=n.filter=this.#f(l);this.#g(h,h,h,d),this.#m(d);const u=(f,g)=>{const p=r[f]/255,b=a[f]/255,m=new Array(g+1);for(let _=0;_<=g;_++)m[_]=p+_/g*(b-p);return m.join(",")};return this.#g(u(0,5),u(1,5),u(2,5),d),n.url=this.#l(l),n.url}addAlphaFilter(t){let e=this.#o.get(t);if(e)return e;const[s]=this.#u([t]),i=`alpha_${s}`;if(e=this.#o.get(i),e)return this.#o.set(t,e),e;const n=`g_${this.#i}_alpha_map_${this.#a++}`,r=this.#l(n);this.#o.set(t,r),this.#o.set(i,r);const a=this.#f(n);return this.#y(s,a),r}addLuminosityFilter(t){let e=this.#o.get(t||"luminosity");if(e)return e;let s,i;if(t?([s]=this.#u([t]),i=`luminosity_${s}`):i="luminosity",e=this.#o.get(i),e)return this.#o.set(t,e),e;const n=`g_${this.#i}_luminosity_map_${this.#a++}`,r=this.#l(n);this.#o.set(t,r),this.#o.set(i,r);const a=this.#f(n);return this.#p(a),t&&this.#y(s,a),r}addHighlightHCMFilter(t,e,s,i,n){const r=`${e}-${s}-${i}-${n}`;let a=this.#c.get(t);if(a?.key===r||(a?(a.filter?.remove(),a.key=r,a.url="none",a.filter=null):(a={key:r,url:"none",filter:null},this.#c.set(t,a)),!e||!s))return a.url;const[o,h]=[e,s].map(this.#b.bind(this));let l=Math.round(.2126*o[0]+.7152*o[1]+.0722*o[2]),d=Math.round(.2126*h[0]+.7152*h[1]+.0722*h[2]),[u,f]=[i,n].map(this.#b.bind(this));d<l&&([l,d,u,f]=[d,l,f,u]),this.#h.style.color="";const g=(m,_,y)=>{const w=new Array(256),v=(d-l)/y,E=m/255,A=(_-m)/(255*y);let T=0;for(let k=0;k<=y;k++){const I=Math.round(l+k*v),L=E+k*A;for(let q=T;q<=I;q++)w[q]=L;T=I+1}for(let k=T;k<256;k++)w[k]=w[T-1];return w.join(",")},p=`g_${this.#i}_hcm_${t}_filter`,b=a.filter=this.#f(p);return this.#m(b),this.#g(g(u[0],f[0],5),g(u[1],f[1],5),g(u[2],f[2],5),b),a.url=this.#l(p),a.url}destroy(t=!1){t&&this.#n?.size||(this.#s?.parentNode.parentNode.remove(),this.#s=null,this.#e?.clear(),this.#e=null,this.#n?.clear(),this.#n=null,this.#a=0)}#p(t){const e=this.#r.createElementNS(Vt,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0"),t.append(e)}#m(t){const e=this.#r.createElementNS(Vt,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),t.append(e)}#f(t){const e=this.#r.createElementNS(Vt,"filter");return e.setAttribute("color-interpolation-filters","sRGB"),e.setAttribute("id",t),this.#h.append(e),e}#d(t,e,s){const i=this.#r.createElementNS(Vt,e);i.setAttribute("type","discrete"),i.setAttribute("tableValues",s),t.append(i)}#g(t,e,s,i){const n=this.#r.createElementNS(Vt,"feComponentTransfer");i.append(n),this.#d(n,"feFuncR",t),this.#d(n,"feFuncG",e),this.#d(n,"feFuncB",s)}#y(t,e){const s=this.#r.createElementNS(Vt,"feComponentTransfer");e.append(s),this.#d(s,"feFuncA",t)}#b(t){return this.#h.style.color=t,rs(getComputedStyle(this.#h).getPropertyValue("color"))}}class Ni{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `standardFontDataUrl` API parameter is provided.");if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch(s=>{throw new Error(`Unable to load font data at: ${e}`)})}async _fetch(t){Q("Abstract method `_fetch` called.")}}class ai extends Ni{async _fetch(t){const e=await Le(t,"arraybuffer");return new Uint8Array(e)}}class Fi{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `wasmUrl` API parameter is provided.");if(!t)throw new Error("Wasm filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch(s=>{throw new Error(`Unable to load wasm data at: ${e}`)})}async _fetch(t){Q("Abstract method `_fetch` called.")}}class oi extends Fi{async _fetch(t){const e=await Le(t,"arraybuffer");return new Uint8Array(e)}}yt&&V("Please use the `legacy` build in Node.js environments.");async function Hs(c){const e=await process.getBuiltinModule("fs").promises.readFile(c);return new Uint8Array(e)}class Bn extends Di{}class $n extends Ii{_createCanvas(t,e){return process.getBuiltinModule("module").createRequire(import.meta.url)("@napi-rs/canvas").createCanvas(t,e)}}class Hn extends Li{async _fetch(t){return Hs(t)}}class zn extends Ni{async _fetch(t){return Hs(t)}}class Vn extends Fi{async _fetch(t){return Hs(t)}}const gt={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};function ks(c,t){if(!t)return;const e=t[2]-t[0],s=t[3]-t[1],i=new Path2D;i.rect(t[0],t[1],e,s),c.clip(i)}class zs{isModifyingCurrentTransform(){return!1}getPattern(){Q("Abstract method `getPattern` called.")}}class Un extends zs{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;this._type==="axial"?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):this._type==="radial"&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const s of this._colorStops)e.addColorStop(s[0],s[1]);return e}getPattern(t,e,s,i){let n;if(i===gt.STROKE||i===gt.FILL){const r=e.current.getClippedPathBoundingBox(i,nt(t))||[0,0,0,0],a=Math.ceil(r[2]-r[0])||1,o=Math.ceil(r[3]-r[1])||1,h=e.cachedCanvases.getCanvas("pattern",a,o),l=h.context;l.clearRect(0,0,l.canvas.width,l.canvas.height),l.beginPath(),l.rect(0,0,l.canvas.width,l.canvas.height),l.translate(-r[0],-r[1]),s=D.transform(s,[1,0,0,1,r[0],r[1]]),l.transform(...e.baseTransform),this.matrix&&l.transform(...this.matrix),ks(l,this._bbox),l.fillStyle=this._createGradient(l),l.fill(),n=t.createPattern(h.canvas,"no-repeat");const d=new DOMMatrix(s);n.setTransform(d)}else ks(t,this._bbox),n=this._createGradient(t);return n}}function vs(c,t,e,s,i,n,r,a){const o=t.coords,h=t.colors,l=c.data,d=c.width*4;let u;o[e+1]>o[s+1]&&(u=e,e=s,s=u,u=n,n=r,r=u),o[s+1]>o[i+1]&&(u=s,s=i,i=u,u=r,r=a,a=u),o[e+1]>o[s+1]&&(u=e,e=s,s=u,u=n,n=r,r=u);const f=(o[e]+t.offsetX)*t.scaleX,g=(o[e+1]+t.offsetY)*t.scaleY,p=(o[s]+t.offsetX)*t.scaleX,b=(o[s+1]+t.offsetY)*t.scaleY,m=(o[i]+t.offsetX)*t.scaleX,_=(o[i+1]+t.offsetY)*t.scaleY;if(g>=_)return;const y=h[n],w=h[n+1],v=h[n+2],E=h[r],A=h[r+1],T=h[r+2],k=h[a],I=h[a+1],L=h[a+2],q=Math.round(g),K=Math.round(_);let G,dt,C,x,U,P,H,J;for(let Y=q;Y<=K;Y++){if(Y<b){const et=Y<g?0:(g-Y)/(g-b);G=f-(f-p)*et,dt=y-(y-E)*et,C=w-(w-A)*et,x=v-(v-T)*et}else{let et;Y>_?et=1:b===_?et=0:et=(b-Y)/(b-_),G=p-(p-m)*et,dt=E-(E-k)*et,C=A-(A-I)*et,x=T-(T-L)*et}let Z;Y<g?Z=0:Y>_?Z=1:Z=(g-Y)/(g-_),U=f-(f-m)*Z,P=y-(y-k)*Z,H=w-(w-I)*Z,J=v-(v-L)*Z;const Gt=Math.round(Math.min(G,U)),It=Math.round(Math.max(G,U));let Ht=d*Y+Gt*4;for(let et=Gt;et<=It;et++)Z=(G-et)/(G-U),Z<0?Z=0:Z>1&&(Z=1),l[Ht++]=dt-(dt-P)*Z|0,l[Ht++]=C-(C-H)*Z|0,l[Ht++]=x-(x-J)*Z|0,l[Ht++]=255}}function jn(c,t,e){const s=t.coords,i=t.colors;let n,r;switch(t.type){case"lattice":const a=t.verticesPerRow,o=Math.floor(s.length/a)-1,h=a-1;for(n=0;n<o;n++){let l=n*a;for(let d=0;d<h;d++,l++)vs(c,e,s[l],s[l+1],s[l+a],i[l],i[l+1],i[l+a]),vs(c,e,s[l+a+1],s[l+1],s[l+a],i[l+a+1],i[l+1],i[l+a])}break;case"triangles":for(n=0,r=s.length;n<r;n+=3)vs(c,e,s[n],s[n+1],s[n+2],i[n],i[n+1],i[n+2]);break;default:throw new Error("illegal figure")}}class Gn extends zs{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[6],this._background=t[7],this.matrix=null}_createMeshCanvas(t,e,s){const a=Math.floor(this._bounds[0]),o=Math.floor(this._bounds[1]),h=Math.ceil(this._bounds[2])-a,l=Math.ceil(this._bounds[3])-o,d=Math.min(Math.ceil(Math.abs(h*t[0]*1.1)),3e3),u=Math.min(Math.ceil(Math.abs(l*t[1]*1.1)),3e3),f=h/d,g=l/u,p={coords:this._coords,colors:this._colors,offsetX:-a,offsetY:-o,scaleX:1/f,scaleY:1/g},b=d+4,m=u+4,_=s.getCanvas("mesh",b,m),y=_.context,w=y.createImageData(d,u);if(e){const E=w.data;for(let A=0,T=E.length;A<T;A+=4)E[A]=e[0],E[A+1]=e[1],E[A+2]=e[2],E[A+3]=255}for(const E of this._figures)jn(w,E,p);return y.putImageData(w,2,2),{canvas:_.canvas,offsetX:a-2*f,offsetY:o-2*g,scaleX:f,scaleY:g}}isModifyingCurrentTransform(){return!0}getPattern(t,e,s,i){ks(t,this._bbox);const n=new Float32Array(2);if(i===gt.SHADING)D.singularValueDecompose2dScale(nt(t),n);else if(this.matrix){D.singularValueDecompose2dScale(this.matrix,n);const[a,o]=n;D.singularValueDecompose2dScale(e.baseTransform,n),n[0]*=a,n[1]*=o}else D.singularValueDecompose2dScale(e.baseTransform,n);const r=this._createMeshCanvas(n,i===gt.SHADING?null:this._background,e.cachedCanvases);return i!==gt.SHADING&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(r.offsetX,r.offsetY),t.scale(r.scaleX,r.scaleY),t.createPattern(r.canvas,"no-repeat")}}class Wn extends zs{getPattern(){return"hotpink"}}function qn(c){switch(c[0]){case"RadialAxial":return new Un(c);case"Mesh":return new Gn(c);case"Dummy":return new Wn}throw new Error(`Unknown IR type: ${c[0]}`)}const li={COLORED:1,UNCOLORED:2};class Vs{static MAX_PATTERN_SIZE=3e3;constructor(t,e,s,i){this.color=t[1],this.operatorList=t[2],this.matrix=t[3],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.ctx=e,this.canvasGraphicsFactory=s,this.baseTransform=i}createPatternCanvas(t){const{bbox:e,operatorList:s,paintType:i,tilingType:n,color:r,canvasGraphicsFactory:a}=this;let{xstep:o,ystep:h}=this;o=Math.abs(o),h=Math.abs(h),is("TilingType: "+n);const l=e[0],d=e[1],u=e[2],f=e[3],g=u-l,p=f-d,b=new Float32Array(2);D.singularValueDecompose2dScale(this.matrix,b);const[m,_]=b;D.singularValueDecompose2dScale(this.baseTransform,b);const y=m*b[0],w=_*b[1];let v=g,E=p,A=!1,T=!1;const k=Math.ceil(o*y),I=Math.ceil(h*w),L=Math.ceil(g*y),q=Math.ceil(p*w);k>=L?v=o:A=!0,I>=q?E=h:T=!0;const K=this.getSizeAndScale(v,this.ctx.canvas.width,y),G=this.getSizeAndScale(E,this.ctx.canvas.height,w),dt=t.cachedCanvases.getCanvas("pattern",K.size,G.size),C=dt.context,x=a.createCanvasGraphics(C);if(x.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(x,i,r),C.translate(-K.scale*l,-G.scale*d),x.transform(K.scale,0,0,G.scale,0,0),C.save(),this.clipBbox(x,l,d,u,f),x.baseTransform=nt(x.ctx),x.executeOperatorList(s),x.endDrawing(),C.restore(),A||T){const U=dt.canvas;A&&(v=o),T&&(E=h);const P=this.getSizeAndScale(v,this.ctx.canvas.width,y),H=this.getSizeAndScale(E,this.ctx.canvas.height,w),J=P.size,Y=H.size,Z=t.cachedCanvases.getCanvas("pattern-workaround",J,Y),Gt=Z.context,It=A?Math.floor(g/o):0,Ht=T?Math.floor(p/h):0;for(let et=0;et<=It;et++)for(let zt=0;zt<=Ht;zt++)Gt.drawImage(U,J*et,Y*zt,J,Y,0,0,J,Y);return{canvas:Z.canvas,scaleX:P.scale,scaleY:H.scale,offsetX:l,offsetY:d}}return{canvas:dt.canvas,scaleX:K.scale,scaleY:G.scale,offsetX:l,offsetY:d}}getSizeAndScale(t,e,s){const i=Math.max(Vs.MAX_PATTERN_SIZE,e);let n=Math.ceil(t*s);return n>=i?n=i:s=n/t,{scale:s,size:n}}clipBbox(t,e,s,i,n){const r=i-e,a=n-s;t.ctx.rect(e,s,r,a),D.axialAlignedBoundingBox([e,s,i,n],nt(t.ctx),t.current.minMax),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,s){const i=t.ctx,n=t.current;switch(e){case li.COLORED:const{fillStyle:r,strokeStyle:a}=this.ctx;i.fillStyle=n.fillColor=r,i.strokeStyle=n.strokeColor=a;break;case li.UNCOLORED:i.fillStyle=i.strokeStyle=s,n.fillColor=n.strokeColor=s;break;default:throw new un(`Unsupported paint type: ${e}`)}}isModifyingCurrentTransform(){return!1}getPattern(t,e,s,i){let n=s;i!==gt.SHADING&&(n=D.transform(n,e.baseTransform),this.matrix&&(n=D.transform(n,this.matrix)));const r=this.createPatternCanvas(e);let a=new DOMMatrix(n);a=a.translate(r.offsetX,r.offsetY),a=a.scale(1/r.scaleX,1/r.scaleY);const o=t.createPattern(r.canvas,"repeat");return o.setTransform(a),o}}function Xn({src:c,srcPos:t=0,dest:e,width:s,height:i,nonBlackColor:n=4294967295,inverseDecode:r=!1}){const a=mt.isLittleEndian?4278190080:255,[o,h]=r?[n,a]:[a,n],l=s>>3,d=s&7,u=c.length;e=new Uint32Array(e.buffer);let f=0;for(let g=0;g<i;g++){for(const b=t+l;t<b;t++){const m=t<u?c[t]:255;e[f++]=m&128?h:o,e[f++]=m&64?h:o,e[f++]=m&32?h:o,e[f++]=m&16?h:o,e[f++]=m&8?h:o,e[f++]=m&4?h:o,e[f++]=m&2?h:o,e[f++]=m&1?h:o}if(d===0)continue;const p=t<u?c[t++]:255;for(let b=0;b<d;b++)e[f++]=p&1<<7-b?h:o}return{srcPos:t,destPos:f}}const hi=16,ci=100,Yn=15,di=10,Et=16,ys=new DOMMatrix,xt=new Float32Array(2),le=new Float32Array([1/0,1/0,-1/0,-1/0]);function Kn(c,t){if(c._removeMirroring)throw new Error("Context is already forwarding operations.");c.__originalSave=c.save,c.__originalRestore=c.restore,c.__originalRotate=c.rotate,c.__originalScale=c.scale,c.__originalTranslate=c.translate,c.__originalTransform=c.transform,c.__originalSetTransform=c.setTransform,c.__originalResetTransform=c.resetTransform,c.__originalClip=c.clip,c.__originalMoveTo=c.moveTo,c.__originalLineTo=c.lineTo,c.__originalBezierCurveTo=c.bezierCurveTo,c.__originalRect=c.rect,c.__originalClosePath=c.closePath,c.__originalBeginPath=c.beginPath,c._removeMirroring=()=>{c.save=c.__originalSave,c.restore=c.__originalRestore,c.rotate=c.__originalRotate,c.scale=c.__originalScale,c.translate=c.__originalTranslate,c.transform=c.__originalTransform,c.setTransform=c.__originalSetTransform,c.resetTransform=c.__originalResetTransform,c.clip=c.__originalClip,c.moveTo=c.__originalMoveTo,c.lineTo=c.__originalLineTo,c.bezierCurveTo=c.__originalBezierCurveTo,c.rect=c.__originalRect,c.closePath=c.__originalClosePath,c.beginPath=c.__originalBeginPath,delete c._removeMirroring},c.save=function(){t.save(),this.__originalSave()},c.restore=function(){t.restore(),this.__originalRestore()},c.translate=function(e,s){t.translate(e,s),this.__originalTranslate(e,s)},c.scale=function(e,s){t.scale(e,s),this.__originalScale(e,s)},c.transform=function(e,s,i,n,r,a){t.transform(e,s,i,n,r,a),this.__originalTransform(e,s,i,n,r,a)},c.setTransform=function(e,s,i,n,r,a){t.setTransform(e,s,i,n,r,a),this.__originalSetTransform(e,s,i,n,r,a)},c.resetTransform=function(){t.resetTransform(),this.__originalResetTransform()},c.rotate=function(e){t.rotate(e),this.__originalRotate(e)},c.clip=function(e){t.clip(e),this.__originalClip(e)},c.moveTo=function(e,s){t.moveTo(e,s),this.__originalMoveTo(e,s)},c.lineTo=function(e,s){t.lineTo(e,s),this.__originalLineTo(e,s)},c.bezierCurveTo=function(e,s,i,n,r,a){t.bezierCurveTo(e,s,i,n,r,a),this.__originalBezierCurveTo(e,s,i,n,r,a)},c.rect=function(e,s,i,n){t.rect(e,s,i,n),this.__originalRect(e,s,i,n)},c.closePath=function(){t.closePath(),this.__originalClosePath()},c.beginPath=function(){t.beginPath(),this.__originalBeginPath()}}class Qn{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,s){let i;return this.cache[t]!==void 0?(i=this.cache[t],this.canvasFactory.reset(i,e,s)):(i=this.canvasFactory.create(e,s),this.cache[t]=i),i}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function Ue(c,t,e,s,i,n,r,a,o,h){const[l,d,u,f,g,p]=nt(c);if(d===0&&u===0){const _=r*l+g,y=Math.round(_),w=a*f+p,v=Math.round(w),E=(r+o)*l+g,A=Math.abs(Math.round(E)-y)||1,T=(a+h)*f+p,k=Math.abs(Math.round(T)-v)||1;return c.setTransform(Math.sign(l),0,0,Math.sign(f),y,v),c.drawImage(t,e,s,i,n,0,0,A,k),c.setTransform(l,d,u,f,g,p),[A,k]}if(l===0&&f===0){const _=a*u+g,y=Math.round(_),w=r*d+p,v=Math.round(w),E=(a+h)*u+g,A=Math.abs(Math.round(E)-y)||1,T=(r+o)*d+p,k=Math.abs(Math.round(T)-v)||1;return c.setTransform(0,Math.sign(d),Math.sign(u),0,y,v),c.drawImage(t,e,s,i,n,0,0,k,A),c.setTransform(l,d,u,f,g,p),[k,A]}c.drawImage(t,e,s,i,n,r,a,o,h);const b=Math.hypot(l,d),m=Math.hypot(u,f);return[b*o,m*h]}class ui{alphaIsShape=!1;fontSize=0;fontSizeScale=1;textMatrix=null;textMatrixScale=1;fontMatrix=_s;leading=0;x=0;y=0;lineX=0;lineY=0;charSpacing=0;wordSpacing=0;textHScale=1;textRenderingMode=bt.FILL;textRise=0;fillColor="#000000";strokeColor="#000000";patternFill=!1;patternStroke=!1;fillAlpha=1;strokeAlpha=1;lineWidth=1;activeSMask=null;transferMaps="none";constructor(t,e){this.clipBox=new Float32Array([0,0,t,e]),this.minMax=le.slice()}clone(){const t=Object.create(this);return t.clipBox=this.clipBox.slice(),t.minMax=this.minMax.slice(),t}getPathBoundingBox(t=gt.FILL,e=null){const s=this.minMax.slice();if(t===gt.STROKE){e||Q("Stroke bounding box must include transform."),D.singularValueDecompose2dScale(e,xt);const i=xt[0]*this.lineWidth/2,n=xt[1]*this.lineWidth/2;s[0]-=i,s[1]-=n,s[2]+=i,s[3]+=n}return s}updateClipFromPath(){const t=D.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minMax[0]===1/0}startNewPathAndClipBox(t){this.clipBox.set(t,0),this.minMax.set(le,0)}getClippedPathBoundingBox(t=gt.FILL,e=null){return D.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function fi(c,t){if(t instanceof ImageData){c.putImageData(t,0,0);return}const e=t.height,s=t.width,i=e%Et,n=(e-i)/Et,r=i===0?n:n+1,a=c.createImageData(s,Et);let o=0,h;const l=t.data,d=a.data;let u,f,g,p;if(t.kind===qe.GRAYSCALE_1BPP){const b=l.byteLength,m=new Uint32Array(d.buffer,0,d.byteLength>>2),_=m.length,y=s+7>>3,w=4294967295,v=mt.isLittleEndian?4278190080:255;for(u=0;u<r;u++){for(g=u<n?Et:i,h=0,f=0;f<g;f++){const E=b-o;let A=0;const T=E>y?s:E*8-7,k=T&-8;let I=0,L=0;for(;A<k;A+=8)L=l[o++],m[h++]=L&128?w:v,m[h++]=L&64?w:v,m[h++]=L&32?w:v,m[h++]=L&16?w:v,m[h++]=L&8?w:v,m[h++]=L&4?w:v,m[h++]=L&2?w:v,m[h++]=L&1?w:v;for(;A<T;A++)I===0&&(L=l[o++],I=128),m[h++]=L&I?w:v,I>>=1}for(;h<_;)m[h++]=0;c.putImageData(a,0,u*Et)}}else if(t.kind===qe.RGBA_32BPP){for(f=0,p=s*Et*4,u=0;u<n;u++)d.set(l.subarray(o,o+p)),o+=p,c.putImageData(a,0,f),f+=Et;u<r&&(p=s*i*4,d.set(l.subarray(o,o+p)),c.putImageData(a,0,f))}else if(t.kind===qe.RGB_24BPP)for(g=Et,p=s*g,u=0;u<r;u++){for(u>=n&&(g=i,p=s*g),h=0,f=p;f--;)d[h++]=l[o++],d[h++]=l[o++],d[h++]=l[o++],d[h++]=255;c.putImageData(a,0,u*Et)}else throw new Error(`bad image kind: ${t.kind}`)}function pi(c,t){if(t.bitmap){c.drawImage(t.bitmap,0,0);return}const e=t.height,s=t.width,i=e%Et,n=(e-i)/Et,r=i===0?n:n+1,a=c.createImageData(s,Et);let o=0;const h=t.data,l=a.data;for(let d=0;d<r;d++){const u=d<n?Et:i;({srcPos:o}=Xn({src:h,srcPos:o,dest:l,width:s,height:u,nonBlackColor:0})),c.putImageData(a,0,d*Et)}}function ve(c,t){const e=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of e)c[s]!==void 0&&(t[s]=c[s]);c.setLineDash!==void 0&&(t.setLineDash(c.getLineDash()),t.lineDashOffset=c.lineDashOffset)}function je(c){c.strokeStyle=c.fillStyle="#000000",c.fillRule="nonzero",c.globalAlpha=1,c.lineWidth=1,c.lineCap="butt",c.lineJoin="miter",c.miterLimit=10,c.globalCompositeOperation="source-over",c.font="10px sans-serif",c.setLineDash!==void 0&&(c.setLineDash([]),c.lineDashOffset=0);const{filter:t}=c;t!=="none"&&t!==""&&(c.filter="none")}function gi(c,t){if(t)return!0;D.singularValueDecompose2dScale(c,xt);const e=Math.fround($t.pixelRatio*ge.PDF_TO_CSS_UNITS);return xt[0]<=e&&xt[1]<=e}const Zn=["butt","round","square"],Jn=["miter","round","bevel"],tr={},mi={};class ue{constructor(t,e,s,i,n,{optionalContentConfig:r,markedContentStack:a=null},o,h){this.ctx=t,this.current=new ui(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=s,this.canvasFactory=i,this.filterFactory=n,this.groupStack=[],this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=a||[],this.optionalContentConfig=r,this.cachedCanvases=new Qn(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=o,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=h,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t,e=null){return typeof t=="string"?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:s=!1,background:i=null}){const n=this.ctx.canvas.width,r=this.ctx.canvas.height,a=this.ctx.fillStyle;if(this.ctx.fillStyle=i||"#ffffff",this.ctx.fillRect(0,0,n,r),this.ctx.fillStyle=a,s){const o=this.cachedCanvases.getCanvas("transparent",n,r);this.compositeCtx=this.ctx,this.transparentCanvas=o.canvas,this.ctx=o.context,this.ctx.save(),this.ctx.transform(...nt(this.compositeCtx))}this.ctx.save(),je(this.ctx),t&&(this.ctx.transform(...t),this.outputScaleX=t[0],this.outputScaleY=t[0]),this.ctx.transform(...e.transform),this.viewportScale=e.scale,this.baseTransform=nt(this.ctx)}executeOperatorList(t,e,s,i){const n=t.argsArray,r=t.fnArray;let a=e||0;const o=n.length;if(o===a)return a;const h=o-a>di&&typeof s=="function",l=h?Date.now()+Yn:0;let d=0;const u=this.commonObjs,f=this.objs;let g;for(;;){if(i!==void 0&&a===i.nextBreakPoint)return i.breakIt(a,s),a;if(g=r[a],g!==Qe.dependency)this[g].apply(this,n[a]);else for(const p of n[a]){const b=p.startsWith("g_")?u:f;if(!b.has(p))return b.get(p,s),a}if(a++,a===o)return a;if(h&&++d>di){if(Date.now()>l)return s(),a;d=0}}}#t(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.current.activeSMask=null,this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)}endDrawing(){this.#t(),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())typeof HTMLCanvasElement<"u"&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),this.#e()}#e(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if(t!=="none"){const e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}}_scaleImage(t,e){const s=t.width??t.displayWidth,i=t.height??t.displayHeight;let n=Math.max(Math.hypot(e[0],e[1]),1),r=Math.max(Math.hypot(e[2],e[3]),1),a=s,o=i,h="prescale1",l,d;for(;n>2&&a>1||r>2&&o>1;){let u=a,f=o;n>2&&a>1&&(u=a>=16384?Math.floor(a/2)-1||1:Math.ceil(a/2),n/=a/u),r>2&&o>1&&(f=o>=16384?Math.floor(o/2)-1||1:Math.ceil(o)/2,r/=o/f),l=this.cachedCanvases.getCanvas(h,u,f),d=l.context,d.clearRect(0,0,u,f),d.drawImage(t,0,0,a,o,0,0,u,f),t=l.canvas,a=u,o=f,h=h==="prescale1"?"prescale2":"prescale1"}return{img:t,paintWidth:a,paintHeight:o}}_createMaskCanvas(t){const e=this.ctx,{width:s,height:i}=t,n=this.current.fillColor,r=this.current.patternFill,a=nt(e);let o,h,l,d;if((t.bitmap||t.data)&&t.count>1){const k=t.bitmap||t.data.buffer;h=JSON.stringify(r?a:[a.slice(0,4),n]),o=this._cachedBitmapsMap.get(k),o||(o=new Map,this._cachedBitmapsMap.set(k,o));const I=o.get(h);if(I&&!r){const L=Math.round(Math.min(a[0],a[2])+a[4]),q=Math.round(Math.min(a[1],a[3])+a[5]);return{canvas:I,offsetX:L,offsetY:q}}l=I}l||(d=this.cachedCanvases.getCanvas("maskCanvas",s,i),pi(d.context,t));let u=D.transform(a,[1/s,0,0,-1/i,0,0]);u=D.transform(u,[1,0,0,1,0,-i]);const f=le.slice();D.axialAlignedBoundingBox([0,0,s,i],u,f);const[g,p,b,m]=f,_=Math.round(b-g)||1,y=Math.round(m-p)||1,w=this.cachedCanvases.getCanvas("fillCanvas",_,y),v=w.context,E=g,A=p;v.translate(-E,-A),v.transform(...u),l||(l=this._scaleImage(d.canvas,Nt(v)),l=l.img,o&&r&&o.set(h,l)),v.imageSmoothingEnabled=gi(nt(v),t.interpolate),Ue(v,l,0,0,l.width,l.height,0,0,s,i),v.globalCompositeOperation="source-in";const T=D.transform(Nt(v),[1,0,0,1,-E,-A]);return v.fillStyle=r?n.getPattern(e,this,T,gt.FILL):n,v.fillRect(0,0,s,i),o&&!r&&(this.cachedCanvases.delete("fillCanvas"),o.set(h,w.canvas)),{canvas:w.canvas,offsetX:Math.round(E),offsetY:Math.round(A)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=Zn[t]}setLineJoin(t){this.ctx.lineJoin=Jn[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const s=this.ctx;s.setLineDash!==void 0&&(s.setLineDash(t),s.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,s]of t)switch(e){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s[0],s[1]);break;case"CA":this.current.strokeAlpha=s;break;case"ca":this.ctx.globalAlpha=this.current.fillAlpha=s;break;case"BM":this.ctx.globalCompositeOperation=s;break;case"SMask":this.current.activeSMask=s?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(s);break}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,s="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(s,t,e);this.suspendedCtx=this.ctx;const n=this.ctx=i.context;n.setTransform(this.suspendedCtx.getTransform()),ve(this.suspendedCtx,n),Kn(n,this.suspendedCtx),this.setGState([["BM","source-over"]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),ve(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,s=this.suspendedCtx;this.composeSMask(s,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}composeSMask(t,e,s,i){const n=i[0],r=i[1],a=i[2]-n,o=i[3]-r;a===0||o===0||(this.genericComposeSMask(e.context,s,a,o,e.subtype,e.backdrop,e.transferMap,n,r,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(s.canvas,0,0),t.restore())}genericComposeSMask(t,e,s,i,n,r,a,o,h,l,d){let u=t.canvas,f=o-l,g=h-d;if(r)if(f<0||g<0||f+s>u.width||g+i>u.height){const b=this.cachedCanvases.getCanvas("maskExtension",s,i),m=b.context;m.drawImage(u,-f,-g),m.globalCompositeOperation="destination-atop",m.fillStyle=r,m.fillRect(0,0,s,i),m.globalCompositeOperation="source-over",u=b.canvas,f=g=0}else{t.save(),t.globalAlpha=1,t.setTransform(1,0,0,1,0,0);const b=new Path2D;b.rect(f,g,s,i),t.clip(b),t.globalCompositeOperation="destination-atop",t.fillStyle=r,t.fillRect(f,g,s,i),t.restore()}e.save(),e.globalAlpha=1,e.setTransform(1,0,0,1,0,0),n==="Alpha"&&a?e.filter=this.filterFactory.addAlphaFilter(a):n==="Luminosity"&&(e.filter=this.filterFactory.addLuminosityFilter(a));const p=new Path2D;p.rect(o,h,s,i),e.clip(p),e.globalCompositeOperation="destination-in",e.drawImage(u,f,g,s,i,o,h,s,i),e.restore()}save(){this.inSMaskMode&&ve(this.ctx,this.suspendedCtx),this.ctx.save();const t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){if(this.stateStack.length===0){this.inSMaskMode&&this.endSMaskMode();return}this.current=this.stateStack.pop(),this.ctx.restore(),this.inSMaskMode&&ve(this.suspendedCtx,this.ctx),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}transform(t,e,s,i,n,r){this.ctx.transform(t,e,s,i,n,r),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,s){let[i]=e;if(!s){i||=e[0]=new Path2D,this[t](i);return}if(!(i instanceof Path2D)){const n=e[0]=new Path2D;for(let r=0,a=i.length;r<a;)switch(i[r++]){case $e.moveTo:n.moveTo(i[r++],i[r++]);break;case $e.lineTo:n.lineTo(i[r++],i[r++]);break;case $e.curveTo:n.bezierCurveTo(i[r++],i[r++],i[r++],i[r++],i[r++],i[r++]);break;case $e.closePath:n.closePath();break;default:V(`Unrecognized drawing path operator: ${i[r-1]}`);break}i=n}D.axialAlignedBoundingBox(s,nt(this.ctx),this.current.minMax),this[t](i)}closePath(){this.ctx.closePath()}stroke(t,e=!0){const s=this.ctx,i=this.current.strokeColor;if(s.globalAlpha=this.current.strokeAlpha,this.contentVisible)if(typeof i=="object"&&i?.getPattern){const n=i.isModifyingCurrentTransform()?s.getTransform():null;if(s.save(),s.strokeStyle=i.getPattern(s,this,Nt(s),gt.STROKE),n){const r=new Path2D;r.addPath(t,s.getTransform().invertSelf().multiplySelf(n)),t=r}this.rescaleAndStroke(t,!1),s.restore()}else this.rescaleAndStroke(t,!0);e&&this.consumePath(t,this.current.getClippedPathBoundingBox(gt.STROKE,nt(this.ctx))),s.globalAlpha=this.current.fillAlpha}closeStroke(t){this.stroke(t)}fill(t,e=!0){const s=this.ctx,i=this.current.fillColor,n=this.current.patternFill;let r=!1;if(n){const o=i.isModifyingCurrentTransform()?s.getTransform():null;if(s.save(),s.fillStyle=i.getPattern(s,this,Nt(s),gt.FILL),o){const h=new Path2D;h.addPath(t,s.getTransform().invertSelf().multiplySelf(o)),t=h}r=!0}const a=this.current.getClippedPathBoundingBox();this.contentVisible&&a!==null&&(this.pendingEOFill?(s.fill(t,"evenodd"),this.pendingEOFill=!1):s.fill(t)),r&&s.restore(),e&&this.consumePath(t,a)}eoFill(t){this.pendingEOFill=!0,this.fill(t)}fillStroke(t){this.fill(t,!1),this.stroke(t,!1),this.consumePath(t)}eoFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}closeFillStroke(t){this.fillStroke(t)}closeEOFillStroke(t){this.pendingEOFill=!0,this.fillStroke(t)}endPath(t){this.consumePath(t)}rawFillPath(t){this.ctx.fill(t)}clip(){this.pendingClip=tr}eoClip(){this.pendingClip=mi}beginText(){this.current.textMatrix=null,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(t===void 0)return;const s=new Path2D,i=e.getTransform().invertSelf();for(const{transform:n,x:r,y:a,fontSize:o,path:h}of t)h&&s.addPath(h,new DOMMatrix(n).preMultiplySelf(i).translate(r,a).scale(o,-o));e.clip(s),delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const s=this.commonObjs.get(t),i=this.current;if(!s)throw new Error(`Can't find font for ${t}`);if(i.fontMatrix=s.fontMatrix||_s,(i.fontMatrix[0]===0||i.fontMatrix[3]===0)&&V("Invalid font matrix for font "+t),e<0?(e=-e,i.fontDirection=-1):i.fontDirection=1,this.current.font=s,this.current.fontSize=e,s.isType3Font)return;const n=s.loadedName||"sans-serif",r=s.systemFontInfo?.css||`"${n}", ${s.fallbackName}`;let a="normal";s.black?a="900":s.bold&&(a="bold");const o=s.italic?"italic":"normal";let h=e;e<hi?h=hi:e>ci&&(h=ci),this.current.fontSizeScale=e/h,this.ctx.font=`${o} ${a} ${h}px ${r}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t){const{current:e}=this;e.textMatrix=t,e.textMatrixScale=Math.hypot(t[0],t[1]),e.x=e.lineX=0,e.y=e.lineY=0}nextLine(){this.moveText(0,this.current.leading)}#s(t,e,s){const i=new Path2D;return i.addPath(t,new DOMMatrix(s).invertSelf().multiplySelf(e)),i}paintChar(t,e,s,i,n){const r=this.ctx,a=this.current,o=a.font,h=a.textRenderingMode,l=a.fontSize/a.fontSizeScale,d=h&bt.FILL_STROKE_MASK,u=!!(h&bt.ADD_TO_PATH_FLAG),f=a.patternFill&&!o.missingFile,g=a.patternStroke&&!o.missingFile;let p;if((o.disableFontFace||u||f||g)&&!o.missingFile&&(p=o.getPathGenerator(this.commonObjs,t)),p&&(o.disableFontFace||f||g)){r.save(),r.translate(e,s),r.scale(l,-l);let b;if((d===bt.FILL||d===bt.FILL_STROKE)&&(i?(b=r.getTransform(),r.setTransform(...i),r.fill(this.#s(p,b,i))):r.fill(p)),d===bt.STROKE||d===bt.FILL_STROKE)if(n){b||=r.getTransform(),r.setTransform(...n);const{a:m,b:_,c:y,d:w}=b,v=D.inverseTransform(n),E=D.transform([m,_,y,w,0,0],v);D.singularValueDecompose2dScale(E,xt),r.lineWidth*=Math.max(xt[0],xt[1])/l,r.stroke(this.#s(p,b,n))}else r.lineWidth/=l,r.stroke(p);r.restore()}else(d===bt.FILL||d===bt.FILL_STROKE)&&r.fillText(t,e,s),(d===bt.STROKE||d===bt.FILL_STROKE)&&r.strokeText(t,e,s);u&&(this.pendingTextPaths||=[]).push({transform:nt(r),x:e,y:s,fontSize:l,path:p})}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let s=!1;for(let i=3;i<e.length;i+=4)if(e[i]>0&&e[i]<255){s=!0;break}return j(this,"isFontSubpixelAAEnabled",s)}showText(t){const e=this.current,s=e.font;if(s.isType3Font)return this.showType3Text(t);const i=e.fontSize;if(i===0)return;const n=this.ctx,r=e.fontSizeScale,a=e.charSpacing,o=e.wordSpacing,h=e.fontDirection,l=e.textHScale*h,d=t.length,u=s.vertical,f=u?1:-1,g=s.defaultVMetrics,p=i*e.fontMatrix[0],b=e.textRenderingMode===bt.FILL&&!s.disableFontFace&&!e.patternFill;n.save(),e.textMatrix&&n.transform(...e.textMatrix),n.translate(e.x,e.y+e.textRise),h>0?n.scale(l,-1):n.scale(l,1);let m,_;if(e.patternFill){n.save();const A=e.fillColor.getPattern(n,this,Nt(n),gt.FILL);m=nt(n),n.restore(),n.fillStyle=A}if(e.patternStroke){n.save();const A=e.strokeColor.getPattern(n,this,Nt(n),gt.STROKE);_=nt(n),n.restore(),n.strokeStyle=A}let y=e.lineWidth;const w=e.textMatrixScale;if(w===0||y===0){const A=e.textRenderingMode&bt.FILL_STROKE_MASK;(A===bt.STROKE||A===bt.FILL_STROKE)&&(y=this.getSinglePixelWidth())}else y/=w;if(r!==1&&(n.scale(r,r),y/=r),n.lineWidth=y,s.isInvalidPDFjsFont){const A=[];let T=0;for(const k of t)A.push(k.unicode),T+=k.width;n.fillText(A.join(""),0,0),e.x+=T*p*l,n.restore(),this.compose();return}let v=0,E;for(E=0;E<d;++E){const A=t[E];if(typeof A=="number"){v+=f*A*i/1e3;continue}let T=!1;const k=(A.isSpace?o:0)+a,I=A.fontChar,L=A.accent;let q,K,G=A.width;if(u){const C=A.vmetric||g,x=-(A.vmetric?C[1]:G*.5)*p,U=C[2]*p;G=C?-C[0]:G,q=x/r,K=(v+U)/r}else q=v/r,K=0;if(s.remeasure&&G>0){const C=n.measureText(I).width*1e3/i*r;if(G<C&&this.isFontSubpixelAAEnabled){const x=G/C;T=!0,n.save(),n.scale(x,1),q/=x}else G!==C&&(q+=(G-C)/2e3*i/r)}if(this.contentVisible&&(A.isInFont||s.missingFile)){if(b&&!L)n.fillText(I,q,K);else if(this.paintChar(I,q,K,m,_),L){const C=q+i*L.offset.x/r,x=K-i*L.offset.y/r;this.paintChar(L.fontChar,C,x,m,_)}}const dt=u?G*p-k*h:G*p+k*h;v+=dt,T&&n.restore()}u?e.y-=v:e.x+=v*l,n.restore(),this.compose()}showType3Text(t){const e=this.ctx,s=this.current,i=s.font,n=s.fontSize,r=s.fontDirection,a=i.vertical?1:-1,o=s.charSpacing,h=s.wordSpacing,l=s.textHScale*r,d=s.fontMatrix||_s,u=t.length,f=s.textRenderingMode===bt.INVISIBLE;let g,p,b,m;if(!(f||n===0)){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,e.save(),s.textMatrix&&e.transform(...s.textMatrix),e.translate(s.x,s.y+s.textRise),e.scale(l,r),g=0;g<u;++g){if(p=t[g],typeof p=="number"){m=a*p*n/1e3,this.ctx.translate(m,0),s.x+=m*l;continue}const _=(p.isSpace?h:0)+o,y=i.charProcOperatorList[p.operatorListId];y?this.contentVisible&&(this.save(),e.scale(n,n),e.transform(...d),this.executeOperatorList(y),this.restore()):V(`Type3 character "${p.operatorListId}" is not available.`);const w=[p.width,0];D.applyTransform(w,d),b=w[0]*n+_,e.translate(b,0),s.x+=b*l}e.restore()}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,s,i,n,r){const a=new Path2D;a.rect(s,i,n-s,r-i),this.ctx.clip(a),this.endPath()}getColorN_Pattern(t){let e;if(t[0]==="TilingPattern"){const s=this.baseTransform||nt(this.ctx),i={createCanvasGraphics:n=>new ue(n,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new Vs(t,this.ctx,i,s)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments),this.current.patternStroke=!0}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t){this.ctx.strokeStyle=this.current.strokeColor=t,this.current.patternStroke=!1}setStrokeTransparent(){this.ctx.strokeStyle=this.current.strokeColor="transparent",this.current.patternStroke=!1}setFillRGBColor(t){this.ctx.fillStyle=this.current.fillColor=t,this.current.patternFill=!1}setFillTransparent(){this.ctx.fillStyle=this.current.fillColor="transparent",this.current.patternFill=!1}_getPattern(t,e=null){let s;return this.cachedPatterns.has(t)?s=this.cachedPatterns.get(t):(s=qn(this.getObject(t)),this.cachedPatterns.set(t,s)),e&&(s.matrix=e),s}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const s=this._getPattern(t);e.fillStyle=s.getPattern(e,this,Nt(e),gt.SHADING);const i=Nt(e);if(i){const{width:n,height:r}=e.canvas,a=le.slice();D.axialAlignedBoundingBox([0,0,n,r],i,a);const[o,h,l,d]=a;this.ctx.fillRect(o,h,l-o,d-h)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){Q("Should not call beginInlineImage")}beginImageData(){Q("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),t&&this.transform(...t),this.baseTransform=nt(this.ctx),e)){D.axialAlignedBoundingBox(e,this.baseTransform,this.current.minMax);const[s,i,n,r]=e,a=new Path2D;a.rect(s,i,n-s,r-i),this.ctx.clip(a),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const e=this.ctx;t.isolated||is("TODO: Support non-isolated groups."),t.knockout&&V("Knockout groups not supported.");const s=nt(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw new Error("Bounding box is required.");let i=le.slice();D.axialAlignedBoundingBox(t.bbox,nt(e),i);const n=[0,0,e.canvas.width,e.canvas.height];i=D.intersect(i,n)||[0,0,0,0];const r=Math.floor(i[0]),a=Math.floor(i[1]),o=Math.max(Math.ceil(i[2])-r,1),h=Math.max(Math.ceil(i[3])-a,1);this.current.startNewPathAndClipBox([0,0,o,h]);let l="groupAt"+this.groupLevel;t.smask&&(l+="_smask_"+this.smaskCounter++%2);const d=this.cachedCanvases.getCanvas(l,o,h),u=d.context;u.translate(-r,-a),u.transform(...s);let f=new Path2D;const[g,p,b,m]=t.bbox;if(f.rect(g,p,b-g,m-p),t.matrix){const _=new Path2D;_.addPath(f,new DOMMatrix(t.matrix)),f=_}u.clip(f),t.smask?this.smaskStack.push({canvas:d.canvas,context:u,offsetX:r,offsetY:a,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(r,a),e.save()),ve(e,u),this.ctx=u,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,s=this.groupStack.pop();if(this.ctx=s,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const i=nt(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...i);const n=le.slice();D.axialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],i,n),this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(n)}}beginAnnotation(t,e,s,i,n){if(this.#t(),je(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),e){const r=e[2]-e[0],a=e[3]-e[1];if(n&&this.annotationCanvasMap){s=s.slice(),s[4]-=e[0],s[5]-=e[1],e=e.slice(),e[0]=e[1]=0,e[2]=r,e[3]=a,D.singularValueDecompose2dScale(nt(this.ctx),xt);const{viewportScale:o}=this,h=Math.ceil(r*this.outputScaleX*o),l=Math.ceil(a*this.outputScaleY*o);this.annotationCanvas=this.canvasFactory.create(h,l);const{canvas:d,context:u}=this.annotationCanvas;this.annotationCanvasMap.set(t,d),this.annotationCanvas.savedCtx=this.ctx,this.ctx=u,this.ctx.save(),this.ctx.setTransform(xt[0],0,0,-xt[1],0,a*xt[1]),je(this.ctx)}else{je(this.ctx),this.endPath();const o=new Path2D;o.rect(e[0],e[1],r,a),this.ctx.clip(o)}}this.current=new ui(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...s),this.transform(...i)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),this.#e(),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;t=this.getObject(t.data,t),t.count=e;const s=this.ctx,i=this._createMaskCanvas(t),n=i.canvas;s.save(),s.setTransform(1,0,0,1,0,0),s.drawImage(n,i.offsetX,i.offsetY),s.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e,s=0,i=0,n,r){if(!this.contentVisible)return;t=this.getObject(t.data,t);const a=this.ctx;a.save();const o=nt(a);a.transform(e,s,i,n,0,0);const h=this._createMaskCanvas(t);a.setTransform(1,0,0,1,h.offsetX-o[4],h.offsetY-o[5]);for(let l=0,d=r.length;l<d;l+=2){const u=D.transform(o,[e,s,i,n,r[l],r[l+1]]);a.drawImage(h.canvas,u[4],u[5])}a.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,s=this.current.fillColor,i=this.current.patternFill;for(const n of t){const{data:r,width:a,height:o,transform:h}=n,l=this.cachedCanvases.getCanvas("maskCanvas",a,o),d=l.context;d.save();const u=this.getObject(r,n);pi(d,u),d.globalCompositeOperation="source-in",d.fillStyle=i?s.getPattern(d,this,Nt(e),gt.FILL):s,d.fillRect(0,0,a,o),d.restore(),e.save(),e.transform(...h),e.scale(1,-1),Ue(e,l.canvas,0,0,a,o,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);if(!e){V("Dependent image isn't ready yet");return}this.paintInlineImageXObject(e)}paintImageXObjectRepeat(t,e,s,i){if(!this.contentVisible)return;const n=this.getObject(t);if(!n){V("Dependent image isn't ready yet");return}const r=n.width,a=n.height,o=[];for(let h=0,l=i.length;h<l;h+=2)o.push({transform:[e,0,0,s,i[h],i[h+1]],x:0,y:0,w:r,h:a});this.paintInlineImageXObjectGroup(n,o)}applyTransferMapsToCanvas(t){return this.current.transferMaps!=="none"&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if(this.current.transferMaps==="none")return t.bitmap;const{bitmap:e,width:s,height:i}=t,n=this.cachedCanvases.getCanvas("inlineImage",s,i),r=n.context;return r.filter=this.current.transferMaps,r.drawImage(e,0,0),r.filter="none",n.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,s=t.height,i=this.ctx;this.save();const{filter:n}=i;n!=="none"&&n!==""&&(i.filter="none"),i.scale(1/e,-1/s);let r;if(t.bitmap)r=this.applyTransferMapsToBitmap(t);else if(typeof HTMLElement=="function"&&t instanceof HTMLElement||!t.data)r=t;else{const h=this.cachedCanvases.getCanvas("inlineImage",e,s).context;fi(h,t),r=this.applyTransferMapsToCanvas(h)}const a=this._scaleImage(r,Nt(i));i.imageSmoothingEnabled=gi(nt(i),t.interpolate),Ue(i,a.img,0,0,a.paintWidth,a.paintHeight,0,-s,e,s),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const s=this.ctx;let i;if(t.bitmap)i=t.bitmap;else{const n=t.width,r=t.height,o=this.cachedCanvases.getCanvas("inlineImage",n,r).context;fi(o,t),i=this.applyTransferMapsToCanvas(o)}for(const n of e)s.save(),s.transform(...n.transform),s.scale(1,-1),Ue(s,i,n.x,n.y,n.w,n.h,0,-1,1,1),s.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){t==="OC"?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t,e){const s=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(e);const i=this.ctx;this.pendingClip&&(s||(this.pendingClip===mi?i.clip(t,"evenodd"):i.clip(t)),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox)}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=nt(this.ctx);if(t[1]===0&&t[2]===0)this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),s=Math.hypot(t[0],t[2]),i=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(s,i)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(this._cachedScaleForStroking[0]===-1){const{lineWidth:t}=this.current,{a:e,b:s,c:i,d:n}=this.ctx.getTransform();let r,a;if(s===0&&i===0){const o=Math.abs(e),h=Math.abs(n);if(o===h)if(t===0)r=a=1/o;else{const l=o*t;r=a=l<1?1/l:1}else if(t===0)r=1/o,a=1/h;else{const l=o*t,d=h*t;r=l<1?1/l:1,a=d<1?1/d:1}}else{const o=Math.abs(e*n-s*i),h=Math.hypot(e,s),l=Math.hypot(i,n);if(t===0)r=l/o,a=h/o;else{const d=t*o;r=l>d?l/d:1,a=h>d?h/d:1}}this._cachedScaleForStroking[0]=r,this._cachedScaleForStroking[1]=a}return this._cachedScaleForStroking}rescaleAndStroke(t,e){const{ctx:s,current:{lineWidth:i}}=this,[n,r]=this.getScaleForStroking();if(n===r){s.lineWidth=(i||1)*n,s.stroke(t);return}const a=s.getLineDash();e&&s.save(),s.scale(n,r),ys.a=1/n,ys.d=1/r;const o=new Path2D;if(o.addPath(t,ys),a.length>0){const h=Math.max(n,r);s.setLineDash(a.map(l=>l/h)),s.lineDashOffset/=h}s.lineWidth=i||1,s.stroke(o),e&&s.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}for(const c in Qe)ue.prototype[c]!==void 0&&(ue.prototype[Qe[c]]=ue.prototype[c]);class fe{static#t=null;static#e="";static get workerPort(){return this.#t}static set workerPort(t){if(!(typeof Worker<"u"&&t instanceof Worker)&&t!==null)throw new Error("Invalid `workerPort` type.");this.#t=t}static get workerSrc(){return this.#e}static set workerSrc(t){if(typeof t!="string")throw new Error("Invalid `workerSrc` type.");this.#e=t}}class er{#t;#e;constructor({parsedData:t,rawData:e}){this.#t=t,this.#e=e}getRaw(){return this.#e}get(t){return this.#t.get(t)??null}[Symbol.iterator](){return this.#t.entries()}}const ae=Symbol("INTERNAL");class sr{#t=!1;#e=!1;#s=!1;#i=!0;constructor(t,{name:e,intent:s,usage:i,rbGroups:n}){this.#t=!!(t&Ct.DISPLAY),this.#e=!!(t&Ct.PRINT),this.name=e,this.intent=s,this.usage=i,this.rbGroups=n}get visible(){if(this.#s)return this.#i;if(!this.#i)return!1;const{print:t,view:e}=this.usage;return this.#t?e?.viewState!=="OFF":this.#e?t?.printState!=="OFF":!0}_setVisible(t,e,s=!1){t!==ae&&Q("Internal method `_setVisible` called."),this.#s=s,this.#i=e}}class ir{#t=null;#e=new Map;#s=null;#i=null;constructor(t,e=Ct.DISPLAY){if(this.renderingIntent=e,this.name=null,this.creator=null,t!==null){this.name=t.name,this.creator=t.creator,this.#i=t.order;for(const s of t.groups)this.#e.set(s.id,new sr(e,s));if(t.baseState==="OFF")for(const s of this.#e.values())s._setVisible(ae,!1);for(const s of t.on)this.#e.get(s)._setVisible(ae,!0);for(const s of t.off)this.#e.get(s)._setVisible(ae,!1);this.#s=this.getHash()}}#r(t){const e=t.length;if(e<2)return!0;const s=t[0];for(let i=1;i<e;i++){const n=t[i];let r;if(Array.isArray(n))r=this.#r(n);else if(this.#e.has(n))r=this.#e.get(n).visible;else return V(`Optional content group not found: ${n}`),!0;switch(s){case"And":if(!r)return!1;break;case"Or":if(r)return!0;break;case"Not":return!r;default:return!0}}return s==="And"}isVisible(t){if(this.#e.size===0)return!0;if(!t)return is("Optional content group not defined."),!0;if(t.type==="OCG")return this.#e.has(t.id)?this.#e.get(t.id).visible:(V(`Optional content group not found: ${t.id}`),!0);if(t.type==="OCMD"){if(t.expression)return this.#r(t.expression);if(!t.policy||t.policy==="AnyOn"){for(const e of t.ids){if(!this.#e.has(e))return V(`Optional content group not found: ${e}`),!0;if(this.#e.get(e).visible)return!0}return!1}else if(t.policy==="AllOn"){for(const e of t.ids){if(!this.#e.has(e))return V(`Optional content group not found: ${e}`),!0;if(!this.#e.get(e).visible)return!1}return!0}else if(t.policy==="AnyOff"){for(const e of t.ids){if(!this.#e.has(e))return V(`Optional content group not found: ${e}`),!0;if(!this.#e.get(e).visible)return!0}return!1}else if(t.policy==="AllOff"){for(const e of t.ids){if(!this.#e.has(e))return V(`Optional content group not found: ${e}`),!0;if(this.#e.get(e).visible)return!1}return!0}return V(`Unknown optional content policy ${t.policy}.`),!0}return V(`Unknown group type ${t.type}.`),!0}setVisibility(t,e=!0,s=!0){const i=this.#e.get(t);if(!i){V(`Optional content group not found: ${t}`);return}if(s&&e&&i.rbGroups.length)for(const n of i.rbGroups)for(const r of n)r!==t&&this.#e.get(r)?._setVisible(ae,!1,!0);i._setVisible(ae,!!e,!0),this.#t=null}setOCGState({state:t,preserveRB:e}){let s;for(const i of t){switch(i){case"ON":case"OFF":case"Toggle":s=i;continue}const n=this.#e.get(i);if(n)switch(s){case"ON":this.setVisibility(i,!0,e);break;case"OFF":this.setVisibility(i,!1,e);break;case"Toggle":this.setVisibility(i,!n.visible,e);break}}this.#t=null}get hasInitialVisibility(){return this.#s===null||this.getHash()===this.#s}getOrder(){return this.#e.size?this.#i?this.#i.slice():[...this.#e.keys()]:null}getGroup(t){return this.#e.get(t)||null}getHash(){if(this.#t!==null)return this.#t;const t=new Ri;for(const[e,s]of this.#e)t.update(`${e}:${s.visible}`);return this.#t=t.hexdigest()}[Symbol.iterator](){return this.#e.entries()}}class nr{constructor(t,{disableRange:e=!1,disableStream:s=!1}){ct(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:i,initialData:n,progressiveDone:r,contentDispositionFilename:a}=t;if(this._queuedChunks=[],this._progressiveDone=r,this._contentDispositionFilename=a,n?.length>0){const o=n instanceof Uint8Array&&n.byteLength===n.buffer.byteLength?n.buffer:new Uint8Array(n).buffer;this._queuedChunks.push(o)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!s,this._isRangeSupported=!e,this._contentLength=i,this._fullRequestReader=null,this._rangeReaders=[],t.addRangeListener((o,h)=>{this._onReceiveData({begin:o,chunk:h})}),t.addProgressListener((o,h)=>{this._onProgress({loaded:o,total:h})}),t.addProgressiveReadListener(o=>{this._onReceiveData({chunk:o})}),t.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),t.transportReady()}_onReceiveData({begin:t,chunk:e}){const s=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(t===void 0)this._fullRequestReader?this._fullRequestReader._enqueue(s):this._queuedChunks.push(s);else{const i=this._rangeReaders.some(function(n){return n._begin!==t?!1:(n._enqueue(s),!0)});ct(i,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){t.total===void 0?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){ct(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;return this._queuedChunks=null,new rr(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new ar(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}}class rr{constructor(t,e,s=!1,i=null){this._stream=t,this._done=s||!1,this._filename=Fs(i)?i:null,this._queuedChunks=e||[],this._loaded=0;for(const n of this._queuedChunks)this._loaded+=n.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){this._done||(this._requests.length>0?this._requests.shift().resolve({value:t,done:!1}):this._queuedChunks.push(t),this._loaded+=t.byteLength)}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class ar{constructor(t,e,s){this._stream=t,this._begin=e,this._end=s,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length===0)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}function or(c){let t=!0,e=s("filename\\*","i").exec(c);if(e){e=e[1];let l=a(e);return l=unescape(l),l=o(l),l=h(l),n(l)}if(e=r(c),e){const l=h(e);return n(l)}if(e=s("filename","i").exec(c),e){e=e[1];let l=a(e);return l=h(l),n(l)}function s(l,d){return new RegExp("(?:^|;)\\s*"+l+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',d)}function i(l,d){if(l){if(!/^[\x00-\xFF]+$/.test(d))return d;try{const u=new TextDecoder(l,{fatal:!0}),f=Ie(d);d=u.decode(f),t=!1}catch{}}return d}function n(l){return t&&/[\x80-\xff]/.test(l)&&(l=i("utf-8",l),t&&(l=i("iso-8859-1",l))),l}function r(l){const d=[];let u;const f=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;(u=f.exec(l))!==null;){let[,p,b,m]=u;if(p=parseInt(p,10),p in d){if(p===0)break;continue}d[p]=[b,m]}const g=[];for(let p=0;p<d.length&&p in d;++p){let[b,m]=d[p];m=a(m),b&&(m=unescape(m),p===0&&(m=o(m))),g.push(m)}return g.join("")}function a(l){if(l.startsWith('"')){const d=l.slice(1).split('\\"');for(let u=0;u<d.length;++u){const f=d[u].indexOf('"');f!==-1&&(d[u]=d[u].slice(0,f),d.length=u+1),d[u]=d[u].replaceAll(/\\(.)/g,"$1")}l=d.join('"')}return l}function o(l){const d=l.indexOf("'");if(d===-1)return l;const u=l.slice(0,d),g=l.slice(d+1).replace(/^[^']*'/,"");return i(u,g)}function h(l){return!l.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(l)?l:l.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(d,u,f,g){if(f==="q"||f==="Q")return g=g.replaceAll("_"," "),g=g.replaceAll(/=([0-9a-fA-F]{2})/g,function(p,b){return String.fromCharCode(parseInt(b,16))}),i(u,g);try{g=atob(g)}catch{}return i(u,g)})}return""}function Oi(c,t){const e=new Headers;if(!c||!t||typeof t!="object")return e;for(const s in t){const i=t[s];i!==void 0&&e.append(s,i)}return e}function os(c){return URL.parse(c)?.origin??null}function Bi({responseHeaders:c,isHttp:t,rangeChunkSize:e,disableRange:s}){const i={allowRangeRequests:!1,suggestedLength:void 0},n=parseInt(c.get("Content-Length"),10);return!Number.isInteger(n)||(i.suggestedLength=n,n<=2*e)||s||!t||c.get("Accept-Ranges")!=="bytes"||(c.get("Content-Encoding")||"identity")!=="identity"||(i.allowRangeRequests=!0),i}function $i(c){const t=c.get("Content-Disposition");if(t){let e=or(t);if(e.includes("%"))try{e=decodeURIComponent(e)}catch{}if(Fs(e))return e}return null}function Fe(c,t){return new Ze(`Unexpected server response (${c}) while retrieving PDF "${t}".`,c,c===404||c===0&&t.startsWith("file:"))}function Hi(c){return c===200||c===206}function zi(c,t,e){return{method:"GET",headers:c,signal:e.signal,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}function Vi(c){return c instanceof Uint8Array?c.buffer:c instanceof ArrayBuffer?c:(V(`getArrayBuffer - unexpected data format: ${c}`),new Uint8Array(c).buffer)}class lr{_responseOrigin=null;constructor(t){this.source=t,this.isHttp=/^https?:/i.test(t.url),this.headers=Oi(this.isHttp,t.httpHeaders),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return ct(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new hr(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new cr(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class hr{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange;const s=new Headers(t.headers),i=e.url;fetch(i,zi(s,this._withCredentials,this._abortController)).then(n=>{if(t._responseOrigin=os(n.url),!Hi(n.status))throw Fe(n.status,i);this._reader=n.body.getReader(),this._headersCapability.resolve();const r=n.headers,{allowRangeRequests:a,suggestedLength:o}=Bi({responseHeaders:r,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=a,this._contentLength=o||this._contentLength,this._filename=$i(r),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new Yt("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:Vi(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class cr{constructor(t,e,s){this._stream=t,this._reader=null,this._loaded=0;const i=t.source;this._withCredentials=i.withCredentials||!1,this._readCapability=Promise.withResolvers(),this._isStreamingSupported=!i.disableStream,this._abortController=new AbortController;const n=new Headers(t.headers);n.append("Range",`bytes=${e}-${s-1}`);const r=i.url;fetch(r,zi(n,this._withCredentials,this._abortController)).then(a=>{const o=os(a.url);if(o!==t._responseOrigin)throw new Error(`Expected range response-origin "${o}" to match "${t._responseOrigin}".`);if(!Hi(a.status))throw Fe(a.status,r);this._readCapability.resolve(),this._reader=a.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded}),{value:Vi(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}const ws=200,As=206;function dr(c){const t=c.response;return typeof t!="string"?t:Ie(t).buffer}class ur{_responseOrigin=null;constructor({url:t,httpHeaders:e,withCredentials:s}){this.url=t,this.isHttp=/^https?:/i.test(t),this.headers=Oi(this.isHttp,e),this.withCredentials=s||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}request(t){const e=new XMLHttpRequest,s=this.currXhrId++,i=this.pendingRequests[s]={xhr:e};e.open("GET",this.url),e.withCredentials=this.withCredentials;for(const[n,r]of this.headers)e.setRequestHeader(n,r);return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),i.expectedStatus=As):i.expectedStatus=ws,e.responseType="arraybuffer",ct(t.onError,"Expected `onError` callback to be provided."),e.onerror=()=>{t.onError(e.status)},e.onreadystatechange=this.onStateChange.bind(this,s),e.onprogress=this.onProgress.bind(this,s),i.onHeadersReceived=t.onHeadersReceived,i.onDone=t.onDone,i.onError=t.onError,i.onProgress=t.onProgress,e.send(null),s}onProgress(t,e){const s=this.pendingRequests[t];s&&s.onProgress?.(e)}onStateChange(t,e){const s=this.pendingRequests[t];if(!s)return;const i=s.xhr;if(i.readyState>=2&&s.onHeadersReceived&&(s.onHeadersReceived(),delete s.onHeadersReceived),i.readyState!==4||!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],i.status===0&&this.isHttp){s.onError(i.status);return}const n=i.status||ws;if(!(n===ws&&s.expectedStatus===As)&&n!==s.expectedStatus){s.onError(i.status);return}const a=dr(i);if(n===As){const o=i.getResponseHeader("Content-Range"),h=/bytes (\d+)-(\d+)\/(\d+)/.exec(o);h?s.onDone({begin:parseInt(h[1],10),chunk:a}):(V('Missing or invalid "Content-Range" header.'),s.onError(0))}else a?s.onDone({begin:0,chunk:a}):s.onError(i.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class fr{constructor(t){this._source=t,this._manager=new ur(t),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return ct(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new pr(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){const s=new gr(this._manager,t,e);return s.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class pr{constructor(t,e){this._manager=t,this._url=e.url,this._fullRequestId=t.request({onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t);this._manager._responseOrigin=os(e.responseURL);const s=e.getAllResponseHeaders(),i=new Headers(s?s.trimStart().replace(/[^\S ]+$/,"").split(/[\r\n]+/).map(a=>{const[o,...h]=a.split(": ");return[o,h.join(": ")]}):[]),{allowRangeRequests:n,suggestedLength:r}=Bi({responseHeaders:i,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});n&&(this._isRangeSupported=!0),this._contentLength=r||this._contentLength,this._filename=$i(i),this._isRangeSupported&&this._manager.abortRequest(t),this._headersCapability.resolve()}_onDone(t){if(t&&(this._requests.length>0?this._requests.shift().resolve({value:t.chunk,done:!1}):this._cachedChunks.push(t.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=Fe(t,this._url),this._headersCapability.reject(this._storedError);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersCapability.promise}async read(){if(await this._headersCapability.promise,this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0,this._headersCapability.reject(t);for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class gr{constructor(t,e,s){this._manager=t,this._url=t.url,this._requestId=t.request({begin:e,end:s,onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_onHeadersReceived(){const t=os(this._manager.getRequestXhr(this._requestId)?.responseURL);t!==this._manager._responseOrigin&&(this._storedError=new Error(`Expected range response-origin "${t}" to match "${this._manager._responseOrigin}".`),this._onError(0))}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;this._requests.length>0?this._requests.shift().resolve({value:e,done:!1}):this._queuedChunk=e,this._done=!0;for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){this._storedError??=Fe(t,this._url);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(this._queuedChunk!==null){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}const mr=/^[a-z][a-z0-9\-+.]+:/i;function br(c){if(mr.test(c))return new URL(c);const t=process.getBuiltinModule("url");return new URL(t.pathToFileURL(c))}class vr{constructor(t){this.source=t,this.url=br(t.url),ct(this.url.protocol==="file:","PDFNodeStream only supports file:// URLs."),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return ct(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=new yr(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new wr(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class yr{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;const e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=Promise.withResolvers(),this._headersCapability=Promise.withResolvers();const s=process.getBuiltinModule("fs");s.promises.lstat(this._url).then(i=>{this._contentLength=i.size,this._setReadableStream(s.createReadStream(this._url)),this._headersCapability.resolve()},i=>{i.code==="ENOENT"&&(i=Fe(0,this._url.href)),this._storedError=i,this._headersCapability.reject(i)})}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new Yt("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class wr{constructor(t,e,s){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=Promise.withResolvers();const i=t.source;this._isStreamingSupported=!i.disableStream;const n=process.getBuiltinModule("fs");this._setReadableStream(n.createReadStream(this._url,{start:e,end:s-1}))}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,this.onProgress?.({loaded:this._loaded}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),this._storedError&&this._readableStream.destroy(this._storedError)}}const ye=Symbol("INITIAL_DATA");class Ui{#t=Object.create(null);#e(t){return this.#t[t]||={...Promise.withResolvers(),data:ye}}get(t,e=null){if(e){const i=this.#e(t);return i.promise.then(()=>e(i.data)),null}const s=this.#t[t];if(!s||s.data===ye)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return s.data}has(t){const e=this.#t[t];return!!e&&e.data!==ye}delete(t){const e=this.#t[t];return!e||e.data===ye?!1:(delete this.#t[t],!0)}resolve(t,e=null){const s=this.#e(t);s.data=e,s.resolve()}clear(){for(const t in this.#t){const{data:e}=this.#t[t];e?.bitmap?.close()}this.#t=Object.create(null)}*[Symbol.iterator](){for(const t in this.#t){const{data:e}=this.#t[t];e!==ye&&(yield[t,e])}}}const Ar=1e5,bi=30;class vt{#t=Promise.withResolvers();#e=null;#s=!1;#i=!!globalThis.FontInspector?.enabled;#r=null;#n=null;#a=0;#o=0;#c=null;#h=null;#u=0;#l=0;#p=Object.create(null);#m=[];#f=null;#d=[];#g=new WeakMap;#y=null;static#b=new Map;static#A=new Map;static#_=new WeakMap;static#v=null;static#E=new Set;constructor({textContentSource:t,container:e,viewport:s}){if(t instanceof ReadableStream)this.#f=t;else if(typeof t=="object")this.#f=new ReadableStream({start(o){o.enqueue(t),o.close()}});else throw new Error('No "textContentSource" parameter specified.');this.#e=this.#h=e,this.#l=s.scale*$t.pixelRatio,this.#u=s.rotation,this.#n={div:null,properties:null,ctx:null};const{pageWidth:i,pageHeight:n,pageX:r,pageY:a}=s.rawDims;this.#y=[1,0,0,-1,-r,a+n],this.#o=i,this.#a=n,vt.#x(),ee(e,s),this.#t.promise.finally(()=>{vt.#E.delete(this),this.#n=null,this.#p=null}).catch(()=>{})}static get fontFamilyMap(){const{isWindows:t,isFirefox:e}=mt.platform;return j(this,"fontFamilyMap",new Map([["sans-serif",`${t&&e?"Calibri, ":""}sans-serif`],["monospace",`${t&&e?"Lucida Console, ":""}monospace`]]))}render(){const t=()=>{this.#c.read().then(({value:e,done:s})=>{if(s){this.#t.resolve();return}this.#r??=e.lang,Object.assign(this.#p,e.styles),this.#P(e.items),t()},this.#t.reject)};return this.#c=this.#f.getReader(),vt.#E.add(this),t(),this.#t.promise}update({viewport:t,onBefore:e=null}){const s=t.scale*$t.pixelRatio,i=t.rotation;if(i!==this.#u&&(e?.(),this.#u=i,ee(this.#h,{rotation:i})),s!==this.#l){e?.(),this.#l=s;const n={div:null,properties:null,ctx:vt.#k(this.#r)};for(const r of this.#d)n.properties=this.#g.get(r),n.div=r,this.#R(n)}}cancel(){const t=new Yt("TextLayer task cancelled.");this.#c?.cancel(t).catch(()=>{}),this.#c=null,this.#t.reject(t)}get textDivs(){return this.#d}get textContentItemsStr(){return this.#m}#P(t){if(this.#s)return;this.#n.ctx??=vt.#k(this.#r);const e=this.#d,s=this.#m;for(const i of t){if(e.length>Ar){V("Ignoring additional textDivs for performance reasons."),this.#s=!0;return}if(i.str===void 0){if(i.type==="beginMarkedContentProps"||i.type==="beginMarkedContent"){const n=this.#e;this.#e=document.createElement("span"),this.#e.classList.add("markedContent"),i.id&&this.#e.setAttribute("id",`${i.id}`),n.append(this.#e)}else i.type==="endMarkedContent"&&(this.#e=this.#e.parentNode);continue}s.push(i.str),this.#S(i)}}#S(t){const e=document.createElement("span"),s={angle:0,canvasWidth:0,hasText:t.str!=="",hasEOL:t.hasEOL,fontSize:0};this.#d.push(e);const i=D.transform(this.#y,t.transform);let n=Math.atan2(i[1],i[0]);const r=this.#p[t.fontName];r.vertical&&(n+=Math.PI/2);let a=this.#i&&r.fontSubstitution||r.fontFamily;a=vt.fontFamilyMap.get(a)||a;const o=Math.hypot(i[2],i[3]),h=o*vt.#I(a,r,this.#r);let l,d;n===0?(l=i[4],d=i[5]-h):(l=i[4]+h*Math.sin(n),d=i[5]-h*Math.cos(n));const u="calc(var(--total-scale-factor) *",f=e.style;this.#e===this.#h?(f.left=`${(100*l/this.#o).toFixed(2)}%`,f.top=`${(100*d/this.#a).toFixed(2)}%`):(f.left=`${u}${l.toFixed(2)}px)`,f.top=`${u}${d.toFixed(2)}px)`),f.fontSize=`${u}${(vt.#v*o).toFixed(2)}px)`,f.fontFamily=a,s.fontSize=o,e.setAttribute("role","presentation"),e.textContent=t.str,e.dir=t.dir,this.#i&&(e.dataset.fontName=r.fontSubstitutionLoadedName||t.fontName),n!==0&&(s.angle=n*(180/Math.PI));let g=!1;if(t.str.length>1)g=!0;else if(t.str!==" "&&t.transform[0]!==t.transform[3]){const p=Math.abs(t.transform[0]),b=Math.abs(t.transform[3]);p!==b&&Math.max(p,b)/Math.min(p,b)>1.5&&(g=!0)}if(g&&(s.canvasWidth=r.vertical?t.height:t.width),this.#g.set(e,s),this.#n.div=e,this.#n.properties=s,this.#R(this.#n),s.hasText&&this.#e.append(e),s.hasEOL){const p=document.createElement("br");p.setAttribute("role","presentation"),this.#e.append(p)}}#R(t){const{div:e,properties:s,ctx:i}=t,{style:n}=e;let r="";if(vt.#v>1&&(r=`scale(${1/vt.#v})`),s.canvasWidth!==0&&s.hasText){const{fontFamily:a}=n,{canvasWidth:o,fontSize:h}=s;vt.#T(i,h*this.#l,a);const{width:l}=i.measureText(e.textContent);l>0&&(r=`scaleX(${o*this.#l/l}) ${r}`)}s.angle!==0&&(r=`rotate(${s.angle}deg) ${r}`),r.length>0&&(n.transform=r)}static cleanup(){if(!(this.#E.size>0)){this.#b.clear();for(const{canvas:t}of this.#A.values())t.remove();this.#A.clear()}}static#k(t=null){let e=this.#A.get(t||="");if(!e){const s=document.createElement("canvas");s.className="hiddenCanvasElement",s.lang=t,document.body.append(s),e=s.getContext("2d",{alpha:!1,willReadFrequently:!0}),this.#A.set(t,e),this.#_.set(e,{size:0,family:""})}return e}static#T(t,e,s){const i=this.#_.get(t);e===i.size&&s===i.family||(t.font=`${e}px ${s}`,i.size=e,i.family=s)}static#x(){if(this.#v!==null)return;const t=document.createElement("div");t.style.opacity=0,t.style.lineHeight=1,t.style.fontSize="1px",t.style.position="absolute",t.textContent="X",document.body.append(t),this.#v=t.getBoundingClientRect().height,t.remove()}static#I(t,e,s){const i=this.#b.get(t);if(i)return i;const n=this.#k(s);n.canvas.width=n.canvas.height=bi,this.#T(n,bi,t);const r=n.measureText(""),a=r.fontBoundingBoxAscent,o=Math.abs(r.fontBoundingBoxDescent);n.canvas.width=n.canvas.height=0;let h=.8;return a?h=a/(a+o):(mt.platform.isFirefox&&V("Enable the `dom.textMetrics.fontBoundingBox.enabled` preference in `about:config` to improve TextLayer rendering."),e.ascent?h=e.ascent:e.descent&&(h=1+e.descent)),this.#b.set(t,h),h}}class Te{static textContent(t){const e=[],s={items:e,styles:Object.create(null)};function i(n){if(!n)return;let r=null;const a=n.name;if(a==="#text")r=n.value;else if(Te.shouldBuildText(a))n?.attributes?.textContent?r=n.attributes.textContent:n.value&&(r=n.value);else return;if(r!==null&&e.push({str:r}),!!n.children)for(const o of n.children)i(o)}return i(t),s}static shouldBuildText(t){return!(t==="textarea"||t==="input"||t==="option"||t==="select")}}const _r=100;function Ps(c={}){typeof c=="string"||c instanceof URL?c={url:c}:(c instanceof ArrayBuffer||ArrayBuffer.isView(c))&&(c={data:c});const t=new Us,{docId:e}=t,s=c.url?Mn(c.url):null,i=c.data?In(c.data):null,n=c.httpHeaders||null,r=c.withCredentials===!0,a=c.password??null,o=c.range instanceof ji?c.range:null,h=Number.isInteger(c.rangeChunkSize)&&c.rangeChunkSize>0?c.rangeChunkSize:2**16;let l=c.worker instanceof ke?c.worker:null;const d=c.verbosity,u=typeof c.docBaseUrl=="string"&&!ns(c.docBaseUrl)?c.docBaseUrl:null,f=ze(c.cMapUrl),g=c.cMapPacked!==!1,p=c.CMapReaderFactory||(yt?Hn:ri),b=ze(c.iccUrl),m=ze(c.standardFontDataUrl),_=c.StandardFontDataFactory||(yt?zn:ai),y=ze(c.wasmUrl),w=c.WasmFactory||(yt?Vn:oi),v=c.stopAtErrors!==!0,E=Number.isInteger(c.maxImageSize)&&c.maxImageSize>-1?c.maxImageSize:-1,A=c.isEvalSupported!==!1,T=typeof c.isOffscreenCanvasSupported=="boolean"?c.isOffscreenCanvasSupported:!yt,k=typeof c.isImageDecoderSupported=="boolean"?c.isImageDecoderSupported:!yt&&(mt.platform.isFirefox||!globalThis.chrome),I=Number.isInteger(c.canvasMaxAreaInBytes)?c.canvasMaxAreaInBytes:-1,L=typeof c.disableFontFace=="boolean"?c.disableFontFace:yt,q=c.fontExtraProperties===!0,K=c.enableXfa===!0,G=c.ownerDocument||globalThis.document,dt=c.disableRange===!0,C=c.disableStream===!0,x=c.disableAutoFetch===!0,U=c.pdfBug===!0,P=c.CanvasFactory||(yt?$n:Fn),H=c.FilterFactory||(yt?Bn:On),J=c.enableHWA===!0,Y=c.useWasm!==!1,Z=o?o.length:c.length??NaN,Gt=typeof c.useSystemFonts=="boolean"?c.useSystemFonts:!yt&&!L,It=typeof c.useWorkerFetch=="boolean"?c.useWorkerFetch:!!(p===ri&&_===ai&&w===oi&&f&&m&&y&&_e(f,document.baseURI)&&_e(m,document.baseURI)&&_e(y,document.baseURI)),Ht=null;hn(d);const et={canvasFactory:new P({ownerDocument:G,enableHWA:J}),filterFactory:new H({docId:e,ownerDocument:G}),cMapReaderFactory:It?null:new p({baseUrl:f,isCompressed:g}),standardFontDataFactory:It?null:new _({baseUrl:m}),wasmFactory:It?null:new w({baseUrl:y})};l||(l=ke.create({verbosity:d,port:fe.workerPort}),t._worker=l);const zt={docId:e,apiVersion:"5.4.54",data:i,password:a,disableAutoFetch:x,rangeChunkSize:h,length:Z,docBaseUrl:u,enableXfa:K,evaluatorOptions:{maxImageSize:E,disableFontFace:L,ignoreErrors:v,isEvalSupported:A,isOffscreenCanvasSupported:T,isImageDecoderSupported:k,canvasMaxAreaInBytes:I,fontExtraProperties:q,useSystemFonts:Gt,useWasm:Y,useWorkerFetch:It,cMapUrl:f,iccUrl:b,standardFontDataUrl:m,wasmUrl:y}},me={ownerDocument:G,pdfBug:U,styleElement:Ht,loadingParams:{disableAutoFetch:x,enableXfa:K}};return l.promise.then(function(){if(t.destroyed)throw new Error("Loading aborted");if(l.destroyed)throw new Error("Worker was destroyed");const hs=l.messageHandler.sendWithPromise("GetDocRequest",zt,i?[i.buffer]:null);let cs;if(o)cs=new nr(o,{disableRange:dt,disableStream:C});else if(!i){if(!s)throw new Error("getDocument - no `url` parameter provided.");const ds=_e(s)?lr:yt?vr:fr;cs=new ds({url:s,length:Z,httpHeaders:n,withCredentials:r,rangeChunkSize:h,disableRange:dt,disableStream:C})}return hs.then(ds=>{if(t.destroyed)throw new Error("Loading aborted");if(l.destroyed)throw new Error("Worker was destroyed");const Ks=new Se(e,ds,l.port),en=new Cr(Ks,t,cs,me,et,J);t._transport=en,Ks.send("Ready",null)})}).catch(t._capability.reject),t}class Us{static#t=0;_capability=Promise.withResolvers();_transport=null;_worker=null;docId=`d${Us.#t++}`;destroyed=!1;onPassword=null;onProgress=null;get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0),await this._transport?.destroy()}catch(t){throw this._worker?.port&&delete this._worker._pendingDestroy,t}this._transport=null,this._worker?.destroy(),this._worker=null}async getData(){return this._transport.getData()}}class ji{#t=Promise.withResolvers();#e=[];#s=[];#i=[];#r=[];constructor(t,e,s=!1,i=null){this.length=t,this.initialData=e,this.progressiveDone=s,this.contentDispositionFilename=i}addRangeListener(t){this.#r.push(t)}addProgressListener(t){this.#i.push(t)}addProgressiveReadListener(t){this.#s.push(t)}addProgressiveDoneListener(t){this.#e.push(t)}onDataRange(t,e){for(const s of this.#r)s(t,e)}onDataProgress(t,e){this.#t.promise.then(()=>{for(const s of this.#i)s(t,e)})}onDataProgressiveRead(t){this.#t.promise.then(()=>{for(const e of this.#s)e(t)})}onDataProgressiveDone(){this.#t.promise.then(()=>{for(const t of this.#e)t()})}transportReady(){this.#t.resolve()}requestDataRange(t,e){Q("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class Sr{constructor(t,e){this._pdfInfo=t,this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get canvasFactory(){return this._transport.canvasFactory}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return j(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}class Er{#t=!1;constructor(t,e,s,i=!1){this._pageIndex=t,this._pageInfo=e,this._transport=s,this._stats=i?new si:null,this._pdfBug=i,this.commonObjs=s.commonObjs,this.objs=new Ui,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:s=0,offsetY:i=0,dontFlip:n=!1}={}){return new De({viewBox:this.view,userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:n})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return j(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,canvas:e=t.canvas,viewport:s,intent:i="display",annotationMode:n=Wt.ENABLE,transform:r=null,background:a=null,optionalContentConfigPromise:o=null,annotationCanvasMap:h=null,pageColors:l=null,printAnnotationStorage:d=null,isEditing:u=!1}){this._stats?.time("Overall");const f=this._transport.getRenderingIntent(i,n,d,u),{renderingIntent:g,cacheKey:p}=f;this.#t=!1,o||=this._transport.getOptionalContentConfig(g);let b=this._intentStates.get(p);b||(b=Object.create(null),this._intentStates.set(p,b)),b.streamReaderCancelTimeout&&(clearTimeout(b.streamReaderCancelTimeout),b.streamReaderCancelTimeout=null);const m=!!(g&Ct.PRINT);b.displayReadyCapability||(b.displayReadyCapability=Promise.withResolvers(),b.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(f));const _=v=>{b.renderTasks.delete(y),m&&(this.#t=!0),this.#e(),v?(y.capability.reject(v),this._abortOperatorList({intentState:b,reason:v instanceof Error?v:new Error(v)})):y.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"),globalThis.Stats?.enabled&&globalThis.Stats.add(this.pageNumber,this._stats))},y=new he({callback:_,params:{canvas:e,canvasContext:t,viewport:s,transform:r,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:h,operatorList:b.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!m,pdfBug:this._pdfBug,pageColors:l,enableHWA:this._transport.enableHWA});(b.renderTasks||=new Set).add(y);const w=y.task;return Promise.all([b.displayReadyCapability.promise,o]).then(([v,E])=>{if(this.destroyed){_();return}if(this._stats?.time("Rendering"),!(E.renderingIntent&g))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");y.initializeGraphics({transparency:v,optionalContentConfig:E}),y.operatorListChanged()}).catch(_),w}getOperatorList({intent:t="display",annotationMode:e=Wt.ENABLE,printAnnotationStorage:s=null,isEditing:i=!1}={}){function n(){a.operatorList.lastChunk&&(a.opListReadCapability.resolve(a.operatorList),a.renderTasks.delete(o))}const r=this._transport.getRenderingIntent(t,e,s,i,!0);let a=this._intentStates.get(r.cacheKey);a||(a=Object.create(null),this._intentStates.set(r.cacheKey,a));let o;return a.opListReadCapability||(o=Object.create(null),o.operatorListChanged=n,a.opListReadCapability=Promise.withResolvers(),(a.renderTasks||=new Set).add(o),a.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(r)),a.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:t===!0,disableNormalization:e===!0},{highWaterMark:100,size(i){return i.items.length}})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then(s=>Te.textContent(s));const e=this.streamTextContent(t);return new Promise(function(s,i){function n(){r.read().then(function({value:o,done:h}){if(h){s(a);return}a.lang??=o.lang,Object.assign(a.styles,o.styles),a.items.push(...o.items),n()},i)}const r=e.getReader(),a={items:[],styles:Object.create(null),lang:null};n()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(const s of e.renderTasks)t.push(s.completed),s.cancel();return this.objs.clear(),this.#t=!1,Promise.all(t)}cleanup(t=!1){this.#t=!0;const e=this.#e();return t&&e&&(this._stats&&=new si),e}#e(){if(!this.#t||this.destroyed)return!1;for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),this.#t=!1,!0}_startRenderPage(t,e){const s=this._intentStates.get(e);s&&(this._stats?.timeEnd("Page Request"),s.displayReadyCapability?.resolve(t))}_renderPageChunk(t,e){for(let s=0,i=t.length;s<i;s++)e.operatorList.fnArray.push(t.fnArray[s]),e.operatorList.argsArray.push(t.argsArray[s]);e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots;for(const s of e.renderTasks)s.operatorListChanged();t.lastChunk&&this.#e()}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:s,modifiedIds:i}){const{map:n,transfer:r}=s,o=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:n,modifiedIds:i},r).getReader(),h=this._intentStates.get(e);h.streamReader=o;const l=()=>{o.read().then(({value:d,done:u})=>{if(u){h.streamReader=null;return}this._transport.destroyed||(this._renderPageChunk(d,h),l())},d=>{if(h.streamReader=null,!this._transport.destroyed){if(h.operatorList){h.operatorList.lastChunk=!0;for(const u of h.renderTasks)u.operatorListChanged();this.#e()}if(h.displayReadyCapability)h.displayReadyCapability.reject(d);else if(h.opListReadCapability)h.opListReadCapability.reject(d);else throw d}})};l()}_abortOperatorList({intentState:t,reason:e,force:s=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout&&(clearTimeout(t.streamReaderCancelTimeout),t.streamReaderCancelTimeout=null),!s){if(t.renderTasks.size>0)return;if(e instanceof Ns){let i=_r;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay),t.streamReaderCancelTimeout=setTimeout(()=>{t.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:t,reason:e,force:!0})},i);return}}if(t.streamReader.cancel(new Yt(e.message)).catch(()=>{}),t.streamReader=null,!this._transport.destroyed){for(const[i,n]of this._intentStates)if(n===t){this._intentStates.delete(i);break}this.cleanup()}}}get stats(){return this._stats}}var qt,Mt,jt,Zt,ts,Jt,te,At,Xe,Gi,Wi,Ee,pe,Ye;const it=class it{constructor({name:t=null,port:e=null,verbosity:s=cn()}={}){Lt(this,At);Lt(this,qt,Promise.withResolvers());Lt(this,Mt,null);Lt(this,jt,null);Lt(this,Zt,null);if(this.name=t,this.destroyed=!1,this.verbosity=s,e){if(st(it,te).has(e))throw new Error("Cannot use more than one PDFWorker per port.");st(it,te).set(e,this),Dt(this,At,Gi).call(this,e)}else Dt(this,At,Wi).call(this)}get promise(){return st(this,qt).promise}get port(){return st(this,jt)}get messageHandler(){return st(this,Mt)}destroy(){this.destroyed=!0,st(this,Zt)?.terminate(),_t(this,Zt,null),st(it,te).delete(st(this,jt)),_t(this,jt,null),st(this,Mt)?.destroy(),_t(this,Mt,null)}static create(t){const e=st(this,te).get(t?.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.create - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new it(t)}static get workerSrc(){if(fe.workerSrc)return fe.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _setupFakeWorkerGlobal(){return j(this,"_setupFakeWorkerGlobal",(async()=>st(this,pe,Ye)?st(this,pe,Ye):(await import(this.workerSrc)).WorkerMessageHandler)())}};qt=new WeakMap,Mt=new WeakMap,jt=new WeakMap,Zt=new WeakMap,ts=new WeakMap,Jt=new WeakMap,te=new WeakMap,At=new WeakSet,Xe=function(){st(this,qt).resolve(),st(this,Mt).send("configure",{verbosity:this.verbosity})},Gi=function(t){_t(this,jt,t),_t(this,Mt,new Se("main","worker",t)),st(this,Mt).on("ready",()=>{}),Dt(this,At,Xe).call(this)},Wi=function(){if(st(it,Jt)||st(it,pe,Ye)){Dt(this,At,Ee).call(this);return}let{workerSrc:t}=it;try{it._isSameOrigin(window.location,t)||(t=it._createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),s=new Se("main","worker",e),i=()=>{n.abort(),s.destroy(),e.terminate(),this.destroyed?st(this,qt).reject(new Error("Worker was destroyed")):Dt(this,At,Ee).call(this)},n=new AbortController;e.addEventListener("error",()=>{st(this,Zt)||i()},{signal:n.signal}),s.on("test",a=>{if(n.abort(),this.destroyed||!a){i();return}_t(this,Mt,s),_t(this,jt,e),_t(this,Zt,e),Dt(this,At,Xe).call(this)}),s.on("ready",a=>{if(n.abort(),this.destroyed){i();return}try{r()}catch{Dt(this,At,Ee).call(this)}});const r=()=>{const a=new Uint8Array;s.send("test",a,[a.buffer])};r();return}catch{is("The worker has been disabled.")}Dt(this,At,Ee).call(this)},Ee=function(){st(it,Jt)||(V("Setting up fake worker."),_t(it,Jt,!0)),it._setupFakeWorkerGlobal.then(t=>{if(this.destroyed){st(this,qt).reject(new Error("Worker was destroyed"));return}const e=new Nn;_t(this,jt,e);const s=`fake${Zs(it,ts)._++}`,i=new Se(s+"_worker",s,e);t.setup(i,e),_t(this,Mt,new Se(s,s+"_worker",e)),Dt(this,At,Xe).call(this)}).catch(t=>{st(this,qt).reject(new Error(`Setting up fake worker failed: "${t.message}".`))})},pe=new WeakSet,Ye=function(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}},Lt(it,pe),Lt(it,ts,0),Lt(it,Jt,!1),Lt(it,te,new WeakMap),yt&&(_t(it,Jt,!0),fe.workerSrc||="./pdf.worker.mjs"),it._isSameOrigin=(t,e)=>{const s=URL.parse(t);if(!s?.origin||s.origin==="null")return!1;const i=new URL(e,s);return s.origin===i.origin},it._createCDNWrapper=t=>{const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))},it.fromPort=t=>{if(_n("`PDFWorker.fromPort` - please use `PDFWorker.create` instead."),!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");return it.create(t)};let ke=it;class Cr{#t=new Map;#e=new Map;#s=new Map;#i=new Map;#r=null;constructor(t,e,s,i,n,r){this.messageHandler=t,this.loadingTask=e,this.commonObjs=new Ui,this.fontLoader=new Pn({ownerDocument:i.ownerDocument,styleElement:i.styleElement}),this.loadingParams=i.loadingParams,this._params=i,this.canvasFactory=n.canvasFactory,this.filterFactory=n.filterFactory,this.cMapReaderFactory=n.cMapReaderFactory,this.standardFontDataFactory=n.standardFontDataFactory,this.wasmFactory=n.wasmFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=s,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=Promise.withResolvers(),this.enableHWA=r,this.setupMessageHandler()}#n(t,e=null){const s=this.#t.get(t);if(s)return s;const i=this.messageHandler.sendWithPromise(t,e);return this.#t.set(t,i),i}get annotationStorage(){return j(this,"annotationStorage",new $s)}getRenderingIntent(t,e=Wt.ENABLE,s=null,i=!1,n=!1){let r=Ct.DISPLAY,a=xs;switch(t){case"any":r=Ct.ANY;break;case"display":break;case"print":r=Ct.PRINT;break;default:V(`getRenderingIntent - invalid intent: ${t}`)}const o=r&Ct.PRINT&&s instanceof Mi?s:this.annotationStorage;switch(e){case Wt.DISABLE:r+=Ct.ANNOTATIONS_DISABLE;break;case Wt.ENABLE:break;case Wt.ENABLE_FORMS:r+=Ct.ANNOTATIONS_FORMS;break;case Wt.ENABLE_STORAGE:r+=Ct.ANNOTATIONS_STORAGE,a=o.serializable;break;default:V(`getRenderingIntent - invalid annotationMode: ${e}`)}i&&(r+=Ct.IS_EDITING),n&&(r+=Ct.OPLIST);const{ids:h,hash:l}=o.modifiedIds,d=[r,a.hash,l];return{renderingIntent:r,cacheKey:d.join("_"),annotationStorageSerializable:a,modifiedIds:h}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=Promise.withResolvers(),this.#r?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const s of this.#e.values())t.push(s._destroy());this.#e.clear(),this.#s.clear(),this.#i.clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then(()=>{this.commonObjs.clear(),this.fontLoader.clear(),this.#t.clear(),this.filterFactory.destroy(),vt.cleanup(),this._networkStream?.cancelAllRequests(new Yt("Worker was terminated.")),this.messageHandler?.destroy(),this.messageHandler=null,this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",(s,i)=>{ct(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=n=>{this._lastProgress={loaded:n.loaded,total:n.total}},i.onPull=()=>{this._fullReader.read().then(function({value:n,done:r}){if(r){i.close();return}ct(n instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(n),1,[n])}).catch(n=>{i.error(n)})},i.onCancel=n=>{this._fullReader.cancel(n),i.ready.catch(r=>{if(!this.destroyed)throw r})}}),t.on("ReaderHeadersReady",async s=>{await this._fullReader.headersReady;const{isStreamingSupported:i,isRangeSupported:n,contentLength:r}=this._fullReader;return(!i||!n)&&(this._lastProgress&&e.onProgress?.(this._lastProgress),this._fullReader.onProgress=a=>{e.onProgress?.({loaded:a.loaded,total:a.total})}),{isStreamingSupported:i,isRangeSupported:n,contentLength:r}}),t.on("GetRangeReader",(s,i)=>{ct(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const n=this._networkStream.getRangeReader(s.begin,s.end);if(!n){i.close();return}i.onPull=()=>{n.read().then(function({value:r,done:a}){if(a){i.close();return}ct(r instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(r),1,[r])}).catch(r=>{i.error(r)})},i.onCancel=r=>{n.cancel(r),i.ready.catch(a=>{if(!this.destroyed)throw a})}}),t.on("GetDoc",({pdfInfo:s})=>{this._numPages=s.numPages,this._htmlForXfa=s.htmlForXfa,delete s.htmlForXfa,e._capability.resolve(new Sr(s,this))}),t.on("DocException",s=>{e._capability.reject(St(s))}),t.on("PasswordRequest",s=>{this.#r=Promise.withResolvers();try{if(!e.onPassword)throw St(s);const i=n=>{n instanceof Error?this.#r.reject(n):this.#r.resolve({password:n})};e.onPassword(i,s.code)}catch(i){this.#r.reject(i)}return this.#r.promise}),t.on("DataLoaded",s=>{e.onProgress?.({loaded:s.length,total:s.length}),this.downloadInfoCapability.resolve(s)}),t.on("StartRenderPage",s=>{if(this.destroyed)return;this.#e.get(s.pageIndex)._startRenderPage(s.transparency,s.cacheKey)}),t.on("commonobj",([s,i,n])=>{if(this.destroyed||this.commonObjs.has(s))return null;switch(i){case"Font":if("error"in n){const h=n.error;V(`Error during font loading: ${h}`),this.commonObjs.resolve(s,h);break}const r=this._params.pdfBug&&globalThis.FontInspector?.enabled?(h,l)=>globalThis.FontInspector.fontAdded(h,l):null,a=new Rn(n,r);this.fontLoader.bind(a).catch(()=>t.sendWithPromise("FontFallback",{id:s})).finally(()=>{!a.fontExtraProperties&&a.data&&(a.data=null),this.commonObjs.resolve(s,a)});break;case"CopyLocalImage":const{imageRef:o}=n;ct(o,"The imageRef must be defined.");for(const h of this.#e.values())for(const[,l]of h.objs)if(l?.ref===o)return l.dataLen?(this.commonObjs.resolve(s,structuredClone(l)),l.dataLen):null;break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(s,n);break;default:throw new Error(`Got unknown common object type ${i}`)}return null}),t.on("obj",([s,i,n,r])=>{if(this.destroyed)return;const a=this.#e.get(i);if(!a.objs.has(s)){if(a._intentStates.size===0){r?.bitmap?.close();return}switch(n){case"Image":case"Pattern":a.objs.resolve(s,r);break;default:throw new Error(`Got unknown object type ${n}`)}}}),t.on("DocProgress",s=>{this.destroyed||e.onProgress?.({loaded:s.loaded,total:s.total})}),t.on("FetchBinaryData",async s=>{if(this.destroyed)throw new Error("Worker was destroyed.");const i=this[s.type];if(!i)throw new Error(`${s.type} not initialized, see the \`useWorkerFetch\` parameter.`);return i.fetch(s)})}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&V("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally(()=>{this.annotationStorage.resetModified()})}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,s=this.#s.get(e);if(s)return s;const i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then(n=>{if(this.destroyed)throw new Error("Transport destroyed");n.refStr&&this.#i.set(n.refStr,t);const r=new Er(e,n,this,this._params.pdfBug);return this.#e.set(e,r),r});return this.#s.set(e,i),i}getPageIndex(t){return Ts(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#n("GetFieldObjects")}hasJSActions(){return this.#n("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return typeof t!="string"?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#n("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return this.#n("GetOptionalContentConfig").then(e=>new ir(e,t))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=this.#t.get(t);if(e)return e;const s=this.messageHandler.sendWithPromise(t,null).then(i=>({info:i[0],metadata:i[1]?new er(i[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null}));return this.#t.set(t,s),s}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const e of this.#e.values())if(!e.cleanup())throw new Error(`startCleanup: Page ${e.pageNumber} is currently rendering.`);this.commonObjs.clear(),t||this.fontLoader.clear(),this.#t.clear(),this.filterFactory.destroy(!0),vt.cleanup()}}cachedPageNumber(t){if(!Ts(t))return null;const e=t.gen===0?`${t.num}R`:`${t.num}R${t.gen}`;return this.#i.get(e)??null}}class xr{#t=null;onContinue=null;onError=null;constructor(t){this.#t=t}get promise(){return this.#t.capability.promise}cancel(t=0){this.#t.cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=this.#t.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#t;return t.form||t.canvas&&e?.size>0}}class he{#t=null;static#e=new WeakSet;constructor({callback:t,params:e,objs:s,commonObjs:i,annotationCanvasMap:n,operatorList:r,pageIndex:a,canvasFactory:o,filterFactory:h,useRequestAnimationFrame:l=!1,pdfBug:d=!1,pageColors:u=null,enableHWA:f=!1}){this.callback=t,this.params=e,this.objs=s,this.commonObjs=i,this.annotationCanvasMap=n,this.operatorListIdx=null,this.operatorList=r,this._pageIndex=a,this.canvasFactory=o,this.filterFactory=h,this._pdfBug=d,this.pageColors=u,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=l===!0&&typeof window<"u",this.cancelled=!1,this.capability=Promise.withResolvers(),this.task=new xr(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=e.canvas,this._canvasContext=e.canvas?null:e.canvasContext,this._enableHWA=f}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(he.#e.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");he.#e.add(this._canvas)}this._pdfBug&&globalThis.StepperManager?.enabled&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{viewport:s,transform:i,background:n}=this.params,r=this._canvasContext||this._canvas.getContext("2d",{alpha:!1,willReadFrequently:!this._enableHWA});this.gfx=new ue(r,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:i,viewport:s,transparency:t,background:n}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1,this.cancelled=!0,this.gfx?.endDrawing(),this.#t&&(window.cancelAnimationFrame(this.#t),this.#t=null),he.#e.delete(this._canvas),t||=new Ns(`Rendering cancelled, page ${this._pageIndex+1}`,e),this.callback(t),this.task.onError?.(t)}operatorListChanged(){if(!this.graphicsReady){this.graphicsReadyCallback||=this._continueBound;return}this.stepper?.updateOperatorList(this.operatorList),!this.running&&this._continue()}_continue(){this.running=!0,!this.cancelled&&(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?this.#t=window.requestAnimationFrame(()=>{this.#t=null,this._nextBound().catch(this._cancelBound)}):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),he.#e.delete(this._canvas),this.callback())))}}const Tr="5.4.54",kr="295fb3ec4";class kt{#t=null;#e=null;#s;#i=null;#r=!1;#n=!1;#a=null;#o;#c=null;#h=null;static#u=null;static get _keyboardManager(){return j(this,"_keyboardManager",new Ne([[["Escape","mac+Escape"],kt.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],kt.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],kt.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],kt.prototype._moveToPrevious],[["Home","mac+Home"],kt.prototype._moveToBeginning],[["End","mac+End"],kt.prototype._moveToEnd]]))}constructor({editor:t=null,uiManager:e=null}){t?(this.#n=!1,this.#a=t):this.#n=!0,this.#h=t?._uiManager||e,this.#o=this.#h._eventBus,this.#s=t?.color?.toUpperCase()||this.#h?.highlightColors.values().next().value||"#FFFF98",kt.#u||=Object.freeze({blue:"pdfjs-editor-colorpicker-blue",green:"pdfjs-editor-colorpicker-green",pink:"pdfjs-editor-colorpicker-pink",red:"pdfjs-editor-colorpicker-red",yellow:"pdfjs-editor-colorpicker-yellow"})}renderButton(){const t=this.#t=document.createElement("button");t.className="colorPicker",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button"),t.ariaHasPopup="true",this.#a&&(t.ariaControls=`${this.#a.id}_colorpicker_dropdown`);const e=this.#h._signal;t.addEventListener("click",this.#f.bind(this),{signal:e}),t.addEventListener("keydown",this.#m.bind(this),{signal:e});const s=this.#e=document.createElement("span");return s.className="swatch",s.ariaHidden="true",s.style.backgroundColor=this.#s,t.append(s),t}renderMainDropdown(){const t=this.#i=this.#l();return t.ariaOrientation="horizontal",t.ariaLabelledBy="highlightColorPickerLabel",t}#l(){const t=document.createElement("div"),e=this.#h._signal;t.addEventListener("contextmenu",Pt,{signal:e}),t.className="dropdown",t.role="listbox",t.ariaMultiSelectable="false",t.ariaOrientation="vertical",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown"),this.#a&&(t.id=`${this.#a.id}_colorpicker_dropdown`);for(const[s,i]of this.#h.highlightColors){const n=document.createElement("button");n.tabIndex="0",n.role="option",n.setAttribute("data-color",i),n.title=s,n.setAttribute("data-l10n-id",kt.#u[s]);const r=document.createElement("span");n.append(r),r.className="swatch",r.style.backgroundColor=i,n.ariaSelected=i===this.#s,n.addEventListener("click",this.#p.bind(this,i),{signal:e}),t.append(n)}return t.addEventListener("keydown",this.#m.bind(this),{signal:e}),t}#p(t,e){e.stopPropagation(),this.#o.dispatch("switchannotationeditorparams",{source:this,type:W.HIGHLIGHT_COLOR,value:t}),this.updateColor(t)}_colorSelectFromKeyboard(t){if(t.target===this.#t){this.#f(t);return}const e=t.target.getAttribute("data-color");e&&this.#p(e,t)}_moveToNext(t){if(!this.#g){this.#f(t);return}if(t.target===this.#t){this.#i.firstChild?.focus();return}t.target.nextSibling?.focus()}_moveToPrevious(t){if(t.target===this.#i?.firstChild||t.target===this.#t){this.#g&&this._hideDropdownFromKeyboard();return}this.#g||this.#f(t),t.target.previousSibling?.focus()}_moveToBeginning(t){if(!this.#g){this.#f(t);return}this.#i.firstChild?.focus()}_moveToEnd(t){if(!this.#g){this.#f(t);return}this.#i.lastChild?.focus()}#m(t){kt._keyboardManager.exec(this,t)}#f(t){if(this.#g){this.hideDropdown();return}if(this.#r=t.detail===0,this.#c||(this.#c=new AbortController,window.addEventListener("pointerdown",this.#d.bind(this),{signal:this.#h.combinedSignal(this.#c)})),this.#t.ariaExpanded="true",this.#i){this.#i.classList.remove("hidden");return}const e=this.#i=this.#l();this.#t.append(e)}#d(t){this.#i?.contains(t.target)||this.hideDropdown()}hideDropdown(){this.#i?.classList.add("hidden"),this.#t.ariaExpanded="false",this.#c?.abort(),this.#c=null}get#g(){return this.#i&&!this.#i.classList.contains("hidden")}_hideDropdownFromKeyboard(){if(!this.#n){if(!this.#g){this.#a?.unselect();return}this.hideDropdown(),this.#t.focus({preventScroll:!0,focusVisible:this.#r})}}updateColor(t){if(this.#e&&(this.#e.style.backgroundColor=t),!this.#i)return;const e=this.#h.highlightColors.values();for(const s of this.#i.children)s.ariaSelected=e.next().value===t.toUpperCase()}destroy(){this.#t?.remove(),this.#t=null,this.#e=null,this.#i?.remove(),this.#i=null}}class Pe{#t=null;#e=null;#s=null;static#i=null;constructor(t){this.#e=t,this.#s=t._uiManager,Pe.#i||=Object.freeze({freetext:"pdfjs-editor-color-picker-free-text-input",ink:"pdfjs-editor-color-picker-ink-input"})}renderButton(){if(this.#t)return this.#t;const{editorType:t,colorType:e,colorValue:s}=this.#e,i=this.#t=document.createElement("input");return i.type="color",i.value=s||"#000000",i.className="basicColorPicker",i.tabIndex=0,i.setAttribute("data-l10n-id",Pe.#i[t]),i.addEventListener("input",()=>{this.#s.updateParams(e,i.value)},{signal:this.#s._signal}),i}update(t){this.#t&&(this.#t.value=t)}destroy(){this.#t?.remove(),this.#t=null}hideDropdown(){}}function vi(c){return Math.floor(Math.max(0,Math.min(1,c))*255).toString(16).padStart(2,"0")}function we(c){return Math.max(0,Math.min(255,255*c))}class yi{static CMYK_G([t,e,s,i]){return["G",1-Math.min(1,.3*t+.59*s+.11*e+i)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return t=we(t),[t,t,t]}static G_HTML([t]){const e=vi(t);return`#${e}${e}${e}`}static RGB_G([t,e,s]){return["G",.3*t+.59*e+.11*s]}static RGB_rgb(t){return t.map(we)}static RGB_HTML(t){return`#${t.map(vi).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,s,i]){return["RGB",1-Math.min(1,t+i),1-Math.min(1,s+i),1-Math.min(1,e+i)]}static CMYK_rgb([t,e,s,i]){return[we(1-Math.min(1,t+i)),we(1-Math.min(1,s+i)),we(1-Math.min(1,e+i))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,s]){const i=1-t,n=1-e,r=1-s,a=Math.min(i,n,r);return["CMYK",i,n,r,a]}}class Pr{create(t,e,s=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const i=this._createSVG("svg:svg");return i.setAttribute("version","1.1"),s||(i.setAttribute("width",`${t}px`),i.setAttribute("height",`${e}px`)),i.setAttribute("preserveAspectRatio","none"),i.setAttribute("viewBox",`0 0 ${t} ${e}`),i}createElement(t){if(typeof t!="string")throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){Q("Abstract method `_createSVG` called.")}}class Je extends Pr{_createSVG(t){return document.createElementNS(Vt,t)}}class qi{static setupStorage(t,e,s,i,n){const r=i.getValue(e,{value:null});switch(s.name){case"textarea":if(r.value!==null&&(t.textContent=r.value),n==="print")break;t.addEventListener("input",a=>{i.setValue(e,{value:a.target.value})});break;case"input":if(s.attributes.type==="radio"||s.attributes.type==="checkbox"){if(r.value===s.attributes.xfaOn?t.setAttribute("checked",!0):r.value===s.attributes.xfaOff&&t.removeAttribute("checked"),n==="print")break;t.addEventListener("change",a=>{i.setValue(e,{value:a.target.checked?a.target.getAttribute("xfaOn"):a.target.getAttribute("xfaOff")})})}else{if(r.value!==null&&t.setAttribute("value",r.value),n==="print")break;t.addEventListener("input",a=>{i.setValue(e,{value:a.target.value})})}break;case"select":if(r.value!==null){t.setAttribute("value",r.value);for(const a of s.children)a.attributes.value===r.value?a.attributes.selected=!0:a.attributes.hasOwnProperty("selected")&&delete a.attributes.selected}t.addEventListener("input",a=>{const o=a.target.options,h=o.selectedIndex===-1?"":o[o.selectedIndex].value;i.setValue(e,{value:h})});break}}static setAttributes({html:t,element:e,storage:s=null,intent:i,linkService:n}){const{attributes:r}=e,a=t instanceof HTMLAnchorElement;r.type==="radio"&&(r.name=`${r.name}-${i}`);for(const[o,h]of Object.entries(r))if(h!=null)switch(o){case"class":h.length&&t.setAttribute(o,h.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",h);break;case"style":Object.assign(t.style,h);break;case"textContent":t.textContent=h;break;default:(!a||o!=="href"&&o!=="newWindow")&&t.setAttribute(o,h)}a&&n.addLinkAttributes(t,r.href,r.newWindow),s&&r.dataId&&this.setupStorage(t,r.dataId,e,s)}static render(t){const e=t.annotationStorage,s=t.linkService,i=t.xfaHtml,n=t.intent||"display",r=document.createElement(i.name);i.attributes&&this.setAttributes({html:r,element:i,intent:n,linkService:s});const a=n!=="richText",o=t.div;if(o.append(r),t.viewport){const d=`matrix(${t.viewport.transform.join(",")})`;o.style.transform=d}a&&o.setAttribute("class","xfaLayer xfaFont");const h=[];if(i.children.length===0){if(i.value){const d=document.createTextNode(i.value);r.append(d),a&&Te.shouldBuildText(i.name)&&h.push(d)}return{textDivs:h}}const l=[[i,-1,r]];for(;l.length>0;){const[d,u,f]=l.at(-1);if(u+1===d.children.length){l.pop();continue}const g=d.children[++l.at(-1)[1]];if(g===null)continue;const{name:p}=g;if(p==="#text"){const m=document.createTextNode(g.value);h.push(m),f.append(m);continue}const b=g?.attributes?.xmlns?document.createElementNS(g.attributes.xmlns,p):document.createElement(p);if(f.append(b),g.attributes&&this.setAttributes({html:b,element:g,storage:e,intent:n,linkService:s}),g.children?.length>0)l.push([g,-1,b]);else if(g.value){const m=document.createTextNode(g.value);a&&Te.shouldBuildText(p)&&h.push(m),b.append(m)}}for(const d of o.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))d.setAttribute("readOnly",!0);return{textDivs:h}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}const Rr=9,se=new WeakSet,Mr=new Date().getTimezoneOffset()*60*1e3;class wi{static create(t){switch(t.data.annotationType){case lt.LINK:return new js(t);case lt.TEXT:return new Ir(t);case lt.WIDGET:switch(t.data.fieldType){case"Tx":return new Lr(t);case"Btn":return t.data.radioButton?new Xi(t):t.data.checkBox?new Nr(t):new Fr(t);case"Ch":return new Or(t);case"Sig":return new Dr(t)}return new ne(t);case lt.POPUP:return new Rs(t);case lt.FREETEXT:return new Yi(t);case lt.LINE:return new $r(t);case lt.SQUARE:return new Hr(t);case lt.CIRCLE:return new zr(t);case lt.POLYLINE:return new Ki(t);case lt.CARET:return new Ur(t);case lt.INK:return new Gs(t);case lt.POLYGON:return new Vr(t);case lt.HIGHLIGHT:return new Qi(t);case lt.UNDERLINE:return new jr(t);case lt.SQUIGGLY:return new Gr(t);case lt.STRIKEOUT:return new Wr(t);case lt.STAMP:return new Zi(t);case lt.FILEATTACHMENT:return new qr(t);default:return new at(t)}}}class at{#t=null;#e=!1;#s=null;constructor(t,{isRenderable:e=!1,ignoreBorder:s=!1,createQuadrilaterals:i=!1}={}){this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(s)),i&&this._createQuadrilaterals()}static _hasPopupData({contentsObj:t,richText:e}){return!!(t?.str||e?.str)}get _isEditable(){return this.data.isEditable}get hasPopupData(){return at._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;t.rect&&(this.#t||={rect:this.data.rect.slice(0)});const{rect:e,popup:s}=t;e&&this.#i(e);let i=this.#s?.popup||this.popup;!i&&s?.text&&(this._createPopup(s),i=this.#s.popup),i&&(i.updateEdited(t),s?.deleted&&(i.remove(),this.#s=null,this.popup=null))}resetEdited(){this.#t&&(this.#i(this.#t.rect),this.#s?.popup.resetEdited(),this.#t=null)}#i(t){const{container:{style:e},data:{rect:s,rotation:i},parent:{viewport:{rawDims:{pageWidth:n,pageHeight:r,pageX:a,pageY:o}}}}=this;s?.splice(0,4,...t),e.left=`${100*(t[0]-a)/n}%`,e.top=`${100*(r-t[3]+o)/r}%`,i===0?(e.width=`${100*(t[2]-t[0])/n}%`,e.height=`${100*(t[3]-t[1])/r}%`):this.setRotation(i)}_createContainer(t){const{data:e,parent:{page:s,viewport:i}}=this,n=document.createElement("section");n.setAttribute("data-annotation-id",e.id),!(this instanceof ne)&&!(this instanceof js)&&(n.tabIndex=0);const{style:r}=n;if(r.zIndex=this.parent.zIndex++,e.alternativeText&&(n.title=e.alternativeText),e.noRotate&&n.classList.add("norotate"),!e.rect||this instanceof Rs){const{rotation:p}=e;return!e.hasOwnCanvas&&p!==0&&this.setRotation(p,n),n}const{width:a,height:o}=this;if(!t&&e.borderStyle.width>0){r.borderWidth=`${e.borderStyle.width}px`;const p=e.borderStyle.horizontalCornerRadius,b=e.borderStyle.verticalCornerRadius;if(p>0||b>0){const _=`calc(${p}px * var(--total-scale-factor)) / calc(${b}px * var(--total-scale-factor))`;r.borderRadius=_}else if(this instanceof Xi){const _=`calc(${a}px * var(--total-scale-factor)) / calc(${o}px * var(--total-scale-factor))`;r.borderRadius=_}switch(e.borderStyle.style){case re.SOLID:r.borderStyle="solid";break;case re.DASHED:r.borderStyle="dashed";break;case re.BEVELED:V("Unimplemented border style: beveled");break;case re.INSET:V("Unimplemented border style: inset");break;case re.UNDERLINE:r.borderBottomStyle="solid";break}const m=e.borderColor||null;m?(this.#e=!0,r.borderColor=D.makeHexColor(m[0]|0,m[1]|0,m[2]|0)):r.borderWidth=0}const h=D.normalizeRect([e.rect[0],s.view[3]-e.rect[1]+s.view[1],e.rect[2],s.view[3]-e.rect[3]+s.view[1]]),{pageWidth:l,pageHeight:d,pageX:u,pageY:f}=i.rawDims;r.left=`${100*(h[0]-u)/l}%`,r.top=`${100*(h[1]-f)/d}%`;const{rotation:g}=e;return e.hasOwnCanvas||g===0?(r.width=`${100*a/l}%`,r.height=`${100*o/d}%`):this.setRotation(g,n),n}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:s,pageHeight:i}=this.parent.viewport.rawDims;let{width:n,height:r}=this;t%180!==0&&([n,r]=[r,n]),e.style.width=`${100*n/s}%`,e.style.height=`${100*r/i}%`,e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const t=(e,s,i)=>{const n=i.detail[e],r=n[0],a=n.slice(1);i.target.style[s]=yi[`${r}_HTML`](a),this.annotationStorage.setValue(this.data.id,{[s]:yi[`${r}_rgb`](a)})};return j(this,"_commonActions",{display:e=>{const{display:s}=e.detail,i=s%2===1;this.container.style.visibility=i?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:s===1||s===2})},print:e=>{this.annotationStorage.setValue(this.data.id,{noPrint:!e.detail.print})},hidden:e=>{const{hidden:s}=e.detail;this.container.style.visibility=s?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:s,noView:s})},focus:e=>{setTimeout(()=>e.target.focus({preventScroll:!1}),0)},userName:e=>{e.target.title=e.detail.userName},readonly:e=>{e.target.disabled=e.detail.readonly},required:e=>{this._setRequired(e.target,e.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:e=>{const s=e.detail.rotation;this.setRotation(s),this.annotationStorage.setValue(this.data.id,{rotation:s})}})}_dispatchEventFromSandbox(t,e){const s=this._commonActions;for(const i of Object.keys(e.detail))(t[i]||s[i])?.(e)}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const s=this._commonActions;for(const[i,n]of Object.entries(e)){const r=s[i];if(r){const a={detail:{[i]:n},target:t};r(a),delete e[i]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,s,i,n]=this.data.rect.map(p=>Math.fround(p));if(t.length===8){const[p,b,m,_]=t.subarray(2,6);if(i===p&&n===b&&e===m&&s===_)return}const{style:r}=this.container;let a;if(this.#e){const{borderColor:p,borderWidth:b}=r;r.borderWidth=0,a=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${p}" stroke-width="${b}">`],this.container.classList.add("hasBorder")}const o=i-e,h=n-s,{svgFactory:l}=this,d=l.createElement("svg");d.classList.add("quadrilateralsContainer"),d.setAttribute("width",0),d.setAttribute("height",0),d.role="none";const u=l.createElement("defs");d.append(u);const f=l.createElement("clipPath"),g=`clippath_${this.data.id}`;f.setAttribute("id",g),f.setAttribute("clipPathUnits","objectBoundingBox"),u.append(f);for(let p=2,b=t.length;p<b;p+=8){const m=t[p],_=t[p+1],y=t[p+2],w=t[p+3],v=l.createElement("rect"),E=(y-e)/o,A=(n-_)/h,T=(m-y)/o,k=(_-w)/h;v.setAttribute("x",E),v.setAttribute("y",A),v.setAttribute("width",T),v.setAttribute("height",k),f.append(v),a?.push(`<rect vector-effect="non-scaling-stroke" x="${E}" y="${A}" width="${T}" height="${k}"/>`)}this.#e&&(a.push("</g></svg>')"),r.backgroundImage=a.join("")),this.container.append(d),this.container.style.clipPath=`url(#${g})`}_createPopup(t=null){const{data:e}=this;let s,i;t?(s={str:t.text},i=t.date):(s=e.contentsObj,i=e.modificationDate);const n=this.#s=new Rs({data:{color:e.color,titleObj:e.titleObj,modificationDate:i,contentsObj:s,richText:e.richText,parentRect:e.rect,borderStyle:0,id:`popup_${e.id}`,rotation:e.rotation,noRotate:!0},linkService:this.linkService,parent:this.parent,elements:[this]});this.parent.div.append(n.render())}get hasPopupElement(){return!!(this.#s||this.popup||this.data.popupRef)}render(){Q("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const s=[];if(this._fieldObjects){const i=this._fieldObjects[t];if(i)for(const{page:n,id:r,exportValues:a}of i){if(n===-1||r===e)continue;const o=typeof a=="string"?a:null,h=document.querySelector(`[data-element-id="${r}"]`);if(h&&!se.has(h)){V(`_getElementsByName - element not allowed: ${r}`);continue}s.push({id:r,exportValue:o,domElement:h})}return s}for(const i of document.getElementsByName(t)){const{exportValue:n}=i,r=i.getAttribute("data-element-id");r!==e&&se.has(i)&&s.push({id:r,exportValue:n,domElement:i})}return s}show(){this.container&&(this.container.hidden=!1),this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0),this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e,mustEnterInEditMode:!0})})}get width(){return this.data.rect[2]-this.data.rect[0]}get height(){return this.data.rect[3]-this.data.rect[1]}}class js extends at{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0}),this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,s=document.createElement("a");s.setAttribute("data-element-id",t.id);let i=!1;return t.url?(e.addLinkAttributes(s,t.url,t.newWindow),i=!0):t.action?(this._bindNamedAction(s,t.action,t.overlaidText),i=!0):t.attachment?(this.#e(s,t.attachment,t.overlaidText,t.attachmentDest),i=!0):t.setOCGState?(this.#s(s,t.setOCGState,t.overlaidText),i=!0):t.dest?(this._bindLink(s,t.dest,t.overlaidText),i=!0):(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(s,t),i=!0),t.resetForm?(this._bindResetFormAction(s,t.resetForm),i=!0):this.isTooltipOnly&&!i&&(this._bindLink(s,""),i=!0)),this.container.classList.add("linkAnnotation"),i&&this.container.append(s),this.container}#t(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e,s=""){t.href=this.linkService.getDestinationHash(e),t.onclick=()=>(e&&this.linkService.goToDestination(e),!1),(e||e==="")&&this.#t(),s&&(t.title=s)}_bindNamedAction(t,e,s=""){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeNamedAction(e),!1),s&&(t.title=s),this.#t()}#e(t,e,s="",i=null){t.href=this.linkService.getAnchorUrl(""),e.description?t.title=e.description:s&&(t.title=s),t.onclick=()=>(this.downloadManager?.openOrDownloadData(e.content,e.filename,i),!1),this.#t()}#s(t,e,s=""){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeSetOCGState(e),!1),s&&(t.title=s),this.#t()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const s=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const i of Object.keys(e.actions)){const n=s.get(i);n&&(t[n]=()=>(this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:i}}),!1))}e.overlaidText&&(t.title=e.overlaidText),t.onclick||(t.onclick=()=>!1),this.#t()}_bindResetFormAction(t,e){const s=t.onclick;if(s||(t.href=this.linkService.getAnchorUrl("")),this.#t(),!this._fieldObjects){V('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),s||(t.onclick=()=>!1);return}t.onclick=()=>{s?.();const{fields:i,refs:n,include:r}=e,a=[];if(i.length!==0||n.length!==0){const l=new Set(n);for(const d of i){const u=this._fieldObjects[d]||[];for(const{id:f}of u)l.add(f)}for(const d of Object.values(this._fieldObjects))for(const u of d)l.has(u.id)===r&&a.push(u)}else for(const l of Object.values(this._fieldObjects))a.push(...l);const o=this.annotationStorage,h=[];for(const l of a){const{id:d}=l;switch(h.push(d),l.type){case"text":{const f=l.defaultValue||"";o.setValue(d,{value:f});break}case"checkbox":case"radiobutton":{const f=l.defaultValue===l.exportValues;o.setValue(d,{value:f});break}case"combobox":case"listbox":{const f=l.defaultValue||"";o.setValue(d,{value:f});break}default:continue}const u=document.querySelector(`[data-element-id="${d}"]`);if(u){if(!se.has(u)){V(`_bindResetFormAction - element not allowed: ${d}`);continue}}else continue;u.dispatchEvent(new Event("resetform"))}return this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:h,name:"ResetForm"}}),!1}}}class Ir extends at{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.setAttribute("data-l10n-id","pdfjs-text-annotation-type"),t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name})),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class ne extends at{render(){return this.container}showElementAndHideCanvas(t){this.data.hasOwnCanvas&&(t.previousSibling?.nodeName==="CANVAS"&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){return mt.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,s,i,n){s.includes("mouse")?t.addEventListener(s,r=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:n(r),shift:r.shiftKey,modifier:this._getKeyModifier(r)}})}):t.addEventListener(s,r=>{if(s==="blur"){if(!e.focused||!r.relatedTarget)return;e.focused=!1}else if(s==="focus"){if(e.focused)return;e.focused=!0}n&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:n(r)}})})}_setEventListeners(t,e,s,i){for(const[n,r]of s)(r==="Action"||this.data.actions?.[r])&&((r==="Focus"||r==="Blur")&&(e||={focused:!1}),this._setEventListener(t,e,n,r,i),r==="Focus"&&!this.data.actions?.Blur?this._setEventListener(t,e,"blur","Blur",null):r==="Blur"&&!this.data.actions?.Focus&&this._setEventListener(t,e,"focus","Focus",null))}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=e===null?"transparent":D.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:s}=this.data.defaultAppearanceData,i=this.data.defaultAppearanceData.fontSize||Rr,n=t.style;let r;const a=2,o=h=>Math.round(10*h)/10;if(this.data.multiLine){const h=Math.abs(this.data.rect[3]-this.data.rect[1]-a),l=Math.round(h/(ps*i))||1,d=h/l;r=Math.min(i,o(d/ps))}else{const h=Math.abs(this.data.rect[3]-this.data.rect[1]-a);r=Math.min(i,o(h/ps))}n.fontSize=`calc(${r}px * var(--total-scale-factor))`,n.color=D.makeHexColor(s[0],s[1],s[2]),this.data.textAlignment!==null&&(n.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class Lr extends ne{constructor(t){const e=t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue;super(t,{isRenderable:e})}setPropertyOnSiblings(t,e,s,i){const n=this.annotationStorage;for(const r of this._getElementsByName(t.name,t.id))r.domElement&&(r.domElement[e]=s),n.setValue(r.id,{[i]:s})}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let s=null;if(this.renderForms){const i=t.getValue(e,{value:this.data.fieldValue});let n=i.value||"";const r=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;r&&n.length>r&&(n=n.slice(0,r));let a=i.formattedValue||this.data.textContent?.join(`
`)||null;a&&this.data.comb&&(a=a.replaceAll(/\s+/g,""));const o={userValue:n,formattedValue:a,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(s=document.createElement("textarea"),s.textContent=a??n,this.data.doNotScroll&&(s.style.overflowY="hidden")):(s=document.createElement("input"),s.type=this.data.password?"password":"text",s.setAttribute("value",a??n),this.data.doNotScroll&&(s.style.overflowX="hidden")),this.data.hasOwnCanvas&&(s.hidden=!0),se.add(s),s.setAttribute("data-element-id",e),s.disabled=this.data.readOnly,s.name=this.data.fieldName,s.tabIndex=0;const{datetimeFormat:h,datetimeType:l,timeStep:d}=this.data,u=!!l&&this.enableScripting;h&&(s.title=h),this._setRequired(s,this.data.required),r&&(s.maxLength=r),s.addEventListener("input",g=>{t.setValue(e,{value:g.target.value}),this.setPropertyOnSiblings(s,"value",g.target.value,"value"),o.formattedValue=null}),s.addEventListener("resetform",g=>{const p=this.data.defaultFieldValue??"";s.value=o.userValue=p,o.formattedValue=null});let f=g=>{const{formattedValue:p}=o;p!=null&&(g.target.value=p),g.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){s.addEventListener("focus",p=>{if(o.focused)return;const{target:b}=p;if(u&&(b.type=l,d&&(b.step=d)),o.userValue){const m=o.userValue;if(u)if(l==="time"){const _=new Date(m),y=[_.getHours(),_.getMinutes(),_.getSeconds()];b.value=y.map(w=>w.toString().padStart(2,"0")).join(":")}else b.value=new Date(m-Mr).toISOString().split(l==="date"?"T":".",1)[0];else b.value=m}o.lastCommittedValue=b.value,o.commitKey=1,this.data.actions?.Focus||(o.focused=!0)}),s.addEventListener("updatefromsandbox",p=>{this.showElementAndHideCanvas(p.target);const b={value(m){o.userValue=m.detail.value??"",u||t.setValue(e,{value:o.userValue.toString()}),m.target.value=o.userValue},formattedValue(m){const{formattedValue:_}=m.detail;o.formattedValue=_,_!=null&&m.target!==document.activeElement&&(m.target.value=_);const y={formattedValue:_};u&&(y.value=_),t.setValue(e,y)},selRange(m){m.target.setSelectionRange(...m.detail.selRange)},charLimit:m=>{const{charLimit:_}=m.detail,{target:y}=m;if(_===0){y.removeAttribute("maxLength");return}y.setAttribute("maxLength",_);let w=o.userValue;!w||w.length<=_||(w=w.slice(0,_),y.value=o.userValue=w,t.setValue(e,{value:w}),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:w,willCommit:!0,commitKey:1,selStart:y.selectionStart,selEnd:y.selectionEnd}}))}};this._dispatchEventFromSandbox(b,p)}),s.addEventListener("keydown",p=>{o.commitKey=1;let b=-1;if(p.key==="Escape"?b=0:p.key==="Enter"&&!this.data.multiLine?b=2:p.key==="Tab"&&(o.commitKey=3),b===-1)return;const{value:m}=p.target;o.lastCommittedValue!==m&&(o.lastCommittedValue=m,o.userValue=m,this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:m,willCommit:!0,commitKey:b,selStart:p.target.selectionStart,selEnd:p.target.selectionEnd}}))});const g=f;f=null,s.addEventListener("blur",p=>{if(!o.focused||!p.relatedTarget)return;this.data.actions?.Blur||(o.focused=!1);const{target:b}=p;let{value:m}=b;if(u){if(m&&l==="time"){const _=m.split(":").map(y=>parseInt(y,10));m=new Date(2e3,0,1,_[0],_[1],_[2]||0).valueOf(),b.step=""}else m=new Date(m).valueOf();b.type="text"}o.userValue=m,o.lastCommittedValue!==m&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:m,willCommit:!0,commitKey:o.commitKey,selStart:p.target.selectionStart,selEnd:p.target.selectionEnd}}),g(p)}),this.data.actions?.Keystroke&&s.addEventListener("beforeinput",p=>{o.lastCommittedValue=null;const{data:b,target:m}=p,{value:_,selectionStart:y,selectionEnd:w}=m;let v=y,E=w;switch(p.inputType){case"deleteWordBackward":{const A=_.substring(0,y).match(/\w*[^\w]*$/);A&&(v-=A[0].length);break}case"deleteWordForward":{const A=_.substring(y).match(/^[^\w]*\w*/);A&&(E+=A[0].length);break}case"deleteContentBackward":y===w&&(v-=1);break;case"deleteContentForward":y===w&&(E+=1);break}p.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:_,change:b||"",willCommit:!1,selStart:v,selEnd:E}})}),this._setEventListeners(s,o,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],p=>p.target.value)}if(f&&s.addEventListener("blur",f),this.data.comb){const p=(this.data.rect[2]-this.data.rect[0])/r;s.classList.add("comb"),s.style.letterSpacing=`calc(${p}px * var(--total-scale-factor) - 1ch)`}}else s=document.createElement("div"),s.textContent=this.data.fieldValue,s.style.verticalAlign="middle",s.style.display="table-cell",this.data.hasOwnCanvas&&(s.hidden=!0);return this._setTextStyle(s),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class Dr extends ne{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class Nr extends ne{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.exportValue===e.fieldValue}).value;typeof i=="string"&&(i=i!=="Off",t.setValue(s,{value:i})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const n=document.createElement("input");return se.add(n),n.setAttribute("data-element-id",s),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="checkbox",n.name=e.fieldName,i&&n.setAttribute("checked",!0),n.setAttribute("exportValue",e.exportValue),n.tabIndex=0,n.addEventListener("change",r=>{const{name:a,checked:o}=r.target;for(const h of this._getElementsByName(a,s)){const l=o&&h.exportValue===e.exportValue;h.domElement&&(h.domElement.checked=l),t.setValue(h.id,{value:l})}t.setValue(s,{value:o})}),n.addEventListener("resetform",r=>{const a=e.defaultFieldValue||"Off";r.target.checked=a===e.exportValue}),this.enableScripting&&this.hasJSActions&&(n.addEventListener("updatefromsandbox",r=>{const a={value(o){o.target.checked=o.detail.value!=="Off",t.setValue(s,{value:o.target.checked})}};this._dispatchEventFromSandbox(a,r)}),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],r=>r.target.checked)),this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class Xi extends ne{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.fieldValue===e.buttonValue}).value;if(typeof i=="string"&&(i=i!==e.buttonValue,t.setValue(s,{value:i})),i)for(const r of this._getElementsByName(e.fieldName,s))t.setValue(r.id,{value:!1});const n=document.createElement("input");if(se.add(n),n.setAttribute("data-element-id",s),n.disabled=e.readOnly,this._setRequired(n,this.data.required),n.type="radio",n.name=e.fieldName,i&&n.setAttribute("checked",!0),n.tabIndex=0,n.addEventListener("change",r=>{const{name:a,checked:o}=r.target;for(const h of this._getElementsByName(a,s))t.setValue(h.id,{value:!1});t.setValue(s,{value:o})}),n.addEventListener("resetform",r=>{const a=e.defaultFieldValue;r.target.checked=a!=null&&a===e.buttonValue}),this.enableScripting&&this.hasJSActions){const r=e.buttonValue;n.addEventListener("updatefromsandbox",a=>{const o={value:h=>{const l=r===h.detail.value;for(const d of this._getElementsByName(h.target.name)){const u=l&&d.id===s;d.domElement&&(d.domElement.checked=u),t.setValue(d.id,{value:u})}}};this._dispatchEventFromSandbox(o,a)}),this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],a=>a.target.checked)}return this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class Fr extends js{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",s=>{this._dispatchEventFromSandbox({},s)})),t}}class Or extends ne{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,s=t.getValue(e,{value:this.data.fieldValue}),i=document.createElement("select");se.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,this._setRequired(i,this.data.required),i.name=this.data.fieldName,i.tabIndex=0;let n=this.data.combo&&this.data.options.length>0;this.data.combo||(i.size=this.data.options.length,this.data.multiSelect&&(i.multiple=!0)),i.addEventListener("resetform",l=>{const d=this.data.defaultFieldValue;for(const u of i.options)u.selected=u.value===d});for(const l of this.data.options){const d=document.createElement("option");d.textContent=l.displayValue,d.value=l.exportValue,s.value.includes(l.exportValue)&&(d.setAttribute("selected",!0),n=!1),i.append(d)}let r=null;if(n){const l=document.createElement("option");l.value=" ",l.setAttribute("hidden",!0),l.setAttribute("selected",!0),i.prepend(l),r=()=>{l.remove(),i.removeEventListener("input",r),r=null},i.addEventListener("input",r)}const a=l=>{const d=l?"value":"textContent",{options:u,multiple:f}=i;return f?Array.prototype.filter.call(u,g=>g.selected).map(g=>g[d]):u.selectedIndex===-1?null:u[u.selectedIndex][d]};let o=a(!1);const h=l=>{const d=l.target.options;return Array.prototype.map.call(d,u=>({displayValue:u.textContent,exportValue:u.value}))};return this.enableScripting&&this.hasJSActions?(i.addEventListener("updatefromsandbox",l=>{const d={value(u){r?.();const f=u.detail.value,g=new Set(Array.isArray(f)?f:[f]);for(const p of i.options)p.selected=g.has(p.value);t.setValue(e,{value:a(!0)}),o=a(!1)},multipleSelection(u){i.multiple=!0},remove(u){const f=i.options,g=u.detail.remove;f[g].selected=!1,i.remove(g),f.length>0&&Array.prototype.findIndex.call(f,b=>b.selected)===-1&&(f[0].selected=!0),t.setValue(e,{value:a(!0),items:h(u)}),o=a(!1)},clear(u){for(;i.length!==0;)i.remove(0);t.setValue(e,{value:null,items:[]}),o=a(!1)},insert(u){const{index:f,displayValue:g,exportValue:p}=u.detail.insert,b=i.children[f],m=document.createElement("option");m.textContent=g,m.value=p,b?b.before(m):i.append(m),t.setValue(e,{value:a(!0),items:h(u)}),o=a(!1)},items(u){const{items:f}=u.detail;for(;i.length!==0;)i.remove(0);for(const g of f){const{displayValue:p,exportValue:b}=g,m=document.createElement("option");m.textContent=p,m.value=b,i.append(m)}i.options.length>0&&(i.options[0].selected=!0),t.setValue(e,{value:a(!0),items:h(u)}),o=a(!1)},indices(u){const f=new Set(u.detail.indices);for(const g of u.target.options)g.selected=f.has(g.index);t.setValue(e,{value:a(!0)}),o=a(!1)},editable(u){u.target.disabled=!u.detail.editable}};this._dispatchEventFromSandbox(d,l)}),i.addEventListener("input",l=>{const d=a(!0),u=a(!1);t.setValue(e,{value:d}),l.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:o,change:u,changeEx:d,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(i,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],l=>l.target.value)):i.addEventListener("input",function(l){t.setValue(e,{value:a(!0)})}),this.data.combo&&this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class Rs extends at{constructor(t){const{data:e,elements:s}=t;super(t,{isRenderable:at._hasPopupData(e)}),this.elements=s,this.popup=null}render(){const{container:t}=this;t.classList.add("popupAnnotation"),t.role="comment";const e=this.popup=new Br({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate||this.data.creationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),s=[];for(const i of this.elements)i.popup=e,i.container.ariaHasPopup="dialog",s.push(i.data.id),i.addHighlightArea();return this.container.setAttribute("aria-controls",s.map(i=>`${Ds}${i}`).join(",")),this.container}}class Br{#t=this.#R.bind(this);#e=this.#I.bind(this);#s=this.#x.bind(this);#i=this.#T.bind(this);#r=null;#n=null;#a=null;#o=null;#c=null;#h=null;#u=null;#l=!1;#p=null;#m=null;#f=null;#d=null;#g=null;#y=null;#b=null;#A=!1;constructor({container:t,color:e,elements:s,titleObj:i,modificationDate:n,contentsObj:r,richText:a,parent:o,rect:h,parentRect:l,open:d}){this.#n=t,this.#y=i,this.#a=r,this.#g=a,this.#h=o,this.#r=e,this.#d=h,this.#u=l,this.#c=s,this.#o=Es.toDateObject(n),this.trigger=s.flatMap(u=>u.getElementsToTriggerPopup()),this.#_(),this.#n.hidden=!0,d&&this.#T()}#_(){if(this.#m)return;this.#m=new AbortController;const{signal:t}=this.#m;for(const e of this.trigger)e.addEventListener("click",this.#i,{signal:t}),e.addEventListener("mouseenter",this.#s,{signal:t}),e.addEventListener("mouseleave",this.#e,{signal:t}),e.classList.add("popupTriggerArea");for(const e of this.#c)e.container?.addEventListener("keydown",this.#t,{signal:t})}render(){if(this.#p)return;const t=this.#p=document.createElement("div");if(t.className="popup",this.#r){const i=t.style.outlineColor=D.makeHexColor(...this.#r);t.style.backgroundColor=`color-mix(in srgb, ${i} 30%, white)`}const e=document.createElement("span");if(e.className="header",this.#y?.str){const i=document.createElement("span");i.className="title",e.append(i),{dir:i.dir,str:i.textContent}=this.#y}if(t.append(e),this.#o){const i=document.createElement("time");i.className="popupDate",i.setAttribute("data-l10n-id","pdfjs-annotation-date-time-string"),i.setAttribute("data-l10n-args",JSON.stringify({dateObj:this.#o.valueOf()})),i.dateTime=this.#o.toISOString(),e.append(i)}const s=this.#v;if(s)qi.render({xfaHtml:s,intent:"richText",div:t}),t.lastChild.classList.add("richText","popupContent");else{const i=this._formatContents(this.#a);t.append(i)}this.#n.append(t)}get#v(){const t=this.#g,e=this.#a;return t?.str&&(!e?.str||e.str===t.str)&&this.#g.html||null}get#E(){return this.#v?.attributes?.style?.fontSize||0}get#P(){return this.#v?.attributes?.style?.color||null}#S(t){const e=[],s={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},i={style:{color:this.#P,fontSize:this.#E?`calc(${this.#E}px * var(--total-scale-factor))`:""}};for(const n of t.split(`
`))e.push({name:"span",value:n,attributes:i});return s}_formatContents({str:t,dir:e}){const s=document.createElement("p");s.classList.add("popupContent"),s.dir=e;const i=t.split(/(?:\r\n?|\n)/);for(let n=0,r=i.length;n<r;++n){const a=i[n];s.append(document.createTextNode(a)),n<r-1&&s.append(document.createElement("br"))}return s}#R(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||(t.key==="Enter"||t.key==="Escape"&&this.#l)&&this.#T()}updateEdited({rect:t,popup:e,deleted:s}){if(s||e?.deleted){this.remove();return}this.#_(),this.#b||={contentsObj:this.#a,richText:this.#g},t&&(this.#f=null),e&&(this.#g=this.#S(e.text),this.#o=Es.toDateObject(e.date),this.#a=null),this.#p?.remove(),this.#p=null}resetEdited(){this.#b&&({contentsObj:this.#a,richText:this.#g}=this.#b,this.#b=null,this.#p?.remove(),this.#p=null,this.#f=null)}remove(){this.#m?.abort(),this.#m=null,this.#p?.remove(),this.#p=null,this.#A=!1,this.#l=!1;for(const t of this.trigger)t.classList.remove("popupTriggerArea")}#k(){if(this.#f!==null)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:s,pageX:i,pageY:n}}}=this.#h;let r=!!this.#u,a=r?this.#u:this.#d;for(const g of this.#c)if(!a||D.intersect(g.data.rect,a)!==null){a=g.data.rect,r=!0;break}const o=D.normalizeRect([a[0],t[3]-a[1]+t[1],a[2],t[3]-a[3]+t[1]]),l=r?a[2]-a[0]+5:0,d=o[0]+l,u=o[1];this.#f=[100*(d-i)/e,100*(u-n)/s];const{style:f}=this.#n;f.left=`${this.#f[0]}%`,f.top=`${this.#f[1]}%`}#T(){this.#l=!this.#l,this.#l?(this.#x(),this.#n.addEventListener("click",this.#i),this.#n.addEventListener("keydown",this.#t)):(this.#I(),this.#n.removeEventListener("click",this.#i),this.#n.removeEventListener("keydown",this.#t))}#x(){this.#p||this.render(),this.isVisible?this.#l&&this.#n.classList.add("focused"):(this.#k(),this.#n.hidden=!1,this.#n.style.zIndex=parseInt(this.#n.style.zIndex)+1e3)}#I(){this.#n.classList.remove("focused"),!(this.#l||!this.isVisible)&&(this.#n.hidden=!0,this.#n.style.zIndex=parseInt(this.#n.style.zIndex)-1e3)}forceHide(){this.#A=this.isVisible,this.#A&&(this.#n.hidden=!0)}maybeShow(){this.#_(),this.#A&&(this.#p||this.#x(),this.#A=!1,this.#n.hidden=!1)}get isVisible(){return this.#n.hidden===!1}}class Yi extends at{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=z.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent"),t.setAttribute("role","comment");for(const e of this.textContent){const s=document.createElement("span");s.textContent=e,t.append(s)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}class $r extends at{#t=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");const{data:t,width:e,height:s}=this,i=this.svgFactory.create(e,s,!0),n=this.#t=this.svgFactory.createElement("svg:line");return n.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]),n.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]),n.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]),n.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]),n.setAttribute("stroke-width",t.borderStyle.width||1),n.setAttribute("stroke","transparent"),n.setAttribute("fill","transparent"),i.append(n),this.container.append(i),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#t}addHighlightArea(){this.container.classList.add("highlightArea")}}class Hr extends at{#t=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");const{data:t,width:e,height:s}=this,i=this.svgFactory.create(e,s,!0),n=t.borderStyle.width,r=this.#t=this.svgFactory.createElement("svg:rect");return r.setAttribute("x",n/2),r.setAttribute("y",n/2),r.setAttribute("width",e-n),r.setAttribute("height",s-n),r.setAttribute("stroke-width",n||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),i.append(r),this.container.append(i),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#t}addHighlightArea(){this.container.classList.add("highlightArea")}}class zr extends at{#t=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");const{data:t,width:e,height:s}=this,i=this.svgFactory.create(e,s,!0),n=t.borderStyle.width,r=this.#t=this.svgFactory.createElement("svg:ellipse");return r.setAttribute("cx",e/2),r.setAttribute("cy",s/2),r.setAttribute("rx",e/2-n/2),r.setAttribute("ry",s/2-n/2),r.setAttribute("stroke-width",n||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),i.append(r),this.container.append(i),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#t}addHighlightArea(){this.container.classList.add("highlightArea")}}class Ki extends at{#t=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,vertices:e,borderStyle:s,popupRef:i},width:n,height:r}=this;if(!e)return this.container;const a=this.svgFactory.create(n,r,!0);let o=[];for(let l=0,d=e.length;l<d;l+=2){const u=e[l]-t[0],f=t[3]-e[l+1];o.push(`${u},${f}`)}o=o.join(" ");const h=this.#t=this.svgFactory.createElement(this.svgElementName);return h.setAttribute("points",o),h.setAttribute("stroke-width",s.width||1),h.setAttribute("stroke","transparent"),h.setAttribute("fill","transparent"),a.append(h),this.container.append(a),!i&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#t}addHighlightArea(){this.container.classList.add("highlightArea")}}class Vr extends Ki{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class Ur extends at{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class Gs extends at{#t=null;#e=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=this.data.it==="InkHighlight"?z.HIGHLIGHT:z.INK}#s(t,e){switch(t){case 90:return{transform:`rotate(90) translate(${-e[0]},${e[1]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};case 180:return{transform:`rotate(180) translate(${-e[2]},${e[1]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]};case 270:return{transform:`rotate(270) translate(${-e[2]},${e[3]}) scale(1,-1)`,width:e[3]-e[1],height:e[2]-e[0]};default:return{transform:`translate(${-e[0]},${e[3]}) scale(1,-1)`,width:e[2]-e[0],height:e[3]-e[1]}}}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,rotation:e,inkLists:s,borderStyle:i,popupRef:n}}=this,{transform:r,width:a,height:o}=this.#s(e,t),h=this.svgFactory.create(a,o,!0),l=this.#t=this.svgFactory.createElement("svg:g");h.append(l),l.setAttribute("stroke-width",i.width||1),l.setAttribute("stroke-linecap","round"),l.setAttribute("stroke-linejoin","round"),l.setAttribute("stroke-miterlimit",10),l.setAttribute("stroke","transparent"),l.setAttribute("fill","transparent"),l.setAttribute("transform",r);for(let d=0,u=s.length;d<u;d++){const f=this.svgFactory.createElement(this.svgElementName);this.#e.push(f),f.setAttribute("points",s[d].join(",")),l.append(f)}return!n&&this.hasPopupData&&this._createPopup(),this.container.append(h),this._editOnDoubleClick(),this.container}updateEdited(t){super.updateEdited(t);const{thickness:e,points:s,rect:i}=t,n=this.#t;if(e>=0&&n.setAttribute("stroke-width",e||1),s)for(let r=0,a=this.#e.length;r<a;r++)this.#e[r].setAttribute("points",s[r].join(","));if(i){const{transform:r,width:a,height:o}=this.#s(this.data.rotation,i);n.parentElement.setAttribute("viewBox",`0 0 ${a} ${o}`),n.setAttribute("transform",r)}}getElementsToTriggerPopup(){return this.#e}addHighlightArea(){this.container.classList.add("highlightArea")}}class Qi extends at{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}),this.annotationEditorType=z.HIGHLIGHT}render(){const{data:{overlaidText:t,popupRef:e}}=this;if(!e&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this._editOnDoubleClick(),t){const s=document.createElement("mark");s.classList.add("overlaidText"),s.textContent=t,this.container.append(s)}return this.container}}class jr extends at{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){const{data:{overlaidText:t,popupRef:e}}=this;if(!e&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),t){const s=document.createElement("u");s.classList.add("overlaidText"),s.textContent=t,this.container.append(s)}return this.container}}class Gr extends at{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){const{data:{overlaidText:t,popupRef:e}}=this;if(!e&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),t){const s=document.createElement("u");s.classList.add("overlaidText"),s.textContent=t,this.container.append(s)}return this.container}}class Wr extends at{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){const{data:{overlaidText:t,popupRef:e}}=this;if(!e&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),t){const s=document.createElement("s");s.classList.add("overlaidText"),s.textContent=t,this.container.append(s)}return this.container}}class Zi extends at{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.annotationEditorType=z.STAMP}render(){return this.container.classList.add("stampAnnotation"),this.container.setAttribute("role","img"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}class qr extends at{#t=null;constructor(t){super(t,{isRenderable:!0});const{file:e}=this.data;this.filename=e.filename,this.content=e.content,this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,...e})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:t,data:e}=this;let s;e.hasAppearance||e.fillAlpha===0?s=document.createElement("div"):(s=document.createElement("img"),s.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(e.name)?"paperclip":"pushpin"}.svg`,e.fillAlpha&&e.fillAlpha<1&&(s.style=`filter: opacity(${Math.round(e.fillAlpha*100)}%);`)),s.addEventListener("dblclick",this.#e.bind(this)),this.#t=s;const{isMac:i}=mt.platform;return t.addEventListener("keydown",n=>{n.key==="Enter"&&(i?n.metaKey:n.ctrlKey)&&this.#e()}),!e.popupRef&&this.hasPopupData?this._createPopup():s.classList.add("popupTriggerArea"),t.append(s),t}getElementsToTriggerPopup(){return this.#t}addHighlightArea(){this.container.classList.add("highlightArea")}#e(){this.downloadManager?.openOrDownloadData(this.content,this.filename)}}class Ws{#t=null;#e=null;#s=new Map;#i=null;constructor({div:t,accessibilityManager:e,annotationCanvasMap:s,annotationEditorUIManager:i,page:n,viewport:r,structTreeLayer:a}){this.div=t,this.#t=e,this.#e=s,this.#i=a||null,this.page=n,this.viewport=r,this.zIndex=0,this._annotationEditorUIManager=i}hasEditableAnnotations(){return this.#s.size>0}async#r(t,e,s){const i=t.firstChild||t,n=i.id=`${Ds}${e}`,r=await this.#i?.getAriaAttributes(n);if(r)for(const[a,o]of r)i.setAttribute(a,o);s?s.at(-1).container.after(t):(this.div.append(t),this.#t?.moveElementInDOM(this.div,t,i,!1))}async render(t){const{annotations:e}=t,s=this.div;ee(s,this.viewport);const i=new Map,n={data:null,layer:s,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:t.renderForms!==!1,svgFactory:new Je,annotationStorage:t.annotationStorage||new $s,enableScripting:t.enableScripting===!0,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const r of e){if(r.noHTML)continue;const a=r.annotationType===lt.POPUP;if(a){const l=i.get(r.id);if(!l)continue;n.elements=l}else if(r.rect[2]===r.rect[0]||r.rect[3]===r.rect[1])continue;n.data=r;const o=wi.create(n);if(!o.isRenderable)continue;if(!a&&r.popupRef){const l=i.get(r.popupRef);l?l.push(o):i.set(r.popupRef,[o])}const h=o.render();r.hidden&&(h.style.visibility="hidden"),await this.#r(h,r.id,n.elements),o._isEditable&&(this.#s.set(o.data.id,o),this._annotationEditorUIManager?.renderAnnotationElement(o))}this.#n()}async addLinkAnnotations(t,e){const s={data:null,layer:this.div,linkService:e,svgFactory:new Je,parent:this};for(const i of t){i.borderStyle||=Ws._defaultBorderStyle,s.data=i;const n=wi.create(s);if(!n.isRenderable)continue;const r=n.render();await this.#r(r,i.id,null)}}update({viewport:t}){const e=this.div;this.viewport=t,ee(e,{rotation:t.rotation}),this.#n(),e.hidden=!1}#n(){if(!this.#e)return;const t=this.div;for(const[e,s]of this.#e){const i=t.querySelector(`[data-annotation-id="${e}"]`);if(!i)continue;s.className="annotationContent";const{firstChild:n}=i;n?n.nodeName==="CANVAS"?n.replaceWith(s):n.classList.contains("annotationContent")?n.after(s):n.before(s):i.append(s);const r=this.#s.get(e);r&&(r._hasNoCanvas?(this._annotationEditorUIManager?.setMissingCanvas(e,i.id,s),r._hasNoCanvas=!1):r.canvas=s)}this.#e.clear()}getEditableAnnotations(){return Array.from(this.#s.values())}getEditableAnnotation(t){return this.#s.get(t)}static get _defaultBorderStyle(){return j(this,"_defaultBorderStyle",Object.freeze({width:1,rawWidth:1,style:re.SOLID,dashArray:[3],horizontalCornerRadius:0,verticalCornerRadius:0}))}}const Ge=/\r\n?|\n/g;class ut extends N{#t;#e="";#s=`${this.id}-editor`;#i=null;#r;_colorPicker=null;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){const t=ut.prototype,e=n=>n.isEmpty(),s=Kt.TRANSLATE_SMALL,i=Kt.TRANSLATE_BIG;return j(this,"_keyboardManager",new Ne([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-s,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-i,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[s,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[i,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-s],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-i],checker:e}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,s],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,i],checker:e}]]))}static _type="freetext";static _editorType=z.FREETEXT;constructor(t){super({...t,name:"freeTextEditor"}),this.#t=t.color||ut._defaultColor||N._defaultLineColor,this.#r=t.fontSize||ut._defaultFontSize,this.annotationElementId||this._uiManager.a11yAlert("pdfjs-editor-freetext-added-alert")}static initialize(t,e){N.initialize(t,e);const s=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(s.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case W.FREETEXT_SIZE:ut._defaultFontSize=e;break;case W.FREETEXT_COLOR:ut._defaultColor=e;break}}updateParams(t,e){switch(t){case W.FREETEXT_SIZE:this.#n(e);break;case W.FREETEXT_COLOR:this.#a(e);break}}static get defaultPropertiesToUpdate(){return[[W.FREETEXT_SIZE,ut._defaultFontSize],[W.FREETEXT_COLOR,ut._defaultColor||N._defaultLineColor]]}get propertiesToUpdate(){return[[W.FREETEXT_SIZE,this.#r],[W.FREETEXT_COLOR,this.#t]]}get toolbarButtons(){return this._colorPicker||=new Pe(this),[["colorPicker",this._colorPicker]]}get colorType(){return W.FREETEXT_COLOR}get colorValue(){return this.#t}#n(t){const e=i=>{this.editorDiv.style.fontSize=`calc(${i}px * var(--total-scale-factor))`,this.translate(0,-(i-this.#r)*this.parentScale),this.#r=i,this.#c()},s=this.#r;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:W.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#a(t){const e=i=>{this.#t=this.editorDiv.style.color=i,this._colorPicker?.update(i)},s=this.#t;this.addCommands({cmd:e.bind(this,t),undo:e.bind(this,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:W.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){const t=this.parentScale;return[-ut._internalPadding*t,-(ut._internalPadding+this.#r)*t]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){if(!super.enableEditMode())return!1;this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),this.#i=new AbortController;const t=this._uiManager.combinedSignal(this.#i);return this.editorDiv.addEventListener("keydown",this.editorDivKeydown.bind(this),{signal:t}),this.editorDiv.addEventListener("focus",this.editorDivFocus.bind(this),{signal:t}),this.editorDiv.addEventListener("blur",this.editorDivBlur.bind(this),{signal:t}),this.editorDiv.addEventListener("input",this.editorDivInput.bind(this),{signal:t}),this.editorDiv.addEventListener("paste",this.editorDivPaste.bind(this),{signal:t}),!0}disableEditMode(){return super.disableEditMode()?(this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",this.#s),this._isDraggable=!0,this.#i?.abort(),this.#i=null,this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freetextEditing"),!0):!1}focusin(t){this._focusEventsAllowed&&(super.focusin(t),t.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(t){this.width||(this.enableEditMode(),t&&this.editorDiv.focus(),this._initialOptions?.isCentered&&this.center(),this._initialOptions=null)}isEmpty(){return!this.editorDiv||this.editorDiv.innerText.trim()===""}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freetextEditing")),super.remove()}#o(){const t=[];this.editorDiv.normalize();let e=null;for(const s of this.editorDiv.childNodes)e?.nodeType===Node.TEXT_NODE&&s.nodeName==="BR"||(t.push(ut.#h(s)),e=s);return t.join(`
`)}#c(){const[t,e]=this.parentDimensions;let s;if(this.isAttachedToDOM)s=this.div.getBoundingClientRect();else{const{currentLayer:i,div:n}=this,r=n.style.display,a=n.classList.contains("hidden");n.classList.remove("hidden"),n.style.display="hidden",i.div.append(this.div),s=n.getBoundingClientRect(),n.remove(),n.style.display=r,n.classList.toggle("hidden",a)}this.rotation%180===this.parentRotation%180?(this.width=s.width/t,this.height=s.height/e):(this.width=s.height/t,this.height=s.width/e),this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const t=this.#e,e=this.#e=this.#o().trimEnd();if(t===e)return;const s=i=>{if(this.#e=i,!i){this.remove();return}this.#u(),this._uiManager.rebuild(this),this.#c()};this.addCommands({cmd:()=>{s(e)},undo:()=>{s(t)},mustExec:!1}),this.#c()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}keydown(t){t.target===this.div&&t.key==="Enter"&&(this.enterInEditMode(),t.preventDefault())}editorDivKeydown(t){ut._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}get canChangeContent(){return!0}render(){if(this.div)return this.div;let t,e;(this._isCopy||this.annotationElementId)&&(t=this.x,e=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",this.#s),this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text2"),this.editorDiv.setAttribute("data-l10n-attrs","default-content"),this.enableEditing(),this.editorDiv.contentEditable=!0;const{style:s}=this.editorDiv;if(s.fontSize=`calc(${this.#r}px * var(--total-scale-factor))`,s.color=this.#t,this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),this._isCopy||this.annotationElementId){const[i,n]=this.parentDimensions;if(this.annotationElementId){const{position:r}=this._initialData;let[a,o]=this.getInitialTranslation();[a,o]=this.pageTranslationToScreen(a,o);const[h,l]=this.pageDimensions,[d,u]=this.pageTranslation;let f,g;switch(this.rotation){case 0:f=t+(r[0]-d)/h,g=e+this.height-(r[1]-u)/l;break;case 90:f=t+(r[0]-d)/h,g=e-(r[1]-u)/l,[a,o]=[o,-a];break;case 180:f=t-this.width+(r[0]-d)/h,g=e-(r[1]-u)/l,[a,o]=[-a,-o];break;case 270:f=t+(r[0]-d-this.height*l)/h,g=e+(r[1]-u-this.width*h)/l,[a,o]=[-o,a];break}this.setAt(f*i,g*n,a,o)}else this._moveAfterPaste(t,e);this.#u(),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}static#h(t){return(t.nodeType===Node.TEXT_NODE?t.nodeValue:t.innerText).replaceAll(Ge,"")}editorDivPaste(t){const e=t.clipboardData||window.clipboardData,{types:s}=e;if(s.length===1&&s[0]==="text/plain")return;t.preventDefault();const i=ut.#p(e.getData("text")||"").replaceAll(Ge,`
`);if(!i)return;const n=window.getSelection();if(!n.rangeCount)return;this.editorDiv.normalize(),n.deleteFromDocument();const r=n.getRangeAt(0);if(!i.includes(`
`)){r.insertNode(document.createTextNode(i)),this.editorDiv.normalize(),n.collapseToStart();return}const{startContainer:a,startOffset:o}=r,h=[],l=[];if(a.nodeType===Node.TEXT_NODE){const f=a.parentElement;if(l.push(a.nodeValue.slice(o).replaceAll(Ge,"")),f!==this.editorDiv){let g=h;for(const p of this.editorDiv.childNodes){if(p===f){g=l;continue}g.push(ut.#h(p))}}h.push(a.nodeValue.slice(0,o).replaceAll(Ge,""))}else if(a===this.editorDiv){let f=h,g=0;for(const p of this.editorDiv.childNodes)g++===o&&(f=l),f.push(ut.#h(p))}this.#e=`${h.join(`
`)}${i}${l.join(`
`)}`,this.#u();const d=new Range;let u=Math.sumPrecise(h.map(f=>f.length));for(const{firstChild:f}of this.editorDiv.childNodes)if(f.nodeType===Node.TEXT_NODE){const g=f.nodeValue.length;if(u<=g){d.setStart(f,u),d.setEnd(f,u);break}u-=g}n.removeAllRanges(),n.addRange(d)}#u(){if(this.editorDiv.replaceChildren(),!!this.#e)for(const t of this.#e.split(`
`)){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br")),this.editorDiv.append(e)}}#l(){return this.#e.replaceAll(" "," ")}static#p(t){return t.replaceAll(" "," ")}get contentDiv(){return this.editorDiv}static async deserialize(t,e,s){let i=null;if(t instanceof Yi){const{data:{defaultAppearanceData:{fontSize:r,fontColor:a},rect:o,rotation:h,id:l,popupRef:d,contentsObj:u},textContent:f,textPosition:g,parent:{page:{pageNumber:p}}}=t;if(!f||f.length===0)return null;i=t={annotationType:z.FREETEXT,color:Array.from(a),fontSize:r,value:f.join(`
`),position:g,pageIndex:p-1,rect:o.slice(0),rotation:h,annotationElementId:l,id:l,deleted:!1,popupRef:d,comment:u?.str||null}}const n=await super.deserialize(t,e,s);return n.#r=t.fontSize,n.#t=D.makeHexColor(...t.color),n.#e=ut.#p(t.value),n._initialData=i,t.comment&&n.setCommentData(t.comment),n}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const e=ut._internalPadding*this.parentScale,s=this.getRect(e,e),i=N._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#t),n={annotationType:z.FREETEXT,color:i,fontSize:this.#r,value:this.#l(),pageIndex:this.pageIndex,rect:s,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return this.addComment(n),t?(n.isCopy=!0,n):this.annotationElementId&&!this.#m(n)?null:(n.id=this.annotationElementId,n)}#m(t){const{value:e,fontSize:s,color:i,pageIndex:n}=this._initialData;return this.hasEditedComment||this._hasBeenMoved||t.value!==e||t.fontSize!==s||t.color.some((r,a)=>r!==i[a])||t.pageIndex!==n}renderAnnotationElement(t){const e=super.renderAnnotationElement(t),{style:s}=e;s.fontSize=`calc(${this.#r}px * var(--total-scale-factor))`,s.color=this.#t,e.replaceChildren();for(const r of this.#e.split(`
`)){const a=document.createElement("div");a.append(r?document.createTextNode(r):document.createElement("br")),e.append(a)}const i=ut._internalPadding*this.parentScale,n={rect:this.getRect(i,i)};return n.popup=this.hasEditedComment?this.comment:{text:this.#e},t.updateEdited(n),e}resetAnnotationElement(t){super.resetAnnotationElement(t),t.resetEdited()}}class M{static PRECISION=1e-4;toSVGPath(){Q("Abstract method `toSVGPath` must be implemented.")}get box(){Q("Abstract getter `box` must be implemented.")}serialize(t,e){Q("Abstract method `serialize` must be implemented.")}static _rescale(t,e,s,i,n,r){r||=new Float32Array(t.length);for(let a=0,o=t.length;a<o;a+=2)r[a]=e+t[a]*i,r[a+1]=s+t[a+1]*n;return r}static _rescaleAndSwap(t,e,s,i,n,r){r||=new Float32Array(t.length);for(let a=0,o=t.length;a<o;a+=2)r[a]=e+t[a+1]*i,r[a+1]=s+t[a]*n;return r}static _translate(t,e,s,i){i||=new Float32Array(t.length);for(let n=0,r=t.length;n<r;n+=2)i[n]=e+t[n],i[n+1]=s+t[n+1];return i}static svgRound(t){return Math.round(t*1e4)}static _normalizePoint(t,e,s,i,n){switch(n){case 90:return[1-e/s,t/i];case 180:return[1-t/s,1-e/i];case 270:return[e/s,1-t/i];default:return[t/s,e/i]}}static _normalizePagePoint(t,e,s){switch(s){case 90:return[1-e,t];case 180:return[1-t,1-e];case 270:return[e,1-t];default:return[t,e]}}static createBezierPoints(t,e,s,i,n,r){return[(t+5*s)/6,(e+5*i)/6,(5*s+n)/6,(5*i+r)/6,(s+n)/2,(i+r)/2]}}class Qt{#t;#e=[];#s;#i;#r=[];#n=new Float32Array(18);#a;#o;#c;#h;#u;#l;#p=[];static#m=8;static#f=2;static#d=Qt.#m+Qt.#f;constructor({x:t,y:e},s,i,n,r,a=0){this.#t=s,this.#l=n*i,this.#i=r,this.#n.set([NaN,NaN,NaN,NaN,t,e],6),this.#s=a,this.#h=Qt.#m*i,this.#c=Qt.#d*i,this.#u=i,this.#p.push(t,e)}isEmpty(){return isNaN(this.#n[8])}#g(){const t=this.#n.subarray(4,6),e=this.#n.subarray(16,18),[s,i,n,r]=this.#t;return[(this.#a+(t[0]-e[0])/2-s)/n,(this.#o+(t[1]-e[1])/2-i)/r,(this.#a+(e[0]-t[0])/2-s)/n,(this.#o+(e[1]-t[1])/2-i)/r]}add({x:t,y:e}){this.#a=t,this.#o=e;const[s,i,n,r]=this.#t;let[a,o,h,l]=this.#n.subarray(8,12);const d=t-h,u=e-l,f=Math.hypot(d,u);if(f<this.#c)return!1;const g=f-this.#h,p=g/f,b=p*d,m=p*u;let _=a,y=o;a=h,o=l,h+=b,l+=m,this.#p?.push(t,e);const w=-m/g,v=b/g,E=w*this.#l,A=v*this.#l;return this.#n.set(this.#n.subarray(2,8),0),this.#n.set([h+E,l+A],4),this.#n.set(this.#n.subarray(14,18),12),this.#n.set([h-E,l-A],16),isNaN(this.#n[6])?(this.#r.length===0&&(this.#n.set([a+E,o+A],2),this.#r.push(NaN,NaN,NaN,NaN,(a+E-s)/n,(o+A-i)/r),this.#n.set([a-E,o-A],14),this.#e.push(NaN,NaN,NaN,NaN,(a-E-s)/n,(o-A-i)/r)),this.#n.set([_,y,a,o,h,l],6),!this.isEmpty()):(this.#n.set([_,y,a,o,h,l],6),Math.abs(Math.atan2(y-o,_-a)-Math.atan2(m,b))<Math.PI/2?([a,o,h,l]=this.#n.subarray(2,6),this.#r.push(NaN,NaN,NaN,NaN,((a+h)/2-s)/n,((o+l)/2-i)/r),[a,o,_,y]=this.#n.subarray(14,18),this.#e.push(NaN,NaN,NaN,NaN,((_+a)/2-s)/n,((y+o)/2-i)/r),!0):([_,y,a,o,h,l]=this.#n.subarray(0,6),this.#r.push(((_+5*a)/6-s)/n,((y+5*o)/6-i)/r,((5*a+h)/6-s)/n,((5*o+l)/6-i)/r,((a+h)/2-s)/n,((o+l)/2-i)/r),[h,l,a,o,_,y]=this.#n.subarray(12,18),this.#e.push(((_+5*a)/6-s)/n,((y+5*o)/6-i)/r,((5*a+h)/6-s)/n,((5*o+l)/6-i)/r,((a+h)/2-s)/n,((o+l)/2-i)/r),!0))}toSVGPath(){if(this.isEmpty())return"";const t=this.#r,e=this.#e;if(isNaN(this.#n[6])&&!this.isEmpty())return this.#y();const s=[];s.push(`M${t[4]} ${t[5]}`);for(let i=6;i<t.length;i+=6)isNaN(t[i])?s.push(`L${t[i+4]} ${t[i+5]}`):s.push(`C${t[i]} ${t[i+1]} ${t[i+2]} ${t[i+3]} ${t[i+4]} ${t[i+5]}`);this.#A(s);for(let i=e.length-6;i>=6;i-=6)isNaN(e[i])?s.push(`L${e[i+4]} ${e[i+5]}`):s.push(`C${e[i]} ${e[i+1]} ${e[i+2]} ${e[i+3]} ${e[i+4]} ${e[i+5]}`);return this.#b(s),s.join(" ")}#y(){const[t,e,s,i]=this.#t,[n,r,a,o]=this.#g();return`M${(this.#n[2]-t)/s} ${(this.#n[3]-e)/i} L${(this.#n[4]-t)/s} ${(this.#n[5]-e)/i} L${n} ${r} L${a} ${o} L${(this.#n[16]-t)/s} ${(this.#n[17]-e)/i} L${(this.#n[14]-t)/s} ${(this.#n[15]-e)/i} Z`}#b(t){const e=this.#e;t.push(`L${e[4]} ${e[5]} Z`)}#A(t){const[e,s,i,n]=this.#t,r=this.#n.subarray(4,6),a=this.#n.subarray(16,18),[o,h,l,d]=this.#g();t.push(`L${(r[0]-e)/i} ${(r[1]-s)/n} L${o} ${h} L${l} ${d} L${(a[0]-e)/i} ${(a[1]-s)/n}`)}newFreeDrawOutline(t,e,s,i,n,r){return new Ji(t,e,s,i,n,r)}getOutlines(){const t=this.#r,e=this.#e,s=this.#n,[i,n,r,a]=this.#t,o=new Float32Array((this.#p?.length??0)+2);for(let d=0,u=o.length-2;d<u;d+=2)o[d]=(this.#p[d]-i)/r,o[d+1]=(this.#p[d+1]-n)/a;if(o[o.length-2]=(this.#a-i)/r,o[o.length-1]=(this.#o-n)/a,isNaN(s[6])&&!this.isEmpty())return this.#_(o);const h=new Float32Array(this.#r.length+24+this.#e.length);let l=t.length;for(let d=0;d<l;d+=2){if(isNaN(t[d])){h[d]=h[d+1]=NaN;continue}h[d]=t[d],h[d+1]=t[d+1]}l=this.#E(h,l);for(let d=e.length-6;d>=6;d-=6)for(let u=0;u<6;u+=2){if(isNaN(e[d+u])){h[l]=h[l+1]=NaN,l+=2;continue}h[l]=e[d+u],h[l+1]=e[d+u+1],l+=2}return this.#v(h,l),this.newFreeDrawOutline(h,o,this.#t,this.#u,this.#s,this.#i)}#_(t){const e=this.#n,[s,i,n,r]=this.#t,[a,o,h,l]=this.#g(),d=new Float32Array(36);return d.set([NaN,NaN,NaN,NaN,(e[2]-s)/n,(e[3]-i)/r,NaN,NaN,NaN,NaN,(e[4]-s)/n,(e[5]-i)/r,NaN,NaN,NaN,NaN,a,o,NaN,NaN,NaN,NaN,h,l,NaN,NaN,NaN,NaN,(e[16]-s)/n,(e[17]-i)/r,NaN,NaN,NaN,NaN,(e[14]-s)/n,(e[15]-i)/r],0),this.newFreeDrawOutline(d,t,this.#t,this.#u,this.#s,this.#i)}#v(t,e){const s=this.#e;return t.set([NaN,NaN,NaN,NaN,s[4],s[5]],e),e+=6}#E(t,e){const s=this.#n.subarray(4,6),i=this.#n.subarray(16,18),[n,r,a,o]=this.#t,[h,l,d,u]=this.#g();return t.set([NaN,NaN,NaN,NaN,(s[0]-n)/a,(s[1]-r)/o,NaN,NaN,NaN,NaN,h,l,NaN,NaN,NaN,NaN,d,u,NaN,NaN,NaN,NaN,(i[0]-n)/a,(i[1]-r)/o],e),e+=24}}class Ji extends M{#t;#e=new Float32Array(4);#s;#i;#r;#n;#a;constructor(t,e,s,i,n,r){super(),this.#a=t,this.#r=e,this.#t=s,this.#n=i,this.#s=n,this.#i=r,this.lastPoint=[NaN,NaN],this.#o(r);const[a,o,h,l]=this.#e;for(let d=0,u=t.length;d<u;d+=2)t[d]=(t[d]-a)/h,t[d+1]=(t[d+1]-o)/l;for(let d=0,u=e.length;d<u;d+=2)e[d]=(e[d]-a)/h,e[d+1]=(e[d+1]-o)/l}toSVGPath(){const t=[`M${this.#a[4]} ${this.#a[5]}`];for(let e=6,s=this.#a.length;e<s;e+=6){if(isNaN(this.#a[e])){t.push(`L${this.#a[e+4]} ${this.#a[e+5]}`);continue}t.push(`C${this.#a[e]} ${this.#a[e+1]} ${this.#a[e+2]} ${this.#a[e+3]} ${this.#a[e+4]} ${this.#a[e+5]}`)}return t.push("Z"),t.join(" ")}serialize([t,e,s,i],n){const r=s-t,a=i-e;let o,h;switch(n){case 0:o=M._rescale(this.#a,t,i,r,-a),h=M._rescale(this.#r,t,i,r,-a);break;case 90:o=M._rescaleAndSwap(this.#a,t,e,r,a),h=M._rescaleAndSwap(this.#r,t,e,r,a);break;case 180:o=M._rescale(this.#a,s,e,-r,a),h=M._rescale(this.#r,s,e,-r,a);break;case 270:o=M._rescaleAndSwap(this.#a,s,i,-r,-a),h=M._rescaleAndSwap(this.#r,s,i,-r,-a);break}return{outline:Array.from(o),points:[Array.from(h)]}}#o(t){const e=this.#a;let s=e[4],i=e[5];const n=[s,i,s,i];let r=s,a=i;const o=t?Math.max:Math.min;for(let l=6,d=e.length;l<d;l+=6){const u=e[l+4],f=e[l+5];if(isNaN(e[l]))D.pointBoundingBox(u,f,n),a<f?(r=u,a=f):a===f&&(r=o(r,u));else{const g=[1/0,1/0,-1/0,-1/0];D.bezierBoundingBox(s,i,...e.slice(l,l+6),g),D.rectBoundingBox(...g,n),a<g[3]?(r=g[2],a=g[3]):a===g[3]&&(r=o(r,g[2]))}s=u,i=f}const h=this.#e;h[0]=n[0]-this.#s,h[1]=n[1]-this.#s,h[2]=n[2]-n[0]+2*this.#s,h[3]=n[3]-n[1]+2*this.#s,this.lastPoint=[r,a]}get box(){return this.#e}newOutliner(t,e,s,i,n,r=0){return new Qt(t,e,s,i,n,r)}getNewOutline(t,e){const[s,i,n,r]=this.#e,[a,o,h,l]=this.#t,d=n*h,u=r*l,f=s*h+a,g=i*l+o,p=this.newOutliner({x:this.#r[0]*d+f,y:this.#r[1]*u+g},this.#t,this.#n,t,this.#i,e??this.#s);for(let b=2;b<this.#r.length;b+=2)p.add({x:this.#r[b]*d+f,y:this.#r[b+1]*u+g});return p.getOutlines()}}class Ms{#t;#e;#s=[];#i=[];constructor(t,e=0,s=0,i=!0){const n=[1/0,1/0,-1/0,-1/0],r=10**-4;for(const{x:f,y:g,width:p,height:b}of t){const m=Math.floor((f-e)/r)*r,_=Math.ceil((f+p+e)/r)*r,y=Math.floor((g-e)/r)*r,w=Math.ceil((g+b+e)/r)*r,v=[m,y,w,!0],E=[_,y,w,!1];this.#s.push(v,E),D.rectBoundingBox(m,y,_,w,n)}const a=n[2]-n[0]+2*s,o=n[3]-n[1]+2*s,h=n[0]-s,l=n[1]-s,d=this.#s.at(i?-1:-2),u=[d[0],d[2]];for(const f of this.#s){const[g,p,b]=f;f[0]=(g-h)/a,f[1]=(p-l)/o,f[2]=(b-l)/o}this.#t=new Float32Array([h,l,a,o]),this.#e=u}getOutlines(){this.#s.sort((e,s)=>e[0]-s[0]||e[1]-s[1]||e[2]-s[2]);const t=[];for(const e of this.#s)e[3]?(t.push(...this.#c(e)),this.#a(e)):(this.#o(e),t.push(...this.#c(e)));return this.#r(t)}#r(t){const e=[],s=new Set;for(const r of t){const[a,o,h]=r;e.push([a,o,r],[a,h,r])}e.sort((r,a)=>r[1]-a[1]||r[0]-a[0]);for(let r=0,a=e.length;r<a;r+=2){const o=e[r][2],h=e[r+1][2];o.push(h),h.push(o),s.add(o),s.add(h)}const i=[];let n;for(;s.size>0;){const r=s.values().next().value;let[a,o,h,l,d]=r;s.delete(r);let u=a,f=o;for(n=[a,h],i.push(n);;){let g;if(s.has(l))g=l;else if(s.has(d))g=d;else break;s.delete(g),[a,o,h,l,d]=g,u!==a&&(n.push(u,f,a,f===o?o:h),u=a),f=f===o?h:o}n.push(u,f)}return new Xr(i,this.#t,this.#e)}#n(t){const e=this.#i;let s=0,i=e.length-1;for(;s<=i;){const n=s+i>>1,r=e[n][0];if(r===t)return n;r<t?s=n+1:i=n-1}return i+1}#a([,t,e]){const s=this.#n(t);this.#i.splice(s,0,[t,e])}#o([,t,e]){const s=this.#n(t);for(let i=s;i<this.#i.length;i++){const[n,r]=this.#i[i];if(n!==t)break;if(n===t&&r===e){this.#i.splice(i,1);return}}for(let i=s-1;i>=0;i--){const[n,r]=this.#i[i];if(n!==t)break;if(n===t&&r===e){this.#i.splice(i,1);return}}}#c(t){const[e,s,i]=t,n=[[e,s,i]],r=this.#n(i);for(let a=0;a<r;a++){const[o,h]=this.#i[a];for(let l=0,d=n.length;l<d;l++){const[,u,f]=n[l];if(!(h<=u||f<=o)){if(u>=o){if(f>h)n[l][1]=h;else{if(d===1)return[];n.splice(l,1),l--,d--}continue}n[l][2]=o,f>h&&n.push([e,h,f])}}}return n}}class Xr extends M{#t;#e;constructor(t,e,s){super(),this.#e=t,this.#t=e,this.lastPoint=s}toSVGPath(){const t=[];for(const e of this.#e){let[s,i]=e;t.push(`M${s} ${i}`);for(let n=2;n<e.length;n+=2){const r=e[n],a=e[n+1];r===s?(t.push(`V${a}`),i=a):a===i&&(t.push(`H${r}`),s=r)}t.push("Z")}return t.join(" ")}serialize([t,e,s,i],n){const r=[],a=s-t,o=i-e;for(const h of this.#e){const l=new Array(h.length);for(let d=0;d<h.length;d+=2)l[d]=t+h[d]*a,l[d+1]=i-h[d+1]*o;r.push(l)}return r}get box(){return this.#t}get classNamesForOutlining(){return["highlightOutline"]}}class Is extends Qt{newFreeDrawOutline(t,e,s,i,n,r){return new Yr(t,e,s,i,n,r)}}class Yr extends Ji{newOutliner(t,e,s,i,n,r=0){return new Is(t,e,s,i,n,r)}}class rt extends N{#t=null;#e=0;#s;#i=null;#r=null;#n=null;#a=null;#o=0;#c=null;#h=null;#u=null;#l=!1;#p=null;#m;#f=null;#d="";#g;#y="";static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=12;static _type="highlight";static _editorType=z.HIGHLIGHT;static _freeHighlightId=-1;static _freeHighlight=null;static _freeHighlightClipId="";static get _keyboardManager(){const t=rt.prototype;return j(this,"_keyboardManager",new Ne([[["ArrowLeft","mac+ArrowLeft"],t._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],t._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],t._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],t._moveCaret,{args:[3]}]]))}constructor(t){super({...t,name:"highlightEditor"}),this.color=t.color||rt._defaultColor,this.#g=t.thickness||rt._defaultThickness,this.#m=t.opacity||rt._defaultOpacity,this.#s=t.boxes||null,this.#y=t.methodOfCreation||"",this.#d=t.text||"",this._isDraggable=!1,this.defaultL10nId="pdfjs-editor-highlight-editor",t.highlightId>-1?(this.#l=!0,this.#A(t),this.#S()):this.#s&&(this.#t=t.anchorNode,this.#e=t.anchorOffset,this.#a=t.focusNode,this.#o=t.focusOffset,this.#b(),this.#S(),this.rotate(this.rotation)),this.annotationElementId||this._uiManager.a11yAlert("pdfjs-editor-highlight-added-alert")}get telemetryInitialData(){return{action:"added",type:this.#l?"free_highlight":"highlight",color:this._uiManager.getNonHCMColorName(this.color),thickness:this.#g,methodOfCreation:this.#y}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.getNonHCMColorName(this.color)}}get commentColor(){return this.color}static computeTelemetryFinalData(t){return{numberOfColors:t.get("color").size}}#b(){const t=new Ms(this.#s,.001);this.#h=t.getOutlines(),[this.x,this.y,this.width,this.height]=this.#h.box;const e=new Ms(this.#s,.0025,.001,this._uiManager.direction==="ltr");this.#n=e.getOutlines();const{lastPoint:s}=this.#n;this.#p=[(s[0]-this.x)/this.width,(s[1]-this.y)/this.height]}#A({highlightOutlines:t,highlightId:e,clipPathId:s}){this.#h=t;const i=1.5;if(this.#n=t.getNewOutline(this.#g/2+i,.0025),e>=0)this.#u=e,this.#i=s,this.parent.drawLayer.finalizeDraw(e,{bbox:t.box,path:{d:t.toSVGPath()}}),this.#f=this.parent.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:!0},bbox:this.#n.box,path:{d:this.#n.toSVGPath()}},!0);else if(this.parent){const l=this.parent.viewport.rotation;this.parent.drawLayer.updateProperties(this.#u,{bbox:rt.#R(this.#h.box,(l-this.rotation+360)%360),path:{d:t.toSVGPath()}}),this.parent.drawLayer.updateProperties(this.#f,{bbox:rt.#R(this.#n.box,l),path:{d:this.#n.toSVGPath()}})}const[n,r,a,o]=t.box;switch(this.rotation){case 0:this.x=n,this.y=r,this.width=a,this.height=o;break;case 90:{const[l,d]=this.parentDimensions;this.x=r,this.y=1-n,this.width=a*d/l,this.height=o*l/d;break}case 180:this.x=1-n,this.y=1-r,this.width=a,this.height=o;break;case 270:{const[l,d]=this.parentDimensions;this.x=1-r,this.y=n,this.width=a*d/l,this.height=o*l/d;break}}const{lastPoint:h}=this.#n;this.#p=[(h[0]-n)/a,(h[1]-r)/o]}static initialize(t,e){N.initialize(t,e),rt._defaultColor||=e.highlightColors?.values().next().value||"#fff066"}static updateDefaultParams(t,e){switch(t){case W.HIGHLIGHT_COLOR:rt._defaultColor=e;break;case W.HIGHLIGHT_THICKNESS:rt._defaultThickness=e;break}}translateInPage(t,e){}get toolbarPosition(){return this.#p}updateParams(t,e){switch(t){case W.HIGHLIGHT_COLOR:this.#_(e);break;case W.HIGHLIGHT_THICKNESS:this.#v(e);break}}static get defaultPropertiesToUpdate(){return[[W.HIGHLIGHT_COLOR,rt._defaultColor],[W.HIGHLIGHT_THICKNESS,rt._defaultThickness]]}get propertiesToUpdate(){return[[W.HIGHLIGHT_COLOR,this.color||rt._defaultColor],[W.HIGHLIGHT_THICKNESS,this.#g||rt._defaultThickness],[W.HIGHLIGHT_FREE,this.#l]]}#_(t){const e=(n,r)=>{this.color=n,this.#m=r,this.parent?.drawLayer.updateProperties(this.#u,{root:{fill:n,"fill-opacity":r}}),this.#r?.updateColor(n)},s=this.color,i=this.#m;this.addCommands({cmd:e.bind(this,t,rt._defaultOpacity),undo:e.bind(this,s,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:W.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"color_changed",color:this._uiManager.getNonHCMColorName(t)},!0)}#v(t){const e=this.#g,s=i=>{this.#g=i,this.#E(i)};this.addCommands({cmd:s.bind(this,t),undo:s.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:W.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"thickness_changed",thickness:t},!0)}get toolbarButtons(){return this._uiManager.highlightColors?[["colorPicker",this.#r=new kt({editor:this})]]:super.toolbarButtons}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(this.#x())}getBaseTranslation(){return[0,0]}getRect(t,e){return super.getRect(t,e,this.#x())}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this),t&&this.div.focus()}remove(){this.#P(),this._reportTelemetry({action:"deleted"}),super.remove()}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.#S(),this.isAttachedToDOM||this.parent.add(this)))}setParent(t){let e=!1;this.parent&&!t?this.#P():t&&(this.#S(t),e=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(t),this.show(this._isVisible),e&&this.select()}#E(t){if(!this.#l)return;this.#A({highlightOutlines:this.#h.getNewOutline(t/2)}),this.fixAndSetPosition();const[e,s]=this.parentDimensions;this.setDims(this.width*e,this.height*s)}#P(){this.#u===null||!this.parent||(this.parent.drawLayer.remove(this.#u),this.#u=null,this.parent.drawLayer.remove(this.#f),this.#f=null)}#S(t=this.parent){this.#u===null&&({id:this.#u,clipPathId:this.#i}=t.drawLayer.draw({bbox:this.#h.box,root:{viewBox:"0 0 1 1",fill:this.color,"fill-opacity":this.#m},rootClass:{highlight:!0,free:this.#l},path:{d:this.#h.toSVGPath()}},!1,!0),this.#f=t.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:this.#l},bbox:this.#n.box,path:{d:this.#n.toSVGPath()}},this.#l),this.#c&&(this.#c.style.clipPath=this.#i))}static#R([t,e,s,i],n){switch(n){case 90:return[1-e-i,t,i,s];case 180:return[1-t-s,1-e-i,s,i];case 270:return[e,1-t-s,i,s]}return[t,e,s,i]}rotate(t){const{drawLayer:e}=this.parent;let s;this.#l?(t=(t-this.rotation+360)%360,s=rt.#R(this.#h.box,t)):s=rt.#R([this.x,this.y,this.width,this.height],t),e.updateProperties(this.#u,{bbox:s,root:{"data-main-rotation":t}}),e.updateProperties(this.#f,{bbox:rt.#R(this.#n.box,t),root:{"data-main-rotation":t}})}render(){if(this.div)return this.div;const t=super.render();this.#d&&(t.setAttribute("aria-label",this.#d),t.setAttribute("role","mark")),this.#l?t.classList.add("free"):this.div.addEventListener("keydown",this.#k.bind(this),{signal:this._uiManager._signal});const e=this.#c=document.createElement("div");t.append(e),e.setAttribute("aria-hidden","true"),e.className="internal",e.style.clipPath=this.#i;const[s,i]=this.parentDimensions;return this.setDims(this.width*s,this.height*i),Pi(this,this.#c,["pointerover","pointerleave"]),this.enableEditing(),t}pointerover(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#f,{rootClass:{hovered:!0}})}pointerleave(){this.isSelected||this.parent?.drawLayer.updateProperties(this.#f,{rootClass:{hovered:!1}})}#k(t){rt._keyboardManager.exec(this,t)}_moveCaret(t){switch(this.parent.unselect(this),t){case 0:case 2:this.#T(!0);break;case 1:case 3:this.#T(!1);break}}#T(t){if(!this.#t)return;const e=window.getSelection();t?e.setPosition(this.#t,this.#e):e.setPosition(this.#a,this.#o)}select(){super.select(),this.#f&&this.parent?.drawLayer.updateProperties(this.#f,{rootClass:{hovered:!1,selected:!0}})}unselect(){super.unselect(),this.#f&&(this.parent?.drawLayer.updateProperties(this.#f,{rootClass:{selected:!1}}),this.#l||this.#T(!1))}get _mustFixPosition(){return!this.#l}show(t=this._isVisible){super.show(t),this.parent&&(this.parent.drawLayer.updateProperties(this.#u,{rootClass:{hidden:!t}}),this.parent.drawLayer.updateProperties(this.#f,{rootClass:{hidden:!t}}))}#x(){return this.#l?this.rotation:0}#I(){if(this.#l)return null;const[t,e]=this.pageDimensions,[s,i]=this.pageTranslation,n=this.#s,r=new Float32Array(n.length*8);let a=0;for(const{x:o,y:h,width:l,height:d}of n){const u=o*t+s,f=(1-h)*e+i;r[a]=r[a+4]=u,r[a+1]=r[a+3]=f,r[a+2]=r[a+6]=u+l*t,r[a+5]=r[a+7]=f-d*e,a+=8}return r}#O(t){return this.#h.serialize(t,this.#x())}static startHighlighting(t,e,{target:s,x:i,y:n}){const{x:r,y:a,width:o,height:h}=s.getBoundingClientRect(),l=new AbortController,d=t.combinedSignal(l),u=f=>{l.abort(),this.#z(t,f)};window.addEventListener("blur",u,{signal:d}),window.addEventListener("pointerup",u,{signal:d}),window.addEventListener("pointerdown",ht,{capture:!0,passive:!1,signal:d}),window.addEventListener("contextmenu",Pt,{signal:d}),s.addEventListener("pointermove",this.#L.bind(this,t),{signal:d}),this._freeHighlight=new Is({x:i,y:n},[r,a,o,h],t.scale,this._defaultThickness/2,e,.001),{id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=t.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:this._defaultColor,"fill-opacity":this._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:this._freeHighlight.toSVGPath()}},!0,!0)}static#L(t,e){this._freeHighlight.add(e)&&t.drawLayer.updateProperties(this._freeHighlightId,{path:{d:this._freeHighlight.toSVGPath()}})}static#z(t,e){this._freeHighlight.isEmpty()?t.drawLayer.remove(this._freeHighlightId):t.createAndAddNewEditor(e,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"}),this._freeHighlightId=-1,this._freeHighlight=null,this._freeHighlightClipId=""}static async deserialize(t,e,s){let i=null;if(t instanceof Qi){const{data:{quadPoints:g,rect:p,rotation:b,id:m,color:_,opacity:y,popupRef:w,contentsObj:v},parent:{page:{pageNumber:E}}}=t;i=t={annotationType:z.HIGHLIGHT,color:Array.from(_),opacity:y,quadPoints:g,boxes:null,pageIndex:E-1,rect:p.slice(0),rotation:b,annotationElementId:m,id:m,deleted:!1,popupRef:w,comment:v?.str||null}}else if(t instanceof Gs){const{data:{inkLists:g,rect:p,rotation:b,id:m,color:_,borderStyle:{rawWidth:y},popupRef:w,contentsObj:v},parent:{page:{pageNumber:E}}}=t;i=t={annotationType:z.HIGHLIGHT,color:Array.from(_),thickness:y,inkLists:g,boxes:null,pageIndex:E-1,rect:p.slice(0),rotation:b,annotationElementId:m,id:m,deleted:!1,popupRef:w,comment:v?.str||null}}const{color:n,quadPoints:r,inkLists:a,opacity:o}=t,h=await super.deserialize(t,e,s);h.color=D.makeHexColor(...n),h.#m=o||1,a&&(h.#g=t.thickness),h._initialData=i,t.comment&&h.setCommentData(t.comment);const[l,d]=h.pageDimensions,[u,f]=h.pageTranslation;if(r){const g=h.#s=[];for(let p=0;p<r.length;p+=8)g.push({x:(r[p]-u)/l,y:1-(r[p+1]-f)/d,width:(r[p+2]-r[p])/l,height:(r[p+1]-r[p+5])/d});h.#b(),h.#S(),h.rotate(h.rotation)}else if(a){h.#l=!0;const g=a[0],p={x:g[0]-u,y:d-(g[1]-f)},b=new Is(p,[0,0,l,d],1,h.#g/2,!0,.001);for(let y=0,w=g.length;y<w;y+=2)p.x=g[y]-u,p.y=d-(g[y+1]-f),b.add(p);const{id:m,clipPathId:_}=e.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:h.color,"fill-opacity":h._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:b.toSVGPath()}},!0,!0);h.#A({highlightOutlines:b.getOutlines(),highlightId:m,clipPathId:_}),h.#S(),h.rotate(h.parentRotation)}return h}serialize(t=!1){if(this.isEmpty()||t)return null;if(this.deleted)return this.serializeDeleted();const e=this.getRect(0,0),s=N._colorManager.convert(this._uiManager.getNonHCMColor(this.color)),i={annotationType:z.HIGHLIGHT,color:s,opacity:this.#m,thickness:this.#g,quadPoints:this.#I(),outlines:this.#O(e),pageIndex:this.pageIndex,rect:e,rotation:this.#x(),structTreeParentId:this._structTreeParentId};return this.addComment(i),this.annotationElementId&&!this.#C(i)?null:(i.id=this.annotationElementId,i)}#C(t){const{color:e}=this._initialData;return this.hasEditedComment||t.color.some((s,i)=>s!==e[i])}renderAnnotationElement(t){const e={rect:this.getRect(0,0)};return this.hasEditedComment&&(e.popup=this.comment),t.updateEdited(e),null}static canCreateNewEmptyEditor(){return!1}}class tn{#t=Object.create(null);updateProperty(t,e){this[t]=e,this.updateSVGProperty(t,e)}updateProperties(t){if(t)for(const[e,s]of Object.entries(t))e.startsWith("_")||this.updateProperty(e,s)}updateSVGProperty(t,e){this.#t[t]=e}toSVGProperties(){const t=this.#t;return this.#t=Object.create(null),{root:t}}reset(){this.#t=Object.create(null)}updateAll(t=this){this.updateProperties(t)}clone(){Q("Not implemented")}}class B extends N{#t=null;#e;_colorPicker=null;_drawId=null;static _currentDrawId=-1;static _currentParent=null;static#s=null;static#i=null;static#r=null;static#n=NaN;static#a=null;static#o=null;static#c=NaN;static _INNER_MARGIN=3;constructor(t){super(t),this.#e=t.mustBeCommitted||!1,this._addOutlines(t)}_addOutlines(t){t.drawOutlines&&(this.#h(t),this.#p())}#h({drawOutlines:t,drawId:e,drawingOptions:s}){this.#t=t,this._drawingOptions||=s,this.annotationElementId||this._uiManager.a11yAlert(`pdfjs-editor-${this.editorType}-added-alert`),e>=0?(this._drawId=e,this.parent.drawLayer.finalizeDraw(e,t.defaultProperties)):this._drawId=this.#u(t,this.parent),this.#d(t.box)}#u(t,e){const{id:s}=e.drawLayer.draw(B._mergeSVGProperties(this._drawingOptions.toSVGProperties(),t.defaultSVGProperties),!1,!1);return s}static _mergeSVGProperties(t,e){const s=new Set(Object.keys(t));for(const[i,n]of Object.entries(e))s.has(i)?Object.assign(t[i],n):t[i]=n;return t}static getDefaultDrawingOptions(t){Q("Not implemented")}static get typesMap(){Q("Not implemented")}static get isDrawer(){return!0}static get supportMultipleDrawings(){return!1}static updateDefaultParams(t,e){const s=this.typesMap.get(t);s&&this._defaultDrawingOptions.updateProperty(s,e),this._currentParent&&(B.#s.updateProperty(s,e),this._currentParent.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}updateParams(t,e){const s=this.constructor.typesMap.get(t);s&&this._updateProperty(t,s,e)}static get defaultPropertiesToUpdate(){const t=[],e=this._defaultDrawingOptions;for(const[s,i]of this.typesMap)t.push([s,e[i]]);return t}get propertiesToUpdate(){const t=[],{_drawingOptions:e}=this;for(const[s,i]of this.constructor.typesMap)t.push([s,e[i]]);return t}_updateProperty(t,e,s){const i=this._drawingOptions,n=i[e],r=a=>{i.updateProperty(e,a);const o=this.#t.updateProperty(e,a);o&&this.#d(o),this.parent?.drawLayer.updateProperties(this._drawId,i.toSVGProperties()),t===this.colorType&&this._colorPicker?.update(a)};this.addCommands({cmd:r.bind(this,s),undo:r.bind(this,n),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:t,overwriteIfSameType:!0,keepUndo:!0})}_onResizing(){this.parent?.drawLayer.updateProperties(this._drawId,B._mergeSVGProperties(this.#t.getPathResizingSVGProperties(this.#f()),{bbox:this.#g()}))}_onResized(){this.parent?.drawLayer.updateProperties(this._drawId,B._mergeSVGProperties(this.#t.getPathResizedSVGProperties(this.#f()),{bbox:this.#g()}))}_onTranslating(t,e){this.parent?.drawLayer.updateProperties(this._drawId,{bbox:this.#g()})}_onTranslated(){this.parent?.drawLayer.updateProperties(this._drawId,B._mergeSVGProperties(this.#t.getPathTranslatedSVGProperties(this.#f(),this.parentDimensions),{bbox:this.#g()}))}_onStartDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!0}})}_onStopDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!1}})}commit(){super.commit(),this.disableEditMode(),this.disableEditing()}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}getBaseTranslation(){return[0,0]}get isResizable(){return!0}onceAdded(t){this.annotationElementId||this.parent.addUndoableEditor(this),this._isDraggable=!0,this.#e&&(this.#e=!1,this.commit(),this.parent.setSelected(this),t&&this.isOnScreen&&this.div.focus())}remove(){this.#l(),super.remove()}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.#p(),this.#d(this.#t.box),this.isAttachedToDOM||this.parent.add(this)))}setParent(t){let e=!1;this.parent&&!t?(this._uiManager.removeShouldRescale(this),this.#l()):t&&(this._uiManager.addShouldRescale(this),this.#p(t),e=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(t),e&&this.select()}#l(){this._drawId===null||!this.parent||(this.parent.drawLayer.remove(this._drawId),this._drawId=null,this._drawingOptions.reset())}#p(t=this.parent){if(!(this._drawId!==null&&this.parent===t)){if(this._drawId!==null){this.parent.drawLayer.updateParent(this._drawId,t.drawLayer);return}this._drawingOptions.updateAll(),this._drawId=this.#u(this.#t,t)}}#m([t,e,s,i]){const{parentDimensions:[n,r],rotation:a}=this;switch(a){case 90:return[e,1-t,s*(r/n),i*(n/r)];case 180:return[1-t,1-e,s,i];case 270:return[1-e,t,s*(r/n),i*(n/r)];default:return[t,e,s,i]}}#f(){const{x:t,y:e,width:s,height:i,parentDimensions:[n,r],rotation:a}=this;switch(a){case 90:return[1-e,t,s*(n/r),i*(r/n)];case 180:return[1-t,1-e,s,i];case 270:return[e,1-t,s*(n/r),i*(r/n)];default:return[t,e,s,i]}}#d(t){if([this.x,this.y,this.width,this.height]=this.#m(t),this.div){this.fixAndSetPosition();const[e,s]=this.parentDimensions;this.setDims(this.width*e,this.height*s)}this._onResized()}#g(){const{x:t,y:e,width:s,height:i,rotation:n,parentRotation:r,parentDimensions:[a,o]}=this;switch((n*4+r)/90){case 1:return[1-e-i,t,i,s];case 2:return[1-t-s,1-e-i,s,i];case 3:return[e,1-t-s,i,s];case 4:return[t,e-s*(a/o),i*(o/a),s*(a/o)];case 5:return[1-e,t,s*(a/o),i*(o/a)];case 6:return[1-t-i*(o/a),1-e,i*(o/a),s*(a/o)];case 7:return[e-s*(a/o),1-t-i*(o/a),s*(a/o),i*(o/a)];case 8:return[t-s,e-i,s,i];case 9:return[1-e,t-s,i,s];case 10:return[1-t,1-e,s,i];case 11:return[e-i,1-t,i,s];case 12:return[t-i*(o/a),e,i*(o/a),s*(a/o)];case 13:return[1-e-s*(a/o),t-i*(o/a),s*(a/o),i*(o/a)];case 14:return[1-t,1-e-s*(a/o),i*(o/a),s*(a/o)];case 15:return[e,1-t,s*(a/o),i*(o/a)];default:return[t,e,s,i]}}rotate(){this.parent&&this.parent.drawLayer.updateProperties(this._drawId,B._mergeSVGProperties({bbox:this.#g()},this.#t.updateRotation((this.parentRotation-this.rotation+360)%360)))}onScaleChanging(){this.parent&&this.#d(this.#t.updateParentDimensions(this.parentDimensions,this.parent.scale))}static onScaleChangingWhenDrawing(){}render(){if(this.div)return this.div;let t,e;this._isCopy&&(t=this.x,e=this.y);const s=super.render();s.classList.add("draw");const i=document.createElement("div");s.append(i),i.setAttribute("aria-hidden","true"),i.className="internal";const[n,r]=this.parentDimensions;return this.setDims(this.width*n,this.height*r),this._uiManager.addShouldRescale(this),this.disableEditing(),this._isCopy&&this._moveAfterPaste(t,e),s}static createDrawerInstance(t,e,s,i,n){Q("Not implemented")}static startDrawing(t,e,s,i){const{target:n,offsetX:r,offsetY:a,pointerId:o,pointerType:h}=i;if(B.#a&&B.#a!==h)return;const{viewport:{rotation:l}}=t,{width:d,height:u}=n.getBoundingClientRect(),f=B.#i=new AbortController,g=t.combinedSignal(f);if(B.#n||=o,B.#a??=h,window.addEventListener("pointerup",p=>{B.#n===p.pointerId?this._endDraw(p):B.#o?.delete(p.pointerId)},{signal:g}),window.addEventListener("pointercancel",p=>{B.#n===p.pointerId?this._currentParent.endDrawingSession():B.#o?.delete(p.pointerId)},{signal:g}),window.addEventListener("pointerdown",p=>{B.#a===p.pointerType&&((B.#o||=new Set).add(p.pointerId),B.#s.isCancellable()&&(B.#s.removeLastElement(),B.#s.isEmpty()?this._currentParent.endDrawingSession(!0):this._endDraw(null)))},{capture:!0,passive:!1,signal:g}),window.addEventListener("contextmenu",Pt,{signal:g}),n.addEventListener("pointermove",this._drawMove.bind(this),{signal:g}),n.addEventListener("touchmove",p=>{p.timeStamp===B.#c&&ht(p)},{signal:g}),t.toggleDrawing(),e._editorUndoBar?.hide(),B.#s){t.drawLayer.updateProperties(this._currentDrawId,B.#s.startNew(r,a,d,u,l));return}e.updateUIForDefaultProperties(this),B.#s=this.createDrawerInstance(r,a,d,u,l),B.#r=this.getDefaultDrawingOptions(),this._currentParent=t,{id:this._currentDrawId}=t.drawLayer.draw(this._mergeSVGProperties(B.#r.toSVGProperties(),B.#s.defaultSVGProperties),!0,!1)}static _drawMove(t){if(B.#c=-1,!B.#s)return;const{offsetX:e,offsetY:s,pointerId:i}=t;if(B.#n===i){if(B.#o?.size>=1){this._endDraw(t);return}this._currentParent.drawLayer.updateProperties(this._currentDrawId,B.#s.add(e,s)),B.#c=t.timeStamp,ht(t)}}static _cleanup(t){t&&(this._currentDrawId=-1,this._currentParent=null,B.#s=null,B.#r=null,B.#a=null,B.#c=NaN),B.#i&&(B.#i.abort(),B.#i=null,B.#n=NaN,B.#o=null)}static _endDraw(t){const e=this._currentParent;if(e){if(e.toggleDrawing(!0),this._cleanup(!1),t?.target===e.div&&e.drawLayer.updateProperties(this._currentDrawId,B.#s.end(t.offsetX,t.offsetY)),this.supportMultipleDrawings){const s=B.#s,i=this._currentDrawId,n=s.getLastElement();e.addCommands({cmd:()=>{e.drawLayer.updateProperties(i,s.setLastElement(n))},undo:()=>{e.drawLayer.updateProperties(i,s.removeLastElement())},mustExec:!1,type:W.DRAW_STEP});return}this.endDrawing(!1)}}static endDrawing(t){const e=this._currentParent;if(!e)return null;if(e.toggleDrawing(!0),e.cleanUndoStack(W.DRAW_STEP),!B.#s.isEmpty()){const{pageDimensions:[s,i],scale:n}=e,r=e.createAndAddNewEditor({offsetX:0,offsetY:0},!1,{drawId:this._currentDrawId,drawOutlines:B.#s.getOutlines(s*n,i*n,n,this._INNER_MARGIN),drawingOptions:B.#r,mustBeCommitted:!t});return this._cleanup(!0),r}return e.drawLayer.remove(this._currentDrawId),this._cleanup(!0),null}createDrawingOptions(t){}static deserializeDraw(t,e,s,i,n,r){Q("Not implemented")}static async deserialize(t,e,s){const{rawDims:{pageWidth:i,pageHeight:n,pageX:r,pageY:a}}=e.viewport,o=this.deserializeDraw(r,a,i,n,this._INNER_MARGIN,t),h=await super.deserialize(t,e,s);return h.createDrawingOptions(t),h.#h({drawOutlines:o}),h.#p(),h.onScaleChanging(),h.rotate(),h}serializeDraw(t){const[e,s]=this.pageTranslation,[i,n]=this.pageDimensions;return this.#t.serialize([e,s,i,n],t)}renderAnnotationElement(t){return t.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}}class Kr{#t=new Float64Array(6);#e;#s;#i;#r;#n;#a="";#o=0;#c=new Oe;#h;#u;constructor(t,e,s,i,n,r){this.#h=s,this.#u=i,this.#i=n,this.#r=r,[t,e]=this.#l(t,e);const a=this.#e=[NaN,NaN,NaN,NaN,t,e];this.#n=[t,e],this.#s=[{line:a,points:this.#n}],this.#t.set(a,0)}updateProperty(t,e){t==="stroke-width"&&(this.#r=e)}#l(t,e){return M._normalizePoint(t,e,this.#h,this.#u,this.#i)}isEmpty(){return!this.#s||this.#s.length===0}isCancellable(){return this.#n.length<=10}add(t,e){[t,e]=this.#l(t,e);const[s,i,n,r]=this.#t.subarray(2,6),a=t-n,o=e-r;return Math.hypot(this.#h*a,this.#u*o)<=2?null:(this.#n.push(t,e),isNaN(s)?(this.#t.set([n,r,t,e],2),this.#e.push(NaN,NaN,NaN,NaN,t,e),{path:{d:this.toSVGPath()}}):(isNaN(this.#t[0])&&this.#e.splice(6,6),this.#t.set([s,i,n,r,t,e],0),this.#e.push(...M.createBezierPoints(s,i,n,r,t,e)),{path:{d:this.toSVGPath()}}))}end(t,e){const s=this.add(t,e);return s||(this.#n.length===2?{path:{d:this.toSVGPath()}}:null)}startNew(t,e,s,i,n){this.#h=s,this.#u=i,this.#i=n,[t,e]=this.#l(t,e);const r=this.#e=[NaN,NaN,NaN,NaN,t,e];this.#n=[t,e];const a=this.#s.at(-1);return a&&(a.line=new Float32Array(a.line),a.points=new Float32Array(a.points)),this.#s.push({line:r,points:this.#n}),this.#t.set(r,0),this.#o=0,this.toSVGPath(),null}getLastElement(){return this.#s.at(-1)}setLastElement(t){return this.#s?(this.#s.push(t),this.#e=t.line,this.#n=t.points,this.#o=0,{path:{d:this.toSVGPath()}}):this.#c.setLastElement(t)}removeLastElement(){if(!this.#s)return this.#c.removeLastElement();this.#s.pop(),this.#a="";for(let t=0,e=this.#s.length;t<e;t++){const{line:s,points:i}=this.#s[t];this.#e=s,this.#n=i,this.#o=0,this.toSVGPath()}return{path:{d:this.#a}}}toSVGPath(){const t=M.svgRound(this.#e[4]),e=M.svgRound(this.#e[5]);if(this.#n.length===2)return this.#a=`${this.#a} M ${t} ${e} Z`,this.#a;if(this.#n.length<=6){const i=this.#a.lastIndexOf("M");this.#a=`${this.#a.slice(0,i)} M ${t} ${e}`,this.#o=6}if(this.#n.length===4){const i=M.svgRound(this.#e[10]),n=M.svgRound(this.#e[11]);return this.#a=`${this.#a} L ${i} ${n}`,this.#o=12,this.#a}const s=[];this.#o===0&&(s.push(`M ${t} ${e}`),this.#o=6);for(let i=this.#o,n=this.#e.length;i<n;i+=6){const[r,a,o,h,l,d]=this.#e.slice(i,i+6).map(M.svgRound);s.push(`C${r} ${a} ${o} ${h} ${l} ${d}`)}return this.#a+=s.join(" "),this.#o=this.#e.length,this.#a}getOutlines(t,e,s,i){const n=this.#s.at(-1);return n.line=new Float32Array(n.line),n.points=new Float32Array(n.points),this.#c.build(this.#s,t,e,s,this.#i,this.#r,i),this.#t=null,this.#e=null,this.#s=null,this.#a=null,this.#c}get defaultSVGProperties(){return{root:{viewBox:"0 0 10000 10000"},rootClass:{draw:!0},bbox:[0,0,1,1]}}}class Oe extends M{#t;#e=0;#s;#i;#r;#n;#a;#o;#c;build(t,e,s,i,n,r,a){this.#r=e,this.#n=s,this.#a=i,this.#o=n,this.#c=r,this.#s=a??0,this.#i=t,this.#l()}get thickness(){return this.#c}setLastElement(t){return this.#i.push(t),{path:{d:this.toSVGPath()}}}removeLastElement(){return this.#i.pop(),{path:{d:this.toSVGPath()}}}toSVGPath(){const t=[];for(const{line:e}of this.#i){if(t.push(`M${M.svgRound(e[4])} ${M.svgRound(e[5])}`),e.length===6){t.push("Z");continue}if(e.length===12&&isNaN(e[6])){t.push(`L${M.svgRound(e[10])} ${M.svgRound(e[11])}`);continue}for(let s=6,i=e.length;s<i;s+=6){const[n,r,a,o,h,l]=e.subarray(s,s+6).map(M.svgRound);t.push(`C${n} ${r} ${a} ${o} ${h} ${l}`)}}return t.join("")}serialize([t,e,s,i],n){const r=[],a=[],[o,h,l,d]=this.#u();let u,f,g,p,b,m,_,y,w;switch(this.#o){case 0:w=M._rescale,u=t,f=e+i,g=s,p=-i,b=t+o*s,m=e+(1-h-d)*i,_=t+(o+l)*s,y=e+(1-h)*i;break;case 90:w=M._rescaleAndSwap,u=t,f=e,g=s,p=i,b=t+h*s,m=e+o*i,_=t+(h+d)*s,y=e+(o+l)*i;break;case 180:w=M._rescale,u=t+s,f=e,g=-s,p=i,b=t+(1-o-l)*s,m=e+h*i,_=t+(1-o)*s,y=e+(h+d)*i;break;case 270:w=M._rescaleAndSwap,u=t+s,f=e+i,g=-s,p=-i,b=t+(1-h-d)*s,m=e+(1-o-l)*i,_=t+(1-h)*s,y=e+(1-o)*i;break}for(const{line:v,points:E}of this.#i)r.push(w(v,u,f,g,p,n?new Array(v.length):null)),a.push(w(E,u,f,g,p,n?new Array(E.length):null));return{lines:r,points:a,rect:[b,m,_,y]}}static deserialize(t,e,s,i,n,{paths:{lines:r,points:a},rotation:o,thickness:h}){const l=[];let d,u,f,g,p;switch(o){case 0:p=M._rescale,d=-t/s,u=e/i+1,f=1/s,g=-1/i;break;case 90:p=M._rescaleAndSwap,d=-e/i,u=-t/s,f=1/i,g=1/s;break;case 180:p=M._rescale,d=t/s+1,u=-e/i,f=-1/s,g=1/i;break;case 270:p=M._rescaleAndSwap,d=e/i+1,u=t/s+1,f=-1/i,g=-1/s;break}if(!r){r=[];for(const m of a){const _=m.length;if(_===2){r.push(new Float32Array([NaN,NaN,NaN,NaN,m[0],m[1]]));continue}if(_===4){r.push(new Float32Array([NaN,NaN,NaN,NaN,m[0],m[1],NaN,NaN,NaN,NaN,m[2],m[3]]));continue}const y=new Float32Array(3*(_-2));r.push(y);let[w,v,E,A]=m.subarray(0,4);y.set([NaN,NaN,NaN,NaN,w,v],0);for(let T=4;T<_;T+=2){const k=m[T],I=m[T+1];y.set(M.createBezierPoints(w,v,E,A,k,I),(T-2)*3),[w,v,E,A]=[E,A,k,I]}}}for(let m=0,_=r.length;m<_;m++)l.push({line:p(r[m].map(y=>y??NaN),d,u,f,g),points:p(a[m].map(y=>y??NaN),d,u,f,g)});const b=new this.prototype.constructor;return b.build(l,s,i,1,o,h,n),b}#h(t=this.#c){const e=this.#s+t/2*this.#a;return this.#o%180===0?[e/this.#r,e/this.#n]:[e/this.#n,e/this.#r]}#u(){const[t,e,s,i]=this.#t,[n,r]=this.#h(0);return[t+n,e+r,s-2*n,i-2*r]}#l(){const t=this.#t=new Float32Array([1/0,1/0,-1/0,-1/0]);for(const{line:i}of this.#i){if(i.length<=12){for(let a=4,o=i.length;a<o;a+=6)D.pointBoundingBox(i[a],i[a+1],t);continue}let n=i[4],r=i[5];for(let a=6,o=i.length;a<o;a+=6){const[h,l,d,u,f,g]=i.subarray(a,a+6);D.bezierBoundingBox(n,r,h,l,d,u,f,g,t),n=f,r=g}}const[e,s]=this.#h();t[0]=wt(t[0]-e,0,1),t[1]=wt(t[1]-s,0,1),t[2]=wt(t[2]+e,0,1),t[3]=wt(t[3]+s,0,1),t[2]-=t[0],t[3]-=t[1]}get box(){return this.#t}updateProperty(t,e){return t==="stroke-width"?this.#p(e):null}#p(t){const[e,s]=this.#h();this.#c=t;const[i,n]=this.#h(),[r,a]=[i-e,n-s],o=this.#t;return o[0]-=r,o[1]-=a,o[2]+=2*r,o[3]+=2*a,o}updateParentDimensions([t,e],s){const[i,n]=this.#h();this.#r=t,this.#n=e,this.#a=s;const[r,a]=this.#h(),o=r-i,h=a-n,l=this.#t;return l[0]-=o,l[1]-=h,l[2]+=2*o,l[3]+=2*h,l}updateRotation(t){return this.#e=t,{path:{transform:this.rotationTransform}}}get viewBox(){return this.#t.map(M.svgRound).join(" ")}get defaultProperties(){const[t,e]=this.#t;return{root:{viewBox:this.viewBox},path:{"transform-origin":`${M.svgRound(t)} ${M.svgRound(e)}`}}}get rotationTransform(){const[,,t,e]=this.#t;let s=0,i=0,n=0,r=0,a=0,o=0;switch(this.#e){case 90:i=e/t,n=-t/e,a=t;break;case 180:s=-1,r=-1,a=t,o=e;break;case 270:i=-e/t,n=t/e,o=e;break;default:return""}return`matrix(${s} ${i} ${n} ${r} ${M.svgRound(a)} ${M.svgRound(o)})`}getPathResizingSVGProperties([t,e,s,i]){const[n,r]=this.#h(),[a,o,h,l]=this.#t;if(Math.abs(h-n)<=M.PRECISION||Math.abs(l-r)<=M.PRECISION){const p=t+s/2-(a+h/2),b=e+i/2-(o+l/2);return{path:{"transform-origin":`${M.svgRound(t)} ${M.svgRound(e)}`,transform:`${this.rotationTransform} translate(${p} ${b})`}}}const d=(s-2*n)/(h-2*n),u=(i-2*r)/(l-2*r),f=h/s,g=l/i;return{path:{"transform-origin":`${M.svgRound(a)} ${M.svgRound(o)}`,transform:`${this.rotationTransform} scale(${f} ${g}) translate(${M.svgRound(n)} ${M.svgRound(r)}) scale(${d} ${u}) translate(${M.svgRound(-n)} ${M.svgRound(-r)})`}}}getPathResizedSVGProperties([t,e,s,i]){const[n,r]=this.#h(),a=this.#t,[o,h,l,d]=a;if(a[0]=t,a[1]=e,a[2]=s,a[3]=i,Math.abs(l-n)<=M.PRECISION||Math.abs(d-r)<=M.PRECISION){const b=t+s/2-(o+l/2),m=e+i/2-(h+d/2);for(const{line:_,points:y}of this.#i)M._translate(_,b,m,_),M._translate(y,b,m,y);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${M.svgRound(t)} ${M.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}const u=(s-2*n)/(l-2*n),f=(i-2*r)/(d-2*r),g=-u*(o+n)+t+n,p=-f*(h+r)+e+r;if(u!==1||f!==1||g!==0||p!==0)for(const{line:b,points:m}of this.#i)M._rescale(b,g,p,u,f,b),M._rescale(m,g,p,u,f,m);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${M.svgRound(t)} ${M.svgRound(e)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}getPathTranslatedSVGProperties([t,e],s){const[i,n]=s,r=this.#t,a=t-r[0],o=e-r[1];if(this.#r===i&&this.#n===n)for(const{line:h,points:l}of this.#i)M._translate(h,a,o,h),M._translate(l,a,o,l);else{const h=this.#r/i,l=this.#n/n;this.#r=i,this.#n=n;for(const{line:d,points:u}of this.#i)M._rescale(d,a,o,h,l,d),M._rescale(u,a,o,h,l,u);r[2]*=h,r[3]*=l}return r[0]=t,r[1]=e,{root:{viewBox:this.viewBox},path:{d:this.toSVGPath(),"transform-origin":`${M.svgRound(t)} ${M.svgRound(e)}`}}}get defaultSVGProperties(){const t=this.#t;return{root:{viewBox:this.viewBox},rootClass:{draw:!0},path:{d:this.toSVGPath(),"transform-origin":`${M.svgRound(t[0])} ${M.svgRound(t[1])}`,transform:this.rotationTransform||null},bbox:t}}}class ls extends tn{constructor(t){super(),this._viewParameters=t,super.updateProperties({fill:"none",stroke:N._defaultLineColor,"stroke-opacity":1,"stroke-width":1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":10})}updateSVGProperty(t,e){t==="stroke-width"&&(e??=this["stroke-width"],e*=this._viewParameters.realScale),super.updateSVGProperty(t,e)}clone(){const t=new ls(this._viewParameters);return t.updateAll(this),t}}class qs extends B{static _type="ink";static _editorType=z.INK;static _defaultDrawingOptions=null;constructor(t){super({...t,name:"inkEditor"}),this._willKeepAspectRatio=!0,this.defaultL10nId="pdfjs-editor-ink-editor"}static initialize(t,e){N.initialize(t,e),this._defaultDrawingOptions=new ls(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();return e.updateProperties(t),e}static get supportMultipleDrawings(){return!0}static get typesMap(){return j(this,"typesMap",new Map([[W.INK_THICKNESS,"stroke-width"],[W.INK_COLOR,"stroke"],[W.INK_OPACITY,"stroke-opacity"]]))}static createDrawerInstance(t,e,s,i,n){return new Kr(t,e,s,i,n,this._defaultDrawingOptions["stroke-width"])}static deserializeDraw(t,e,s,i,n,r){return Oe.deserialize(t,e,s,i,n,r)}static async deserialize(t,e,s){let i=null;if(t instanceof Gs){const{data:{inkLists:r,rect:a,rotation:o,id:h,color:l,opacity:d,borderStyle:{rawWidth:u},popupRef:f,contentsObj:g},parent:{page:{pageNumber:p}}}=t;i=t={annotationType:z.INK,color:Array.from(l),thickness:u,opacity:d,paths:{points:r},boxes:null,pageIndex:p-1,rect:a.slice(0),rotation:o,annotationElementId:h,id:h,deleted:!1,popupRef:f,comment:g?.str||null}}const n=await super.deserialize(t,e,s);return n._initialData=i,t.comment&&n.setCommentData(t.comment),n}get toolbarButtons(){return this._colorPicker||=new Pe(this),[["colorPicker",this._colorPicker]]}get colorType(){return W.INK_COLOR}get colorValue(){return this._drawingOptions.stroke}onScaleChanging(){if(!this.parent)return;super.onScaleChanging();const{_drawId:t,_drawingOptions:e,parent:s}=this;e.updateSVGProperty("stroke-width"),s.drawLayer.updateProperties(t,e.toSVGProperties())}static onScaleChangingWhenDrawing(){const t=this._currentParent;t&&(super.onScaleChangingWhenDrawing(),this._defaultDrawingOptions.updateSVGProperty("stroke-width"),t.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}createDrawingOptions({color:t,thickness:e,opacity:s}){this._drawingOptions=qs.getDefaultDrawingOptions({stroke:D.makeHexColor(...t),"stroke-width":e,"stroke-opacity":s})}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const{lines:e,points:s,rect:i}=this.serializeDraw(t),{_drawingOptions:{stroke:n,"stroke-opacity":r,"stroke-width":a}}=this,o={annotationType:z.INK,color:N._colorManager.convert(n),opacity:r,thickness:a,paths:{lines:e,points:s},pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return this.addComment(o),t?(o.isCopy=!0,o):this.annotationElementId&&!this.#t(o)?null:(o.id=this.annotationElementId,o)}#t(t){const{color:e,thickness:s,opacity:i,pageIndex:n}=this._initialData;return this.hasEditedComment||this._hasBeenMoved||this._hasBeenResized||t.color.some((r,a)=>r!==e[a])||t.thickness!==s||t.opacity!==i||t.pageIndex!==n}renderAnnotationElement(t){const{points:e,rect:s}=this.serializeDraw(!1),i={rect:s,thickness:this._drawingOptions["stroke-width"],points:e};return this.hasEditedComment&&(i.popup=this.comment),t.updateEdited(i),null}}class Ls extends Oe{toSVGPath(){let t=super.toSVGPath();return t.endsWith("Z")||(t+="Z"),t}}const We=8,Ae=3;class oe{static#t={maxDim:512,sigmaSFactor:.02,sigmaR:25,kernelSize:16};static#e(t,e,s,i){return s-=t,i-=e,s===0?i>0?0:4:s===1?i+6:2-i}static#s=new Int32Array([0,1,-1,1,-1,0,-1,-1,0,-1,1,-1,1,0,1,1]);static#i(t,e,s,i,n,r,a){const o=this.#e(s,i,n,r);for(let h=0;h<8;h++){const l=(-h+o-a+16)%8,d=this.#s[2*l],u=this.#s[2*l+1];if(t[(s+d)*e+(i+u)]!==0)return l}return-1}static#r(t,e,s,i,n,r,a){const o=this.#e(s,i,n,r);for(let h=0;h<8;h++){const l=(h+o+a+16)%8,d=this.#s[2*l],u=this.#s[2*l+1];if(t[(s+d)*e+(i+u)]!==0)return l}return-1}static#n(t,e,s,i){const n=t.length,r=new Int32Array(n);for(let l=0;l<n;l++)r[l]=t[l]<=i?1:0;for(let l=1;l<s-1;l++)r[l*e]=r[l*e+e-1]=0;for(let l=0;l<e;l++)r[l]=r[e*s-1-l]=0;let a=1,o;const h=[];for(let l=1;l<s-1;l++){o=1;for(let d=1;d<e-1;d++){const u=l*e+d,f=r[u];if(f===0)continue;let g=l,p=d;if(f===1&&r[u-1]===0)a+=1,p-=1;else if(f>=1&&r[u+1]===0)a+=1,p+=1,f>1&&(o=f);else{f!==1&&(o=Math.abs(f));continue}const b=[d,l],m=p===d+1,_={isHole:m,points:b,id:a,parent:0};h.push(_);let y;for(const L of h)if(L.id===o){y=L;break}y?y.isHole?_.parent=m?y.parent:o:_.parent=m?o:y.parent:_.parent=m?o:0;const w=this.#i(r,e,l,d,g,p,0);if(w===-1){r[u]=-a,r[u]!==1&&(o=Math.abs(r[u]));continue}let v=this.#s[2*w],E=this.#s[2*w+1];const A=l+v,T=d+E;g=A,p=T;let k=l,I=d;for(;;){const L=this.#r(r,e,k,I,g,p,1);v=this.#s[2*L],E=this.#s[2*L+1];const q=k+v,K=I+E;b.push(K,q);const G=k*e+I;if(r[G+1]===0?r[G]=-a:r[G]===1&&(r[G]=a),q===l&&K===d&&k===A&&I===T){r[u]!==1&&(o=Math.abs(r[u]));break}else g=k,p=I,k=q,I=K}}}return h}static#a(t,e,s,i){if(s-e<=4){for(let A=e;A<s-2;A+=2)i.push(t[A],t[A+1]);return}const n=t[e],r=t[e+1],a=t[s-4]-n,o=t[s-3]-r,h=Math.hypot(a,o),l=a/h,d=o/h,u=l*r-d*n,f=o/a,g=1/h,p=Math.atan(f),b=Math.cos(p),m=Math.sin(p),_=g*(Math.abs(b)+Math.abs(m)),y=g*(1-_+_**2),w=Math.max(Math.atan(Math.abs(m+b)*y),Math.atan(Math.abs(m-b)*y));let v=0,E=e;for(let A=e+2;A<s-2;A+=2){const T=Math.abs(u-l*t[A+1]+d*t[A]);T>v&&(E=A,v=T)}v>(h*w)**2?(this.#a(t,e,E+2,i),this.#a(t,E,s,i)):i.push(n,r)}static#o(t){const e=[],s=t.length;return this.#a(t,0,s,e),e.push(t[s-2],t[s-1]),e.length<=4?null:e}static#c(t,e,s,i,n,r){const a=new Float32Array(r**2),o=-2*i**2,h=r>>1;for(let p=0;p<r;p++){const b=(p-h)**2;for(let m=0;m<r;m++)a[p*r+m]=Math.exp((b+(m-h)**2)/o)}const l=new Float32Array(256),d=-2*n**2;for(let p=0;p<256;p++)l[p]=Math.exp(p**2/d);const u=t.length,f=new Uint8Array(u),g=new Uint32Array(256);for(let p=0;p<s;p++)for(let b=0;b<e;b++){const m=p*e+b,_=t[m];let y=0,w=0;for(let E=0;E<r;E++){const A=p+E-h;if(!(A<0||A>=s))for(let T=0;T<r;T++){const k=b+T-h;if(k<0||k>=e)continue;const I=t[A*e+k],L=a[E*r+T]*l[Math.abs(I-_)];y+=I*L,w+=L}}const v=f[m]=Math.round(y/w);g[v]++}return[f,g]}static#h(t){const e=new Uint32Array(256);for(const s of t)e[s]++;return e}static#u(t){const e=t.length,s=new Uint8ClampedArray(e>>2);let i=-1/0,n=1/0;for(let a=0,o=s.length;a<o;a++){const h=s[a]=t[a<<2];i=Math.max(i,h),n=Math.min(n,h)}const r=255/(i-n);for(let a=0,o=s.length;a<o;a++)s[a]=(s[a]-n)*r;return s}static#l(t){let e,s=-1/0,i=-1/0;const n=t.findIndex(o=>o!==0);let r=n,a=n;for(e=n;e<256;e++){const o=t[e];o>s&&(e-r>i&&(i=e-r,a=e-1),s=o,r=e)}for(e=a-1;e>=0&&!(t[e]>t[e+1]);e--);return e}static#p(t){const e=t,{width:s,height:i}=t,{maxDim:n}=this.#t;let r=s,a=i;if(s>n||i>n){let u=s,f=i,g=Math.log2(Math.max(s,i)/n);const p=Math.floor(g);g=g===p?p-1:p;for(let m=0;m<g;m++){r=Math.ceil(u/2),a=Math.ceil(f/2);const _=new OffscreenCanvas(r,a);_.getContext("2d").drawImage(t,0,0,u,f,0,0,r,a),u=r,f=a,t!==e&&t.close(),t=_.transferToImageBitmap()}const b=Math.min(n/r,n/a);r=Math.round(r*b),a=Math.round(a*b)}const h=new OffscreenCanvas(r,a).getContext("2d",{willReadFrequently:!0});h.fillStyle="white",h.fillRect(0,0,r,a),h.filter="grayscale(1)",h.drawImage(t,0,0,t.width,t.height,0,0,r,a);const l=h.getImageData(0,0,r,a).data;return[this.#u(l),r,a]}static extractContoursFromText(t,{fontFamily:e,fontStyle:s,fontWeight:i},n,r,a,o){let h=new OffscreenCanvas(1,1),l=h.getContext("2d",{alpha:!1});const d=200,u=l.font=`${s} ${i} ${d}px ${e}`,{actualBoundingBoxLeft:f,actualBoundingBoxRight:g,actualBoundingBoxAscent:p,actualBoundingBoxDescent:b,fontBoundingBoxAscent:m,fontBoundingBoxDescent:_,width:y}=l.measureText(t),w=1.5,v=Math.ceil(Math.max(Math.abs(f)+Math.abs(g)||0,y)*w),E=Math.ceil(Math.max(Math.abs(p)+Math.abs(b)||d,Math.abs(m)+Math.abs(_)||d)*w);h=new OffscreenCanvas(v,E),l=h.getContext("2d",{alpha:!0,willReadFrequently:!0}),l.font=u,l.filter="grayscale(1)",l.fillStyle="white",l.fillRect(0,0,v,E),l.fillStyle="black",l.fillText(t,v*(w-1)/2,E*(3-w)/2);const A=this.#u(l.getImageData(0,0,v,E).data),T=this.#h(A),k=this.#l(T),I=this.#n(A,v,E,k);return this.processDrawnLines({lines:{curves:I,width:v,height:E},pageWidth:n,pageHeight:r,rotation:a,innerMargin:o,mustSmooth:!0,areContours:!0})}static process(t,e,s,i,n){const[r,a,o]=this.#p(t),[h,l]=this.#c(r,a,o,Math.hypot(a,o)*this.#t.sigmaSFactor,this.#t.sigmaR,this.#t.kernelSize),d=this.#l(l),u=this.#n(h,a,o,d);return this.processDrawnLines({lines:{curves:u,width:a,height:o},pageWidth:e,pageHeight:s,rotation:i,innerMargin:n,mustSmooth:!0,areContours:!0})}static processDrawnLines({lines:t,pageWidth:e,pageHeight:s,rotation:i,innerMargin:n,mustSmooth:r,areContours:a}){i%180!==0&&([e,s]=[s,e]);const{curves:o,width:h,height:l}=t,d=t.thickness??0,u=[],f=Math.min(e/h,s/l),g=f/e,p=f/s,b=[];for(const{points:_}of o){const y=r?this.#o(_):_;if(!y)continue;b.push(y);const w=y.length,v=new Float32Array(w),E=new Float32Array(3*(w===2?2:w-2));if(u.push({line:E,points:v}),w===2){v[0]=y[0]*g,v[1]=y[1]*p,E.set([NaN,NaN,NaN,NaN,v[0],v[1]],0);continue}let[A,T,k,I]=y;A*=g,T*=p,k*=g,I*=p,v.set([A,T,k,I],0),E.set([NaN,NaN,NaN,NaN,A,T],0);for(let L=4;L<w;L+=2){const q=v[L]=y[L]*g,K=v[L+1]=y[L+1]*p;E.set(M.createBezierPoints(A,T,k,I,q,K),(L-2)*3),[A,T,k,I]=[k,I,q,K]}}if(u.length===0)return null;const m=a?new Ls:new Oe;return m.build(u,e,s,1,i,a?0:d,n),{outline:m,newCurves:b,areContours:a,thickness:d,width:h,height:l}}static async compressSignature({outlines:t,areContours:e,thickness:s,width:i,height:n}){let r=1/0,a=-1/0,o=0;for(const y of t){o+=y.length;for(let w=2,v=y.length;w<v;w++){const E=y[w]-y[w-2];r=Math.min(r,E),a=Math.max(a,E)}}let h;r>=-128&&a<=127?h=Int8Array:r>=-32768&&a<=32767?h=Int16Array:h=Int32Array;const l=t.length,d=We+Ae*l,u=new Uint32Array(d);let f=0;u[f++]=d*Uint32Array.BYTES_PER_ELEMENT+(o-2*l)*h.BYTES_PER_ELEMENT,u[f++]=0,u[f++]=i,u[f++]=n,u[f++]=e?0:1,u[f++]=Math.max(0,Math.floor(s??0)),u[f++]=l,u[f++]=h.BYTES_PER_ELEMENT;for(const y of t)u[f++]=y.length-2,u[f++]=y[0],u[f++]=y[1];const g=new CompressionStream("deflate-raw"),p=g.writable.getWriter();await p.ready,p.write(u);const b=h.prototype.constructor;for(const y of t){const w=new b(y.length-2);for(let v=2,E=y.length;v<E;v++)w[v-2]=y[v]-y[v-2];p.write(w)}p.close();const m=await new Response(g.readable).arrayBuffer(),_=new Uint8Array(m);return ki(_)}static async decompressSignature(t){try{const e=yn(t),{readable:s,writable:i}=new DecompressionStream("deflate-raw"),n=i.getWriter();await n.ready,n.write(e).then(async()=>{await n.ready,await n.close()}).catch(()=>{});let r=null,a=0;for await(const y of s)r||=new Uint8Array(new Uint32Array(y.buffer,0,4)[0]),r.set(y,a),a+=y.length;const o=new Uint32Array(r.buffer,0,r.length>>2),h=o[1];if(h!==0)throw new Error(`Invalid version: ${h}`);const l=o[2],d=o[3],u=o[4]===0,f=o[5],g=o[6],p=o[7],b=[],m=(We+Ae*g)*Uint32Array.BYTES_PER_ELEMENT;let _;switch(p){case Int8Array.BYTES_PER_ELEMENT:_=new Int8Array(r.buffer,m);break;case Int16Array.BYTES_PER_ELEMENT:_=new Int16Array(r.buffer,m);break;case Int32Array.BYTES_PER_ELEMENT:_=new Int32Array(r.buffer,m);break}a=0;for(let y=0;y<g;y++){const w=o[Ae*y+We],v=new Float32Array(w+2);b.push(v);for(let E=0;E<Ae-1;E++)v[E]=o[Ae*y+We+E+1];for(let E=0;E<w;E++)v[E+2]=v[E]+_[a++]}return{areContours:u,thickness:f,outlines:b,width:l,height:d}}catch(e){return V(`decompressSignature: ${e}`),null}}}class Xs extends tn{constructor(){super(),super.updateProperties({fill:N._defaultLineColor,"stroke-width":0})}clone(){const t=new Xs;return t.updateAll(this),t}}class Ys extends ls{constructor(t){super(t),super.updateProperties({stroke:N._defaultLineColor,"stroke-width":1})}clone(){const t=new Ys(this._viewParameters);return t.updateAll(this),t}}class Ot extends B{#t=!1;#e=null;#s=null;#i=null;static _type="signature";static _editorType=z.SIGNATURE;static _defaultDrawingOptions=null;constructor(t){super({...t,mustBeCommitted:!0,name:"signatureEditor"}),this._willKeepAspectRatio=!0,this.#s=t.signatureData||null,this.#e=null,this.defaultL10nId="pdfjs-editor-signature-editor1"}static initialize(t,e){N.initialize(t,e),this._defaultDrawingOptions=new Xs,this._defaultDrawnSignatureOptions=new Ys(e.viewParameters)}static getDefaultDrawingOptions(t){const e=this._defaultDrawingOptions.clone();return e.updateProperties(t),e}static get supportMultipleDrawings(){return!1}static get typesMap(){return j(this,"typesMap",new Map)}static get isDrawer(){return!1}get telemetryFinalData(){return{type:"signature",hasDescription:!!this.#e}}static computeTelemetryFinalData(t){const e=t.get("hasDescription");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}get isResizable(){return!0}onScaleChanging(){this._drawId!==null&&super.onScaleChanging()}render(){if(this.div)return this.div;let t,e;const{_isCopy:s}=this;if(s&&(this._isCopy=!1,t=this.x,e=this.y),super.render(),this._drawId===null)if(this.#s){const{lines:i,mustSmooth:n,areContours:r,description:a,uuid:o,heightInPage:h}=this.#s,{rawDims:{pageWidth:l,pageHeight:d},rotation:u}=this.parent.viewport,f=oe.processDrawnLines({lines:i,pageWidth:l,pageHeight:d,rotation:u,innerMargin:Ot._INNER_MARGIN,mustSmooth:n,areContours:r});this.addSignature(f,h,a,o)}else this.div.setAttribute("data-l10n-args",JSON.stringify({description:""})),this.div.hidden=!0,this._uiManager.getSignature(this);else this.div.setAttribute("data-l10n-args",JSON.stringify({description:this.#e||""}));return s&&(this._isCopy=!0,this._moveAfterPaste(t,e)),this.div}setUuid(t){this.#i=t,this.addEditToolbar()}getUuid(){return this.#i}get description(){return this.#e}set description(t){this.#e=t,this.div&&(this.div.setAttribute("data-l10n-args",JSON.stringify({description:t})),super.addEditToolbar().then(e=>{e?.updateEditSignatureButton(t)}))}getSignaturePreview(){const{newCurves:t,areContours:e,thickness:s,width:i,height:n}=this.#s,r=Math.max(i,n),a=oe.processDrawnLines({lines:{curves:t.map(o=>({points:o})),thickness:s,width:i,height:n},pageWidth:r,pageHeight:r,rotation:0,innerMargin:0,mustSmooth:!1,areContours:e});return{areContours:e,outline:a.outline}}get toolbarButtons(){return this._uiManager.signatureManager?[["editSignature",this._uiManager.signatureManager]]:super.toolbarButtons}addSignature(t,e,s,i){const{x:n,y:r}=this,{outline:a}=this.#s=t;this.#t=a instanceof Ls,this.description=s;let o;this.#t?o=Ot.getDefaultDrawingOptions():(o=Ot._defaultDrawnSignatureOptions.clone(),o.updateProperties({"stroke-width":a.thickness})),this._addOutlines({drawOutlines:a,drawingOptions:o});const[h,l]=this.parentDimensions,[,d]=this.pageDimensions;let u=e/d;u=u>=1?.5:u,this.width*=u/this.height,this.width>=1&&(u*=.9/this.width,this.width=.9),this.height=u,this.setDims(h*this.width,l*this.height),this.x=n,this.y=r,this.center(),this._onResized(),this.onScaleChanging(),this.rotate(),this._uiManager.addToAnnotationStorage(this),this.setUuid(i),this._reportTelemetry({action:"pdfjs.signature.inserted",data:{hasBeenSaved:!!i,hasDescription:!!s}}),this.div.hidden=!1}getFromImage(t){const{rawDims:{pageWidth:e,pageHeight:s},rotation:i}=this.parent.viewport;return oe.process(t,e,s,i,Ot._INNER_MARGIN)}getFromText(t,e){const{rawDims:{pageWidth:s,pageHeight:i},rotation:n}=this.parent.viewport;return oe.extractContoursFromText(t,e,s,i,n,Ot._INNER_MARGIN)}getDrawnSignature(t){const{rawDims:{pageWidth:e,pageHeight:s},rotation:i}=this.parent.viewport;return oe.processDrawnLines({lines:t,pageWidth:e,pageHeight:s,rotation:i,innerMargin:Ot._INNER_MARGIN,mustSmooth:!1,areContours:!1})}createDrawingOptions({areContours:t,thickness:e}){t?this._drawingOptions=Ot.getDefaultDrawingOptions():(this._drawingOptions=Ot._defaultDrawnSignatureOptions.clone(),this._drawingOptions.updateProperties({"stroke-width":e}))}serialize(t=!1){if(this.isEmpty())return null;const{lines:e,points:s,rect:i}=this.serializeDraw(t),{_drawingOptions:{"stroke-width":n}}=this,r={annotationType:z.SIGNATURE,isSignature:!0,areContours:this.#t,color:[0,0,0],thickness:this.#t?0:n,pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return this.addComment(r),t?(r.paths={lines:e,points:s},r.uuid=this.#i,r.isCopy=!0):r.lines=e,this.#e&&(r.accessibilityData={type:"Figure",alt:this.#e}),r}static deserializeDraw(t,e,s,i,n,r){return r.areContours?Ls.deserialize(t,e,s,i,n,r):Oe.deserialize(t,e,s,i,n,r)}static async deserialize(t,e,s){const i=await super.deserialize(t,e,s);return i.#t=t.areContours,i.description=t.accessibilityData?.alt||"",i.#i=t.uuid,i}}class Qr extends N{#t=null;#e=null;#s=null;#i=null;#r=null;#n="";#a=null;#o=!1;#c=null;#h=!1;#u=!1;static _type="stamp";static _editorType=z.STAMP;constructor(t){super({...t,name:"stampEditor"}),this.#i=t.bitmapUrl,this.#r=t.bitmapFile,this.defaultL10nId="pdfjs-editor-stamp-editor"}static initialize(t,e){N.initialize(t,e)}static isHandlingMimeForPasting(t){return Cs.includes(t)}static paste(t,e){e.pasteEditor({mode:z.STAMP},{bitmapFile:t.getAsFile()})}altTextFinish(){this._uiManager.useNewAltTextFlow&&(this.div.hidden=!1),super.altTextFinish()}get telemetryFinalData(){return{type:"stamp",hasAltText:!!this.altTextData?.altText}}static computeTelemetryFinalData(t){const e=t.get("hasAltText");return{hasAltText:e.get(!0)??0,hasNoAltText:e.get(!1)??0}}#l(t,e=!1){if(!t){this.remove();return}this.#t=t.bitmap,e||(this.#e=t.id,this.#h=t.isSvg),t.file&&(this.#n=t.file.name),this.#f()}#p(){if(this.#s=null,this._uiManager.enableWaiting(!1),!!this.#a){if(this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#t){this.addEditToolbar().then(()=>{this._editToolbar.hide(),this._uiManager.editAltText(this,!0)});return}if(!this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&this.#t){this._reportTelemetry({action:"pdfjs.image.image_added",data:{alt_text_modal:!1,alt_text_type:"empty"}});try{this.mlGuessAltText()}catch{}}this.div.focus()}}async mlGuessAltText(t=null,e=!0){if(this.hasAltTextData())return null;const{mlManager:s}=this._uiManager;if(!s)throw new Error("No ML.");if(!await s.isEnabledFor("altText"))throw new Error("ML isn't enabled for alt text.");const{data:i,width:n,height:r}=t||this.copyCanvas(null,null,!0).imageData,a=await s.guess({name:"altText",request:{data:i,width:n,height:r,channels:i.length/(n*r)}});if(!a)throw new Error("No response from the AI service.");if(a.error)throw new Error("Error from the AI service.");if(a.cancel)return null;if(!a.output)throw new Error("No valid response from the AI service.");const o=a.output;return await this.setGuessedAltText(o),e&&!this.hasAltTextData()&&(this.altTextData={alt:o,decorative:!1}),o}#m(){if(this.#e){this._uiManager.enableWaiting(!0),this._uiManager.imageManager.getFromId(this.#e).then(s=>this.#l(s,!0)).finally(()=>this.#p());return}if(this.#i){const s=this.#i;this.#i=null,this._uiManager.enableWaiting(!0),this.#s=this._uiManager.imageManager.getFromUrl(s).then(i=>this.#l(i)).finally(()=>this.#p());return}if(this.#r){const s=this.#r;this.#r=null,this._uiManager.enableWaiting(!0),this.#s=this._uiManager.imageManager.getFromFile(s).then(i=>this.#l(i)).finally(()=>this.#p());return}const t=document.createElement("input");t.type="file",t.accept=Cs.join(",");const e=this._uiManager._signal;this.#s=new Promise(s=>{t.addEventListener("change",async()=>{if(!t.files||t.files.length===0)this.remove();else{this._uiManager.enableWaiting(!0);const i=await this._uiManager.imageManager.getFromFile(t.files[0]);this._reportTelemetry({action:"pdfjs.image.image_selected",data:{alt_text_modal:this._uiManager.useNewAltTextFlow}}),this.#l(i)}s()},{signal:e}),t.addEventListener("cancel",()=>{this.remove(),s()},{signal:e})}).finally(()=>this.#p()),t.click()}remove(){this.#e&&(this.#t=null,this._uiManager.imageManager.deleteId(this.#e),this.#a?.remove(),this.#a=null,this.#c&&(clearTimeout(this.#c),this.#c=null)),super.remove()}rebuild(){if(!this.parent){this.#e&&this.#m();return}super.rebuild(),this.div!==null&&(this.#e&&this.#a===null&&this.#m(),this.isAttachedToDOM||this.parent.add(this))}onceAdded(t){this._isDraggable=!0,t&&this.div.focus()}isEmpty(){return!(this.#s||this.#t||this.#i||this.#r||this.#e||this.#o)}get toolbarButtons(){return[["altText",this.createAltText()]]}get isResizable(){return!0}render(){if(this.div)return this.div;let t,e;return this._isCopy&&(t=this.x,e=this.y),super.render(),this.div.hidden=!0,this.createAltText(),this.#o||(this.#t?this.#f():this.#m()),this._isCopy&&this._moveAfterPaste(t,e),this._uiManager.addShouldRescale(this),this.div}setCanvas(t,e){const{id:s,bitmap:i}=this._uiManager.imageManager.getFromCanvas(t,e);e.remove(),s&&this._uiManager.imageManager.isValidId(s)&&(this.#e=s,i&&(this.#t=i),this.#o=!1,this.#f())}_onResized(){this.onScaleChanging()}onScaleChanging(){if(!this.parent)return;this.#c!==null&&clearTimeout(this.#c);const t=200;this.#c=setTimeout(()=>{this.#c=null,this.#g()},t)}#f(){const{div:t}=this;let{width:e,height:s}=this.#t;const[i,n]=this.pageDimensions,r=.75;if(this.width)e=this.width*i,s=this.height*n;else if(e>r*i||s>r*n){const l=Math.min(r*i/e,r*n/s);e*=l,s*=l}const[a,o]=this.parentDimensions;this.setDims(e*a/i,s*o/n),this._uiManager.enableWaiting(!1);const h=this.#a=document.createElement("canvas");h.setAttribute("role","img"),this.addContainer(h),this.width=e/i,this.height=s/n,this._initialOptions?.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,(!this._uiManager.useNewAltTextWhenAddingImage||!this._uiManager.useNewAltTextFlow||this.annotationElementId)&&(t.hidden=!1),this.#g(),this.#u||(this.parent.addUndoableEditor(this),this.#u=!0),this._reportTelemetry({action:"inserted_image"}),this.#n&&this.div.setAttribute("aria-description",this.#n),this.annotationElementId||this._uiManager.a11yAlert("pdfjs-editor-stamp-added-alert")}copyCanvas(t,e,s=!1){t||(t=224);const{width:i,height:n}=this.#t,r=new $t;let a=this.#t,o=i,h=n,l=null;if(e){if(i>e||n>e){const E=Math.min(e/i,e/n);o=Math.floor(i*E),h=Math.floor(n*E)}l=document.createElement("canvas");const u=l.width=Math.ceil(o*r.sx),f=l.height=Math.ceil(h*r.sy);this.#h||(a=this.#d(u,f));const g=l.getContext("2d");g.filter=this._uiManager.hcmFilter;let p="white",b="#cfcfd8";this._uiManager.hcmFilter!=="none"?b="black":window.matchMedia?.("(prefers-color-scheme: dark)").matches&&(p="#8f8f9d",b="#42414d");const m=15,_=m*r.sx,y=m*r.sy,w=new OffscreenCanvas(_*2,y*2),v=w.getContext("2d");v.fillStyle=p,v.fillRect(0,0,_*2,y*2),v.fillStyle=b,v.fillRect(0,0,_,y),v.fillRect(_,y,_,y),g.fillStyle=g.createPattern(w,"repeat"),g.fillRect(0,0,u,f),g.drawImage(a,0,0,a.width,a.height,0,0,u,f)}let d=null;if(s){let u,f;if(r.symmetric&&a.width<t&&a.height<t)u=a.width,f=a.height;else if(a=this.#t,i>t||n>t){const b=Math.min(t/i,t/n);u=Math.floor(i*b),f=Math.floor(n*b),this.#h||(a=this.#d(u,f))}const p=new OffscreenCanvas(u,f).getContext("2d",{willReadFrequently:!0});p.drawImage(a,0,0,a.width,a.height,0,0,u,f),d={width:u,height:f,data:p.getImageData(0,0,u,f).data}}return{canvas:l,width:o,height:h,imageData:d}}#d(t,e){const{width:s,height:i}=this.#t;let n=s,r=i,a=this.#t;for(;n>2*t||r>2*e;){const o=n,h=r;n>2*t&&(n=n>=16384?Math.floor(n/2)-1:Math.ceil(n/2)),r>2*e&&(r=r>=16384?Math.floor(r/2)-1:Math.ceil(r/2));const l=new OffscreenCanvas(n,r);l.getContext("2d").drawImage(a,0,0,o,h,0,0,n,r),a=l.transferToImageBitmap()}return a}#g(){const[t,e]=this.parentDimensions,{width:s,height:i}=this,n=new $t,r=Math.ceil(s*t*n.sx),a=Math.ceil(i*e*n.sy),o=this.#a;if(!o||o.width===r&&o.height===a)return;o.width=r,o.height=a;const h=this.#h?this.#t:this.#d(r,a),l=o.getContext("2d");l.filter=this._uiManager.hcmFilter,l.drawImage(h,0,0,h.width,h.height,0,0,r,a)}#y(t){if(t){if(this.#h){const i=this._uiManager.imageManager.getSvgUrl(this.#e);if(i)return i}const e=document.createElement("canvas");return{width:e.width,height:e.height}=this.#t,e.getContext("2d").drawImage(this.#t,0,0),e.toDataURL()}if(this.#h){const[e,s]=this.pageDimensions,i=Math.round(this.width*e*ge.PDF_TO_CSS_UNITS),n=Math.round(this.height*s*ge.PDF_TO_CSS_UNITS),r=new OffscreenCanvas(i,n);return r.getContext("2d").drawImage(this.#t,0,0,this.#t.width,this.#t.height,0,0,i,n),r.transferToImageBitmap()}return structuredClone(this.#t)}static async deserialize(t,e,s){let i=null,n=!1;if(t instanceof Zi){const{data:{rect:p,rotation:b,id:m,structParent:_,popupRef:y,contentsObj:w},container:v,parent:{page:{pageNumber:E}},canvas:A}=t;let T,k;A?(delete t.canvas,{id:T,bitmap:k}=s.imageManager.getFromCanvas(v.id,A),A.remove()):(n=!0,t._hasNoCanvas=!0);const I=(await e._structTree.getAriaAttributes(`${Ds}${m}`))?.get("aria-label")||"";i=t={annotationType:z.STAMP,bitmapId:T,bitmap:k,pageIndex:E-1,rect:p.slice(0),rotation:b,annotationElementId:m,id:m,deleted:!1,accessibilityData:{decorative:!1,altText:I},isSvg:!1,structParent:_,popupRef:y,comment:w?.str||null}}const r=await super.deserialize(t,e,s),{rect:a,bitmap:o,bitmapUrl:h,bitmapId:l,isSvg:d,accessibilityData:u}=t;n?(s.addMissingCanvas(t.id,r),r.#o=!0):l&&s.imageManager.isValidId(l)?(r.#e=l,o&&(r.#t=o)):r.#i=h,r.#h=d;const[f,g]=r.pageDimensions;return r.width=(a[2]-a[0])/f,r.height=(a[3]-a[1])/g,u&&(r.altTextData=u),r._initialData=i,t.comment&&r.setCommentData(t.comment),r.#u=!!i,r}serialize(t=!1,e=null){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const s={annotationType:z.STAMP,bitmapId:this.#e,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#h,structTreeParentId:this._structTreeParentId};if(this.addComment(s),t)return s.bitmapUrl=this.#y(!0),s.accessibilityData=this.serializeAltText(!0),s.isCopy=!0,s;const{decorative:i,altText:n}=this.serializeAltText(!1);if(!i&&n&&(s.accessibilityData={type:"Figure",alt:n}),this.annotationElementId){const a=this.#b(s);if(a.isSame)return null;a.isSameAltText?delete s.accessibilityData:s.accessibilityData.structParent=this._initialData.structParent??-1}if(s.id=this.annotationElementId,e===null)return s;e.stamps||=new Map;const r=this.#h?(s.rect[2]-s.rect[0])*(s.rect[3]-s.rect[1]):null;if(!e.stamps.has(this.#e))e.stamps.set(this.#e,{area:r,serialized:s}),s.bitmap=this.#y(!1);else if(this.#h){const a=e.stamps.get(this.#e);r>a.area&&(a.area=r,a.serialized.bitmap.close(),a.serialized.bitmap=this.#y(!1))}return s}#b(t){const{pageIndex:e,accessibilityData:{altText:s}}=this._initialData,i=t.pageIndex===e,n=(t.accessibilityData?.alt||"")===s;return{isSame:!this.hasEditedComment&&!this._hasBeenMoved&&!this._hasBeenResized&&i&&n,isSameAltText:n}}renderAnnotationElement(t){const e={rect:this.getRect(0,0)};return this.hasEditedComment&&(e.popup=this.comment),t.updateEdited(e),null}}class Ut{#t;#e=!1;#s=null;#i=null;#r=null;#n=new Map;#a=!1;#o=!1;#c=!1;#h=null;#u=null;#l=null;#p=null;#m=null;#f=-1;#d;static _initialized=!1;static#g=new Map([ut,qs,Qr,rt,Ot].map(t=>[t._editorType,t]));constructor({uiManager:t,pageIndex:e,div:s,structTreeLayer:i,accessibilityManager:n,annotationLayer:r,drawLayer:a,textLayer:o,viewport:h,l10n:l}){const d=[...Ut.#g.values()];if(!Ut._initialized){Ut._initialized=!0;for(const u of d)u.initialize(l,t)}t.registerEditorTypes(d),this.#d=t,this.pageIndex=e,this.div=s,this.#t=n,this.#s=r,this.viewport=h,this.#l=o,this.drawLayer=a,this._structTree=i,this.#d.addLayer(this)}get isEmpty(){return this.#n.size===0}get isInvisible(){return this.isEmpty&&this.#d.getMode()===z.NONE}updateToolbar(t){this.#d.updateToolbar(t)}updateMode(t=this.#d.getMode()){switch(this.#v(),t){case z.NONE:this.disableTextSelection(),this.togglePointerEvents(!1),this.toggleAnnotationLayerPointerEvents(!0),this.disableClick();return;case z.INK:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick();break;case z.HIGHLIGHT:this.enableTextSelection(),this.togglePointerEvents(!1),this.disableClick();break;default:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const s of Ut.#g.values())e.toggle(`${s._type}Editing`,t===s._editorType);this.div.hidden=!1}hasTextLayer(t){return t===this.#l?.div}setEditingState(t){this.#d.setEditingState(t)}addCommands(t){this.#d.addCommands(t)}cleanUndoStack(t){this.#d.cleanUndoStack(t)}toggleDrawing(t=!1){this.div.classList.toggle("drawing",!t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){this.#s?.div.classList.toggle("disabled",!t)}async enable(){this.#c=!0,this.div.tabIndex=0,this.togglePointerEvents(!0),this.#m?.abort(),this.#m=null;const t=new Set;for(const s of this.#n.values())s.enableEditing(),s.show(!0),s.annotationElementId&&(this.#d.removeChangedExistingAnnotation(s),t.add(s.annotationElementId));if(!this.#s){this.#c=!1;return}const e=this.#s.getEditableAnnotations();for(const s of e){if(s.hide(),this.#d.isDeletedAnnotationElement(s.data.id)||t.has(s.data.id))continue;const i=await this.deserialize(s);i&&(this.addOrRebuild(i),i.enableEditing())}this.#c=!1}disable(){if(this.#o=!0,this.div.tabIndex=-1,this.togglePointerEvents(!1),this.#l&&!this.#m){this.#m=new AbortController;const i=this.#d.combinedSignal(this.#m);this.#l.div.addEventListener("pointerdown",n=>{const{clientX:a,clientY:o,timeStamp:h}=n,l=this.#f;if(h-l>500){this.#f=h;return}this.#f=-1;const{classList:d}=this.div;d.toggle("getElements",!0);const u=document.elementsFromPoint(a,o);if(d.toggle("getElements",!1),!this.div.contains(u[0]))return;let f;const g=new RegExp(`^${Si}[0-9]+$`);for(const b of u)if(g.test(b.id)){f=b.id;break}if(!f)return;const p=this.#n.get(f);p?.annotationElementId===null&&(n.stopPropagation(),n.preventDefault(),p.dblclick())},{signal:i,capture:!0})}const t=new Map,e=new Map;for(const i of this.#n.values())if(i.disableEditing(),!!i.annotationElementId){if(i.serialize()!==null){t.set(i.annotationElementId,i);continue}else e.set(i.annotationElementId,i);this.getEditableAnnotation(i.annotationElementId)?.show(),i.remove()}if(this.#s){const i=this.#s.getEditableAnnotations();for(const n of i){const{id:r}=n.data;if(this.#d.isDeletedAnnotationElement(r)){n.updateEdited({deleted:!0});continue}let a=e.get(r);if(a){a.resetAnnotationElement(n),a.show(!1),n.show();continue}a=t.get(r),a&&(this.#d.addChangedExistingAnnotation(a),a.renderAnnotationElement(n)&&a.show(!1)),n.show()}}this.#v(),this.isEmpty&&(this.div.hidden=!0);const{classList:s}=this.div;for(const i of Ut.#g.values())s.remove(`${i._type}Editing`);this.disableTextSelection(),this.toggleAnnotationLayerPointerEvents(!0),this.#o=!1}getEditableAnnotation(t){return this.#s?.getEditableAnnotation(t)||null}setActiveEditor(t){this.#d.getActive()!==t&&this.#d.setActiveEditor(t)}enableTextSelection(){if(this.div.tabIndex=-1,this.#l?.div&&!this.#p){this.#p=new AbortController;const t=this.#d.combinedSignal(this.#p);this.#l.div.addEventListener("pointerdown",this.#y.bind(this),{signal:t}),this.#l.div.classList.add("highlighting")}}disableTextSelection(){this.div.tabIndex=0,this.#l?.div&&this.#p&&(this.#p.abort(),this.#p=null,this.#l.div.classList.remove("highlighting"))}#y(t){this.#d.unselectAll();const{target:e}=t;if(e===this.#l.div||(e.getAttribute("role")==="img"||e.classList.contains("endOfContent"))&&this.#l.div.contains(e)){const{isMac:s}=mt.platform;if(t.button!==0||t.ctrlKey&&s)return;this.#d.showAllEditors("highlight",!0,!0),this.#l.div.classList.add("free"),this.toggleDrawing(),rt.startHighlighting(this,this.#d.direction==="ltr",{target:this.#l.div,x:t.x,y:t.y}),this.#l.div.addEventListener("pointerup",()=>{this.#l.div.classList.remove("free"),this.toggleDrawing(!0)},{once:!0,signal:this.#d._signal}),t.preventDefault()}}enableClick(){if(this.#i)return;this.#i=new AbortController;const t=this.#d.combinedSignal(this.#i);this.div.addEventListener("pointerdown",this.pointerdown.bind(this),{signal:t});const e=this.pointerup.bind(this);this.div.addEventListener("pointerup",e,{signal:t}),this.div.addEventListener("pointercancel",e,{signal:t})}disableClick(){this.#i?.abort(),this.#i=null}attach(t){this.#n.set(t.id,t);const{annotationElementId:e}=t;e&&this.#d.isDeletedAnnotationElement(e)&&this.#d.removeDeletedAnnotationElement(t)}detach(t){this.#n.delete(t.id),this.#t?.removePointerInTextLayer(t.contentDiv),!this.#o&&t.annotationElementId&&this.#d.addDeletedAnnotationElement(t)}remove(t){this.detach(t),this.#d.removeEditor(t),t.div.remove(),t.isAttachedToDOM=!1}changeParent(t){t.parent!==this&&(t.parent&&t.annotationElementId&&(this.#d.addDeletedAnnotationElement(t.annotationElementId),N.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),t.parent?.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}add(t){if(!(t.parent===this&&t.isAttachedToDOM)){if(this.changeParent(t),this.#d.addEditor(t),this.attach(t),!t.isAttachedToDOM){const e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(!this.#c),this.#d.addToAnnotationStorage(t),t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;t.div.contains(e)&&!this.#r&&(t._focusEventsAllowed=!1,this.#r=setTimeout(()=>{this.#r=null,t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:this.#d._signal}),e.focus())},0)),t._structTreeParentId=this.#t?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?(t.parent||=this,t.rebuild(),t.show()):this.add(t)}addUndoableEditor(t){const e=()=>t._uiManager.rebuild(t),s=()=>{t.remove()};this.addCommands({cmd:e,undo:s,mustExec:!1})}getNextId(){return this.#d.getId()}get#b(){return Ut.#g.get(this.#d.getMode())}combinedSignal(t){return this.#d.combinedSignal(t)}#A(t){const e=this.#b;return e?new e.prototype.constructor(t):null}canCreateNewEmptyEditor(){return this.#b?.canCreateNewEmptyEditor()}async pasteEditor(t,e){this.updateToolbar(t),await this.#d.updateMode(t.mode);const{offsetX:s,offsetY:i}=this.#_(),n=this.getNextId(),r=this.#A({parent:this,id:n,x:s,y:i,uiManager:this.#d,isCentered:!0,...e});r&&this.add(r)}async deserialize(t){return await Ut.#g.get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,this.#d)||null}createAndAddNewEditor(t,e,s={}){const i=this.getNextId(),n=this.#A({parent:this,id:i,x:t.offsetX,y:t.offsetY,uiManager:this.#d,isCentered:e,...s});return n&&this.add(n),n}#_(){const{x:t,y:e,width:s,height:i}=this.div.getBoundingClientRect(),n=Math.max(0,t),r=Math.max(0,e),a=Math.min(window.innerWidth,t+s),o=Math.min(window.innerHeight,e+i),h=(n+a)/2-t,l=(r+o)/2-e,[d,u]=this.viewport.rotation%180===0?[h,l]:[l,h];return{offsetX:d,offsetY:u}}addNewEditor(t={}){this.createAndAddNewEditor(this.#_(),!0,t)}setSelected(t){this.#d.setSelected(t)}toggleSelected(t){this.#d.toggleSelected(t)}unselect(t){this.#d.unselect(t)}pointerup(t){const{isMac:e}=mt.platform;if(t.button!==0||t.ctrlKey&&e||t.target!==this.div||!this.#a||(this.#a=!1,this.#b?.isDrawer&&this.#b.supportMultipleDrawings))return;if(!this.#e){this.#e=!0;return}const s=this.#d.getMode();if(s===z.STAMP||s===z.SIGNATURE){this.#d.unselectAll();return}this.createAndAddNewEditor(t,!1)}pointerdown(t){if(this.#d.getMode()===z.HIGHLIGHT&&this.enableTextSelection(),this.#a){this.#a=!1;return}const{isMac:e}=mt.platform;if(t.button!==0||t.ctrlKey&&e||t.target!==this.div)return;if(this.#a=!0,this.#b?.isDrawer){this.startDrawingSession(t);return}const s=this.#d.getActive();this.#e=!s||s.isEmpty()}startDrawingSession(t){if(this.div.focus({preventScroll:!0}),this.#h){this.#b.startDrawing(this,this.#d,!1,t);return}this.#d.setCurrentDrawingSession(this),this.#h=new AbortController;const e=this.#d.combinedSignal(this.#h);this.div.addEventListener("blur",({relatedTarget:s})=>{s&&!this.div.contains(s)&&(this.#u=null,this.commitOrRemove())},{signal:e}),this.#b.startDrawing(this,this.#d,!1,t)}pause(t){if(t){const{activeElement:e}=document;this.div.contains(e)&&(this.#u=e);return}this.#u&&setTimeout(()=>{this.#u?.focus(),this.#u=null},0)}endDrawingSession(t=!1){return this.#h?(this.#d.setCurrentDrawingSession(null),this.#h.abort(),this.#h=null,this.#u=null,this.#b.endDrawing(t)):null}findNewParent(t,e,s){const i=this.#d.findParent(e,s);return i===null||i===this?!1:(i.changeParent(t),!0)}commitOrRemove(){return this.#h?(this.endDrawingSession(),!0):!1}onScaleChanging(){this.#h&&this.#b.onScaleChangingWhenDrawing(this)}destroy(){this.commitOrRemove(),this.#d.getActive()?.parent===this&&(this.#d.commitOrRemove(),this.#d.setActiveEditor(null)),this.#r&&(clearTimeout(this.#r),this.#r=null);for(const t of this.#n.values())this.#t?.removePointerInTextLayer(t.contentDiv),t.setParent(null),t.isAttachedToDOM=!1,t.div.remove();this.div=null,this.#n.clear(),this.#d.removeLayer(this)}#v(){for(const t of this.#n.values())t.isEmpty()&&t.remove()}render({viewport:t}){this.viewport=t,ee(this.div,t);for(const e of this.#d.getEditors(this.pageIndex))this.add(e),e.rebuild();this.updateMode()}update({viewport:t}){this.#d.commitOrRemove(),this.#v();const e=this.viewport.rotation,s=t.rotation;if(this.viewport=t,ee(this.div,{rotation:s}),e!==s)for(const i of this.#n.values())i.rotate(s)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return this.#d.viewParameters.realScale}}class ft{#t=null;#e=new Map;#s=new Map;static#i=0;constructor({pageIndex:t}){this.pageIndex=t}setParent(t){if(!this.#t){this.#t=t;return}if(this.#t!==t){if(this.#e.size>0)for(const e of this.#e.values())e.remove(),t.append(e);this.#t=t}}static get _svgFactory(){return j(this,"_svgFactory",new Je)}static#r(t,[e,s,i,n]){const{style:r}=t;r.top=`${100*s}%`,r.left=`${100*e}%`,r.width=`${100*i}%`,r.height=`${100*n}%`}#n(){const t=ft._svgFactory.create(1,1,!0);return this.#t.append(t),t.setAttribute("aria-hidden",!0),t}#a(t,e){const s=ft._svgFactory.createElement("clipPath");t.append(s);const i=`clip_${e}`;s.setAttribute("id",i),s.setAttribute("clipPathUnits","objectBoundingBox");const n=ft._svgFactory.createElement("use");return s.append(n),n.setAttribute("href",`#${e}`),n.classList.add("clip"),i}#o(t,e){for(const[s,i]of Object.entries(e))i===null?t.removeAttribute(s):t.setAttribute(s,i)}draw(t,e=!1,s=!1){const i=ft.#i++,n=this.#n(),r=ft._svgFactory.createElement("defs");n.append(r);const a=ft._svgFactory.createElement("path");r.append(a);const o=`path_p${this.pageIndex}_${i}`;a.setAttribute("id",o),a.setAttribute("vector-effect","non-scaling-stroke"),e&&this.#s.set(i,a);const h=s?this.#a(r,o):null,l=ft._svgFactory.createElement("use");return n.append(l),l.setAttribute("href",`#${o}`),this.updateProperties(n,t),this.#e.set(i,n),{id:i,clipPathId:`url(#${h})`}}drawOutline(t,e){const s=ft.#i++,i=this.#n(),n=ft._svgFactory.createElement("defs");i.append(n);const r=ft._svgFactory.createElement("path");n.append(r);const a=`path_p${this.pageIndex}_${s}`;r.setAttribute("id",a),r.setAttribute("vector-effect","non-scaling-stroke");let o;if(e){const d=ft._svgFactory.createElement("mask");n.append(d),o=`mask_p${this.pageIndex}_${s}`,d.setAttribute("id",o),d.setAttribute("maskUnits","objectBoundingBox");const u=ft._svgFactory.createElement("rect");d.append(u),u.setAttribute("width","1"),u.setAttribute("height","1"),u.setAttribute("fill","white");const f=ft._svgFactory.createElement("use");d.append(f),f.setAttribute("href",`#${a}`),f.setAttribute("stroke","none"),f.setAttribute("fill","black"),f.setAttribute("fill-rule","nonzero"),f.classList.add("mask")}const h=ft._svgFactory.createElement("use");i.append(h),h.setAttribute("href",`#${a}`),o&&h.setAttribute("mask",`url(#${o})`);const l=h.cloneNode();return i.append(l),h.classList.add("mainOutline"),l.classList.add("secondaryOutline"),this.updateProperties(i,t),this.#e.set(s,i),s}finalizeDraw(t,e){this.#s.delete(t),this.updateProperties(t,e)}updateProperties(t,e){if(!e)return;const{root:s,bbox:i,rootClass:n,path:r}=e,a=typeof t=="number"?this.#e.get(t):t;if(a){if(s&&this.#o(a,s),i&&ft.#r(a,i),n){const{classList:o}=a;for(const[h,l]of Object.entries(n))o.toggle(h,l)}if(r){const h=a.firstChild.firstChild;this.#o(h,r)}}}updateParent(t,e){if(e===this)return;const s=this.#e.get(t);s&&(e.#t.append(s),this.#e.delete(t),e.#e.set(t,s))}remove(t){this.#s.delete(t),this.#t!==null&&(this.#e.get(t).remove(),this.#e.delete(t))}destroy(){this.#t=null;for(const t of this.#e.values())t.remove();this.#e.clear(),this.#s.clear()}}globalThis._pdfjsTestingUtils={HighlightOutliner:Ms};globalThis.pdfjsLib={AbortException:Yt,AnnotationEditorLayer:Ut,AnnotationEditorParamsType:W,AnnotationEditorType:z,AnnotationEditorUIManager:Kt,AnnotationLayer:Ws,AnnotationMode:Wt,AnnotationType:lt,build:kr,ColorPicker:kt,createValidAbsoluteUrl:Ei,DOMSVGFactory:Je,DrawLayer:ft,FeatureTest:mt,fetchData:Le,getDocument:Ps,getFilenameFromUrl:wn,getPdfFilenameFromUrl:An,getRGB:rs,getUuid:Ti,getXfaPageViewport:Sn,GlobalWorkerOptions:fe,ImageKind:qe,InvalidPDFException:Ss,isDataScheme:ns,isPdfFile:Fs,isValidExplicitDest:Dn,MathClamp:wt,noContextMenu:Pt,normalizeUnicode:bn,OPS:Qe,OutputScale:$t,PasswordResponses:ln,PDFDataRangeTransport:ji,PDFDateString:Es,PDFWorker:ke,PermissionFlag:on,PixelsPerInch:ge,RenderingCancelledException:Ns,ResponseException:Ze,setLayerDimensions:ee,shadow:j,SignatureExtractor:oe,stopEvent:ht,SupportedImageMimeTypes:Cs,TextLayer:vt,TouchManager:as,updateUrlHash:Ci,Util:D,VerbosityLevel:es,version:Tr,XfaLayer:qi};const Zr="/assets/pdf.worker-DcN26wb1.mjs";fe.workerSrc=Zr;let Rt;function Jr(){const c=X(1),t=X(0),e=X(1),s=X(0),i=X(!1),n=X(null),r=X(null),a=X(""),o=X(""),h=X(null),l=Xt(()=>Rt!==null),d=Xt(()=>c.value>1),u=Xt(()=>c.value<t.value),f=[.5,.75,1,1.25,1.5,2,2.5,3];async function g(P){if(!P)throw new Error("No file provided");if(P.type!=="application/pdf"&&!P.name.toLowerCase().endsWith(".pdf"))throw new Error("Invalid file type. Please select a PDF file.");i.value=!0,n.value=null;try{r.value=P,a.value=P.name,o.value=x(P.size),h.value&&URL.revokeObjectURL(h.value),h.value=URL.createObjectURL(P);const H=await P.arrayBuffer(),J=new Uint8Array(H);Rt=await Ps({data:J}).promise,t.value=Rt.numPages,c.value=1,e.value=1,s.value=0}catch(H){throw n.value=H instanceof Error?H.message:"Failed to load PDF",H}finally{i.value=!1}}async function p(P){i.value=!0,n.value=null;try{Rt=await Ps(P).promise,t.value=Rt.numPages,c.value=1,e.value=1,s.value=0,r.value=null,a.value=P.split("/").pop()||"PDF Document",o.value="",h.value=P}catch(H){throw n.value=H instanceof Error?H.message:"Failed to load PDF from URL",H}finally{i.value=!1}}async function b(P,H){if(!Rt||!P)return;const J=H||c.value;try{const Y=await Rt.getPage(J),Z=P.parentElement;if(!Z)return;if(Z.clientWidth===0){setTimeout(()=>b(P,H),50);return}console.log("Rendering PDF page:",J,"Container width:",Z.clientWidth);const Gt=Z.clientWidth,It=Y.getViewport({scale:1,rotation:s.value}),et=Gt/It.width*e.value,zt=Y.getViewport({scale:et,rotation:s.value}),me=P.getContext("2d");if(!me)throw new Error("Could not get canvas context");P.width=zt.width,P.height=zt.height,me.clearRect(0,0,P.width,P.height);const hs={canvasContext:me,viewport:zt,canvas:P};await Y.render(hs).promise,console.log("PDF page rendered successfully:",J,"Canvas size:",P.width,"x",P.height)}catch(Y){console.error("Error rendering page:",Y),n.value="Failed to render page"}}async function m(P){if(!Rt)throw new Error("PDF document not loaded");if(P<1||P>t.value)throw new Error("Invalid page number");return await Rt.getPage(P)}function _(P){P>=1&&P<=t.value&&(c.value=P)}function y(){u.value&&c.value++}function w(){d.value&&c.value--}function v(){c.value=1}function E(){c.value=t.value}function A(P){P>0&&P<=5&&(e.value=P)}function T(){const P=f.findIndex(H=>H>=e.value);P<f.length-1&&(e.value=f[P+1])}function k(){const P=f.findIndex(H=>H>=e.value);P>0&&(e.value=f[P-1])}function I(){e.value=1}function L(){e.value=1}function q(){s.value=(s.value-90)%360}function K(){s.value=(s.value+90)%360}function G(){s.value=0}function dt(){h.value&&r.value&&URL.revokeObjectURL(h.value),Rt=null,r.value=null,a.value="",o.value="",h.value=null,c.value=1,t.value=0,e.value=1,s.value=0,i.value=!1,n.value=null}function C(){if(!r.value)return;const P=URL.createObjectURL(r.value),H=document.createElement("a");H.href=P,H.download=r.value.name,document.body.appendChild(H),H.click(),document.body.removeChild(H),URL.revokeObjectURL(P)}function x(P){if(P===0)return"0 Bytes";const H=1024,J=["Bytes","KB","MB","GB","TB"],Y=Math.floor(Math.log(P)/Math.log(H));return parseFloat((P/Math.pow(H,Y)).toFixed(2))+" "+J[Y]}function U(){h.value&&r.value&&URL.revokeObjectURL(h.value)}return{pdfDocument:Rt,hasDocument:l,currentPage:pt(c),pageCount:pt(t),scale:pt(e),rotation:pt(s),isLoading:pt(i),error:pt(n),pdfFile:pt(r),fileName:pt(a),fileSize:pt(o),pdfUrl:pt(h),canNavigatePrevious:d,canNavigateNext:u,zoomLevels:f,loadPdfFromFile:g,loadPdfFromUrl:p,renderPage:b,getPage:m,goToPage:_,nextPage:y,previousPage:w,firstPage:v,lastPage:E,setZoom:A,zoomIn:T,zoomOut:k,resetZoom:I,fitToWidth:L,rotateLeft:q,rotateRight:K,resetRotation:G,reset:dt,downloadPdf:C,cleanup:U}}function ta(){const c=X([]),t=X(null),e=X(!1),s=X(null),i=X("#ff0000"),n=X(2),r=X(null),a=X(null),o=[{type:"text",label:"Text",icon:"T"},{type:"highlight",label:"Highlight",icon:"🖍️"},{type:"rectangle",label:"Rectangle",icon:"⬜"},{type:"circle",label:"Circle",icon:"⭕"},{type:"line",label:"Line",icon:"📏"},{type:"freehand",label:"Freehand",icon:"✏️"}],h=["#ff0000","#00ff00","#0000ff","#ffff00","#ff00ff","#00ffff","#000000","#ffffff","#ffa500","#800080","#008000","#000080"],l=[1,2,3,4,5,8,10],d=Xt(()=>{const C={};return c.value.forEach(x=>{C[x.pageNumber]||(C[x.pageNumber]=[]),C[x.pageNumber].push(x)}),C}),u=Xt(()=>c.value.length);function f(C){s.value=C,t.value=null}function g(C){i.value=C,t.value&&m(t.value.id,{color:C})}function p(C){n.value=C,t.value&&m(t.value.id,{strokeWidth:C})}function b(C,x,U,P,H={}){const J={id:G(),type:C,pageNumber:x,x:U,y:P,color:i.value,strokeWidth:n.value,created:new Date,modified:new Date,...H};return c.value.push(J),J}function m(C,x){const U=c.value.findIndex(P=>P.id===C);U!==-1&&(c.value[U]={...c.value[U],...x,modified:new Date})}function _(C){const x=c.value.findIndex(U=>U.id===C);x!==-1&&(c.value.splice(x,1),t.value?.id===C&&(t.value=null))}function y(C){t.value=C,C&&(i.value=C.color,n.value=C.strokeWidth)}function w(C){return c.value.filter(x=>x.pageNumber===C)}function v(C,x,U){if(s.value){if(e.value=!0,r.value={x,y:U},s.value==="freehand")a.value=b(s.value,C,x,U,{points:[{x,y:U}]});else if(s.value==="text"){const P=b(s.value,C,x,U,{text:"New Text",width:100,height:20});y(P),e.value=!1}}}function E(C,x,U){if(!(!e.value||!r.value||!s.value)){if(s.value==="freehand"&&a.value){const P=a.value.points||[];P.push({x,y:U}),m(a.value.id,{points:P})}else if(a.value){const P=Math.abs(x-r.value.x),H=Math.abs(U-r.value.y),J=Math.min(x,r.value.x),Y=Math.min(U,r.value.y);m(a.value.id,{x:J,y:Y,width:P,height:H})}else if(s.value!=="text"&&s.value!=="freehand"){const P=Math.abs(x-r.value.x),H=Math.abs(U-r.value.y),J=Math.min(x,r.value.x),Y=Math.min(U,r.value.y);a.value=b(s.value,C,J,Y,{width:P,height:H})}}}function A(){e.value=!1,r.value=null,a.value&&(y(a.value),a.value=null)}function T(){a.value&&(_(a.value.id),a.value=null),e.value=!1,r.value=null}function k(){c.value=[],t.value=null}function I(C){c.value=c.value.filter(x=>x.pageNumber!==C),t.value?.pageNumber===C&&(t.value=null)}function L(){return JSON.stringify({version:"1.0",annotations:c.value,exported:new Date().toISOString()},null,2)}function q(C){try{const x=JSON.parse(C);x.annotations&&Array.isArray(x.annotations)&&(c.value=x.annotations.map(U=>({...U,created:new Date(U.created),modified:new Date(U.modified)})))}catch{throw new Error("Invalid annotation data format")}}function K(C="pdf-annotations.json"){const x=L(),U=new Blob([x],{type:"application/json"}),P=URL.createObjectURL(U),H=document.createElement("a");H.href=P,H.download=C,document.body.appendChild(H),H.click(),document.body.removeChild(H),URL.revokeObjectURL(P)}function G(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}function dt(){const C={total:c.value.length,byType:{},byPage:{}};return c.value.forEach(x=>{C.byType[x.type]=(C.byType[x.type]||0)+1,C.byPage[x.pageNumber]=(C.byPage[x.pageNumber]||0)+1}),C}return{annotations:pt(c),selectedAnnotation:pt(t),isDrawing:pt(e),currentTool:pt(s),currentColor:pt(i),currentStrokeWidth:pt(n),tempAnnotation:pt(a),availableTools:o,availableColors:h,strokeWidths:l,annotationsByPage:d,annotationCount:u,setTool:f,setColor:g,setStrokeWidth:p,createAnnotation:b,updateAnnotation:m,deleteAnnotation:_,selectAnnotation:y,getAnnotationsForPage:w,startDrawing:v,continueDrawing:E,finishDrawing:A,cancelDrawing:T,clearAnnotations:k,clearPageAnnotations:I,exportAnnotations:L,importAnnotations:q,downloadAnnotations:K,getAnnotationStats:dt}}const ea={class:"bg-white rounded-lg shadow-md p-6"},sa={class:"text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2"},ia={class:"space-y-4"},na={key:0,class:"text-6xl"},ra={key:1,class:"text-6xl text-gray-400"},aa={class:"text-lg font-medium text-gray-900 mb-2"},oa={class:"text-gray-600"},la={class:"text-sm text-gray-500 mt-2"},ha=["disabled"],ca={key:1,class:"space-y-4"},da={class:"flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg"},ua={class:"flex items-center space-x-3"},fa={class:"font-medium text-green-900"},pa={class:"text-sm text-green-700"},ga=["title"],ma={class:"flex flex-wrap gap-2"},ba={key:0,class:"p-4 bg-gray-50 rounded-lg"},va={class:"block text-sm font-medium text-gray-700 mb-2"},ya={class:"flex gap-2"},wa=["disabled"],Aa={key:2,class:"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg"},_a={class:"flex items-start space-x-3"},Sa={class:"font-medium text-red-900"},Ea={class:"text-red-700 mt-1"},Ca=Re({__name:"PdfUpload",props:{isLoading:{type:Boolean,default:!1},error:{default:null},fileName:{default:""},fileSize:{default:""},maxSizeMB:{default:50},canLoadFromUrl:{type:Boolean,default:!0}},emits:["file-selected","url-selected","clear-file","clear-error"],setup(c,{expose:t,emit:e}){const s=c,i=e,n=X(null),r=X(!1),a=X(0),o=X(!1),h=X(""),l=Xt(()=>s.fileName&&s.fileSize);function d(){s.isLoading||n.value?.click()}function u(w){const v=w.target;v.files&&v.files.length>0&&b(v.files[0])}function f(w){w.preventDefault(),a.value++,r.value=!0}function g(w){w.preventDefault(),a.value--,a.value===0&&(r.value=!1)}function p(w){if(w.preventDefault(),r.value=!1,a.value=0,s.isLoading)return;const v=w.dataTransfer?.files;v&&v.length>0&&b(v[0])}function b(w){if(w.type!=="application/pdf"&&!w.name.toLowerCase().endsWith(".pdf")){i("clear-error"),setTimeout(()=>{i("clear-error")},0);return}const v=s.maxSizeMB*1024*1024;if(w.size>v){i("clear-error"),setTimeout(()=>{`${s.maxSizeMB}`},0);return}i("clear-error"),i("file-selected",w),n.value&&(n.value.value="")}function m(){if(!(!h.value||s.isLoading)){try{new URL(h.value)}catch{return}i("clear-error"),i("url-selected",h.value),h.value="",o.value=!1}}function _(){i("clear-file"),o.value=!1,h.value=""}function y(){i("clear-error")}return t({openFileSelector:d,clearFile:_}),(w,v)=>(O(),F("div",ea,[S("h3",sa,R(w.$t("tools.pdfViewer.uploadSection")),1),l.value?(O(),F("div",ca,[S("div",da,[S("div",ua,[v[4]||(v[4]=S("div",{class:"text-2xl"},"📄",-1)),S("div",null,[S("p",fa,R(w.fileName),1),S("p",pa,R(w.fileSize)+" • "+R(w.$t("tools.pdfViewer.ready")),1)])]),S("button",{onClick:_,class:"text-green-700 hover:text-green-900 transition-colors",title:w.$t("tools.pdfViewer.remove")},[...v[5]||(v[5]=[S("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])],8,ga)]),S("div",ma,[S("button",{onClick:d,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"},R(w.$t("tools.pdfViewer.selectAnother")),1),w.canLoadFromUrl?(O(),F("button",{key:0,onClick:v[1]||(v[1]=E=>o.value=!o.value),class:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"},R(w.$t("tools.pdfViewer.loadFromUrl")),1)):tt("",!0)]),o.value?(O(),F("div",ba,[S("label",va,R(w.$t("tools.pdfViewer.pdfUrl")),1),S("div",ya,[sn(S("input",{"onUpdate:modelValue":v[2]||(v[2]=E=>h.value=E),type:"url",placeholder:"https://example.com/document.pdf",class:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[nn,h.value]]),S("button",{onClick:m,disabled:!h.value||w.isLoading,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"},R(w.$t("tools.pdfViewer.load")),9,wa)])])):tt("",!0)])):(O(),F("div",{key:0,onDrop:p,onDragover:v[0]||(v[0]=Ai(()=>{},["prevent"])),onDragenter:f,onDragleave:g,onClick:d,class:Ke(["border-2 border-dashed rounded-lg p-12 text-center cursor-pointer transition-all duration-200",r.value?"border-blue-500 bg-blue-50 scale-105":"border-gray-300 hover:border-gray-400 hover:bg-gray-50",w.isLoading?"pointer-events-none opacity-50":""])},[S("input",{ref_key:"fileInput",ref:n,type:"file",accept:".pdf,application/pdf",onChange:u,class:"hidden"},null,544),S("div",ia,[w.isLoading?(O(),F("div",na,[...v[3]||(v[3]=[S("div",{class:"animate-spin inline-block w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full"},null,-1)])])):(O(),F("div",ra,"📄")),S("div",null,[S("h3",aa,R(w.isLoading?w.$t("tools.pdfViewer.loading"):w.$t("tools.pdfViewer.uploadTitle")),1),S("p",oa,R(w.isLoading?w.$t("tools.pdfViewer.loadingDescription"):w.$t("tools.pdfViewer.uploadDescription")),1),S("p",la,R(w.$t("tools.pdfViewer.supportedFormats"))+": PDF ("+R(w.$t("tools.pdfViewer.maxSize"))+": "+R(w.maxSizeMB)+"MB) ",1)]),w.isLoading?tt("",!0):(O(),F("button",{key:2,class:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",disabled:w.isLoading},R(w.$t("tools.pdfViewer.selectPdf")),9,ha))])],34)),w.error?(O(),F("div",Aa,[S("div",_a,[v[6]||(v[6]=S("div",{class:"text-red-500 text-xl"},"⚠️",-1)),S("div",null,[S("h4",Sa,R(w.$t("tools.pdfViewer.error")),1),S("p",Ea,R(w.error),1),S("button",{onClick:y,class:"mt-2 text-sm text-red-600 hover:text-red-800 underline"},R(w.$t("tools.pdfViewer.dismiss")),1)])])])):tt("",!0)]))}}),xa=Me(Ca,[["__scopeId","data-v-50ca0898"]]),Ta={class:"bg-white border border-gray-200 rounded-lg p-4 shadow-sm"},ka={class:"flex flex-wrap items-center justify-between gap-4"},Pa={class:"flex items-center gap-2"},Ra=["disabled","title"],Ma=["disabled","title"],Ia={class:"flex items-center gap-3"},La=["disabled","title"],Da=["disabled","title"],Na={class:"flex items-center gap-2"},Fa=["value","max"],Oa={class:"text-gray-600"},Ba=["disabled","title"],$a=["disabled","title"],Ha={class:"flex items-center gap-3"},za={class:"flex items-center gap-2"},Va=["disabled","title"],Ua=["value","disabled"],ja=["value"],Ga=["disabled","title"],Wa=["disabled","title"],qa={class:"flex items-center gap-1 border-l border-gray-300 pl-3"},Xa=["disabled","title"],Ya=["disabled","title"],Ka={key:0,class:"flex items-center gap-1 border-l border-gray-300 pl-3"},Qa=["onClick","disabled","title"],Za={class:"flex items-center gap-1 ml-2"},Ja=["value","disabled"],to=["value","disabled"],eo=["value"],so={key:0,class:"mt-3 pt-3 border-t border-gray-200"},io={class:"flex items-center justify-between"},no={class:"flex items-center gap-2"},ro={class:"text-sm text-gray-600"},ao={class:"text-sm font-medium text-gray-900"},oo={class:"flex items-center gap-2"},lo=Re({__name:"PdfToolbar",props:{hasDocument:{type:Boolean,default:!1},currentPage:{default:1},pageCount:{default:0},canNavigatePrevious:{type:Boolean,default:!1},canNavigateNext:{type:Boolean,default:!1},scale:{default:1},zoomLevels:{default:()=>[.5,.75,1,1.25,1.5,2,2.5,3]},showAnnotationTools:{type:Boolean,default:!1},currentTool:{default:null},currentColor:{default:"#ff0000"},currentStrokeWidth:{default:2},annotationTools:{default:()=>[{type:"text",label:"Text",icon:"T"},{type:"highlight",label:"Highlight",icon:"🖍️"},{type:"rectangle",label:"Rectangle",icon:"⬜"},{type:"circle",label:"Circle",icon:"⭕"},{type:"line",label:"Line",icon:"📏"},{type:"freehand",label:"Freehand",icon:"✏️"}]},strokeWidths:{default:()=>[1,2,3,4,5,8,10]}},emits:["download","reset","first-page","previous-page","next-page","last-page","go-to-page","zoom-in","zoom-out","set-zoom","fit-width","rotate-left","rotate-right","select-tool","set-color","set-stroke-width","clear-annotations","export-annotations","import-annotations"],setup(c,{emit:t}){const e=c,s=t,i=X(e.currentPage);function n(l){const d=l.target;i.value=parseInt(d.value)||1}function r(){const l=Math.max(1,Math.min(i.value,e.pageCount));i.value=l,s("go-to-page",l)}function a(l){const d=l.target,u=parseFloat(d.value);s("set-zoom",u)}function o(l){const d=l.target;s("set-color",d.value)}function h(l){const d=l.target,u=parseInt(d.value);s("set-stroke-width",u)}return xe(()=>e.currentPage,l=>{i.value=l}),(l,d)=>(O(),F("div",Ta,[S("div",ka,[S("div",Pa,[S("button",{onClick:d[0]||(d[0]=u=>l.$emit("download")),disabled:!l.hasDocument,class:"px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:l.$t("tools.pdfViewer.download")},[...d[14]||(d[14]=[S("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)])],8,Ra),S("button",{onClick:d[1]||(d[1]=u=>l.$emit("reset")),disabled:!l.hasDocument,class:"px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",title:l.$t("tools.pdfViewer.selectAnother")},[...d[15]||(d[15]=[S("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)])],8,Ma)]),S("div",Ia,[S("button",{onClick:d[2]||(d[2]=u=>l.$emit("first-page")),disabled:!l.canNavigatePrevious,class:"px-2 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",title:l.$t("tools.pdfViewer.firstPage")},[...d[16]||(d[16]=[S("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 19l-7-7 7-7m8 14l-7-7 7-7"})],-1)])],8,La),S("button",{onClick:d[3]||(d[3]=u=>l.$emit("previous-page")),disabled:!l.canNavigatePrevious,class:"px-2 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",title:l.$t("tools.pdfViewer.previousPage")},[...d[17]||(d[17]=[S("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])],8,Da),S("div",Na,[S("input",{value:l.currentPage,onInput:n,onKeyup:rn(r,["enter"]),type:"number",min:1,max:l.pageCount,class:"w-16 px-2 py-1 text-center border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,40,Fa),S("span",Oa,"/ "+R(l.pageCount),1)]),S("button",{onClick:d[4]||(d[4]=u=>l.$emit("next-page")),disabled:!l.canNavigateNext,class:"px-2 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",title:l.$t("tools.pdfViewer.nextPage")},[...d[18]||(d[18]=[S("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)])],8,Ba),S("button",{onClick:d[5]||(d[5]=u=>l.$emit("last-page")),disabled:!l.canNavigateNext,class:"px-2 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",title:l.$t("tools.pdfViewer.lastPage")},[...d[19]||(d[19]=[S("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 5l7 7-7 7M5 5l7 7-7 7"})],-1)])],8,$a)]),S("div",Ha,[S("div",za,[S("button",{onClick:d[6]||(d[6]=u=>l.$emit("zoom-out")),disabled:!l.hasDocument,class:"px-2 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",title:l.$t("tools.pdfViewer.zoomOut")},[...d[20]||(d[20]=[S("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7"})],-1)])],8,Va),S("select",{value:l.scale,onChange:a,disabled:!l.hasDocument,class:"px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"},[(O(!0),F(ce,null,de(l.zoomLevels,u=>(O(),F("option",{key:u,value:u},R(Math.round(u*100))+"% ",9,ja))),128))],40,Ua),S("button",{onClick:d[7]||(d[7]=u=>l.$emit("zoom-in")),disabled:!l.hasDocument,class:"px-2 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",title:l.$t("tools.pdfViewer.zoomIn")},[...d[21]||(d[21]=[S("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"})],-1)])],8,Ga),S("button",{onClick:d[8]||(d[8]=u=>l.$emit("fit-width")),disabled:!l.hasDocument,class:"px-2 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed text-xs",title:l.$t("tools.pdfViewer.fitWidth")}," Fit ",8,Wa)]),S("div",qa,[S("button",{onClick:d[9]||(d[9]=u=>l.$emit("rotate-left")),disabled:!l.hasDocument,class:"px-2 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",title:l.$t("tools.pdfViewer.rotateLeft")},[...d[22]||(d[22]=[S("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"})],-1)])],8,Xa),S("button",{onClick:d[10]||(d[10]=u=>l.$emit("rotate-right")),disabled:!l.hasDocument,class:"px-2 py-1 bg-white border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",title:l.$t("tools.pdfViewer.rotateRight")},[...d[23]||(d[23]=[S("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 10H11a8 8 0 00-8 8v2m18-10l-6 6m6-6l-6-6"})],-1)])],8,Ya)]),l.showAnnotationTools?(O(),F("div",Ka,[(O(!0),F(ce,null,de(l.annotationTools,u=>(O(),F("button",{key:u.type,onClick:f=>l.$emit("select-tool",u.type),class:Ke(["px-2 py-1 border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed text-sm",l.currentTool===u.type?"bg-blue-100 border-blue-300 text-blue-700":"bg-white border-gray-300"]),disabled:!l.hasDocument,title:u.label},R(u.icon),11,Qa))),128)),S("div",Za,[S("input",{value:l.currentColor,onInput:o,type:"color",class:"w-6 h-6 border border-gray-300 rounded cursor-pointer",disabled:!l.hasDocument},null,40,Ja),S("select",{value:l.currentStrokeWidth,onChange:h,disabled:!l.hasDocument,class:"px-1 py-1 text-xs border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"},[(O(!0),F(ce,null,de(l.strokeWidths,u=>(O(),F("option",{key:u,value:u},R(u)+"px ",9,eo))),128))],40,to)])])):tt("",!0)])]),l.showAnnotationTools&&l.currentTool?(O(),F("div",so,[S("div",io,[S("div",no,[S("span",ro,R(l.$t("tools.pdfViewer.selectedTool"))+":",1),S("span",ao,R(l.annotationTools.find(u=>u.type===l.currentTool)?.label),1)]),S("div",oo,[S("button",{onClick:d[11]||(d[11]=u=>l.$emit("clear-annotations")),class:"px-3 py-1 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"},R(l.$t("tools.pdfViewer.clearAll")),1),S("button",{onClick:d[12]||(d[12]=u=>l.$emit("export-annotations")),class:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"},R(l.$t("tools.pdfViewer.export")),1),S("button",{onClick:d[13]||(d[13]=u=>l.$emit("import-annotations")),class:"px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"},R(l.$t("tools.pdfViewer.import")),1)])])])):tt("",!0)]))}}),ho=Me(lo,[["__scopeId","data-v-50be0876"]]),co={class:"flex justify-center bg-gray-100 p-4 rounded-lg"},uo={key:0,class:"absolute inset-0 pointer-events-none"},fo=["onClick","onDblclick"],po=["viewBox"],go=["width","height","stroke","stroke-width"],mo=["cx","cy","rx","ry","stroke","stroke-width"],bo=["x2","y2","stroke","stroke-width"],vo=["viewBox"],yo=["points","stroke","stroke-width"],wo={key:1,class:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center"},Ao={class:"text-center"},_o={class:"text-gray-600"},So={key:2,class:"absolute inset-0 bg-red-50 flex items-center justify-center"},Eo={class:"text-center p-4"},Co={class:"text-red-700"},xo=Re({__name:"PdfCanvas",props:{isLoading:{type:Boolean,default:!1},error:{default:null},currentPage:{default:1},scale:{default:1},rotation:{default:0},currentTool:{default:null},showAnnotations:{type:Boolean,default:!0},pageAnnotations:{default:()=>[]},selectedAnnotation:{default:null}},emits:["canvas-ready","start-drawing","continue-drawing","finish-drawing","select-annotation","edit-annotation","canvas-click"],setup(c,{expose:t,emit:e}){const s=c,i=e,n=X(null),r=X(null),a=X(!1),o=X(0),h=X(0);function l(v){if(!r.value||s.isLoading)return;const E=r.value.getBoundingClientRect(),A=r.value.width/E.width,T=r.value.height/E.height,k=(v.clientX-E.left)*A,I=(v.clientY-E.top)*T;s.currentTool?(a.value=!0,i("start-drawing",{pageNumber:s.currentPage,x:k,y:I})):i("canvas-click",{x:k,y:I})}function d(v){if(!a.value||!r.value||!s.currentTool)return;const E=r.value.getBoundingClientRect(),A=r.value.width/E.width,T=r.value.height/E.height,k=(v.clientX-E.left)*A,I=(v.clientY-E.top)*T;i("continue-drawing",{pageNumber:s.currentPage,x:k,y:I})}function u(){a.value&&(a.value=!1,i("finish-drawing"))}function f(){a.value&&(a.value=!1,i("finish-drawing"))}function g(v){i("select-annotation",v)}function p(v){i("edit-annotation",v)}function b(v){if(!r.value)return{};const E=r.value.getBoundingClientRect(),A=E.width/r.value.width,T=E.height/r.value.height;return{left:`${v.x*A}px`,top:`${v.y*T}px`,width:`${(v.width||0)*A}px`,height:`${(v.height||0)*T}px`}}function m(){r.value&&(o.value=r.value.width,h.value=r.value.height)}xe(()=>r.value,v=>{v&&(i("canvas-ready",v),m())},{immediate:!0}),xe([()=>s.scale,()=>s.rotation],()=>{m()}),an(()=>{document.addEventListener("keydown",y),document.addEventListener("keyup",w),window.addEventListener("resize",_)}),_i(()=>{document.removeEventListener("keydown",y),document.removeEventListener("keyup",w),window.removeEventListener("resize",_)});function _(){r.value&&(m(),i("canvas-ready",r.value))}function y(v){v.key==="Delete"&&s.selectedAnnotation&&v.preventDefault(),v.key==="Escape"&&a.value&&(a.value=!1,i("finish-drawing"))}function w(){}return t({updateCanvasDimensions:m,getCanvasElement:()=>r.value,getCanvasContainer:()=>n.value}),(v,E)=>(O(),F("div",co,[S("div",{ref_key:"canvasContainer",ref:n,class:"relative w-full max-w-4xl bg-white rounded shadow-lg",style:{aspectRatio:"210/297"}},[S("canvas",{ref_key:"pdfCanvas",ref:r,class:Ke(["block cursor-crosshair w-full h-full",{"cursor-pointer":!v.currentTool}]),style:{display:"block"},onMousedown:l,onMousemove:d,onMouseup:u,onMouseleave:f,onContextmenu:E[0]||(E[0]=Ai(()=>{},["prevent"]))},null,34),v.showAnnotations?(O(),F("div",uo,[(O(!0),F(ce,null,de(v.pageAnnotations,A=>(O(),F("div",{key:A.id,class:Ke(["absolute border-2 pointer-events-auto cursor-pointer",v.selectedAnnotation?.id===A.id?"border-blue-500 bg-blue-100 bg-opacity-30":"border-transparent hover:border-gray-400"]),style:fs(b(A)),onClick:T=>g(A),onDblclick:T=>p(A)},[A.type==="text"?(O(),F("div",{key:0,class:"p-1 text-sm bg-yellow-100 border border-yellow-300 rounded",style:fs({color:A.color})},R(A.text||"Text"),5)):A.type==="highlight"?(O(),F("div",{key:1,class:"w-full h-full opacity-50",style:fs({backgroundColor:A.color})},null,4)):["rectangle","circle","line"].includes(A.type)?(O(),F("svg",{key:2,class:"w-full h-full",viewBox:`0 0 ${A.width||0} ${A.height||0}`},[A.type==="rectangle"?(O(),F("rect",{key:0,width:A.width||0,height:A.height||0,fill:"none",stroke:A.color,"stroke-width":A.strokeWidth},null,8,go)):A.type==="circle"?(O(),F("ellipse",{key:1,cx:(A.width||0)/2,cy:(A.height||0)/2,rx:(A.width||0)/2,ry:(A.height||0)/2,fill:"none",stroke:A.color,"stroke-width":A.strokeWidth},null,8,mo)):A.type==="line"?(O(),F("line",{key:2,x1:"0",y1:"0",x2:A.width||0,y2:A.height||0,stroke:A.color,"stroke-width":A.strokeWidth},null,8,bo)):tt("",!0)],8,po)):A.type==="freehand"&&A.points?(O(),F("svg",{key:3,class:"w-full h-full absolute inset-0",viewBox:`0 0 ${r.value?.width||800} ${r.value?.height||600}`},[S("polyline",{points:A.points.map(T=>`${T.x},${T.y}`).join(" "),fill:"none",stroke:A.color,"stroke-width":A.strokeWidth,"stroke-linecap":"round","stroke-linejoin":"round"},null,8,yo)],8,vo)):tt("",!0)],46,fo))),128))])):tt("",!0),v.isLoading?(O(),F("div",wo,[S("div",Ao,[E[1]||(E[1]=S("div",{class:"animate-spin inline-block w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mb-2"},null,-1)),S("p",_o,R(v.$t("tools.pdfViewer.rendering")),1)])])):tt("",!0),v.error?(O(),F("div",So,[S("div",Eo,[E[2]||(E[2]=S("div",{class:"text-red-500 text-4xl mb-2"},"⚠️",-1)),S("p",Co,R(v.error),1)])])):tt("",!0)],512)]))}}),To=Me(xo,[["__scopeId","data-v-186410e3"]]),ko={class:"bg-white rounded-lg shadow-md p-6"},Po={class:"text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2"},Ro={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6"},Mo={class:"border border-gray-200 rounded-lg p-4"},Io={class:"font-medium text-gray-900 mb-2 flex items-center"},Lo={class:"text-gray-600 break-all"},Do={class:"border border-gray-200 rounded-lg p-4"},No={class:"font-medium text-gray-900 mb-2 flex items-center"},Fo={class:"text-gray-600"},Oo={class:"border border-gray-200 rounded-lg p-4"},Bo={class:"font-medium text-gray-900 mb-2 flex items-center"},$o={class:"text-gray-600"},Ho={key:0,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6"},zo={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},Vo={class:"font-medium text-blue-900 mb-2"},Uo={class:"text-blue-700 text-lg font-semibold"},jo={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Go={class:"font-medium text-green-900 mb-2"},Wo={class:"text-green-700 text-lg font-semibold"},qo={class:"bg-purple-50 border border-purple-200 rounded-lg p-4"},Xo={class:"font-medium text-purple-900 mb-2"},Yo={class:"text-purple-700 text-lg font-semibold"},Ko={class:"bg-orange-50 border border-orange-200 rounded-lg p-4"},Qo={class:"font-medium text-orange-900 mb-2"},Zo={class:"text-orange-700 text-lg font-semibold"},Jo={key:1,class:"border-t border-gray-200 pt-4"},tl={class:"font-medium text-gray-900 mb-4 flex items-center"},el={class:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3"},sl={class:"bg-gray-50 rounded-lg p-3 text-center"},il={class:"text-2xl font-bold text-gray-700"},nl={class:"text-xs text-gray-500"},rl={class:"text-2xl font-bold text-gray-700"},al={class:"text-xs text-gray-500 capitalize"},ol={key:0,class:"mt-4"},ll={class:"font-medium text-gray-700 mb-2"},hl={class:"flex flex-wrap gap-2"},cl={key:2,class:"border-t border-gray-200 pt-4 mt-4"},dl={class:"font-medium text-gray-900 mb-4 flex items-center"},ul={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},fl={key:0,class:"border border-gray-200 rounded-lg p-3"},pl={class:"font-medium text-gray-700 mb-1"},gl={class:"text-gray-600 text-sm"},ml={key:1,class:"border border-gray-200 rounded-lg p-3"},bl={class:"font-medium text-gray-700 mb-1"},vl={class:"text-gray-600 text-sm"},yl={key:2,class:"border border-gray-200 rounded-lg p-3"},wl={class:"font-medium text-gray-700 mb-1"},Al={class:"text-gray-600 text-sm"},_l={key:3,class:"border border-gray-200 rounded-lg p-3"},Sl={class:"font-medium text-gray-700 mb-1"},El={class:"text-gray-600 text-sm"},Cl={key:4,class:"border border-gray-200 rounded-lg p-3"},xl={class:"font-medium text-gray-700 mb-1"},Tl={class:"text-gray-600 text-sm"},kl={key:5,class:"border border-gray-200 rounded-lg p-3"},Pl={class:"font-medium text-gray-700 mb-1"},Rl={class:"text-gray-600 text-sm"},Ml={key:6,class:"border border-gray-200 rounded-lg p-3"},Il={class:"font-medium text-gray-700 mb-1"},Ll={class:"text-gray-600 text-sm"},Dl={key:7,class:"border border-gray-200 rounded-lg p-3"},Nl={class:"font-medium text-gray-700 mb-1"},Fl={class:"text-gray-600 text-sm"},Ol=Re({__name:"PdfInfo",props:{fileName:{default:""},fileSize:{default:""},pageCount:{default:0},currentPage:{default:1},scale:{default:1},rotation:{default:0},hasDocument:{type:Boolean,default:!1},showAnnotationStats:{type:Boolean,default:!1},annotationStats:{default:null},metadata:{default:null}},setup(c){const t=Xt(()=>({text:"Text",highlight:"Highlight",rectangle:"Rectangle",circle:"Circle",line:"Line",freehand:"Freehand"}));function e(i){return t.value[i]||i}function s(i){try{if(i.startsWith("D:")){const r=i.substring(2,16),a=r.substring(0,4),o=r.substring(4,6),h=r.substring(6,8),l=r.substring(8,10),d=r.substring(10,12),u=new Date(`${a}-${o}-${h}T${l}:${d}:00`);return u.toLocaleDateString()+" "+u.toLocaleTimeString()}const n=new Date(i);return n.toLocaleDateString()+" "+n.toLocaleTimeString()}catch{return i}}return(i,n)=>(O(),F("div",ko,[S("h3",Po,R(i.$t("tools.pdfViewer.documentInfo")),1),S("div",Ro,[S("div",Mo,[S("h4",Io,[n[0]||(n[0]=S("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)),be(" "+R(i.$t("tools.pdfViewer.fileName")),1)]),S("p",Lo,R(i.fileName||i.$t("tools.pdfViewer.unknown")),1)]),S("div",Do,[S("h4",No,[n[1]||(n[1]=S("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z"})],-1)),be(" "+R(i.$t("tools.pdfViewer.fileSize")),1)]),S("p",Fo,R(i.fileSize||i.$t("tools.pdfViewer.unknown")),1)]),S("div",Oo,[S("h4",Bo,[n[2]||(n[2]=S("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"})],-1)),be(" "+R(i.$t("tools.pdfViewer.pageCount")),1)]),S("p",$o,R(i.pageCount||0),1)])]),i.hasDocument?(O(),F("div",Ho,[S("div",zo,[S("h4",Vo,R(i.$t("tools.pdfViewer.currentPage")),1),S("p",Uo,R(i.currentPage)+" / "+R(i.pageCount),1)]),S("div",jo,[S("h4",Go,R(i.$t("tools.pdfViewer.zoomLevel")),1),S("p",Wo,R(Math.round(i.scale*100))+"%",1)]),S("div",qo,[S("h4",Xo,R(i.$t("tools.pdfViewer.rotation")),1),S("p",Yo,R(i.rotation)+"°",1)]),S("div",Ko,[S("h4",Qo,R(i.$t("tools.pdfViewer.viewMode")),1),S("p",Zo,R(i.scale===1?i.$t("tools.pdfViewer.actual"):i.scale>1?i.$t("tools.pdfViewer.zoomed"):i.$t("tools.pdfViewer.reduced")),1)])])):tt("",!0),i.showAnnotationStats&&i.annotationStats?(O(),F("div",Jo,[S("h4",tl,[n[3]||(n[3]=S("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)),be(" "+R(i.$t("tools.pdfViewer.annotations")),1)]),S("div",el,[S("div",sl,[S("p",il,R(i.annotationStats.total),1),S("p",nl,R(i.$t("tools.pdfViewer.total")),1)]),(O(!0),F(ce,null,de(i.annotationStats.byType,(r,a)=>(O(),F("div",{key:a,class:"bg-gray-50 rounded-lg p-3 text-center"},[S("p",rl,R(r),1),S("p",al,R(e(a)),1)]))),128))]),Object.keys(i.annotationStats.byPage).length>0?(O(),F("div",ol,[S("h5",ll,R(i.$t("tools.pdfViewer.pagesWithAnnotations"))+":",1),S("div",hl,[(O(!0),F(ce,null,de(i.annotationStats.byPage,(r,a)=>(O(),F("span",{key:a,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},R(i.$t("tools.pdfViewer.page"))+" "+R(a)+" ("+R(r)+") ",1))),128))])])):tt("",!0)])):tt("",!0),i.metadata&&Object.keys(i.metadata).length>0?(O(),F("div",cl,[S("h4",dl,[n[4]||(n[4]=S("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[S("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),be(" "+R(i.$t("tools.pdfViewer.metadata")),1)]),S("div",ul,[i.metadata.Title?(O(),F("div",fl,[S("h5",pl,R(i.$t("tools.pdfViewer.title")),1),S("p",gl,R(i.metadata.Title),1)])):tt("",!0),i.metadata.Author?(O(),F("div",ml,[S("h5",bl,R(i.$t("tools.pdfViewer.author")),1),S("p",vl,R(i.metadata.Author),1)])):tt("",!0),i.metadata.Subject?(O(),F("div",yl,[S("h5",wl,R(i.$t("tools.pdfViewer.subject")),1),S("p",Al,R(i.metadata.Subject),1)])):tt("",!0),i.metadata.Creator?(O(),F("div",_l,[S("h5",Sl,R(i.$t("tools.pdfViewer.creator")),1),S("p",El,R(i.metadata.Creator),1)])):tt("",!0),i.metadata.Producer?(O(),F("div",Cl,[S("h5",xl,R(i.$t("tools.pdfViewer.producer")),1),S("p",Tl,R(i.metadata.Producer),1)])):tt("",!0),i.metadata.CreationDate?(O(),F("div",kl,[S("h5",Pl,R(i.$t("tools.pdfViewer.creationDate")),1),S("p",Rl,R(s(String(i.metadata.CreationDate))),1)])):tt("",!0),i.metadata.ModDate?(O(),F("div",Ml,[S("h5",Il,R(i.$t("tools.pdfViewer.modificationDate")),1),S("p",Ll,R(s(String(i.metadata.ModDate))),1)])):tt("",!0),i.metadata.Keywords?(O(),F("div",Dl,[S("h5",Nl,R(i.$t("tools.pdfViewer.keywords")),1),S("p",Fl,R(i.metadata.Keywords),1)])):tt("",!0)])])):tt("",!0)]))}}),Bl=Me(Ol,[["__scopeId","data-v-361f3836"]]),$l={class:"min-h-screen bg-gray-50"},Hl={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},zl={class:"text-center mb-12"},Vl={class:"text-4xl font-bold text-gray-900 mb-4"},Ul={class:"text-xl text-gray-600 max-w-3xl mx-auto"},jl={class:"mb-8"},Gl={key:0,class:"space-y-6"},Wl={key:1,class:"mt-8"},ql=Re({__name:"PdfViewer",setup(c){const t=Jr(),e=ta(),s=X(!1),i=X(null),n=X(null),r=X(null),a=Xt(()=>e.getAnnotationsForPage(t.currentPage.value));async function o(A){try{await t.loadPdfFromFile(A),await E()}catch(T){console.error("Error loading PDF file:",T)}}async function h(A){try{await t.loadPdfFromUrl(A),await E()}catch(T){console.error("Error loading PDF from URL:",T)}}function l(){t.reset(),e.clearAnnotations(),n.value=null,i.value=null}function d(){}function u(){l()}async function f(A){r.value=A,t.hasDocument.value&&(await Js(),setTimeout(async()=>{await g()},50))}async function g(){if(r.value){s.value=!0,i.value=null;try{await t.renderPage(r.value)}catch(A){i.value=A instanceof Error?A.message:"Failed to render page",console.error("Error rendering page:",A)}finally{s.value=!1}}}function p(A){e.startDrawing(A.pageNumber,A.x,A.y)}function b(A){e.continueDrawing(A.pageNumber,A.x,A.y)}function m(){e.finishDrawing()}function _(A){console.log("Edit annotation:",A)}function y(A){console.log("Canvas clicked:",A)}function w(){e.downloadAnnotations(`${t.fileName.value}-annotations.json`)}function v(){const A=document.createElement("input");A.type="file",A.accept=".json",A.onchange=async T=>{const k=T.target.files?.[0];if(k)try{const I=await k.text();e.importAnnotations(I)}catch(I){console.error("Error importing annotations:",I)}},A.click()}async function E(){if(t.pdfDocument)try{const A=await t.pdfDocument.getMetadata();n.value=A.info}catch(A){console.error("Error loading PDF metadata:",A),n.value=null}}return xe(()=>t.hasDocument.value,async A=>{A&&r.value&&(await Js(),setTimeout(async()=>{await g()},100))}),xe([()=>t.currentPage.value,()=>t.scale.value,()=>t.rotation.value],async()=>{t.hasDocument.value&&r.value&&await g()}),_i(()=>{t.cleanup()}),(A,T)=>(O(),F("div",$l,[S("div",Hl,[S("div",zl,[S("h1",Vl,R(A.$t("tools.pdfViewer.title")),1),S("p",Ul,R(A.$t("tools.pdfViewer.description")),1)]),S("div",jl,[Be(xa,{"is-loading":$(t).isLoading.value,error:$(t).error.value,"file-name":$(t).fileName.value,"file-size":$(t).fileSize.value,"max-size-m-b":50,"can-load-from-url":!0,onFileSelected:o,onUrlSelected:h,onClearFile:l,onClearError:d},null,8,["is-loading","error","file-name","file-size"])]),$(t).hasDocument.value?(O(),F("div",Gl,[Be(ho,{"has-document":$(t).hasDocument.value,"current-page":$(t).currentPage.value,"page-count":$(t).pageCount.value,"can-navigate-previous":$(t).canNavigatePrevious.value,"can-navigate-next":$(t).canNavigateNext.value,scale:$(t).scale.value,"zoom-levels":$(t).zoomLevels,"show-annotation-tools":!0,"current-tool":$(e).currentTool.value,"current-color":$(e).currentColor.value,"current-stroke-width":$(e).currentStrokeWidth.value,"annotation-tools":$(e).availableTools,"stroke-widths":$(e).strokeWidths,onDownload:$(t).downloadPdf,onReset:u,onFirstPage:$(t).firstPage,onPreviousPage:$(t).previousPage,onNextPage:$(t).nextPage,onLastPage:$(t).lastPage,onGoToPage:$(t).goToPage,onZoomIn:$(t).zoomIn,onZoomOut:$(t).zoomOut,onSetZoom:$(t).setZoom,onFitWidth:$(t).fitToWidth,onRotateLeft:$(t).rotateLeft,onRotateRight:$(t).rotateRight,onSelectTool:$(e).setTool,onSetColor:$(e).setColor,onSetStrokeWidth:$(e).setStrokeWidth,onClearAnnotations:$(e).clearAnnotations,onExportAnnotations:w,onImportAnnotations:v},null,8,["has-document","current-page","page-count","can-navigate-previous","can-navigate-next","scale","zoom-levels","current-tool","current-color","current-stroke-width","annotation-tools","stroke-widths","onDownload","onFirstPage","onPreviousPage","onNextPage","onLastPage","onGoToPage","onZoomIn","onZoomOut","onSetZoom","onFitWidth","onRotateLeft","onRotateRight","onSelectTool","onSetColor","onSetStrokeWidth","onClearAnnotations"]),Be(To,{"is-loading":s.value,error:i.value,"current-page":$(t).currentPage.value,scale:$(t).scale.value,rotation:$(t).rotation.value,"current-tool":$(e).currentTool.value,"show-annotations":!0,"page-annotations":a.value,"selected-annotation":$(e).selectedAnnotation.value,onCanvasReady:f,onStartDrawing:p,onContinueDrawing:b,onFinishDrawing:m,onSelectAnnotation:$(e).selectAnnotation,onEditAnnotation:_,onCanvasClick:y},null,8,["is-loading","error","current-page","scale","rotation","current-tool","page-annotations","selected-annotation","onSelectAnnotation"])])):tt("",!0),$(t).hasDocument.value?(O(),F("div",Wl,[Be(Bl,{"file-name":$(t).fileName.value,"file-size":$(t).fileSize.value,"page-count":$(t).pageCount.value,"current-page":$(t).currentPage.value,scale:$(t).scale.value,rotation:$(t).rotation.value,"has-document":$(t).hasDocument.value,"show-annotation-stats":!0,"annotation-stats":$(e).getAnnotationStats(),metadata:n.value},null,8,["file-name","file-size","page-count","current-page","scale","rotation","has-document","annotation-stats","metadata"])])):tt("",!0)])]))}}),Kl=Me(ql,[["__scopeId","data-v-a6cc9892"]]);export{Kl as default};
