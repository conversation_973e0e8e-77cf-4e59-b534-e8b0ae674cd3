import{d,c as s,a as e,X as n,f as i,t as o,F as c,k as m,o as a,h as p}from"./index-CkZTMFXG.js";const u={class:"min-h-screen bg-gradient-to-br from-dark-950 via-dark-900 to-dark-800 p-4 md:p-6"},_={class:"max-w-7xl mx-auto"},f={class:"mb-8 animate-fade-in"},x={class:"flex items-center justify-between mb-6"},h={class:"flex items-center space-x-4"},b={class:"w-16 h-16 bg-primary-500/10 rounded-2xl flex items-center justify-center text-3xl animate-bounce-subtle"},v={class:"text-3xl md:text-4xl font-bold text-gradient mb-2"},g={class:"text-slate-400 text-lg"},k={class:"flex items-center space-x-3"},y={key:0,class:"flex flex-wrap gap-2"},w={class:"mt-12 text-center"},B={class:"glass rounded-xl border border-slate-700/30 p-4"},N={class:"text-slate-400 text-sm"},j=d({__name:"ToolLayout",props:{title:{},description:{},icon:{},features:{default:()=>[]}},setup(V){return(t,r)=>(a(),s("div",u,[e("div",_,[e("div",f,[e("div",x,[e("div",h,[e("div",b,o(t.icon),1),e("div",null,[e("h1",v,o(t.title),1),e("p",g,o(t.description),1)])]),e("div",k,[n(t.$slots,"header-actions")])]),t.features&&t.features.length>0?(a(),s("div",y,[(a(!0),s(c,null,m(t.features,l=>(a(),s("span",{key:l,class:"px-3 py-1 text-xs font-medium bg-slate-800/50 text-slate-300 rounded-full border border-slate-700/50 hover:bg-slate-800/70 transition-all duration-200"},o(l),1))),128))])):i("",!0)]),n(t.$slots,"default"),e("div",w,[e("div",B,[e("p",N,[n(t.$slots,"footer",{},()=>[r[0]||(r[0]=p(" 💡 提示：此工具完全在浏览器中运行，您的数据不会上传到服务器 ",-1))])])])])])]))}});export{j as _};
