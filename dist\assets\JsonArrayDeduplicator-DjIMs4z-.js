import{d as F,u as E,r as y,c,a as e,t as s,e as f,f as v,g as $,v as k,h as S,j as C,o as p}from"./index-CkZTMFXG.js";import{u as I}from"./useToast-virEbLJw.js";const B={class:"min-h-screen bg-gray-50 p-6"},T={class:"max-w-6xl mx-auto space-y-6"},q={class:"text-center mb-8"},L={class:"text-3xl font-bold text-gray-900 mb-2"},z={class:"text-gray-600"},M={class:"grid md:grid-cols-3 gap-6 mb-8"},P={class:"bg-white p-6 rounded-lg shadow-sm border"},G={class:"text-lg font-semibold mb-2"},H={class:"text-gray-600 text-sm"},K={class:"bg-white p-6 rounded-lg shadow-sm border"},Q={class:"text-lg font-semibold mb-2"},W={class:"text-gray-600 text-sm"},X={class:"bg-white p-6 rounded-lg shadow-sm border"},Y={class:"text-lg font-semibold mb-2"},Z={class:"text-gray-600 text-sm"},ee={class:"grid lg:grid-cols-2 gap-6"},te={class:"bg-white p-6 rounded-lg shadow-sm border"},se={class:"flex items-center justify-between mb-4"},oe={class:"text-lg font-semibold text-gray-900"},le={class:"flex space-x-2"},re=["placeholder"],ae={class:"mt-4 space-y-4"},ne={class:"font-medium text-gray-900"},ie={class:"space-y-3"},de={class:"flex items-center space-x-4"},ue={class:"text-sm font-medium text-gray-700 w-32"},ce={value:"deep"},pe={value:"shallow"},me={value:"field"},ve={value:"stringify"},ye={key:0,class:"flex items-center space-x-4"},be={class:"text-sm font-medium text-gray-700 w-32"},ge=["placeholder"],fe={class:"flex items-center space-x-4"},he={class:"text-sm font-medium text-gray-700 w-32"},xe={value:"first"},_e={value:"last"},je={class:"grid grid-cols-1 gap-3"},we={class:"flex items-center"},Ae={class:"flex items-center"},De={key:0,class:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg"},$e={class:"font-medium text-blue-900 mb-2"},ke={class:"text-sm text-blue-800 space-y-1"},Se={key:0},Ce=["disabled"],Oe={class:"bg-white p-6 rounded-lg shadow-sm border"},Je={class:"flex items-center justify-between mb-4"},Re={class:"text-lg font-semibold text-gray-900"},Ne={class:"flex space-x-2"},Ue={key:0,class:"h-80 flex items-center justify-center text-gray-500 border-2 border-dashed border-gray-200 rounded-lg"},Ve={class:"text-center"},Fe={key:1,class:"h-80 flex items-center justify-center"},Ee={class:"text-center text-red-600"},Ie={class:"font-medium"},Be={class:"text-sm"},Te={key:2,class:"space-y-4"},qe={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Le={class:"flex items-center"},ze={class:"font-medium text-green-800"},Me={class:"text-sm text-green-600"},Pe=["value"],Qe=F({__name:"JsonArrayDeduplicator",setup(Ge){const{t:h}=E(),{success:A,copySuccess:O}=I(),m=y(""),a=y(""),i=y(""),b=y(!1),D=y(""),r=y({method:"deep",compareField:"",keepOccurrence:"first",caseSensitive:!0,showDuplicates:!1}),d=y(null);function J(){const t=[{id:1,name:"John",age:30},{id:2,name:"Jane",age:25},{id:1,name:"John",age:30},{id:3,name:"Bob",age:35},{id:2,name:"Jane",age:25},{id:4,name:"Alice",age:28}];m.value=JSON.stringify(t,null,2),g()}function R(){m.value="",a.value="",i.value="",b.value=!1,d.value=null}function g(){if(i.value="",d.value=null,!m.value.trim()){b.value=!1;return}try{const t=JSON.parse(m.value);if(!Array.isArray(t))throw new Error(h("tools.jsonArrayDeduplicator.errors.invalidArray"));b.value=!0;const o=t.length,x=new Set(t.map(n=>JSON.stringify(n))).size,_=o-x;d.value={total:o,unique:x,duplicates:_,toRemove:_}}catch(t){i.value=h("tools.jsonArrayDeduplicator.errors.invalidJson")+" "+t.message,b.value=!1}}function N(){if(b.value)try{const t=JSON.parse(m.value);let o=[],l=0;switch(r.value.method){case"deep":const x=new Set;o=t.filter(n=>{const u=JSON.stringify(n);return x.has(u)?(l++,!1):(x.add(u),!0)});break;case"shallow":o=t.filter((n,u)=>t.findIndex(j=>j===n)!==u?(l++,!1):!0);break;case"field":if(r.value.compareField){const n=new Set;o=t.filter(u=>{const w=u[r.value.compareField],j=r.value.caseSensitive?String(w):String(w).toLowerCase();return n.has(j)?(l++,!1):(n.add(j),!0)})}else o=[...t];break;case"stringify":const _=new Set;o=t.filter(n=>{const u=JSON.stringify(n);return _.has(u)?(l++,!1):(_.add(u),!0)});break}a.value=JSON.stringify(o,null,2),D.value=`Removed ${l} duplicate items`,i.value="",A(h("toast.success"))}catch(t){i.value=h("tools.jsonArrayDeduplicator.errors.invalidJson")+" "+t.message}}function U(){a.value&&(navigator.clipboard.writeText(a.value),O())}function V(){if(a.value){const t=new Blob([a.value],{type:"application/json"}),o=URL.createObjectURL(t),l=document.createElement("a");l.href=o,l.download="deduplicated-array.json",document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(o),A(h("toast.downloadSuccess"))}}return(t,o)=>(p(),c("div",B,[e("div",T,[e("div",q,[e("h1",L,s(t.$t("tools.jsonArrayDeduplicator.title")),1),e("p",z,s(t.$t("tools.jsonArrayDeduplicator.description")),1)]),e("div",M,[e("div",P,[o[6]||(o[6]=e("div",{class:"text-2xl mb-3"},"🔍",-1)),e("h3",G,s(t.$t("tools.jsonArrayDeduplicator.features.smartDetection.title")),1),e("p",H,s(t.$t("tools.jsonArrayDeduplicator.features.smartDetection.description")),1)]),e("div",K,[o[7]||(o[7]=e("div",{class:"text-2xl mb-3"},"⚙️",-1)),e("h3",Q,s(t.$t("tools.jsonArrayDeduplicator.features.flexibleOptions.title")),1),e("p",W,s(t.$t("tools.jsonArrayDeduplicator.features.flexibleOptions.description")),1)]),e("div",X,[o[8]||(o[8]=e("div",{class:"text-2xl mb-3"},"📊",-1)),e("h3",Y,s(t.$t("tools.jsonArrayDeduplicator.features.detailedStats.title")),1),e("p",Z,s(t.$t("tools.jsonArrayDeduplicator.features.detailedStats.description")),1)])]),e("div",ee,[e("div",te,[e("div",se,[e("h3",oe,s(t.$t("tools.jsonArrayDeduplicator.inputTitle")),1),e("div",le,[e("button",{onClick:J,class:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"},s(t.$t("common.loadExample")),1),e("button",{onClick:R,class:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"},s(t.$t("common.clear")),1)])]),f(e("textarea",{"onUpdate:modelValue":o[0]||(o[0]=l=>m.value=l),placeholder:t.$t("tools.jsonArrayDeduplicator.inputPlaceholder"),class:"w-full h-64 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",onInput:g},null,40,re),[[$,m.value]]),e("div",ae,[e("h4",ne,s(t.$t("tools.jsonArrayDeduplicator.deduplicationOptions")),1),e("div",ie,[e("div",de,[e("label",ue,s(t.$t("tools.jsonArrayDeduplicator.comparisonMethod"))+":",1),f(e("select",{"onUpdate:modelValue":o[1]||(o[1]=l=>r.value.method=l),onChange:g,class:"flex-1 px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[e("option",ce,s(t.$t("tools.jsonArrayDeduplicator.deepComparison")),1),e("option",pe,s(t.$t("tools.jsonArrayDeduplicator.shallowComparison")),1),e("option",me,s(t.$t("tools.jsonArrayDeduplicator.fieldComparison")),1),e("option",ve,s(t.$t("tools.jsonArrayDeduplicator.stringComparison")),1)],544),[[k,r.value.method]])]),r.value.method==="field"?(p(),c("div",ye,[e("label",be,s(t.$t("tools.jsonArrayDeduplicator.compareField"))+":",1),f(e("input",{"onUpdate:modelValue":o[2]||(o[2]=l=>r.value.compareField=l),onInput:g,type:"text",placeholder:t.$t("tools.jsonArrayDeduplicator.compareField"),class:"flex-1 px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,40,ge),[[$,r.value.compareField]])])):v("",!0),e("div",fe,[e("label",he,s(t.$t("tools.jsonArrayDeduplicator.keepOccurrence"))+":",1),f(e("select",{"onUpdate:modelValue":o[3]||(o[3]=l=>r.value.keepOccurrence=l),class:"flex-1 px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[e("option",xe,s(t.$t("tools.jsonArrayDeduplicator.firstOccurrence")),1),e("option",_e,s(t.$t("tools.jsonArrayDeduplicator.lastOccurrence")),1)],512),[[k,r.value.keepOccurrence]])]),e("div",je,[e("label",we,[f(e("input",{"onUpdate:modelValue":o[4]||(o[4]=l=>r.value.caseSensitive=l),onChange:g,type:"checkbox",class:"mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"},null,544),[[C,r.value.caseSensitive]]),S(" "+s(t.$t("tools.jsonArrayDeduplicator.caseSensitive")),1)]),e("label",Ae,[f(e("input",{"onUpdate:modelValue":o[5]||(o[5]=l=>r.value.showDuplicates=l),onChange:g,type:"checkbox",class:"mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"},null,544),[[C,r.value.showDuplicates]]),S(" "+s(t.$t("tools.jsonArrayDeduplicator.showDuplicates")),1)])])])]),d.value?(p(),c("div",De,[e("h5",$e,s(t.$t("tools.jsonArrayDeduplicator.analysisResults"))+": ",1),e("div",ke,[e("p",null," • "+s(t.$t("tools.jsonArrayDeduplicator.totalElements"))+": "+s(d.value.total),1),e("p",null," • "+s(t.$t("tools.jsonArrayDeduplicator.uniqueElements"))+": "+s(d.value.unique),1),e("p",null," • "+s(t.$t("tools.jsonArrayDeduplicator.duplicatesFound"))+": "+s(d.value.duplicates),1),d.value.duplicates>0?(p(),c("p",Se," • "+s(t.$t("tools.jsonArrayDeduplicator.willRemove"))+": "+s(d.value.toRemove)+" "+s(t.$t("common.items")),1)):v("",!0)])])):v("",!0),e("button",{onClick:N,disabled:!m.value.trim()||!b.value,class:"w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},s(t.$t("tools.jsonArrayDeduplicator.deduplicateButton")),9,Ce)]),e("div",Oe,[e("div",Je,[e("h3",Re,s(t.$t("tools.jsonArrayDeduplicator.deduplicatedArray")),1),e("div",Ne,[a.value?(p(),c("button",{key:0,onClick:U,class:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"},s(t.$t("common.copy")),1)):v("",!0),a.value?(p(),c("button",{key:1,onClick:V,class:"px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"},s(t.$t("common.download")),1)):v("",!0)])]),!a.value&&!i.value?(p(),c("div",Ue,[e("div",Ve,[o[9]||(o[9]=e("div",{class:"text-3xl mb-2"},"🔍",-1)),e("p",null,s(t.$t("tools.jsonArrayDeduplicator.noResults")),1)])])):v("",!0),i.value?(p(),c("div",Fe,[e("div",Ee,[o[10]||(o[10]=e("div",{class:"text-3xl mb-2"},"❌",-1)),e("p",Ie,s(t.$t("toast.error")),1),e("p",Be,s(i.value),1)])])):v("",!0),a.value&&!i.value?(p(),c("div",Te,[e("div",qe,[e("div",Le,[o[11]||(o[11]=e("div",{class:"text-green-600 text-2xl mr-3"},"✅",-1)),e("div",null,[e("p",ze,s(t.$t("tools.jsonArrayDeduplicator.deduplicationComplete")),1),e("p",Me,s(D.value),1)])])]),e("textarea",{value:a.value,readonly:"",class:"w-full h-80 p-4 border border-gray-300 rounded-lg font-mono text-sm bg-gray-50 resize-none"},null,8,Pe)])):v("",!0)])])])]))}});export{Qe as default};
