const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/jszip.min-BZakjvyN.js","assets/_commonjsHelpers-DsqdWQfm.js","assets/_commonjs-dynamic-modules-TDtrdbi3.js"])))=>i.map(i=>d[i]);
import{P as M,o as A,X as J,Y as N,U as S,Q as $,F as vt,k as bt,Z as wt,e as yt,$ as Kt,a0 as te,f as it,R as Dt,d as Me,u as Se,r as _,a1 as Ee,a2 as Ce,a as p,c as rt,t as x,S as Te,h as at,V as Mt,v as We,j as $e,a3 as De,_ as Le,W as Oe}from"./index-CkZTMFXG.js";import{u as Ie}from"./useToast-virEbLJw.js";import{_ as He}from"./ToolLayout.vue_vue_type_script_setup_true_lang-BsMmX7pX.js";import{_ as ht,a as Pe}from"./Card.vue_vue_type_script_setup_true_lang-DgPPwsWa.js";function Ft(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(s){return Object.getOwnPropertyDescriptor(t,s).enumerable}))),i.push.apply(i,o)}return i}function z(t){for(var e=1;e<arguments.length;e++){var i=arguments[e]!=null?arguments[e]:{};e%2?Ft(Object(i),!0).forEach((function(o){H(t,o,i[o])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Ft(Object(i)).forEach((function(o){Object.defineProperty(t,o,Object.getOwnPropertyDescriptor(i,o))}))}return t}function H(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function je(t,e){if(t==null)return{};var i,o,s=(function(a,h){if(a==null)return{};var r,l,u={},c=Object.keys(a);for(l=0;l<c.length;l++)r=c[l],h.indexOf(r)>=0||(u[r]=a[r]);return u})(t,e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);for(o=0;o<n.length;o++)i=n[o],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(s[i]=t[i])}return s}function Q(t){return(function(e){if(Array.isArray(e))return St(e)})(t)||(function(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)})(t)||(function(e,i){if(e){if(typeof e=="string")return St(e,i);var o=Object.prototype.toString.call(e).slice(8,-1);if(o==="Object"&&e.constructor&&(o=e.constructor.name),o==="Map"||o==="Set")return Array.from(e);if(o==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))return St(e,i)}})(t)||(function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)})()}function St(t,e){(e==null||e>t.length)&&(e=t.length);for(var i=0,o=new Array(e);i<e;i++)o[i]=t[i];return o}var Gt,_e,ut,E=(Gt=function(t){/*!
  Copyright (c) 2018 Jed Watson.
  Licensed under the MIT License (MIT), see
  http://jedwatson.github.io/classnames
*/(function(){var e={}.hasOwnProperty;function i(){for(var o=[],s=0;s<arguments.length;s++){var n=arguments[s];if(n){var a=typeof n;if(a==="string"||a==="number")o.push(n);else if(Array.isArray(n)){if(n.length){var h=i.apply(null,n);h&&o.push(h)}}else if(a==="object")if(n.toString===Object.prototype.toString)for(var r in n)e.call(n,r)&&n[r]&&o.push(r);else o.push(n.toString())}}return o.join(" ")}t.exports?(i.default=i,t.exports=i):window.classNames=i})()},Gt(ut={path:_e,exports:{},require:function(t,e){return(function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")})(e==null&&ut.path)}},ut.exports),ut.exports),k=function(t){return function(e,i){if(!e)return t;var o;typeof e=="string"?o=e:i=e;var s=t;return o&&(s+="__"+o),s+(i?Object.keys(i).reduce((function(n,a){var h=i[a];return h&&(n+=" "+(typeof h=="boolean"?s+"--"+a:s+"--"+a+"_"+h)),n}),""):"")}};function Lt(t,e,i){var o,s,n,a,h;function r(){var u=Date.now()-a;u<e&&u>=0?o=setTimeout(r,e-u):(o=null,i||(h=t.apply(n,s),n=s=null))}e==null&&(e=100);var l=function(){n=this,s=arguments,a=Date.now();var u=i&&!o;return o||(o=setTimeout(r,e)),u&&(h=t.apply(n,s),n=s=null),h};return l.clear=function(){o&&(clearTimeout(o),o=null)},l.flush=function(){o&&(h=t.apply(n,s),n=s=null,clearTimeout(o),o=null)},l}Lt.debounce=Lt;var Ot=Lt,g=function(){return g=Object.assign||function(t){for(var e,i=1,o=arguments.length;i<o;i++)for(var s in e=arguments[i])Object.prototype.hasOwnProperty.call(e,s)&&(t[s]=e[s]);return t},g.apply(this,arguments)};/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */function ee(t,e){var i,o;return t&&e?(i=""+t+e[0].toUpperCase()+e.slice(1),o=t+"-"+e):(i=t||e,o=t||e),{name:i,classname:o}}function ie(t){return/^blob:/.test(t)}function Xt(t){return ie(t)||(function(e){return/^data:/.test(e)})(t)}function et(t){return!!(t&&t.constructor&&t.call&&t.apply)}function C(t){return t===void 0}function pt(t){return typeof t=="object"&&t!==null}function It(t,e,i){var o={};return pt(t)?(Object.keys(e).forEach((function(s){C(t[s])?o[s]=e[s]:pt(e[s])?pt(t[s])?o[s]=It(t[s],e[s],i[s]):o[s]=t[s]?e[s]:i[s]:e[s]===!0||e[s]===!1?o[s]=!!t[s]:o[s]=t[s]})),o):t?e:i}function dt(t){var e=Number(t);return Number.isNaN(e)?t:e}function Yt(t){return typeof(t=="number"||(function(e){return typeof e=="object"&&e!==null})(t)&&toString.call(t)=="[object Number]")&&!ne(t)}function ne(t){return t!=t}function oe(t,e){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}var lt=function(t,e){t===void 0&&(t={}),e===void 0&&(e={}),this.type="manipulateImage",this.move=t,this.scale=e},ke=function(t,e){e===void 0&&(e={}),this.type="resize",this.directions=t,this.params=e},_t=function(t){this.type="move",this.directions=t},Be=(function(){function t(e,i,o,s,n){this.type="drag",this.nativeEvent=e,this.position=o,this.previousPosition=s,this.element=i,this.anchor=n}return t.prototype.shift=function(){var e=this,i=e.element,o=e.anchor,s=e.position;if(i){var n=i.getBoundingClientRect(),a=n.left,h=n.top;return{left:s.left-a-o.left,top:s.top-h-o.top}}return{left:0,top:0}},t})(),kt={name:"DraggableElement",props:{classname:{type:String}},beforeMount:function(){window.addEventListener("mouseup",this.onMouseUp,{passive:!1}),window.addEventListener("mousemove",this.onMouseMove,{passive:!1}),window.addEventListener("touchmove",this.onTouchMove,{passive:!1}),window.addEventListener("touchend",this.onTouchEnd,{passive:!1})},beforeUnmount:function(){window.removeEventListener("mouseup",this.onMouseUp),window.removeEventListener("mousemove",this.onMouseMove),window.removeEventListener("touchmove",this.onTouchMove),window.removeEventListener("touchend",this.onTouchEnd)},mounted:function(){if(!this.$refs.draggable)throw new Error('You should add ref "draggable" to your root element to use draggable mixin');this.touches=[],this.hovered=!1},methods:{onMouseOver:function(){this.hovered||(this.hovered=!0,this.$emit("enter"))},onMouseLeave:function(){this.hovered&&!this.touches.length&&(this.hovered=!1,this.$emit("leave"))},onTouchStart:function(t){t.cancelable&&!this.disabled&&t.touches.length===1&&(this.touches=Q(t.touches),this.hovered||(this.$emit("enter"),this.hovered=!0),t.touches.length&&this.initAnchor(this.touches.reduce((function(e,i){return{clientX:e.clientX+i.clientX/t.touches.length,clientY:e.clientY+i.clientY/t.touches.length}}),{clientX:0,clientY:0})),t.preventDefault&&t.preventDefault(),t.stopPropagation())},onTouchEnd:function(){this.processEnd()},onTouchMove:function(t){this.touches.length&&(this.processMove(t,t.touches),t.preventDefault&&t.preventDefault(),t.stopPropagation&&t.stopPropagation())},onMouseDown:function(t){if(!this.disabled){var e={fake:!0,clientX:t.clientX,clientY:t.clientY};this.touches=[e],this.initAnchor(e),t.stopPropagation()}},onMouseMove:function(t){this.touches.length&&(this.processMove(t,[{fake:!0,clientX:t.clientX,clientY:t.clientY}]),t.preventDefault&&t.preventDefault())},onMouseUp:function(){this.processEnd()},initAnchor:function(t){var e=this.$refs.draggable.getBoundingClientRect(),i=e.left,o=e.right,s=e.bottom,n=e.top;this.anchor={left:t.clientX-i,top:t.clientY-n,bottom:s-t.clientY,right:o-t.clientX}},processMove:function(t,e){var i=Q(e);if(this.touches.length){if(this.touches.length===1&&i.length===1){var o=this.$refs.draggable;this.$emit("drag",new Be(t,o,{left:i[0].clientX,top:i[0].clientY},{left:this.touches[0].clientX,top:this.touches[0].clientY},this.anchor))}this.touches=i}},processEnd:function(){this.touches.length&&this.$emit("drag-end"),this.hovered&&(this.$emit("leave"),this.hovered=!1),this.touches=[]}},emits:["drag","drag-end","leave","enter"]};kt.render=function(t,e,i,o,s,n){return A(),M("div",{ref:"draggable",class:i.classname,onTouchstart:e[1]||(e[1]=function(){return n.onTouchStart&&n.onTouchStart.apply(n,arguments)}),onMousedown:e[2]||(e[2]=function(){return n.onMouseDown&&n.onMouseDown.apply(n,arguments)}),onMouseover:e[3]||(e[3]=function(){return n.onMouseOver&&n.onMouseOver.apply(n,arguments)}),onMouseleave:e[4]||(e[4]=function(){return n.onMouseLeave&&n.onMouseLeave.apply(n,arguments)})},[J(t.$slots,"default")],34)};var Et=k("vue-handler-wrapper"),se={name:"HandlerWrapper",components:{DraggableElement:kt},props:{horizontalPosition:{type:String},verticalPosition:{type:String},disabled:{type:Boolean,default:!1}},computed:{classes:function(){var t;if(this.horizontalPosition||this.verticalPosition){var e,i=ee(this.horizontalPosition,this.verticalPosition);t=Et((H(e={},i.classname,!0),H(e,"disabled",this.disabled),e))}else t=Et({disabled:this.disabled});return{root:t,draggable:Et("draggable")}}},emits:["leave","enter","drag","drag-end"]};se.render=function(t,e,i,o,s,n){var a=N("DraggableElement");return A(),M("div",{class:n.classes.root},[S(a,{class:n.classes.draggable,onDrag:e[1]||(e[1]=function(h){return t.$emit("drag",h)}),onDragEnd:e[2]||(e[2]=function(h){return t.$emit("drag-end")}),onLeave:e[3]||(e[3]=function(h){return t.$emit("leave")}),onEnter:e[4]||(e[4]=function(h){return t.$emit("enter")})},{default:$((function(){return[J(t.$slots,"default")]})),_:3},8,["class"])],2)};var Ue=k("vue-line-wrapper"),re={name:"LineWrapper",components:{DraggableElement:kt},props:{position:{type:String,required:!0},disabled:{type:Boolean,default:!1}},computed:{classname:function(){var t;return Ue((H(t={},this.position,!0),H(t,"disabled",this.disabled),t))}},emits:["leave","enter","drag","drag-end"]};re.render=function(t,e,i,o,s,n){var a=N("DraggableElement");return A(),M(a,{class:n.classname,onDrag:e[1]||(e[1]=function(h){return t.$emit("drag",h)}),onDragEnd:e[2]||(e[2]=function(h){return t.$emit("drag-end")}),onLeave:e[3]||(e[3]=function(h){return t.$emit("leave")}),onEnter:e[4]||(e[4]=function(h){return t.$emit("enter")})},{default:$((function(){return[J(t.$slots,"default")]})),_:3},8,["class"])};var U=["left","right","top","bottom"],Fe=["left","right"],Ge=["top","bottom"],Xe=["left","top"],Ye=["fill-area","fit-area","stencil","none"],Nt={left:0,top:0,width:0,height:0};function Vt(t,e,i){return!(i=i||["width","height","left","top"]).some((function(o){return t[o]!==e[o]}))}function F(t){return{left:t.left,top:t.top,right:t.left+t.width,bottom:t.top+t.height}}function nt(t,e){return{left:t.left-e.left,top:t.top-e.top}}function T(t){return{left:t.left+t.width/2,top:t.top+t.height/2}}function ct(t,e){var i={left:0,top:0,right:0,bottom:0};return U.forEach((function(o){var s=e[o],n=F(t)[o];i[o]=s!==void 0&&n!==void 0?o==="left"||o==="top"?Math.max(0,s-n):Math.max(0,n-s):0})),i}function B(t,e){return{left:t.left-e.left,top:t.top-e.top,width:t.width+e.left+e.right,height:t.height+e.top+e.bottom}}function zt(t){return{left:-t.left,top:-t.top}}function I(t,e){return g(g({},t),{left:t.left+e.left,top:t.top+e.top})}function P(t,e,i,o){if(e!==1){if(i){var s=T(t);return{width:t.width*e,height:t.height*e,left:t.left+t.width*(1-e)/2+(i.left-s.left)*(1-e),top:t.top+t.height*(1-e)/2+(i.top-s.top)*(1-e)}}return{width:t.width*e,height:t.height*e,left:t.left+t.width*(1-e)/2,top:t.top+t.height*(1-e)/2}}return t}function R(t){return t.width/t.height}function ot(t,e){return Math.min(e.right!==void 0&&e.left!==void 0?(e.right-e.left)/t.width:1/0,e.bottom!==void 0&&e.top!==void 0?(e.bottom-e.top)/t.height:1/0)}function st(t,e){var i={left:0,top:0},o=ct(t,e);return o.left&&o.left>0?i.left=o.left:o.right&&o.right>0&&(i.left=-o.right),o.top&&o.top>0?i.top=o.top:o.bottom&&o.bottom>0&&(i.top=-o.bottom),i}function Ct(t,e){var i;return e.minimum&&t<e.minimum?i=e.minimum:e.maximum&&t>e.maximum&&(i=e.maximum),i}function ae(t,e){var i=R(t),o=R(e);return e.width<1/0&&e.height<1/0?i>o?{width:e.width,height:e.width/i}:{width:e.height*i,height:e.height}:e.width<1/0?{width:e.width,height:e.width/i}:e.height<1/0?{width:e.height*i,height:e.height}:t}function he(t,e){var i=e*Math.PI/180;return{width:Math.abs(t.width*Math.cos(i))+Math.abs(t.height*Math.sin(i)),height:Math.abs(t.width*Math.sin(i))+Math.abs(t.height*Math.cos(i))}}function V(t,e){var i=e*Math.PI/180;return{left:t.left*Math.cos(i)-t.top*Math.sin(i),top:t.left*Math.sin(i)+t.top*Math.cos(i)}}function Rt(t,e){var i=ct(W(t,e),e);return i.left+i.right+i.top+i.bottom?i.left+i.right>i.top+i.bottom?Math.min((t.width+i.left+i.right)/t.width,ot(t,e)):Math.min((t.height+i.top+i.bottom)/t.height,ot(t,e)):1}function W(t,e,i){i===void 0&&(i=!1);var o=st(t,e);return I(t,i?zt(o):o)}function Ht(t){return{width:t.right!==void 0&&t.left!==void 0?t.right-t.left:1/0,height:t.bottom!==void 0&&t.top!==void 0?t.bottom-t.top:1/0}}function Ne(t,e){return g(g({},t),{minWidth:Math.min(e.width,t.minWidth),minHeight:Math.min(e.height,t.minHeight),maxWidth:Math.min(e.width,t.maxWidth),maxHeight:Math.min(e.height,t.maxHeight)})}function le(t,e,i){i===void 0&&(i=!0);var o={};return U.forEach((function(s){var n=t[s],a=e[s];n!==void 0&&a!==void 0?o[s]=s==="left"||s==="top"?i?Math.max(n,a):Math.min(n,a):i?Math.min(n,a):Math.max(n,a):a!==void 0?o[s]=a:n!==void 0&&(o[s]=n)})),o}function At(t,e){return le(t,e,!0)}function Zt(t){var e=t.size,i=t.aspectRatio,o=t.ignoreMinimum,s=t.sizeRestrictions;return!!((e.correctRatio||R(e)>=i.minimum&&R(e)<=i.maximum)&&e.height<=s.maxHeight&&e.width<=s.maxWidth&&e.width&&e.height&&(o||e.height>=s.minHeight&&e.width>=s.minWidth))}function qt(t,e){return Math.pow(t.width-e.width,2)+Math.pow(t.height-e.height,2)}function Y(t){var e=t.width,i=t.height,o=t.sizeRestrictions,s={minimum:t.aspectRatio&&t.aspectRatio.minimum||0,maximum:t.aspectRatio&&t.aspectRatio.maximum||1/0},n={width:Math.max(o.minWidth,Math.min(o.maxWidth,e)),height:Math.max(o.minHeight,Math.min(o.maxHeight,i))};function a(l,u){return u===void 0&&(u=!1),l.reduce((function(c,d){return Zt({size:d,aspectRatio:s,sizeRestrictions:o,ignoreMinimum:u})&&(!c||qt(d,{width:e,height:i})<qt(c,{width:e,height:i}))?d:c}),null)}var h=[];s&&[s.minimum,s.maximum].forEach((function(l){l&&h.push({width:n.width,height:n.width/l,correctRatio:!0},{width:n.height*l,height:n.height,correctRatio:!0})})),Zt({size:n,aspectRatio:s,sizeRestrictions:o})&&h.push(n);var r=a(h)||a(h,!0);return r&&{width:r.width,height:r.height}}function Pt(t){var e=t.event,i=t.coordinates,o=t.positionRestrictions,s=o===void 0?{}:o,n=I(i,e.directions);return I(n,st(n,s))}function Ve(t){var e=t.coordinates,i=t.transform,o=t.imageSize,s=t.sizeRestrictions,n=t.positionRestrictions,a=t.aspectRatio,h=t.visibleArea,r=function(u,c){return Pt({coordinates:u,positionRestrictions:n,event:new _t({left:c.left-u.left,top:c.top-u.top})})},l=g({},e);return(Array.isArray(i)?i:[i]).forEach((function(u){var c={};C((c=typeof u=="function"?u({coordinates:l,imageSize:o,visibleArea:h}):u).width)&&C(c.height)||(l=(function(d,f){var w=g(g(g({},d),Y({width:f.width,height:f.height,sizeRestrictions:s,aspectRatio:a})),{left:0,top:0});return r(w,{left:d.left,top:d.top})})(l,g(g({},l),c))),C(c.left)&&C(c.top)||(l=r(l,g(g({},l),c)))})),l}function Ze(t){t.event;var e=t.getAreaRestrictions,i=t.boundaries,o=t.coordinates,s=t.visibleArea;t.aspectRatio;var n=t.stencilSize,a=t.sizeRestrictions,h=t.positionRestrictions;t.stencilReference;var r,l,u,c=g({},o),d=g({},s),f=g({},n);r=R(f),l=R(c),u===void 0&&(u=.001),(r===0||l===0?Math.abs(l-r)<u:Math.abs(l/r)<1+u&&Math.abs(l/r)>1-u)||(c=g(g({},c),Y({sizeRestrictions:a,width:c.width,height:c.height,aspectRatio:{minimum:R(f),maximum:R(f)}})));var w=Rt(d=P(d,c.width*i.width/(d.width*f.width)),e({visibleArea:d,type:"resize"}));return w!==1&&(d=P(d,w),c=P(c,w)),d=W(d=I(d,nt(T(c),T(d))),e({visibleArea:d,type:"move"})),{coordinates:c=W(c,At(F(d),h)),visibleArea:d}}function qe(t){var e=t.event,i=t.getAreaRestrictions,o=t.boundaries,s=t.coordinates,n=t.visibleArea;t.aspectRatio,t.stencilSize,t.sizeRestrictions;var a=t.positionRestrictions;t.stencilReference;var h=g({},s),r=g({},n);if(s&&n&&e.type!=="manipulateImage"){var l={width:0,height:0};r.width,o.width,R(o)>R(h)?(l.height=.8*o.height,l.width=l.height*R(h)):(l.width=.8*o.width,l.height=l.width*R(h));var u=Rt(r=P(r,h.width*o.width/(r.width*l.width)),i({visibleArea:r,type:"resize"}));r=P(r,u),u!==1&&(l.height/=u,l.width/=u),r=W(r=I(r,nt(T(h),T(r))),i({visibleArea:r,type:"move"})),h=W(h,At(F(r),a))}return{coordinates:h,visibleArea:r}}function Qe(t){var e=t.event,i=t.coordinates,o=t.visibleArea,s=t.getAreaRestrictions,n=g({},o),a=g({},i);if(e.type==="setCoordinates"){var h=Math.max(0,a.width-n.width),r=Math.max(0,a.height-n.height);h>r?n=P(n,Math.min(a.width/n.width,ot(n,s({visibleArea:n,type:"resize"})))):r>h&&(n=P(n,Math.min(a.height/n.height,ot(n,s({visibleArea:n,type:"resize"}))))),n=W(n=I(n,zt(st(a,F(n)))),s({visibleArea:n,type:"move"}))}return{visibleArea:n,coordinates:a}}function Je(t){var e=t.imageSize,i=t.visibleArea,o=t.coordinates,s=i||e;return{left:(i?i.left:0)+s.width/2-o.width/2,top:(i?i.top:0)+s.height/2-o.height/2}}function Ke(t){var e=t.imageSize,i=t.visibleArea,o=t.aspectRatio,s=t.sizeRestrictions,n=i||e,a=Math.min(o.maximum||1/0,Math.max(o.minimum||0,R(n))),h=n.width<n.height?{width:.8*n.width,height:.8*n.width/a}:{height:.8*n.height,width:.8*n.height*a};return Y(g(g({},h),{aspectRatio:o,sizeRestrictions:s}))}function ti(t){var e,i,o=t.imageSize,s=t.visibleArea,n=t.boundaries,a=t.aspectRatio,h=t.sizeRestrictions,r=t.stencilSize,l=s||o;return R(l)>R(n)?i=(e=r.height*l.height/n.height)*R(r):e=(i=r.width*l.width/n.width)/R(r),Y({width:i,height:e,aspectRatio:a,sizeRestrictions:h})}function ei(t){var e=t.getAreaRestrictions,i=t.coordinates,o=t.imageSize,s=R(t.boundaries);if(i){var n={height:Math.max(i.height,o.height),width:Math.max(i.width,o.width)},a=ae({width:R(n)>s?n.width:n.height*s,height:R(n)>s?n.width/s:n.height},Ht(e())),h={left:i.left+i.width/2-a.width/2,top:i.top+i.height/2-a.height/2,width:a.width,height:a.height},r=ct(i,F(g({left:0,top:0},o))),l={};return!r.left&&!r.right&&h.width<=o.width&&(l.left=0,l.right=o.width),!r.top&&!r.bottom&&h.height<=o.height&&(l.top=0,l.bottom=o.height),W(h,l)}var u=R(o);return a={height:u>s?o.height:o.width/s,width:u>s?o.height*s:o.width},{left:o.width/2-a.width/2,top:o.height/2-a.height/2,width:a.width,height:a.height}}function mt(t,e){return le(t,F(e))}function ii(t){var e=t.event,i=t.coordinates,o=t.visibleArea,s=t.sizeRestrictions,n=t.getAreaRestrictions,a=t.positionRestrictions,h=t.adjustStencil,r=e.scale,l=e.move,u=g({},o),c=g({},i),d=1,f=1,w=r.factor&&Math.abs(r.factor-1)>.001;u=I(u,{left:l.left||0,top:l.top||0});var v={stencil:{minimum:Math.max(s.minWidth?s.minWidth/c.width:0,s.minHeight?s.minHeight/c.height:0),maximum:Math.min(s.maxWidth?s.maxWidth/c.width:1/0,s.maxHeight?s.maxHeight/c.height:1/0,ot(c,a))},area:{maximum:ot(u,n({visibleArea:u,type:"resize"}))}};r.factor&&w&&(r.factor<1?(f=Math.max(r.factor,v.stencil.minimum))>1&&(f=1):r.factor>1&&(f=Math.min(r.factor,Math.min(v.area.maximum,v.stencil.maximum)))<1&&(f=1)),f&&(u=P(u,f,r.center));var D=i.left-o.left,j=o.width+o.left-(i.width+i.left),Z=i.top-o.top,q=o.height+o.top-(i.height+i.top);return u=W(u=I(u,st(u,{left:a.left!==void 0?a.left-D*f:void 0,top:a.top!==void 0?a.top-Z*f:void 0,bottom:a.bottom!==void 0?a.bottom+q*f:void 0,right:a.right!==void 0?a.right+j*f:void 0})),n({visibleArea:u,type:"move"})),c.width=c.width*f,c.height=c.height*f,c.left=u.left+D*f,c.top=u.top+Z*f,c=W(c,At(F(u),a)),r.factor&&w&&h&&(r.factor>1?d=Math.min(v.area.maximum,r.factor)/f:r.factor<1&&(d=Math.max(c.height/u.height,c.width/u.width,r.factor/f)),d!==1&&(u=I(u=W(u=P(u,d,r.factor>1?r.center:T(c)),n({visibleArea:u,type:"move"})),zt(st(c,F(u)))))),{coordinates:c,visibleArea:u}}function ni(t){var e=t.aspectRatio,i=t.getAreaRestrictions,o=t.coordinates,s=t.visibleArea,n=t.sizeRestrictions,a=t.positionRestrictions,h=t.imageSize,r=t.previousImageSize,l=t.angle,u=g({},o),c=g({},s),d=V(T(g({left:0,top:0},r)),l);return(u=g(g({},Y({sizeRestrictions:n,aspectRatio:e,width:u.width,height:u.height})),V(T(u),l))).left-=d.left-h.width/2+u.width/2,u.top-=d.top-h.height/2+u.height/2,c=P(c,Rt(c,i({visibleArea:c,type:"resize"}))),{coordinates:u=W(u,a),visibleArea:c=W(c=I(c,nt(T(u),T(o))),i({visibleArea:c,type:"move"}))}}function oi(t){var e=t.flip,i=t.previousFlip,o=t.rotate,s=t.getAreaRestrictions,n=t.coordinates,a=t.visibleArea,h=t.imageSize,r=g({},n),l=g({},a),u=i.horizontal!==e.horizontal,c=i.vertical!==e.vertical;if(u||c){var d=V({left:h.width/2,top:h.height/2},-o),f=V(T(r),-o),w=V({left:u?d.left-(f.left-d.left):f.left,top:c?d.top-(f.top-d.top):f.top},o);r=I(r,nt(w,T(r))),f=V(T(l),-o),l=W(l=I(l,nt(w=V({left:u?d.left-(f.left-d.left):f.left,top:c?d.top-(f.top-d.top):f.top},o),T(l))),s({visibleArea:l,type:"move"}))}return{coordinates:r,visibleArea:l}}function Qt(t){var e=t.directions,i=t.coordinates,o=t.positionRestrictions,s=o===void 0?{}:o,n=t.sizeRestrictions,a=t.preserveRatio,h=t.compensate,r=g({},e),l=B(i,r).width,u=B(i,r).height;l<0&&(r.left<0&&r.right<0?(r.left=-(i.width-n.minWidth)/(r.left/r.right),r.right=-(i.width-n.minWidth)/(r.right/r.left)):r.left<0?r.left=-(i.width-n.minWidth):r.right<0&&(r.right=-(i.width-n.minWidth))),u<0&&(r.top<0&&r.bottom<0?(r.top=-(i.height-n.minHeight)/(r.top/r.bottom),r.bottom=-(i.height-n.minHeight)/(r.bottom/r.top)):r.top<0?r.top=-(i.height-n.minHeight):r.bottom<0&&(r.bottom=-(i.height-n.minHeight)));var c=ct(B(i,r),s);h&&(c.left&&c.left>0&&c.right===0?(r.right+=c.left,r.left-=c.left):c.right&&c.right>0&&c.left===0&&(r.left+=c.right,r.right-=c.right),c.top&&c.top>0&&c.bottom===0?(r.bottom+=c.top,r.top-=c.top):c.bottom&&c.bottom>0&&c.top===0&&(r.top+=c.bottom,r.bottom-=c.bottom),c=ct(B(i,r),s));var d={width:1/0,height:1/0,left:1/0,right:1/0,top:1/0,bottom:1/0};if(U.forEach((function(v){var D=c[v];D&&r[v]&&(d[v]=Math.max(0,1-D/r[v]))})),a){var f=Math.min.apply(null,U.map((function(v){return d[v]})));f!==1/0&&U.forEach((function(v){r[v]*=f}))}else U.forEach((function(v){d[v]!==1/0&&(r[v]*=d[v])}));if(l=B(i,r).width,u=B(i,r).height,r.right+r.left&&(l>n.maxWidth?d.width=(n.maxWidth-i.width)/(r.right+r.left):l<n.minWidth&&(d.width=(n.minWidth-i.width)/(r.right+r.left))),r.bottom+r.top&&(u>n.maxHeight?d.height=(n.maxHeight-i.height)/(r.bottom+r.top):u<n.minHeight&&(d.height=(n.minHeight-i.height)/(r.bottom+r.top))),a){var w=Math.min(d.width,d.height);w!==1/0&&U.forEach((function(v){r[v]*=w}))}else d.width!==1/0&&Fe.forEach((function(v){r[v]*=d.width})),d.height!==1/0&&Ge.forEach((function(v){r[v]*=d.height}));return r}function ft(t,e,i){return e==0&&i==0?t/2:e==0?0:i==0?t:t*Math.abs(e/(e+i))}var si=k("vue-simple-handler"),ri=k("vue-simple-handler-wrapper"),Bt={name:"SimpleHandler",components:{HandlerWrapper:se},props:{defaultClass:{type:String},hoverClass:{type:String},wrapperClass:{type:String},horizontalPosition:{type:String},verticalPosition:{type:String},disabled:{type:Boolean,default:!1}},data:function(){return{hover:!1}},computed:{classes:function(){var t,e=(H(t={},this.horizontalPosition,!!this.horizontalPosition),H(t,this.verticalPosition,!!this.verticalPosition),H(t,"".concat(this.horizontalPosition,"-").concat(this.verticalPosition),!!(this.verticalPosition&&this.horizontalPosition)),H(t,"hover",this.hover),t);return{default:E(si(e),this.defaultClass,this.hover&&this.hoverClass),wrapper:E(ri(e),this.wrapperClass)}}},methods:{onDrag:function(t){this.$emit("drag",t)},onEnter:function(){this.hover=!0},onLeave:function(){this.hover=!1},onDragEnd:function(){this.$emit("drag-end")}},emits:["drag","drag-end"]};Bt.render=function(t,e,i,o,s,n){var a=N("HandlerWrapper");return A(),M(a,{class:n.classes.wrapper,"vertical-position":i.verticalPosition,"horizontal-position":i.horizontalPosition,disabled:i.disabled,onDrag:n.onDrag,onDragEnd:n.onDragEnd,onEnter:n.onEnter,onLeave:n.onLeave},{default:$((function(){return[S("div",{class:n.classes.default},null,2)]})),_:1},8,["class","vertical-position","horizontal-position","disabled","onDrag","onDragEnd","onEnter","onLeave"])};var ai=k("vue-simple-line"),hi=k("vue-simple-line-wrapper"),Ut={name:"SimpleLine",components:{LineWrapper:re},props:{defaultClass:{type:String},hoverClass:{type:String},wrapperClass:{type:String},position:{type:String},disabled:{type:Boolean,default:!1}},data:function(){return{hover:!1}},computed:{classes:function(){return{root:E(ai(H({},this.position,!0)),this.defaultClass,this.hover&&this.hoverClass),wrapper:E(hi(H({},this.position,!0)),this.wrapperClass)}}},methods:{onDrag:function(t){this.$emit("drag",t)},onEnter:function(){this.hover=!0},onLeave:function(){this.hover=!1},onDragEnd:function(){this.$emit("drag-end")}},emits:["drag","drag-end"]};Ut.render=function(t,e,i,o,s,n){var a=N("LineWrapper");return A(),M(a,{class:n.classes.wrapper,position:i.position,disabled:i.disabled,onDrag:n.onDrag,onDragEnd:n.onDragEnd,onEnter:n.onEnter,onLeave:n.onLeave},{default:$((function(){return[S("div",{class:n.classes.root},null,2)]})),_:1},8,["class","position","disabled","onDrag","onDragEnd","onEnter","onLeave"])};var Tt=k("vue-bounding-box"),li=["east","west",null],ci=["south","north",null],ce={name:"BoundingBox",props:{width:{type:Number},height:{type:Number},transitions:{type:Object},handlers:{type:Object,default:function(){return{eastNorth:!0,north:!0,westNorth:!0,west:!0,westSouth:!0,south:!0,eastSouth:!0,east:!0}}},handlersComponent:{type:[Object,String],default:function(){return Bt}},handlersClasses:{type:Object,default:function(){return{}}},handlersWrappersClasses:{type:Object,default:function(){return{}}},lines:{type:Object,default:function(){return{west:!0,north:!0,east:!0,south:!0}}},linesComponent:{type:[Object,String],default:function(){return Ut}},linesClasses:{type:Object,default:function(){return{}}},linesWrappersClasses:{type:Object,default:function(){return{}}},resizable:{type:Boolean,default:!0}},data:function(){var t=[];return li.forEach((function(e){ci.forEach((function(i){if(e!==i){var o=ee(e,i),s=o.name,n=o.classname;t.push({name:s,classname:n,verticalDirection:i,horizontalDirection:e})}}))})),{points:t}},computed:{style:function(){var t={};return this.width&&this.height&&(t.width="".concat(this.width,"px"),t.height="".concat(this.height,"px"),this.transitions&&this.transitions.enabled&&(t.transition="".concat(this.transitions.time,"ms ").concat(this.transitions.timingFunction))),t},classes:function(){var t=this.handlersClasses,e=this.handlersWrappersClasses,i=this.linesClasses,o=this.linesWrappersClasses;return{root:Tt(),handlers:t,handlersWrappers:e,lines:i,linesWrappers:o}},lineNodes:function(){var t=this,e=[];return this.points.forEach((function(i){i.horizontalDirection&&i.verticalDirection||!t.lines[i.name]||e.push({name:i.name,component:t.linesComponent,class:E(t.classes.lines.default,t.classes.lines[i.name],!t.resizable&&t.classes.lines.disabled),wrapperClass:E(t.classes.linesWrappers.default,t.classes.linesWrappers[i.name],!t.resizable&&t.classes.linesWrappers.disabled),hoverClass:t.classes.lines.hover,verticalDirection:i.verticalDirection,horizontalDirection:i.horizontalDirection,disabled:!t.resizable})})),e},handlerNodes:function(){var t=this,e=[],i=this.width,o=this.height;return this.points.forEach((function(s){if(t.handlers[s.name]){var n={name:s.name,component:t.handlersComponent,class:E(t.classes.handlers.default,t.classes.handlers[s.name]),wrapperClass:E(t.classes.handlersWrappers.default,t.classes.handlersWrappers[s.name]),hoverClass:t.classes.handlers.hover,verticalDirection:s.verticalDirection,horizontalDirection:s.horizontalDirection,disabled:!t.resizable};if(i&&o){var a=s.horizontalDirection,h=s.verticalDirection,r=a==="east"?i:a==="west"?0:i/2,l=h==="south"?o:h==="north"?0:o/2;n.wrapperClass=Tt("handler"),n.wrapperStyle={transform:"translate(".concat(r,"px, ").concat(l,"px)")},t.transitions&&t.transitions.enabled&&(n.wrapperStyle.transition="".concat(t.transitions.time,"ms ").concat(t.transitions.timingFunction))}else n.wrapperClass=Tt("handler",H({},s.classname,!0));e.push(n)}})),e}},beforeMount:function(){window.addEventListener("mouseup",this.onMouseUp,{passive:!1}),window.addEventListener("mousemove",this.onMouseMove,{passive:!1}),window.addEventListener("touchmove",this.onTouchMove,{passive:!1}),window.addEventListener("touchend",this.onTouchEnd,{passive:!1})},beforeUnmount:function(){window.removeEventListener("mouseup",this.onMouseUp),window.removeEventListener("mousemove",this.onMouseMove),window.removeEventListener("touchmove",this.onTouchMove),window.removeEventListener("touchend",this.onTouchEnd)},mounted:function(){this.touches=[]},methods:{onEnd:function(){this.$emit("resize-end")},onHandlerDrag:function(t,e,i){var o,s=t.shift(),n=s.left,a=s.top,h={left:0,right:0,top:0,bottom:0};e==="west"?h.left-=n:e==="east"&&(h.right+=n),i==="north"?h.top-=a:i==="south"&&(h.bottom+=a),!i&&e?o="width":i&&!e&&(o="height"),this.resizable&&this.$emit("resize",new ke(h,{allowedDirections:{left:e==="west"||!e,right:e==="east"||!e,bottom:i==="south"||!i,top:i==="north"||!i},preserveAspectRatio:t.nativeEvent&&t.nativeEvent.shiftKey,respectDirection:o}))}},emits:["resize","resize-end"]};ce.render=function(t,e,i,o,s,n){return A(),M("div",{ref:"box",class:n.classes.root,style:n.style},[J(t.$slots,"default"),S("div",null,[(A(!0),M(vt,null,bt(n.lineNodes,(function(a){return A(),M(wt(a.component),{key:a.name,"default-class":a.class,"hover-class":a.hoverClass,"wrapper-class":a.wrapperClass,position:a.name,disabled:a.disabled,onDrag:function(h){return n.onHandlerDrag(h,a.horizontalDirection,a.verticalDirection)},onDragEnd:e[1]||(e[1]=function(h){return n.onEnd()})},null,8,["default-class","hover-class","wrapper-class","position","disabled","onDrag"])})),128))]),(A(!0),M(vt,null,bt(n.handlerNodes,(function(a){return A(),M("div",{key:a.name,style:a.wrapperStyle,class:a.wrapperClass},[(A(),M(wt(a.component),{"default-class":a.class,"hover-class":a.hoverClass,"wrapper-class":a.wrapperClass,"horizontal-position":a.horizontalDirection,"vertical-position":a.verticalDirection,disabled:a.disabled,onDrag:function(h){return n.onHandlerDrag(h,a.horizontalDirection,a.verticalDirection)},onDragEnd:e[2]||(e[2]=function(h){return n.onEnd()})},null,8,["default-class","hover-class","wrapper-class","horizontal-position","vertical-position","disabled","onDrag"]))],6)})),128))],6)};var ui=k("vue-draggable-area"),ue={name:"DraggableArea",props:{movable:{type:Boolean,default:!0},activationDistance:{type:Number,default:20}},computed:{classnames:function(){return{default:ui()}}},beforeMount:function(){window.addEventListener("mouseup",this.onMouseUp,{passive:!1}),window.addEventListener("mousemove",this.onMouseMove,{passive:!1}),window.addEventListener("touchmove",this.onTouchMove,{passive:!1}),window.addEventListener("touchend",this.onTouchEnd,{passive:!1})},beforeUnmount:function(){window.removeEventListener("mouseup",this.onMouseUp),window.removeEventListener("mousemove",this.onMouseMove),window.removeEventListener("touchmove",this.onTouchMove),window.removeEventListener("touchend",this.onTouchEnd)},mounted:function(){this.touches=[],this.touchStarted=!1},methods:{onTouchStart:function(t){if(t.cancelable){var e=this.movable&&t.touches.length===1;e&&(this.touches=Q(t.touches)),(this.touchStarted||e)&&(t.preventDefault(),t.stopPropagation())}},onTouchEnd:function(){this.touchStarted=!1,this.processEnd()},onTouchMove:function(t){this.touches.length>=1&&(this.touchStarted?(this.processMove(t,t.touches),t.preventDefault(),t.stopPropagation()):oe({x:this.touches[0].clientX,y:this.touches[0].clientY},{x:t.touches[0].clientX,y:t.touches[0].clientY})>this.activationDistance&&(this.initAnchor({clientX:t.touches[0].clientX,clientY:t.touches[0].clientY}),this.touchStarted=!0))},onMouseDown:function(t){if(this.movable&&t.button===0){var e={fake:!0,clientX:t.clientX,clientY:t.clientY};this.touches=[e],this.initAnchor(e),t.stopPropagation()}},onMouseMove:function(t){this.touches.length&&(this.processMove(t,[{fake:!0,clientX:t.clientX,clientY:t.clientY}]),t.preventDefault&&t.cancelable&&t.preventDefault(),t.stopPropagation())},onMouseUp:function(){this.processEnd()},initAnchor:function(t){var e=this.$refs.container.getBoundingClientRect(),i=e.left,o=e.top;this.anchor={x:t.clientX-i,y:t.clientY-o}},processMove:function(t,e){var i=Q(e);if(this.touches.length){var o=this.$refs.container.getBoundingClientRect(),s=o.left,n=o.top;this.touches.length===1&&i.length===1&&this.$emit("move",new _t({left:i[0].clientX-(s+this.anchor.x),top:i[0].clientY-(n+this.anchor.y)}))}},processEnd:function(){this.touches.length&&this.$emit("move-end"),this.touches=[]}},emits:["move","move-end"]};ue.render=function(t,e,i,o,s,n){return A(),M("div",{ref:"container",onTouchstart:e[1]||(e[1]=function(){return n.onTouchStart&&n.onTouchStart.apply(n,arguments)}),onMousedown:e[2]||(e[2]=function(){return n.onMouseDown&&n.onMouseDown.apply(n,arguments)})},[J(t.$slots,"default")],544)};function Wt(t){var e,i;return{rotate:t.rotate||0,flip:{horizontal:((e=t?.flip)===null||e===void 0?void 0:e.horizontal)||!1,vertical:((i=t?.flip)===null||i===void 0?void 0:i.vertical)||!1}}}function di(t){return new Promise((function(e,i){try{if(t)if(/^data:/i.test(t))e((function(r){r=r.replace(/^data:([^;]+);base64,/gim,"");for(var l=atob(r),u=l.length,c=new ArrayBuffer(u),d=new Uint8Array(c),f=0;f<u;f++)d[f]=l.charCodeAt(f);return c})(t));else if(/^blob:/i.test(t)){var o=new FileReader;o.onload=function(r){e(r.target.result)},n=t,a=function(r){o.readAsArrayBuffer(r)},(h=new XMLHttpRequest).open("GET",n,!0),h.responseType="blob",h.onload=function(){this.status!=200&&this.status!==0||a(this.response)},h.send()}else{var s=new XMLHttpRequest;s.onreadystatechange=function(){s.readyState===4&&(s.status===200||s.status===0?e(s.response):i("Warning: could not load an image to parse its orientation"),s=null)},s.onprogress=function(){s.getResponseHeader("content-type")!=="image/jpeg"&&s.abort()},s.withCredentials=!1,s.open("GET",t,!0),s.responseType="arraybuffer",s.send(null)}else i("Error: the image is empty")}catch(r){i(r)}var n,a,h}))}function de(t){var e=t.rotate,i=t.flip,o=t.scaleX,s=t.scaleY,n="";return n+=" rotate("+e+"deg) ",n+=" scaleX("+o*(i.horizontal?-1:1)+") ",n+=" scaleY("+s*(i.vertical?-1:1)+") "}function mi(t){try{var e,i=new DataView(t),o=void 0,s=void 0,n=void 0,a=void 0;if(i.getUint8(0)===255&&i.getUint8(1)===216)for(var h=i.byteLength,r=2;r+1<h;){if(i.getUint8(r)===255&&i.getUint8(r+1)===225){n=r;break}r++}if(n&&(o=n+10,(function(f,w,v){var D,j="";for(D=w,v+=w;D<v;D++)j+=String.fromCharCode(f.getUint8(D));return j})(i,n+4,4)==="Exif")){var l=i.getUint16(o);if(((s=l===18761)||l===19789)&&i.getUint16(o+2,s)===42){var u=i.getUint32(o+4,s);u>=8&&(a=o+u)}}if(a){for(var c=i.getUint16(a,s),d=0;d<c;d++)if(r=a+12*d+2,i.getUint16(r,s)===274){r+=8,e=i.getUint16(r,s),i.setUint16(r,1,s);break}}return e}catch{return null}}function Jt(t,e){var i=e.getBoundingClientRect(),o=i.left,s=i.top,n={left:0,top:0},a=0;return t.forEach((function(h){n.left+=(h.clientX-o)/t.length,n.top+=(h.clientY-s)/t.length})),t.forEach((function(h){a+=oe({x:n.left,y:n.top},{x:h.clientX-o,y:h.clientY-s})})),{centerMass:n,spread:a,count:t.length}}var me={props:{touchMove:{type:Boolean,required:!0},mouseMove:{type:Boolean,required:!0},touchResize:{type:Boolean,required:!0},wheelResize:{type:[Boolean,Object],required:!0},eventsFilter:{type:Function,required:!1}},beforeMount:function(){window.addEventListener("mouseup",this.onMouseUp,{passive:!1}),window.addEventListener("mousemove",this.onMouseMove,{passive:!1}),window.addEventListener("touchmove",this.onTouchMove,{passive:!1}),window.addEventListener("touchend",this.onTouchEnd,{passive:!1})},beforeUnmount:function(){window.removeEventListener("mouseup",this.onMouseUp),window.removeEventListener("mousemove",this.onMouseMove),window.removeEventListener("touchmove",this.onTouchMove),window.removeEventListener("touchend",this.onTouchEnd)},created:function(){this.transforming=!1,this.debouncedProcessEnd=Ot(this.processEnd),this.touches=[]},methods:{processMove:function(t,e){if(this.touches.length){if(this.touches.length===1&&e.length===1)this.$emit("move",new lt({left:this.touches[0].clientX-e[0].clientX,top:this.touches[0].clientY-e[0].clientY}));else if(this.touches.length>1&&this.touchResize){var i=Jt(e,this.$refs.container),o=this.oldGeometricProperties;o.count===i.count&&o.count>1&&this.$emit("resize",new lt({left:o.centerMass.left-i.centerMass.left,top:o.centerMass.top-i.centerMass.top},{factor:o.spread/i.spread,center:i.centerMass})),this.oldGeometricProperties=i}this.touches=e}},processEnd:function(){this.transforming&&(this.transforming=!1,this.$emit("transform-end"))},processStart:function(){this.transforming=!0,this.debouncedProcessEnd.clear()},processEvent:function(t){return this.eventsFilter?this.eventsFilter(t,this.transforming)!==!1:(t.preventDefault(),t.stopPropagation(),!0)},onTouchStart:function(t){if(t.cancelable&&(this.touchMove||this.touchResize&&t.touches.length>1)&&this.processEvent(t)){var e=this.$refs.container,i=e.getBoundingClientRect(),o=i.left,s=i.top,n=i.bottom,a=i.right;this.touches=Q(t.touches).filter((function(h){return h.clientX>o&&h.clientX<a&&h.clientY>s&&h.clientY<n})),this.oldGeometricProperties=Jt(this.touches,e)}},onTouchEnd:function(t){t.touches.length===0&&(this.touches=[],this.processEnd())},onTouchMove:function(t){var e=this;if(this.touches.length){var i=Q(t.touches).filter((function(o){return!o.identifier||e.touches.find((function(s){return s.identifier===o.identifier}))}));this.processEvent(t)&&(this.processMove(t,i),this.processStart())}},onMouseDown:function(t){if(this.mouseMove&&"buttons"in t&&t.buttons===1&&this.processEvent(t)){var e={fake:!0,clientX:t.clientX,clientY:t.clientY};this.touches=[e],this.processStart()}},onMouseMove:function(t){this.touches.length&&this.processEvent(t)&&this.processMove(t,[{clientX:t.clientX,clientY:t.clientY}])},onMouseUp:function(){this.touches=[],this.processEnd()},onWheel:function(t){if(this.wheelResize&&this.processEvent(t)){var e=this.$refs.container.getBoundingClientRect(),i=e.left,o=e.top,s=1+this.wheelResize.ratio*(a=t.deltaY||t.detail||t.wheelDelta,(h=+a)==0||ne(h)?h:h>0?1:-1),n={left:t.clientX-i,top:t.clientY-o};this.$emit("resize",new lt({},{factor:s,center:n})),this.touches.length||this.debouncedProcessEnd()}var a,h}},emits:["resize","move","transform-end"]};me.render=function(t,e,i,o,s,n){return A(),M("div",{ref:"container",onTouchstart:e[1]||(e[1]=function(){return n.onTouchStart&&n.onTouchStart.apply(n,arguments)}),onMousedown:e[2]||(e[2]=function(){return n.onMouseDown&&n.onMouseDown.apply(n,arguments)}),onWheel:e[3]||(e[3]=function(){return n.onWheel&&n.onWheel.apply(n,arguments)})},[J(t.$slots,"default")],544)};var jt={components:{TransformableImage:me},props:{touchMove:{type:Boolean,required:!0},mouseMove:{type:Boolean,required:!0},touchResize:{type:Boolean,required:!0},wheelResize:{type:[Boolean,Object],required:!0}},emits:["resize","move"]};jt.render=function(t,e,i,o,s,n){var a=N("transformable-image");return A(),M(a,{"touch-move":i.touchMove,"touch-resize":i.touchResize,"mouse-move":i.mouseMove,"wheel-resize":i.wheelResize,onMove:e[1]||(e[1]=function(h){return t.$emit("move",h)}),onResize:e[2]||(e[2]=function(h){return t.$emit("resize",h)})},{default:$((function(){return[J(t.$slots,"default")]})),_:3},8,["touch-move","touch-resize","mouse-move","wheel-resize"])};var gt=k("vue-preview"),fe={props:{coordinates:{type:Object},transitions:{type:Object},image:{type:Object,default:function(){return{}}},imageClass:{type:String},width:{type:Number},height:{type:Number},fill:{type:Boolean}},data:function(){return{calculatedImageSize:{width:0,height:0},calculatedSize:{width:0,height:0}}},computed:{classes:function(){return{root:gt({fill:this.fill}),wrapper:gt("wrapper"),imageWrapper:gt("image-wrapper"),image:E(gt("image"),this.imageClass)}},style:function(){if(this.fill)return{};var t={};return this.width&&(t.width="".concat(this.size.width,"px")),this.height&&(t.height="".concat(this.size.height,"px")),this.transitions&&this.transitions.enabled&&(t.transition="".concat(this.transitions.time,"ms ").concat(this.transitions.timingFunction)),t},wrapperStyle:function(){var t={width:"".concat(this.size.width,"px"),height:"".concat(this.size.height,"px"),left:"calc(50% - ".concat(this.size.width/2,"px)"),top:"calc(50% - ".concat(this.size.height/2,"px)")};return this.transitions&&this.transitions.enabled&&(t.transition="".concat(this.transitions.time,"ms ").concat(this.transitions.timingFunction)),t},imageStyle:function(){if(this.coordinates&&this.image){var t=this.coordinates.width/this.size.width,e=z(z({rotate:0,flip:{horizontal:!1,vertical:!1}},this.image.transforms),{},{scaleX:1/t,scaleY:1/t}),i=this.imageSize.width,o=this.imageSize.height,s=he({width:i,height:o},e.rotate),n={width:"".concat(i,"px"),height:"".concat(o,"px"),left:"0px",top:"0px"},a={rotate:{left:(i-s.width)*e.scaleX/2,top:(o-s.height)*e.scaleY/2},scale:{left:(1-e.scaleX)*i/2,top:(1-e.scaleY)*o/2}};return n.transform=`translate(
				`.concat(-this.coordinates.left/t-a.rotate.left-a.scale.left,"px,").concat(-this.coordinates.top/t-a.rotate.top-a.scale.top,"px) ")+de(e),this.transitions&&this.transitions.enabled&&(n.transition="".concat(this.transitions.time,"ms ").concat(this.transitions.timingFunction)),n}return{}},size:function(){return{width:this.width||this.calculatedSize.width,height:this.height||this.calculatedSize.height}},imageSize:function(){return{width:this.image.width||this.calculatedImageSize.width,height:this.image.height||this.calculatedImageSize.height}}},watch:{image:function(t){(t.width||t.height)&&this.onChangeImage()}},mounted:function(){var t=this;this.onChangeImage(),this.$refs.image.addEventListener("load",(function(){t.refreshImage()})),window.addEventListener("resize",this.refresh),window.addEventListener("orientationchange",this.refresh)},unmounted:function(){window.removeEventListener("resize",this.refresh),window.removeEventListener("orientationchange",this.refresh)},methods:{refreshImage:function(){var t=this.$refs.image;this.calculatedImageSize.height=t.naturalHeight,this.calculatedImageSize.width=t.naturalWidth},refresh:function(){var t=this.$refs.root;this.width||(this.calculatedSize.width=t.clientWidth),this.height||(this.calculatedSize.height=t.clientHeight)},onChangeImage:function(){var t=this.$refs.image;t&&t.complete&&this.refreshImage(),this.refresh()}}};fe.render=function(t,e,i,o,s,n){return A(),M("div",{ref:"root",class:n.classes.root,style:n.style},[S("div",{ref:"wrapper",class:n.classes.wrapper,style:n.wrapperStyle},[yt(S("img",{ref:"image",src:i.image&&i.image.src,class:n.classes.image,style:n.imageStyle},null,14,["src"]),[[Kt,i.image&&i.image.src]])],6)],6)};var ge={components:{Preview:fe},inheritAttrs:!1};ge.render=function(t,e,i,o,s,n){var a=N("preview");return A(),M(a,te(t.$attrs,{fill:!0}),null,16)};var $t=k("vue-rectangle-stencil"),pe={name:"RectangleStencil",components:{StencilPreview:ge,BoundingBox:ce,DraggableArea:ue},props:{image:{type:Object},coordinates:{type:Object},stencilCoordinates:{type:Object},handlers:{type:Object},handlersComponent:{type:[Object,String],default:function(){return Bt}},lines:{type:Object},linesComponent:{type:[Object,String],default:function(){return Ut}},aspectRatio:{type:[Number,String]},minAspectRatio:{type:[Number,String]},maxAspectRatio:{type:[Number,String]},movable:{type:Boolean,default:!0},resizable:{type:Boolean,default:!0},transitions:{type:Object},movingClass:{type:String},resizingClass:{type:String},previewClass:{type:String},boundingBoxClass:{type:String},linesClasses:{type:Object,default:function(){return{}}},linesWrappersClasses:{type:Object,default:function(){return{}}},handlersClasses:{type:Object,default:function(){return{}}},handlersWrappersClasses:{type:Object,default:function(){return{}}}},data:function(){return{moving:!1,resizing:!1}},computed:{classes:function(){return{stencil:E($t({movable:this.movable,moving:this.moving,resizing:this.resizing}),this.moving&&this.movingClass,this.resizing&&this.resizingClass),preview:E($t("preview"),this.previewClass),boundingBox:E($t("bounding-box"),this.boundingBoxClass)}},style:function(){var t=this.stencilCoordinates,e=t.height,i=t.width,o=t.left,s=t.top,n={width:"".concat(i,"px"),height:"".concat(e,"px"),transform:"translate(".concat(o,"px, ").concat(s,"px)")};return this.transitions&&this.transitions.enabled&&(n.transition="".concat(this.transitions.time,"ms ").concat(this.transitions.timingFunction)),n}},methods:{onMove:function(t){this.$emit("move",t),this.moving=!0},onMoveEnd:function(){this.$emit("move-end"),this.moving=!1},onResize:function(t){this.$emit("resize",t),this.resizing=!0},onResizeEnd:function(){this.$emit("resize-end"),this.resizing=!1},aspectRatios:function(){return{minimum:this.aspectRatio||this.minAspectRatio,maximum:this.aspectRatio||this.maxAspectRatio}}},emits:["resize","resize-end","move","move-end"]};pe.render=function(t,e,i,o,s,n){var a=N("stencil-preview"),h=N("draggable-area"),r=N("bounding-box");return A(),M("div",{class:n.classes.stencil,style:n.style},[S(r,{width:i.stencilCoordinates.width,height:i.stencilCoordinates.height,transitions:i.transitions,class:n.classes.boundingBox,handlers:i.handlers,"handlers-component":i.handlersComponent,"handlers-classes":i.handlersClasses,"handlers-wrappers-classes":i.handlersWrappersClasses,lines:i.lines,"lines-component":i.linesComponent,"lines-classes":i.linesClasses,"lines-wrappers-classes":i.linesWrappersClasses,resizable:i.resizable,onResize:n.onResize,onResizeEnd:n.onResizeEnd},{default:$((function(){return[S(h,{movable:i.movable,onMove:n.onMove,onMoveEnd:n.onMoveEnd},{default:$((function(){return[S(a,{image:i.image,coordinates:i.coordinates,width:i.stencilCoordinates.width,height:i.stencilCoordinates.height,class:n.classes.preview,transitions:i.transitions},null,8,["image","coordinates","width","height","class","transitions"])]})),_:1},8,["movable","onMove","onMoveEnd"])]})),_:1},8,["width","height","transitions","class","handlers","handlers-component","handlers-classes","handlers-wrappers-classes","lines","lines-component","lines-classes","lines-wrappers-classes","resizable","onResize","onResizeEnd"])],6)};var fi=["transitions"],X=k("vue-advanced-cropper"),ve={name:"Cropper",components:{BackgroundWrapper:jt},props:{src:{type:String,default:null},stencilComponent:{type:[Object,String],default:function(){return pe}},backgroundWrapperComponent:{type:[Object,String],default:function(){return jt}},stencilProps:{type:Object,default:function(){return{}}},autoZoom:{type:Boolean,default:!1},imageClass:{type:String},boundariesClass:{type:String},backgroundClass:{type:String},foregroundClass:{type:String},minWidth:{type:[Number,String]},minHeight:{type:[Number,String]},maxWidth:{type:[Number,String]},maxHeight:{type:[Number,String]},debounce:{type:[Boolean,Number],default:500},transitions:{type:Boolean,default:!0},checkOrientation:{type:Boolean,default:!0},canvas:{type:[Object,Boolean],default:!0},crossOrigin:{type:[Boolean,String],default:void 0},transitionTime:{type:Number,default:300},imageRestriction:{type:String,default:"fit-area",validator:function(t){return Ye.indexOf(t)!==-1}},roundResult:{type:Boolean,default:!0},defaultSize:{type:[Function,Object]},defaultPosition:{type:[Function,Object]},defaultVisibleArea:{type:[Function,Object]},defaultTransforms:{type:[Function,Object]},defaultBoundaries:{type:[Function,String],validator:function(t){return!(typeof t=="string"&&t!=="fill"&&t!=="fit")}},priority:{type:String,default:"coordinates"},stencilSize:{type:[Object,Function]},resizeImage:{type:[Boolean,Object],default:!0},moveImage:{type:[Boolean,Object],default:!0},autoZoomAlgorithm:{type:Function},resizeAlgorithm:{type:Function,default:function(t){var e=t.event,i=t.coordinates,o=t.aspectRatio,s=t.positionRestrictions,n=t.sizeRestrictions,a=g(g({},i),{right:i.left+i.width,bottom:i.top+i.height}),h=e.params||{},r=g({},e.directions),l=h.allowedDirections||{left:!0,right:!0,bottom:!0,top:!0};n.widthFrozen&&(r.left=0,r.right=0),n.heightFrozen&&(r.top=0,r.bottom=0),U.forEach((function(G){l[G]||(r[G]=0)}));var u=B(a,r=Qt({coordinates:a,directions:r,sizeRestrictions:n,positionRestrictions:s})).width,c=B(a,r).height,d=h.preserveRatio?R(a):Ct(u/c,o);if(d){var f=h.respectDirection;if(f||(f=a.width>=a.height||d===1?"width":"height"),f==="width"){var w=u/d-a.height;if(l.top&&l.bottom){var v=r.top,D=r.bottom;r.bottom=ft(w,D,v),r.top=ft(w,v,D)}else l.bottom?r.bottom=w:l.top?r.top=w:l.right?r.right=0:l.left&&(r.left=0)}else if(f==="height"){var j=a.width-c*d;if(l.left&&l.right){var Z=r.left,q=r.right;r.left=-ft(j,Z,q),r.right=-ft(j,q,Z)}else l.left?r.left=-j:l.right?r.right=-j:l.top?r.top=0:l.bottom&&(r.bottom=0)}r=Qt({directions:r,coordinates:a,sizeRestrictions:n,positionRestrictions:s,preserveRatio:!0,compensate:h.compensate})}return u=B(a,r).width,c=B(a,r).height,(d=h.preserveRatio?R(a):Ct(u/c,o))&&Math.abs(d-u/c)>.001&&U.forEach((function(G){l[G]||(r[G]=0)})),Pt({event:new _t({left:-r.left,top:-r.top}),coordinates:{width:i.width+r.right+r.left,height:i.height+r.top+r.bottom,left:i.left,top:i.top},positionRestrictions:s})}},moveAlgorithm:{type:Function,default:Pt},initStretcher:{type:Function,default:function(t){var e=t.stretcher,i=t.imageSize,o=R(i);e.style.width=i.width+"px",e.style.height=e.clientWidth/o+"px",e.style.width=e.clientWidth+"px"}},fitCoordinates:{type:Function,default:function(t){var e=t.visibleArea,i=t.coordinates,o=t.aspectRatio,s=t.sizeRestrictions,n=t.positionRestrictions,a=g(g({},i),Y({width:i.width,height:i.height,aspectRatio:o,sizeRestrictions:{maxWidth:e.width,maxHeight:e.height,minHeight:Math.min(e.height,s.minHeight),minWidth:Math.min(e.width,s.minWidth)}}));return a=W(a=I(a,nt(T(i),T(a))),At(F(e),n))}},fitVisibleArea:{type:Function,default:function(t){var e=t.visibleArea,i=t.boundaries,o=t.getAreaRestrictions,s=t.coordinates,n=g({},e);n.height=n.width/R(i),n.top+=(e.height-n.height)/2,(s.height-n.height>0||s.width-n.width>0)&&(n=P(n,Math.max(s.height/n.height,s.width/n.width)));var a=zt(st(s,F(n=P(n,Rt(n,o({visibleArea:n,type:"resize"}))))));return n.width<s.width&&(a.left=0),n.height<s.height&&(a.top=0),n=W(n=I(n,a),o({visibleArea:n,type:"move"}))}},areaRestrictionsAlgorithm:{type:Function,default:function(t){var e=t.visibleArea,i=t.boundaries,o=t.imageSize,s=t.imageRestriction,n=t.type,a={};return s==="fill-area"?a={left:0,top:0,right:o.width,bottom:o.height}:s==="fit-area"&&(R(i)>R(o)?(a={top:0,bottom:o.height},e&&n==="move"&&(e.width>o.width?(a.left=-(e.width-o.width)/2,a.right=o.width-a.left):(a.left=0,a.right=o.width))):(a={left:0,right:o.width},e&&n==="move"&&(e.height>o.height?(a.top=-(e.height-o.height)/2,a.bottom=o.height-a.top):(a.top=0,a.bottom=o.height)))),a}},sizeRestrictionsAlgorithm:{type:Function,default:function(t){return{minWidth:t.minWidth,minHeight:t.minHeight,maxWidth:t.maxWidth,maxHeight:t.maxHeight}}},positionRestrictionsAlgorithm:{type:Function,default:function(t){var e=t.imageSize,i={};return t.imageRestriction!=="none"&&(i={left:0,top:0,right:e.width,bottom:e.height}),i}}},data:function(){return{transitionsActive:!1,imageLoaded:!1,imageAttributes:{width:null,height:null,crossOrigin:null,src:null},defaultImageTransforms:{rotate:0,flip:{horizontal:!1,vertical:!1}},appliedImageTransforms:{rotate:0,flip:{horizontal:!1,vertical:!1}},boundaries:{width:0,height:0},visibleArea:null,coordinates:z({},Nt)}},computed:{image:function(){return{src:this.imageAttributes.src,width:this.imageAttributes.width,height:this.imageAttributes.height,transforms:this.imageTransforms}},imageTransforms:function(){return{rotate:this.appliedImageTransforms.rotate,flip:{horizontal:this.appliedImageTransforms.flip.horizontal,vertical:this.appliedImageTransforms.flip.vertical},translateX:this.visibleArea?this.visibleArea.left/this.coefficient:0,translateY:this.visibleArea?this.visibleArea.top/this.coefficient:0,scaleX:1/this.coefficient,scaleY:1/this.coefficient}},imageSize:function(){var t=(function(e){return e*Math.PI/180})(this.imageTransforms.rotate);return{width:Math.abs(this.imageAttributes.width*Math.cos(t))+Math.abs(this.imageAttributes.height*Math.sin(t)),height:Math.abs(this.imageAttributes.width*Math.sin(t))+Math.abs(this.imageAttributes.height*Math.cos(t))}},initialized:function(){return!!(this.visibleArea&&this.imageLoaded)},settings:function(){var t=It(this.resizeImage,{touch:!0,wheel:{ratio:.1},adjustStencil:!0},{touch:!1,wheel:!1,adjustStencil:!1});return{moveImage:It(this.moveImage,{touch:!0,mouse:!0},{touch:!1,mouse:!1}),resizeImage:t}},coefficient:function(){return this.visibleArea?this.visibleArea.width/this.boundaries.width:0},areaRestrictions:function(){return this.imageLoaded?this.areaRestrictionsAlgorithm({imageSize:this.imageSize,imageRestriction:this.imageRestriction,boundaries:this.boundaries}):{}},transitionsOptions:function(){return{enabled:this.transitionsActive,timingFunction:"ease-in-out",time:350}},sizeRestrictions:function(){if(this.boundaries.width&&this.boundaries.height&&this.imageSize.width&&this.imageSize.height){var t=this.sizeRestrictionsAlgorithm({imageSize:this.imageSize,minWidth:C(this.minWidth)?0:dt(this.minWidth),minHeight:C(this.minHeight)?0:dt(this.minHeight),maxWidth:C(this.maxWidth)?1/0:dt(this.maxWidth),maxHeight:C(this.maxHeight)?1/0:dt(this.maxHeight)});if(t=(function(o){var s=o.areaRestrictions,n=o.sizeRestrictions,a=o.boundaries,h=o.positionRestrictions,r=g(g({},n),{minWidth:n.minWidth!==void 0?n.minWidth:0,minHeight:n.minHeight!==void 0?n.minHeight:0,maxWidth:n.maxWidth!==void 0?n.maxWidth:1/0,maxHeight:n.maxHeight!==void 0?n.maxHeight:1/0});h.left!==void 0&&h.right!==void 0&&(r.maxWidth=Math.min(r.maxWidth,h.right-h.left)),h.bottom!==void 0&&h.top!==void 0&&(r.maxHeight=Math.min(r.maxHeight,h.bottom-h.top));var l=Ht(s),u=ae(a,l);return l.width<1/0&&(!r.maxWidth||r.maxWidth>u.width)&&(r.maxWidth=Math.min(r.maxWidth,u.width)),l.height<1/0&&(!r.maxHeight||r.maxHeight>u.height)&&(r.maxHeight=Math.min(r.maxHeight,u.height)),r.minWidth>r.maxWidth&&(r.minWidth=r.maxWidth,r.widthFrozen=!0),r.minHeight>r.maxHeight&&(r.minHeight=r.maxHeight,r.heightFrozen=!0),r})({sizeRestrictions:t,areaRestrictions:this.getAreaRestrictions({visibleArea:this.visibleArea,type:"resize"}),imageSize:this.imageSize,boundaries:this.boundaries,positionRestrictions:this.positionRestrictions,imageRestriction:this.imageRestriction,visibleArea:this.visibleArea,stencilSize:this.getStencilSize()}),this.visibleArea&&this.stencilSize){var e=this.getStencilSize(),i=Ht(this.getAreaRestrictions({visibleArea:this.visibleArea,type:"resize"}));t.maxWidth=Math.min(t.maxWidth,i.width*e.width/this.boundaries.width),t.maxHeight=Math.min(t.maxHeight,i.height*e.height/this.boundaries.height),t.maxWidth<t.minWidth&&(t.minWidth=t.maxWidth),t.maxHeight<t.minHeight&&(t.minHeight=t.maxHeight)}return t}return{minWidth:0,minHeight:0,maxWidth:0,maxHeight:0}},positionRestrictions:function(){return this.positionRestrictionsAlgorithm({imageSize:this.imageSize,imageRestriction:this.imageRestriction})},classes:function(){return{cropper:X(),image:E(X("image"),this.imageClass),stencil:X("stencil"),boundaries:E(X("boundaries"),this.boundariesClass),stretcher:E(X("stretcher")),background:E(X("background"),this.backgroundClass),foreground:E(X("foreground"),this.foregroundClass),imageWrapper:E(X("image-wrapper")),cropperWrapper:E(X("cropper-wrapper"))}},stencilCoordinates:function(){if(this.initialized){var t=this.coordinates,e=t.width,i=t.height,o=t.left,s=t.top;return{width:e/this.coefficient,height:i/this.coefficient,left:(o-this.visibleArea.left)/this.coefficient,top:(s-this.visibleArea.top)/this.coefficient}}return this.defaultCoordinates()},boundariesStyle:function(){var t={width:this.boundaries.width?"".concat(Math.round(this.boundaries.width),"px"):"auto",height:this.boundaries.height?"".concat(Math.round(this.boundaries.height),"px"):"auto",transition:"opacity ".concat(this.transitionTime,"ms"),pointerEvents:this.imageLoaded?"all":"none"};return this.imageLoaded||(t.opacity="0"),t},imageStyle:function(){var t=this.imageAttributes.width>this.imageAttributes.height?{width:Math.min(1024,this.imageAttributes.width),height:Math.min(1024,this.imageAttributes.width)/(this.imageAttributes.width/this.imageAttributes.height)}:{height:Math.min(1024,this.imageAttributes.height),width:Math.min(1024,this.imageAttributes.height)*(this.imageAttributes.width/this.imageAttributes.height)},e={left:(t.width-this.imageSize.width)/(2*this.coefficient),top:(t.height-this.imageSize.height)/(2*this.coefficient)},i={left:(1-1/this.coefficient)*t.width/2,top:(1-1/this.coefficient)*t.height/2},o=z(z({},this.imageTransforms),{},{scaleX:this.imageTransforms.scaleX*(this.imageAttributes.width/t.width),scaleY:this.imageTransforms.scaleY*(this.imageAttributes.height/t.height)}),s={width:"".concat(t.width,"px"),height:"".concat(t.height,"px"),left:"0px",top:"0px",transform:"translate(".concat(-e.left-i.left-this.imageTransforms.translateX,"px, ").concat(-e.top-i.top-this.imageTransforms.translateY,"px)")+de(o)};return this.transitionsOptions.enabled&&(s.transition="".concat(this.transitionsOptions.time,"ms ").concat(this.transitionsOptions.timingFunction)),s}},watch:{src:function(){this.onChangeImage()},stencilComponent:function(){var t=this;this.$nextTick((function(){t.resetCoordinates(),t.runAutoZoom("setCoordinates"),t.onChange()}))},minWidth:function(){this.onPropsChange()},maxWidth:function(){this.onPropsChange()},minHeight:function(){this.onPropsChange()},maxHeight:function(){this.onPropsChange()},imageRestriction:function(){this.reset()},stencilProps:function(t,e){["aspectRatio","minAspectRatio","maxAspectRatio"].find((function(i){return t[i]!==e[i]}))&&this.$nextTick(this.onPropsChange)}},created:function(){this.debouncedUpdate=Ot(this.update,this.debounce),this.debouncedDisableTransitions=Ot(this.disableTransitions,this.transitionsOptions.time),this.awaiting=!1},mounted:function(){this.$refs.image.addEventListener("load",this.onSuccessLoadImage),this.$refs.image.addEventListener("error",this.onFailLoadImage),this.onChangeImage(),window.addEventListener("resize",this.refresh),window.addEventListener("orientationchange",this.refresh)},unmounted:function(){window.removeEventListener("resize",this.refresh),window.removeEventListener("orientationchange",this.refresh),this.imageAttributes.revoke&&this.imageAttributes.src&&URL.revokeObjectURL(this.imageAttributes.src),this.debouncedUpdate.clear(),this.debouncedDisableTransitions.clear()},methods:{getResult:function(){var t=this.initialized?this.prepareResult(z({},this.coordinates)):this.defaultCoordinates(),e={rotate:this.imageTransforms.rotate%360,flip:z({},this.imageTransforms.flip)};if(this.src&&this.imageLoaded){var i=this;return{image:this.image,coordinates:t,visibleArea:this.visibleArea?z({},this.visibleArea):null,imageTransforms:e,get canvas(){return i.canvas?i.getCanvas():void 0}}}return{image:this.image,coordinates:t,visibleArea:this.visibleArea?z({},this.visibleArea):null,canvas:void 0,imageTransforms:e}},zoom:function(t,e){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=i.transitions,s=o===void 0||o;this.onManipulateImage(new lt({},{factor:1/t,center:e}),{normalize:!1,transitions:s})},move:function(t,e){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=i.transitions,s=o===void 0||o;this.onManipulateImage(new lt({left:t||0,top:e||0}),{normalize:!1,transitions:s})},setCoordinates:function(t){var e=this,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=i.autoZoom,s=o===void 0||o,n=i.transitions,a=n===void 0||n;this.$nextTick((function(){e.imageLoaded?(e.transitionsActive||(a&&e.enableTransitions(),e.coordinates=e.applyTransform(t),s&&e.runAutoZoom("setCoordinates"),a&&e.debouncedDisableTransitions()),e.onChange()):e.delayedTransforms=t}))},refresh:function(){var t=this,e=this.$refs.image;if(this.src&&e)return this.initialized?this.updateVisibleArea().then((function(){t.onChange()})):this.resetVisibleArea().then((function(){t.onChange()}))},reset:function(){var t=this;return this.resetVisibleArea().then((function(){t.onChange(!1)}))},awaitRender:function(t){var e=this;this.awaiting||(this.awaiting=!0,this.$nextTick((function(){t(),e.awaiting=!1})))},prepareResult:function(t){return this.roundResult?(function(e){var i=e.coordinates,o=e.sizeRestrictions,s=e.positionRestrictions,n={width:Math.round(i.width),height:Math.round(i.height),left:Math.round(i.left),top:Math.round(i.top)};return n.width>o.maxWidth?n.width=Math.floor(i.width):n.width<o.minWidth&&(n.width=Math.ceil(i.width)),n.height>o.maxHeight?n.height=Math.floor(i.height):n.height<o.minHeight&&(n.height=Math.ceil(i.height)),W(n,s)})(z(z({},this.getPublicProperties()),{},{positionRestrictions:mt(this.positionRestrictions,this.visibleArea),coordinates:t})):t},processAutoZoom:function(t,e,i,o){var s=this.autoZoomAlgorithm;s||(s=this.stencilSize?Ze:this.autoZoom?qe:Qe);var n=s({event:{type:t,params:o},visibleArea:e,coordinates:i,boundaries:this.boundaries,aspectRatio:this.getAspectRatio(),positionRestrictions:this.positionRestrictions,getAreaRestrictions:this.getAreaRestrictions,sizeRestrictions:this.sizeRestrictions,stencilSize:this.getStencilSize()});return z(z({},n),{},{changed:!Vt(n.visibleArea,e)||!Vt(n.coordinates,i)})},runAutoZoom:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=e.transitions,o=i!==void 0&&i,s=je(e,fi),n=this.processAutoZoom(t,this.visibleArea,this.coordinates,s),a=n.visibleArea,h=n.coordinates,r=n.changed;o&&r&&this.enableTransitions(),this.visibleArea=a,this.coordinates=h,o&&r&&this.debouncedDisableTransitions()},normalizeEvent:function(t){return(function(e){var i=e.event,o=e.visibleArea,s=e.coefficient;if(i.type==="manipulateImage")return g(g({},i),{move:{left:i.move&&i.move.left?s*i.move.left:0,top:i.move&&i.move.top?s*i.move.top:0},scale:{factor:i.scale&&i.scale.factor?i.scale.factor:1,center:i.scale&&i.scale.center?{left:i.scale.center.left*s+o.left,top:i.scale.center.top*s+o.top}:null}});if(i.type==="resize"){var n=g(g({},i),{directions:g({},i.directions)});return U.forEach((function(h){n.directions[h]*=s})),n}if(i.type==="move"){var a=g(g({},i),{directions:g({},i.directions)});return Xe.forEach((function(h){a.directions[h]*=s})),a}return i})(z(z({},this.getPublicProperties()),{},{event:t}))},getCanvas:function(){if(this.$refs.canvas){var t=this.$refs.canvas,e=this.$refs.image,i=this.imageTransforms.rotate!==0||this.imageTransforms.flip.horizontal||this.imageTransforms.flip.vertical?(function(h,r,l){var u=l.rotate,c=l.flip,d={width:r.naturalWidth,height:r.naturalHeight},f=he(d,u),w=h.getContext("2d");h.height=f.height,h.width=f.width,w.save();var v=V(T(g({left:0,top:0},d)),u);return w.translate(-(v.left-f.width/2),-(v.top-f.height/2)),w.rotate(u*Math.PI/180),w.translate(c.horizontal?d.width:0,c.vertical?d.height:0),w.scale(c.horizontal?-1:1,c.vertical?-1:1),w.drawImage(r,0,0,d.width,d.height),w.restore(),h})(this.$refs.sourceCanvas,e,this.imageTransforms):e,o=z({minWidth:0,minHeight:0,maxWidth:1/0,maxHeight:1/0,maxArea:this.maxCanvasSize,imageSmoothingEnabled:!0,imageSmoothingQuality:"high",fillColor:"transparent"},this.canvas),s=function(h){return h.find((function(r){return l=r,!Number.isNaN(parseFloat(l))&&isFinite(l);var l}))},n=Y({sizeRestrictions:{minWidth:s([o.width,o.minWidth])||0,minHeight:s([o.height,o.minHeight])||0,maxWidth:s([o.width,o.maxWidth])||1/0,maxHeight:s([o.height,o.maxHeight])||1/0},width:this.coordinates.width,height:this.coordinates.height,aspectRatio:{minimum:this.coordinates.width/this.coordinates.height,maximum:this.coordinates.width/this.coordinates.height}});if(o.maxArea&&n.width*n.height>o.maxArea){var a=Math.sqrt(o.maxArea/(n.width*n.height));n={width:Math.round(a*n.width),height:Math.round(a*n.height)}}return(function(h,r,l,u,c){h.width=u?u.width:l.width,h.height=u?u.height:l.height;var d=h.getContext("2d");d.clearRect(0,0,h.width,h.height),c&&(c.imageSmoothingEnabled&&(d.imageSmoothingEnabled=c.imageSmoothingEnabled),c.imageSmoothingQuality&&(d.imageSmoothingQuality=c.imageSmoothingQuality),c.fillColor&&(d.fillStyle=c.fillColor,d.fillRect(0,0,h.width,h.height),d.save()));var f=l.left<0?-l.left:0,w=l.top<0?-l.top:0;d.drawImage(r,l.left+f,l.top+w,l.width,l.height,f*(h.width/l.width),w*(h.height/l.height),h.width,h.height)})(t,i,this.coordinates,n,o),t}},update:function(){this.$emit("change",this.getResult())},applyTransform:function(t){var e=arguments.length>1&&arguments[1]!==void 0&&arguments[1],i=this.visibleArea&&e?Ne(this.sizeRestrictions,this.visibleArea):this.sizeRestrictions,o=this.visibleArea&&e?mt(this.positionRestrictions,this.visibleArea):this.positionRestrictions;return Ve({transform:t,coordinates:this.coordinates,imageSize:this.imageSize,sizeRestrictions:i,positionRestrictions:o,aspectRatio:this.getAspectRatio(),visibleArea:this.visibleArea})},resetCoordinates:function(){var t=this;if(this.$refs.image){this.$refs.cropper,this.$refs.image;var e=this.defaultSize;e||(e=this.stencilSize?ti:Ke);var i=this.sizeRestrictions;i.minWidth,i.minHeight,i.maxWidth,i.maxHeight;var o=et(e)?e({boundaries:this.boundaries,imageSize:this.imageSize,aspectRatio:this.getAspectRatio(),sizeRestrictions:this.sizeRestrictions,stencilSize:this.getStencilSize(),visibleArea:this.visibleArea}):e,s=this.defaultPosition||Je,n=[o,function(a){var h=a.coordinates;return z({},et(s)?s({coordinates:h,imageSize:t.imageSize,visibleArea:t.visibleArea}):t.defaultPosition)}];this.delayedTransforms&&n.push.apply(n,Q(Array.isArray(this.delayedTransforms)?this.delayedTransforms:[this.delayedTransforms])),this.coordinates=this.applyTransform(n,!0),this.delayedTransforms=null}},clearImage:function(){var t=this;this.imageLoaded=!1,setTimeout((function(){var e=t.$refs.stretcher;e&&(e.style.height="auto",e.style.width="auto"),t.coordinates=t.defaultCoordinates(),t.boundaries={width:0,height:0}}),this.transitionTime)},enableTransitions:function(){this.transitions&&(this.transitionsActive=!0)},disableTransitions:function(){this.transitionsActive=!1},updateBoundaries:function(){var t=this,e=this.$refs.stretcher,i=this.$refs.cropper;return this.initStretcher({cropper:i,stretcher:e,imageSize:this.imageSize}),this.$nextTick().then((function(){var o={cropper:i,imageSize:t.imageSize};if(et(t.defaultBoundaries)?t.boundaries=t.defaultBoundaries(o):t.defaultBoundaries==="fit"?t.boundaries=(function(s){var n=s.cropper,a=s.imageSize,h=n.clientHeight,r=n.clientWidth,l=h,u=a.width*h/a.height;return u>r&&(u=r,l=a.height*r/a.width),{width:u,height:l}})(o):t.boundaries=(function(s){var n=s.cropper;return{width:n.clientWidth,height:n.clientHeight}})(o),!t.boundaries.width||!t.boundaries.height)throw new Error("It's impossible to fit the cropper in the current container")}))},resetVisibleArea:function(){var t=this;return this.appliedImageTransforms=z(z({},this.defaultImageTransforms),{},{flip:z({},this.defaultImageTransforms.flip)}),this.updateBoundaries().then((function(){t.priority!=="visible-area"&&(t.visibleArea=null,t.resetCoordinates());var e,i,o,s,n,a,h=t.defaultVisibleArea||ei;t.visibleArea=et(h)?h({imageSize:t.imageSize,boundaries:t.boundaries,coordinates:t.priority!=="visible-area"?t.coordinates:null,getAreaRestrictions:t.getAreaRestrictions,stencilSize:t.getStencilSize()}):t.defaultVisibleArea,t.visibleArea=(e={visibleArea:t.visibleArea,boundaries:t.boundaries,getAreaRestrictions:t.getAreaRestrictions},i=e.visibleArea,o=e.boundaries,s=e.getAreaRestrictions,n=g({},i),a=R(o),n.width/n.height!==a&&(n.height=n.width/a),W(n,s({visibleArea:n,type:"move"}))),t.priority==="visible-area"?t.resetCoordinates():t.coordinates=t.fitCoordinates({visibleArea:t.visibleArea,coordinates:t.coordinates,aspectRatio:t.getAspectRatio(),positionRestrictions:t.positionRestrictions,sizeRestrictions:t.sizeRestrictions}),t.runAutoZoom("resetVisibleArea")})).catch((function(){t.visibleArea=null}))},updateVisibleArea:function(){var t=this;return this.updateBoundaries().then((function(){t.visibleArea=t.fitVisibleArea({imageSize:t.imageSize,boundaries:t.boundaries,visibleArea:t.visibleArea,coordinates:t.coordinates,getAreaRestrictions:t.getAreaRestrictions}),t.coordinates=t.fitCoordinates({visibleArea:t.visibleArea,coordinates:t.coordinates,aspectRatio:t.getAspectRatio(),positionRestrictions:t.positionRestrictions,sizeRestrictions:t.sizeRestrictions}),t.runAutoZoom("updateVisibleArea")})).catch((function(){t.visibleArea=null}))},onChange:function(){var t=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0];t&&this.debounce?this.debouncedUpdate():this.update()},onChangeImage:function(){var t,e=this;if(this.imageLoaded=!1,this.delayedTransforms=null,this.src){if((function(s){if(Xt(s))return!1;var n=window.location,a=/(\w+:)?(?:\/\/)([\w.-]+)?(?::(\d+))?\/?/.exec(s)||[],h={protocol:a[1]||"",host:a[2]||"",port:a[3]||""},r=function(l){return l.port||((l.protocol||n.protocol)==="http"?80:433)};return!(!h.protocol&&!h.host&&!h.port||h.protocol&&h.protocol==n.protocol&&h.host&&h.host==n.host&&h.host&&r(h)==r(n))})(this.src)){var i=C(this.crossOrigin)?this.canvas:this.crossOrigin;i===!0&&(i="anonymous"),this.imageAttributes.crossOrigin=i||null}if(this.checkOrientation){var o=(t=this.src,new Promise((function(s){di(t).then((function(n){var a=mi(n);s(n?{source:t,arrayBuffer:n,orientation:a}:{source:t,arrayBuffer:null,orientation:null})})).catch((function(n){console.warn(n),s({source:t,arrayBuffer:null,orientation:null})}))})));setTimeout((function(){o.then(e.onParseImage)}),this.transitionTime)}else setTimeout((function(){e.onParseImage({source:e.src})}),this.transitionTime)}else this.clearImage()},onFailLoadImage:function(){this.imageAttributes.src&&(this.clearImage(),this.$emit("error"))},onSuccessLoadImage:function(){var t=this,e=this.$refs.image;e&&!this.imageLoaded&&(this.imageAttributes.height=e.naturalHeight,this.imageAttributes.width=e.naturalWidth,this.imageLoaded=!0,this.resetVisibleArea().then((function(){t.$emit("ready"),t.onChange(!1)})))},onParseImage:function(t){var e=this,i=t.source,o=t.arrayBuffer,s=t.orientation;this.imageAttributes.revoke&&this.imageAttributes.src&&URL.revokeObjectURL(this.imageAttributes.src),this.imageAttributes.revoke=!1,o&&s&&s>1?ie(i)||!Xt(i)?(this.imageAttributes.src=URL.createObjectURL(new Blob([o])),this.imageAttributes.revoke=!0):this.imageAttributes.src=(function(n){for(var a=[],h=new Uint8Array(n);h.length>0;){var r=h.subarray(0,8192);a.push(String.fromCharCode.apply(null,Array.from?Array.from(r):r.slice())),h=h.subarray(8192)}return"data:image/jpeg;base64,"+btoa(a.join(""))})(o):this.imageAttributes.src=i,et(this.defaultTransforms)?this.appliedImageTransforms=Wt(this.defaultTransforms()):pt(this.defaultTransforms)?this.appliedImageTransforms=Wt(this.defaultTransforms):this.appliedImageTransforms=(function(n){var a=Wt({});if(n)switch(n){case 2:a.flip.horizontal=!0;break;case 3:a.rotate=-180;break;case 4:a.flip.vertical=!0;break;case 5:a.rotate=90,a.flip.vertical=!0;break;case 6:a.rotate=90;break;case 7:a.rotate=90,a.flip.horizontal=!0;break;case 8:a.rotate=-90}return a})(s),this.defaultImageTransforms=z(z({},this.appliedImageTransforms),{},{flip:z({},this.appliedImageTransforms.flip)}),this.$nextTick((function(){var n=e.$refs.image;n&&n.complete&&((function(a){return!!a.naturalWidth})(n)?e.onSuccessLoadImage():e.onFailLoadImage())}))},onResizeEnd:function(){this.runAutoZoom("resize",{transitions:!0})},onMoveEnd:function(){this.runAutoZoom("move",{transitions:!0})},onMove:function(t){var e=this;this.transitionsOptions.enabled||this.awaitRender((function(){e.coordinates=e.moveAlgorithm(z(z({},e.getPublicProperties()),{},{positionRestrictions:mt(e.positionRestrictions,e.visibleArea),coordinates:e.coordinates,event:e.normalizeEvent(t)})),e.onChange()}))},onResize:function(t){var e=this;this.transitionsOptions.enabled||this.stencilSize&&!this.autoZoom||this.awaitRender((function(){var i=e.sizeRestrictions,o=Math.min(e.coordinates.width,e.coordinates.height,20*e.coefficient);e.coordinates=e.resizeAlgorithm(z(z({},e.getPublicProperties()),{},{positionRestrictions:mt(e.positionRestrictions,e.visibleArea),sizeRestrictions:{maxWidth:Math.min(i.maxWidth,e.visibleArea.width),maxHeight:Math.min(i.maxHeight,e.visibleArea.height),minWidth:Math.max(i.minWidth,o),minHeight:Math.max(i.minHeight,o)},event:e.normalizeEvent(t)})),e.onChange(),e.ticking=!1}))},onManipulateImage:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.transitionsOptions.enabled){var i=e.transitions,o=i!==void 0&&i,s=e.normalize,n=s===void 0||s;o&&this.enableTransitions();var a=ii(z(z({},this.getPublicProperties()),{},{event:n?this.normalizeEvent(t):t,getAreaRestrictions:this.getAreaRestrictions,imageRestriction:this.imageRestriction,adjustStencil:!this.stencilSize&&this.settings.resizeImage.adjustStencil})),h=a.visibleArea,r=a.coordinates;this.visibleArea=h,this.coordinates=r,this.runAutoZoom("manipulateImage"),this.onChange(),o&&this.debouncedDisableTransitions()}},onPropsChange:function(){this.coordinates=this.applyTransform(this.coordinates,!0),this.onChange(!1)},getAreaRestrictions:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=t.visibleArea,i=t.type,o=i===void 0?"move":i;return this.areaRestrictionsAlgorithm({boundaries:this.boundaries,imageSize:this.imageSize,imageRestriction:this.imageRestriction,visibleArea:e,type:o})},getAspectRatio:function(t){var e,i,o=this.stencilProps,s=o.aspectRatio,n=o.minAspectRatio,a=o.maxAspectRatio;if(this.$refs.stencil&&this.$refs.stencil.aspectRatios){var h=this.$refs.stencil.aspectRatios();e=h.minimum,i=h.maximum}if(C(e)&&(e=C(s)?n:s),C(i)&&(i=C(s)?a:s),!t&&(C(e)||C(i))){var r=this.getStencilSize(),l=r?R(r):null;C(e)&&(e=Yt(l)?l:void 0),C(i)&&(i=Yt(l)?l:void 0)}return{minimum:e,maximum:i}},getStencilSize:function(){if(this.stencilSize)return t={currentStencilSize:{width:this.stencilCoordinates.width,height:this.stencilCoordinates.height},stencilSize:this.stencilSize,boundaries:this.boundaries,coefficient:this.coefficient,coordinates:this.coordinates,aspectRatio:this.getAspectRatio(!0)},e=t.boundaries,i=t.stencilSize,o=t.aspectRatio,Ct(R(s=et(i)?i({boundaries:e,aspectRatio:o}):i),o)&&(s=Y({sizeRestrictions:{maxWidth:e.width,maxHeight:e.height,minWidth:0,minHeight:0},width:s.width,height:s.height,aspectRatio:{minimum:o.minimum,maximum:o.maximum}})),(s.width>e.width||s.height>e.height)&&(s=Y({sizeRestrictions:{maxWidth:e.width,maxHeight:e.height,minWidth:0,minHeight:0},width:s.width,height:s.height,aspectRatio:{minimum:R(s),maximum:R(s)}})),s;var t,e,i,o,s},getPublicProperties:function(){return{coefficient:this.coefficient,visibleArea:this.visibleArea,coordinates:this.coordinates,boundaries:this.boundaries,sizeRestrictions:this.sizeRestrictions,positionRestrictions:this.positionRestrictions,aspectRatio:this.getAspectRatio(),imageRestriction:this.imageRestriction}},defaultCoordinates:function(){return z({},Nt)},flip:function(t,e){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},o=i.transitions,s=o===void 0||o;if(!this.transitionsActive){s&&this.enableTransitions();var n=z({},this.imageTransforms.flip),a=oi({flip:{horizontal:t?!n.horizontal:n.horizontal,vertical:e?!n.vertical:n.vertical},previousFlip:n,rotate:this.imageTransforms.rotate,visibleArea:this.visibleArea,coordinates:this.coordinates,imageSize:this.imageSize,positionRestrictions:this.positionRestrictions,sizeRestrictions:this.sizeRestrictions,getAreaRestrictions:this.getAreaRestrictions,aspectRatio:this.getAspectRatio()}),h=a.visibleArea,r=a.coordinates;t&&(this.appliedImageTransforms.flip.horizontal=!this.appliedImageTransforms.flip.horizontal),e&&(this.appliedImageTransforms.flip.vertical=!this.appliedImageTransforms.flip.vertical),this.visibleArea=h,this.coordinates=r,this.onChange(),s&&this.debouncedDisableTransitions()}},rotate:function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=e.transitions,o=i===void 0||i;if(!this.transitionsActive){o&&this.enableTransitions();var s=z({},this.imageSize);this.appliedImageTransforms.rotate+=t;var n=ni({visibleArea:this.visibleArea,coordinates:this.coordinates,previousImageSize:s,imageSize:this.imageSize,angle:t,positionRestrictions:this.positionRestrictions,sizeRestrictions:this.sizeRestrictions,getAreaRestrictions:this.getAreaRestrictions,aspectRatio:this.getAspectRatio()}),a=n.visibleArea,h=n.coordinates,r=this.processAutoZoom("rotateImage",a,h);a=r.visibleArea,h=r.coordinates,this.visibleArea=a,this.coordinates=h,this.onChange(),o&&this.debouncedDisableTransitions()}}},emits:["change","error","ready"]},gi={key:0,ref:"canvas",style:{display:"none"}},pi={key:1,ref:"sourceCanvas",style:{display:"none"}};ve.render=function(t,e,i,o,s,n){return A(),M("div",{ref:"cropper",class:n.classes.cropper},[S("div",{ref:"stretcher",class:n.classes.stretcher},null,2),S("div",{class:n.classes.boundaries,style:n.boundariesStyle},[(A(),M(wt(i.backgroundWrapperComponent),{class:n.classes.cropperWrapper,"wheel-resize":n.settings.resizeImage.wheel,"touch-resize":n.settings.resizeImage.touch,"touch-move":n.settings.moveImage.touch,"mouse-move":n.settings.moveImage.mouse,onMove:n.onManipulateImage,onResize:n.onManipulateImage},{default:$((function(){return[S("div",{class:n.classes.background,style:n.boundariesStyle},null,6),S("div",{class:n.classes.imageWrapper},[S("img",{ref:"image",crossorigin:s.imageAttributes.crossOrigin,src:s.imageAttributes.src,class:n.classes.image,style:n.imageStyle,onMousedown:e[1]||(e[1]=Dt((function(){}),["prevent"]))},null,46,["crossorigin","src"])],2),S("div",{class:n.classes.foreground,style:n.boundariesStyle},null,6),yt((A(),M(wt(i.stencilComponent),te({ref:"stencil",image:n.image,coordinates:s.coordinates,"stencil-coordinates":n.stencilCoordinates,transitions:n.transitionsOptions},i.stencilProps,{onResize:n.onResize,onResizeEnd:n.onResizeEnd,onMove:n.onMove,onMoveEnd:n.onMoveEnd}),null,16,["image","coordinates","stencil-coordinates","transitions","onResize","onResizeEnd","onMove","onMoveEnd"])),[[Kt,s.imageLoaded]]),i.canvas?(A(),M("canvas",gi,null,512)):it("",!0),i.canvas?(A(),M("canvas",pi,null,512)):it("",!0)]})),_:1},8,["class","wheel-resize","touch-resize","touch-move","mouse-move","onMove","onResize"]))],6)],2)};const vi={template:"<span>📥</span>"},bi={template:"<span>🔄</span>"},wi={class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},yi={class:"text-lg font-semibold text-slate-100 mb-4 border-b border-slate-700/30 pb-2"},zi={class:"space-y-4"},Ri={class:"text-lg font-medium text-slate-100 mb-2"},Ai={class:"text-slate-400"},xi={class:"text-sm text-slate-500 mt-2"},Mi={class:"text-sm text-slate-500 mt-2"},Si={key:1,class:"space-y-6"},Ei={class:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"},Ci={class:"text-md font-medium text-slate-100"},Ti={class:"flex flex-col xl:flex-row gap-6"},Wi={class:"flex-1 min-w-0"},$i={class:"text-sm font-medium text-slate-300 mb-3"},Di={class:"bg-slate-800/50 rounded-xl p-4 border border-slate-700/50 glass"},Li={class:"cropper-container w-full",style:{height:"500px"}},Oi={class:"text-sm text-slate-500 mt-2"},Ii={class:"xl:w-80 w-full xl:flex-shrink-0 space-y-6"},Hi={class:"block text-sm font-medium text-slate-300 mb-2"},Pi={class:"block text-sm font-medium text-slate-300 mb-2"},ji={class:"space-y-2"},_i=["value"],ki={class:"text-sm font-medium text-slate-300"},Bi={key:0,class:"rounded-2xl p-6 mb-8 glass border border-slate-700/50"},Ui={class:"flex justify-between items-center mb-6"},Fi={class:"text-lg font-semibold text-slate-100"},Gi={class:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4"},Xi={class:"flex justify-center items-center h-20 mb-4 bg-slate-800/50 rounded-lg"},Yi=["src","alt"],Ni={class:"text-sm font-medium text-slate-100 mb-1"},Vi={class:"text-xs text-slate-400 mb-4"},Zi={class:"rounded-2xl p-6 mb-8 glass border border-slate-700/50"},qi={class:"text-lg font-semibold text-slate-100 mb-4 border-b border-slate-700/30 pb-2"},Qi={class:"prose prose-sm max-w-none text-slate-300"},Ji={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},Ki={class:"font-medium text-slate-100 mb-2"},tn={class:"font-medium text-slate-100 mb-2"},en={class:"text-sm space-y-2 list-disc list-inside bg-blue-500/10 p-4 rounded-xl border border-blue-500/30"},nn={class:"text-blue-300"},on={class:"text-blue-300"},sn={class:"text-blue-300"},rn={class:"text-blue-300"},an=Me({__name:"FaviconGenerator",setup(t){const{t:e}=Se(),{success:i,error:o}=Ie(),s=_(),n=_(),a=_(null),h=_(""),r=_({width:0,height:0}),l=_(!1),u=_(!1),c=_(null),d=_("ico"),f=[16,32,48,64,128],w=_([16,32,48]),v=_([]);function D(){s.value?.click()}function j(m){const b=m.target.files?.[0];b&&G(b)}function Z(m){m.preventDefault(),l.value=!1;const b=m.dataTransfer?.files?.[0];b&&b.type.startsWith("image/")?G(b):o(e("tools.faviconGenerator.errors.invalidFile"))}function q(m){const b=m.clipboardData?.items;if(b){for(const y of b)if(y.type.startsWith("image/")){const L=y.getAsFile();if(L){G(L),i(e("tools.faviconGenerator.messages.pasteSuccess"));return}}}}function G(m){const b=new FileReader;b.onload=y=>{const L=y.target?.result,O=new Image;O.onload=()=>{a.value=O,h.value=L,r.value={width:O.width,height:O.height},i(e("tools.faviconGenerator.success.imageLoaded"))},O.onerror=()=>{o(e("tools.faviconGenerator.errors.imageLoadFailed"))},O.src=L},b.onerror=()=>{o(e("tools.faviconGenerator.errors.fileReadFailed"))},b.readAsDataURL(m)}function be(){a.value=null,h.value="",r.value={width:0,height:0},c.value=null,v.value=[],s.value&&(s.value.value="")}function we({coordinates:m,canvas:b}){c.value={coordinates:m,canvas:b}}async function ye(){if(!(!a.value||w.value.length===0||!c.value)){u.value=!0,v.value=[];try{for(const m of w.value){const b=document.createElement("canvas"),y=b.getContext("2d");if(!y)continue;if(b.width=m,b.height=m,c.value.canvas)y.drawImage(c.value.canvas,0,0,m,m);else{const tt=c.value.coordinates;y.drawImage(a.value,tt.left,tt.top,tt.width,tt.height,0,0,m,m)}const L=d.value==="ico"?"image/png":`image/${d.value}`,O=await new Promise(tt=>{b.toBlob(xe=>tt(xe),L,.9)}),K=b.toDataURL(L,.9),xt=d.value==="ico"?"ico":d.value,Ae=`favicon-${m}.${xt}`;v.value.push({size:m,format:d.value,dataUrl:K,blob:O,filename:Ae})}i(e("tools.faviconGenerator.success.generationComplete"))}catch(m){console.error("Generation error:",m),o(e("tools.faviconGenerator.errors.generationFailed"))}finally{u.value=!1}}}function ze(m){const b=URL.createObjectURL(m.blob),y=document.createElement("a");y.href=b,y.download=m.filename,document.body.appendChild(y),y.click(),document.body.removeChild(y),URL.revokeObjectURL(b)}async function Re(){if(v.value.length!==0)try{const{default:m}=await Le(async()=>{const{default:K}=await import("./jszip.min-BZakjvyN.js").then(xt=>xt.j);return{default:K}},__vite__mapDeps([0,1,2])),b=new m;for(const K of v.value)b.file(K.filename,K.blob);const y=await b.generateAsync({type:"blob"}),L=URL.createObjectURL(y),O=document.createElement("a");O.href=L,O.download=`favicons_${new Date().toISOString().split("T")[0]}.zip`,document.body.appendChild(O),O.click(),document.body.removeChild(O),URL.revokeObjectURL(L),i(e("tools.faviconGenerator.success.downloadComplete"))}catch(m){console.error("Download error:",m),o(e("tools.faviconGenerator.errors.downloadFailed"))}}return Ee(()=>{document.addEventListener("paste",q),document.addEventListener("dragenter",m=>{m.preventDefault(),l.value=!0}),document.addEventListener("dragleave",m=>{m.relatedTarget||(l.value=!1)}),document.addEventListener("dragover",m=>{m.preventDefault()}),document.addEventListener("drop",m=>{m.preventDefault(),l.value=!1})}),Ce(()=>{document.removeEventListener("paste",q),h.value&&URL.revokeObjectURL(h.value)}),(m,b)=>(A(),M(He,{icon:"🖼️",title:m.$t("tools.faviconGenerator.title"),description:m.$t("tools.faviconGenerator.description"),features:[m.$t("tools.faviconGenerator.features.cropping.title"),m.$t("tools.faviconGenerator.features.multiSize.title"),m.$t("tools.faviconGenerator.features.formats.title")]},{"header-actions":$(()=>[v.value.length>0?(A(),M(ht,{key:0,onClick:Re,variant:"success",size:"md","icon-left":Mt(vi)},{default:$(()=>[at(x(m.$t("tools.faviconGenerator.downloadAll")),1)]),_:1},8,["icon-left"])):it("",!0)]),default:$(()=>[p("div",wi,[p("h3",yi,x(m.$t("tools.faviconGenerator.uploadSection")),1),a.value?it("",!0):(A(),rt("div",{key:0,onDrop:Z,onDragover:b[0]||(b[0]=Dt(()=>{},["prevent"])),onDragenter:b[1]||(b[1]=Dt(()=>{},["prevent"])),onClick:D,class:Te(["border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-300 hover-lift",l.value?"border-primary-500 bg-primary-500/20 shadow-glow":"border-slate-600 hover:border-slate-500"])},[p("input",{ref_key:"fileInput",ref:s,type:"file",accept:"image/*",onChange:j,class:"hidden"},null,544),p("div",zi,[b[4]||(b[4]=p("div",{class:"text-5xl text-slate-400"},"🖼️",-1)),p("div",null,[p("h3",Ri,x(m.$t("tools.faviconGenerator.uploadTitle")),1),p("p",Ai,x(m.$t("tools.faviconGenerator.uploadDescription")),1),p("p",xi,x(m.$t("tools.faviconGenerator.supportedFormats"))+": JPG, PNG, GIF, WebP ",1),p("p",Mi,x(m.$t("tools.faviconGenerator.pasteHint")),1)]),S(ht,{variant:"primary",size:"md"},{default:$(()=>[at(x(m.$t("tools.faviconGenerator.selectImage")),1)]),_:1})])],34)),a.value?(A(),rt("div",Si,[p("div",Ei,[p("h4",Ci,x(m.$t("tools.faviconGenerator.cropImage")),1),S(ht,{onClick:be,variant:"secondary",size:"sm","icon-left":Mt(bi)},{default:$(()=>[at(x(m.$t("tools.faviconGenerator.selectAnother")),1)]),_:1},8,["icon-left"])]),p("div",Ti,[p("div",Wi,[p("h5",$i,x(m.$t("tools.faviconGenerator.cropPreview")),1),p("div",Di,[p("div",Li,[S(Mt(ve),{ref_key:"cropperRef",ref:n,src:h.value,"stencil-props":{aspectRatio:1,movable:!0,resizable:!0},"resize-image":{adjustStencil:!1},"default-size":{width:200,height:200},onChange:we,class:"rounded-xl border border-slate-700 w-full h-full"},null,8,["src"])])]),p("p",Oi,x(m.$t("tools.faviconGenerator.cropInstructionAdvanced")),1)]),p("div",Ii,[p("div",null,[p("label",Hi,x(m.$t("tools.faviconGenerator.outputFormat")),1),yt(p("select",{"onUpdate:modelValue":b[2]||(b[2]=y=>d.value=y),class:"w-full px-4 py-3 bg-slate-800/50 border border-slate-700/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-slate-100 transition-all duration-200"},[...b[5]||(b[5]=[p("option",{value:"ico"},"ICO",-1),p("option",{value:"png"},"PNG",-1),p("option",{value:"jpg"},"JPG",-1)])],512),[[We,d.value]])]),p("div",null,[p("label",Pi,x(m.$t("tools.faviconGenerator.sizes")),1),p("div",ji,[(A(),rt(vt,null,bt(f,y=>p("label",{key:y,class:"flex items-center space-x-3 cursor-pointer p-3 rounded-xl hover:bg-slate-800/70 transition-colors duration-200 border border-slate-700/50"},[yt(p("input",{type:"checkbox","onUpdate:modelValue":b[3]||(b[3]=L=>w.value=L),value:y,class:"h-5 w-5 text-primary-600 rounded focus:ring-primary-500 border-slate-600 bg-slate-800"},null,8,_i),[[$e,w.value]]),p("span",ki,x(y)+"×"+x(y)+" px",1)])),64))])]),S(ht,{onClick:ye,disabled:w.value.length===0||u.value,loading:u.value,variant:"primary",size:"lg",class:"w-full"},{default:$(()=>[at(x(m.$t("tools.faviconGenerator.generate")),1)]),_:1},8,["disabled","loading"])])])])):it("",!0)]),v.value.length>0?(A(),rt("div",Bi,[p("div",Ui,[p("h3",Fi,x(m.$t("tools.faviconGenerator.generatedFavicons"))+" ("+x(v.value.length)+") ",1)]),p("div",Gi,[(A(!0),rt(vt,null,bt(v.value,y=>(A(),M(Pe,{key:`${y.size}-${y.format}`,class:"text-center border border-slate-700/50 bg-slate-800/30"},{content:$(()=>[p("div",Xi,[p("img",{src:y.dataUrl,alt:`${y.size}x${y.size} favicon`,class:"max-w-full max-h-full",style:De({width:Math.min(y.size,48)+"px",height:Math.min(y.size,48)+"px"})},null,12,Yi)]),p("div",Ni,x(y.size)+"×"+x(y.size),1),p("div",Vi,x(y.format.toUpperCase()),1),S(ht,{onClick:L=>ze(y),variant:"primary",size:"sm",class:"w-full"},{default:$(()=>[at(x(m.$t("common.download")),1)]),_:2},1032,["onClick"])]),_:2},1024))),128))])])):it("",!0),p("div",Zi,[p("h3",qi,x(m.$t("tools.faviconGenerator.usageInstructions")),1),p("div",Qi,[p("div",Ji,[p("div",null,[p("h4",Ki,x(m.$t("tools.faviconGenerator.htmlUsage")),1),b[6]||(b[6]=p("pre",{class:"bg-slate-800/50 text-green-400 p-4 rounded-xl text-xs overflow-x-auto border border-slate-700/50"},[p("code",null,`<!-- Basic favicon -->
<link rel="shortcut icon" href="/favicon.ico" />

<!-- Multiple sizes -->
<link rel="icon" sizes="16x16" href="/favicon-16.png" />
<link rel="icon" sizes="32x32" href="/favicon-32.png" />
<link rel="icon" sizes="48x48" href="/favicon-48.png" />`)],-1))]),p("div",null,[p("h4",tn,x(m.$t("tools.faviconGenerator.tips")),1),p("ul",en,[p("li",nn,x(m.$t("tools.faviconGenerator.tip1")),1),p("li",on,x(m.$t("tools.faviconGenerator.tip2")),1),p("li",sn,x(m.$t("tools.faviconGenerator.tip3")),1),p("li",rn,x(m.$t("tools.faviconGenerator.tip4")),1)])])])])])]),_:1},8,["title","description","features"]))}}),dn=Oe(an,[["__scopeId","data-v-20c57a3a"]]);export{dn as default};
