import{d as J,u as q,r as b,c as n,a as e,t as s,e as v,f as i,g as x,v as w,h as k,j as F,o as a}from"./index-CkZTMFXG.js";import{u as B}from"./useToast-virEbLJw.js";const M={class:"min-h-screen bg-gray-50 p-6"},R={class:"max-w-6xl mx-auto space-y-6"},T={class:"text-center mb-8"},L={class:"text-3xl font-bold text-gray-900 mb-2"},P={class:"text-gray-600"},z={class:"grid md:grid-cols-3 gap-6 mb-8"},D={class:"bg-white p-6 rounded-lg shadow-sm border"},H={class:"text-lg font-semibold mb-2"},Y={class:"text-gray-600 text-sm"},G={class:"bg-white p-6 rounded-lg shadow-sm border"},K={class:"text-lg font-semibold mb-2"},Q={class:"text-gray-600 text-sm"},W={class:"bg-white p-6 rounded-lg shadow-sm border"},X={class:"text-lg font-semibold mb-2"},Z={class:"text-gray-600 text-sm"},ee={class:"grid lg:grid-cols-2 gap-6"},te={class:"bg-white p-6 rounded-lg shadow-sm border"},oe={class:"flex items-center justify-between mb-4"},se={class:"text-lg font-semibold text-gray-900"},re={class:"flex space-x-2"},le=["placeholder"],ne={class:"mt-4 space-y-4"},ae={class:"font-medium text-gray-900"},ie={class:"space-y-3"},de={class:"flex items-center space-x-4"},ce={class:"text-sm font-medium text-gray-700 w-24"},ue={value:"index"},me={value:"condition"},pe={value:"random"},ve={key:0,class:"space-y-3"},ge={class:"grid grid-cols-2 gap-4"},ye={class:"flex items-center space-x-2"},he={class:"text-sm font-medium text-gray-700 w-16"},be=["placeholder"],fe={class:"flex items-center space-x-2"},xe={class:"text-sm font-medium text-gray-700 w-16"},_e=["placeholder"],Se={key:1,class:"space-y-3"},Ae={class:"flex items-center space-x-4"},je={class:"text-sm font-medium text-gray-700 w-24"},$e=["placeholder"],we={class:"grid grid-cols-3 gap-2"},ke={value:"equals"},Ce={value:"not-equals"},Ie={value:"greater"},Ve={value:"less"},Ee={value:"contains"},Oe=["placeholder"],Ue={key:2,class:"space-y-3"},Ne={class:"flex items-center space-x-4"},Je={class:"text-sm font-medium text-gray-700 w-24"},qe=["placeholder"],Fe={class:"flex items-center"},Be={key:0,class:"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg"},Me={class:"font-medium text-blue-900 mb-2"},Re={class:"text-sm text-blue-800 space-y-1"},Te={key:0},Le={key:0},Pe={key:1},ze={key:2},De=["disabled"],He={class:"bg-white p-6 rounded-lg shadow-sm border"},Ye={class:"flex items-center justify-between mb-4"},Ge={class:"text-lg font-semibold text-gray-900"},Ke={class:"flex space-x-2"},Qe={key:0,class:"h-80 flex items-center justify-center text-gray-500 border-2 border-dashed border-gray-200 rounded-lg"},We={class:"text-center"},Xe={key:1,class:"h-80 flex items-center justify-center"},Ze={class:"text-center text-red-600"},et={class:"font-medium"},tt={class:"text-sm"},ot={key:2,class:"space-y-4"},st={class:"bg-green-50 border border-green-200 rounded-lg p-4"},rt={class:"flex items-center"},lt={class:"font-medium text-green-800"},nt={class:"text-sm text-green-600"},at=["value"],ut=J({__name:"JsonArraySlicer",setup(it){const{t:_}=q(),{success:S,copySuccess:C}=B(),g=b(""),c=b(""),p=b(""),f=b(!1),A=b(""),r=b({method:"index",startIndex:0,endIndex:null,conditionField:"",operator:"equals",conditionValue:"",sampleCount:5,preserveOrder:!0}),d=b(null);function I(){const t=[{id:1,name:"John",age:30,city:"New York"},{id:2,name:"Jane",age:25,city:"Los Angeles"},{id:3,name:"Bob",age:35,city:"Chicago"},{id:4,name:"Alice",age:28,city:"Houston"},{id:5,name:"Charlie",age:32,city:"Phoenix"},{id:6,name:"Diana",age:27,city:"Philadelphia"},{id:7,name:"Eve",age:29,city:"San Antonio"}];g.value=JSON.stringify(t,null,2),u()}function V(){g.value="",c.value="",p.value="",f.value=!1,d.value=null}function u(){if(p.value="",d.value=null,!g.value.trim()){f.value=!1;return}try{const t=JSON.parse(g.value);if(!Array.isArray(t))throw new Error(_("tools.jsonArraySlicer.errors.invalidArray"));f.value=!0;const o={total:t.length};if(r.value.method==="index"){const l=r.value.startIndex,y=r.value.endIndex!==null?r.value.endIndex:t.length,m=y>t.length?t.length:y;o.willExtract=Math.max(0,m-l),o.range=`${l}-${m}`}else if(r.value.method==="condition"){const l=t.filter(y=>j(y)).length;o.matching=l}d.value=o}catch(t){p.value=_("tools.jsonArraySlicer.errors.invalidJson")+" "+t.message,f.value=!1}}function j(t){const{conditionField:o,operator:l,conditionValue:y}=r.value;if(!o||!(o in t))return!1;const m=t[o],h=y;switch(l){case"equals":return String(m)===h;case"not-equals":return String(m)!==h;case"greater":return Number(m)>Number(h);case"less":return Number(m)<Number(h);case"contains":return String(m).includes(h);default:return!1}}function E(){if(f.value)try{const t=JSON.parse(g.value);let o=[],l=0;switch(r.value.method){case"index":const y=r.value.startIndex,m=r.value.endIndex!==null?r.value.endIndex:t.length;o=t.slice(y,m),l=o.length;break;case"condition":o=t.filter($=>j($)),l=o.length;break;case"random":const h=[...t].sort(()=>.5-Math.random());o=r.value.preserveOrder?t.filter(($,N)=>h.slice(0,r.value.sampleCount).includes(t[N])):h.slice(0,r.value.sampleCount),l=o.length;break}c.value=JSON.stringify(o,null,2),A.value=`${l} items extracted`,p.value="",S(_("toast.success"))}catch(t){p.value=_("tools.jsonArraySlicer.errors.invalidJson")+" "+t.message}}function O(){c.value&&(navigator.clipboard.writeText(c.value),C())}function U(){if(c.value){const t=new Blob([c.value],{type:"application/json"}),o=URL.createObjectURL(t),l=document.createElement("a");l.href=o,l.download="sliced-array.json",document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(o),S(_("toast.downloadSuccess"))}}return(t,o)=>(a(),n("div",M,[e("div",R,[e("div",T,[e("h1",L,s(t.$t("tools.jsonArraySlicer.title")),1),e("p",P,s(t.$t("tools.jsonArraySlicer.description")),1)]),e("div",z,[e("div",D,[o[9]||(o[9]=e("div",{class:"text-2xl mb-3"},"✂️",-1)),e("h3",H,s(t.$t("tools.jsonArraySlicer.features.indexSlicing.title")),1),e("p",Y,s(t.$t("tools.jsonArraySlicer.features.indexSlicing.description")),1)]),e("div",G,[o[10]||(o[10]=e("div",{class:"text-2xl mb-3"},"🎯",-1)),e("h3",K,s(t.$t("tools.jsonArraySlicer.features.conditionalSlicing.title")),1),e("p",Q,s(t.$t("tools.jsonArraySlicer.features.conditionalSlicing.description")),1)]),e("div",W,[o[11]||(o[11]=e("div",{class:"text-2xl mb-3"},"📊",-1)),e("h3",X,s(t.$t("tools.jsonArraySlicer.features.smartPreview.title")),1),e("p",Z,s(t.$t("tools.jsonArraySlicer.features.smartPreview.description")),1)])]),e("div",ee,[e("div",te,[e("div",oe,[e("h3",se,s(t.$t("tools.jsonArraySlicer.inputTitle")),1),e("div",re,[e("button",{onClick:I,class:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"},s(t.$t("common.loadExample")),1),e("button",{onClick:V,class:"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"},s(t.$t("common.clear")),1)])]),v(e("textarea",{"onUpdate:modelValue":o[0]||(o[0]=l=>g.value=l),placeholder:t.$t("tools.jsonArraySlicer.inputPlaceholder"),class:"w-full h-64 p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",onInput:u},null,40,le),[[x,g.value]]),e("div",ne,[e("h4",ae,s(t.$t("tools.jsonArraySlicer.slicingOptions")),1),e("div",ie,[e("div",de,[e("label",ce,s(t.$t("tools.jsonArraySlicer.method"))+":",1),v(e("select",{"onUpdate:modelValue":o[1]||(o[1]=l=>r.value.method=l),onChange:u,class:"flex-1 px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[e("option",ue,s(t.$t("tools.jsonArraySlicer.indexRange")),1),e("option",me,s(t.$t("tools.jsonArraySlicer.conditionalFilter")),1),e("option",pe,s(t.$t("tools.jsonArraySlicer.randomSample")),1)],544),[[w,r.value.method]])]),r.value.method==="index"?(a(),n("div",ve,[e("div",ge,[e("div",ye,[e("label",he,s(t.$t("tools.jsonArraySlicer.startIndex"))+":",1),v(e("input",{"onUpdate:modelValue":o[2]||(o[2]=l=>r.value.startIndex=l),onInput:u,type:"number",min:"0",placeholder:t.$t("tools.jsonArraySlicer.startIndex"),class:"flex-1 px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,40,be),[[x,r.value.startIndex,void 0,{number:!0}]])]),e("div",fe,[e("label",xe,s(t.$t("tools.jsonArraySlicer.endIndex"))+":",1),v(e("input",{"onUpdate:modelValue":o[3]||(o[3]=l=>r.value.endIndex=l),onInput:u,type:"number",placeholder:t.$t("tools.jsonArraySlicer.endIndex"),class:"flex-1 px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,40,_e),[[x,r.value.endIndex,void 0,{number:!0}]])])])])):i("",!0),r.value.method==="condition"?(a(),n("div",Se,[e("div",Ae,[e("label",je,s(t.$t("tools.jsonArraySlicer.conditionField"))+":",1),v(e("input",{"onUpdate:modelValue":o[4]||(o[4]=l=>r.value.conditionField=l),onInput:u,type:"text",placeholder:t.$t("tools.jsonArraySlicer.conditionField"),class:"flex-1 px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,40,$e),[[x,r.value.conditionField]])]),e("div",we,[v(e("select",{"onUpdate:modelValue":o[5]||(o[5]=l=>r.value.operator=l),onChange:u,class:"px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[e("option",ke,s(t.$t("tools.jsonArraySlicer.operators.equals")),1),e("option",Ce,s(t.$t("tools.jsonArraySlicer.operators.notEquals")),1),e("option",Ie,s(t.$t("tools.jsonArraySlicer.operators.greater")),1),e("option",Ve,s(t.$t("tools.jsonArraySlicer.operators.less")),1),e("option",Ee,s(t.$t("tools.jsonArraySlicer.operators.contains")),1)],544),[[w,r.value.operator]]),v(e("input",{"onUpdate:modelValue":o[6]||(o[6]=l=>r.value.conditionValue=l),onInput:u,type:"text",placeholder:t.$t("tools.jsonArraySlicer.conditionValue"),class:"col-span-2 px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,40,Oe),[[x,r.value.conditionValue]])])])):i("",!0),r.value.method==="random"?(a(),n("div",Ue,[e("div",Ne,[e("label",Je,s(t.$t("tools.jsonArraySlicer.sampleCount"))+":",1),v(e("input",{"onUpdate:modelValue":o[7]||(o[7]=l=>r.value.sampleCount=l),onInput:u,type:"number",min:"1",placeholder:t.$t("tools.jsonArraySlicer.sampleCount"),class:"flex-1 px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,40,qe),[[x,r.value.sampleCount,void 0,{number:!0}]])]),e("label",Fe,[v(e("input",{"onUpdate:modelValue":o[8]||(o[8]=l=>r.value.preserveOrder=l),onChange:u,type:"checkbox",class:"mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500"},null,544),[[F,r.value.preserveOrder]]),k(" "+s(t.$t("tools.jsonArraySlicer.preserveOrder")),1)])])):i("",!0)])]),d.value?(a(),n("div",Be,[e("h5",Me,s(t.$t("tools.jsonArraySlicer.arrayInfo"))+": ",1),e("div",Re,[e("p",null,"• "+s(t.$t("tools.jsonArraySlicer.totalElements"))+": "+s(d.value.total),1),r.value.method==="index"&&d.value.willExtract!==void 0?(a(),n("p",Te,[k(" • "+s(t.$t("tools.jsonArraySlicer.willExtract"))+": "+s(d.value.willExtract)+" "+s(t.$t("common.items"))+" ",1),d.value.range?(a(),n("span",Le,"("+s(d.value.range)+")",1)):i("",!0)])):i("",!0),r.value.method==="condition"&&d.value.matching!==void 0?(a(),n("p",Pe," • "+s(t.$t("tools.jsonArraySlicer.matchingElements"))+": "+s(d.value.matching),1)):i("",!0),r.value.method==="random"?(a(),n("p",ze," • "+s(t.$t("tools.jsonArraySlicer.willSample"))+": "+s(Math.min(r.value.sampleCount||0,d.value.total))+" "+s(t.$t("common.items")),1)):i("",!0)])])):i("",!0),e("button",{onClick:E,disabled:!g.value.trim()||!f.value,class:"w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"},s(t.$t("tools.jsonArraySlicer.sliceButton")),9,De)]),e("div",He,[e("div",Ye,[e("h3",Ge,s(t.$t("tools.jsonArraySlicer.slicedArray")),1),e("div",Ke,[c.value?(a(),n("button",{key:0,onClick:O,class:"px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"},s(t.$t("common.copy")),1)):i("",!0),c.value?(a(),n("button",{key:1,onClick:U,class:"px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"},s(t.$t("common.download")),1)):i("",!0)])]),!c.value&&!p.value?(a(),n("div",Qe,[e("div",We,[o[12]||(o[12]=e("div",{class:"text-3xl mb-2"},"📊",-1)),e("p",null,s(t.$t("tools.jsonArraySlicer.noResults")),1)])])):i("",!0),p.value?(a(),n("div",Xe,[e("div",Ze,[o[13]||(o[13]=e("div",{class:"text-3xl mb-2"},"❌",-1)),e("p",et,s(t.$t("toast.error")),1),e("p",tt,s(p.value),1)])])):i("",!0),c.value&&!p.value?(a(),n("div",ot,[e("div",st,[e("div",rt,[o[14]||(o[14]=e("div",{class:"text-green-600 text-2xl mr-3"},"✅",-1)),e("div",null,[e("p",lt,s(t.$t("common.success")),1),e("p",nt,s(A.value),1)])])]),e("textarea",{value:c.value,readonly:"",class:"w-full h-80 p-4 border border-gray-300 rounded-lg font-mono text-sm bg-gray-50 resize-none"},null,8,at)])):i("",!0)])])])]))}});export{ut as default};
