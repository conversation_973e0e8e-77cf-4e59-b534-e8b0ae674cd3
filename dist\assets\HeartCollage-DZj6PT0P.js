import{d as Ie,u as Se,r as m,a4 as K,w as Te,a1 as Pe,a2 as ze,c as C,o as k,a as l,f as z,t as h,S as Q,R as A,e as U,v as O,g as re,j as Ue,a3 as Z,F as ie,k as he,W as De}from"./index-CkZTMFXG.js";import{u as Re}from"./useToast-virEbLJw.js";const qe={class:"min-h-screen bg-gray-50"},Ae={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},Fe={class:"text-center mb-12"},Le={class:"text-4xl font-bold text-gray-900 mb-4"},je={class:"text-xl text-gray-600 max-w-3xl mx-auto"},We={class:"bg-white rounded-lg shadow-md p-6 mb-8"},He={class:"space-y-4"},Xe={class:"text-lg font-medium text-gray-900 mb-2"},Ye={class:"text-gray-600"},Ee={class:"text-sm text-gray-500 mt-2"},Ve={class:"px-6 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors"},Be={class:"bg-white rounded-lg shadow-md p-6 mb-8"},Oe={class:"text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2"},Ge={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Ne={class:"block text-sm font-medium text-gray-700 mb-2"},Je={value:"small"},Ke={value:"medium",selected:""},Qe={value:"large"},Ze={class:"block text-sm font-medium text-gray-700 mb-2"},et={value:"heart"},tt={value:"square"},ot={value:"rectangle"},lt={value:"circle"},st={value:"star"},at={class:"block text-sm font-medium text-gray-700 mb-2"},nt={value:"square"},rt={value:"circle"},it={value:"rounded"},ht={class:"block text-sm font-medium text-gray-700 mb-2"},dt={value:"random"},ct={value:"grid"},ut={value:"fit"},gt={class:"mt-6 pt-6 border-t border-gray-200"},vt={class:"text-md font-medium text-gray-900 mb-4"},ft={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},pt={class:"block text-sm font-medium text-gray-700 mb-2"},mt={class:"block text-sm font-medium text-gray-700 mb-2"},wt={class:"flex items-center space-x-2"},bt={class:"text-sm text-gray-600"},yt={class:"block text-sm font-medium text-gray-700 mb-2"},xt={class:"flex items-center space-x-4"},Mt={class:"inline-flex items-center"},_t={class:"ml-2 text-sm text-gray-700"},Ct={key:0,class:"bg-white rounded-lg shadow-md p-6 mb-8"},kt={class:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4"},$t={class:"text-lg font-semibold text-gray-900"},It={class:"flex flex-wrap gap-3"},St=["disabled"],Tt={class:"flex flex-col items-center"},Pt=["onMousedown"],zt=["src","alt"],Ut=["onMousedown"],Dt=["onMousedown"],Rt=["onMousedown"],qt=["onMousedown"],At={key:0,class:"absolute inset-0 bg-center bg-no-repeat pointer-events-none",style:{backgroundImage:"url(/images/heart.png)",backgroundSize:"100%",backgroundPosition:"center",opacity:.9,zIndex:5}},Ft={class:"mt-4 text-sm text-gray-600"},Lt={key:1,class:"bg-white rounded-lg shadow-md p-6"},jt={class:"text-lg font-semibold text-gray-900 mb-4"},Wt={class:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4"},Ht={class:"aspect-square flex items-center justify-center bg-gray-100"},Xt=["src","alt"],Yt={class:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"},Et=["onClick"],Vt={class:"p-2 text-xs text-gray-600 truncate"},Bt=Ie({__name:"HeartCollage",setup(Ot){const{t:$}=Se(),{success:F,error:D}=Re(),G=m(null),de=m(null),p=K({x:0,y:0,width:0,height:0}),ce=m(0),ee=m(!1),c=m([]),N=m(null),b=m([]),y=m(null),L=K({x:0,y:0}),j=m(!1),W=m(null),H=m("medium"),R=m("heart"),T=m("square"),J=m("random"),I=m(2),X=m("#ffffff"),Y=m(!1),i=K({width:600,height:600});Te(H,()=>{te()});function te(){switch(H.value){case"small":i.width=600,i.height=600;break;case"medium":i.width=800,i.height=800;break;case"large":i.width=1e3,i.height=1e3;break}}function ue(){G.value&&G.value.click()}function ge(t){const o=t.target;o.files&&o.files.length>0&&(oe(Array.from(o.files)),o.value="")}function ve(t){if(t.preventDefault(),ee.value=!1,t.dataTransfer?.files){const o=Array.from(t.dataTransfer.files).filter(s=>s.type.startsWith("image/"));o.length>0?oe(o):D($("tools.heartCollage.errors.noImages"))}}async function oe(t){const o=t.filter(s=>s.type.startsWith("image/"));if(o.length===0){D($("tools.heartCollage.errors.noImages"));return}try{for(const s of o){const r=URL.createObjectURL(s),a=new Image;await new Promise((e,u)=>{a.onload=e,a.onerror=u,a.src=r}),c.value.push({file:s,name:s.name,previewUrl:r,width:a.width,height:a.height})}F($("tools.heartCollage.messages.filesAdded",{count:o.length}))}catch(s){D($("tools.heartCollage.errors.fileProcessing")),console.error(s)}}function fe(t){const o=c.value[t];o&&(URL.revokeObjectURL(o.previewUrl),c.value.splice(t,1))}function pe(){c.value.forEach(t=>{URL.revokeObjectURL(t.previewUrl)}),c.value=[],b.value=[],N.value=null,F($("tools.heartCollage.messages.cleared"))}function me(t,o,s,r,a,e){switch(t.beginPath(),o){case"heart":const u=Math.min(a,e)*.4,g=s+a/2,n=r+e/2;for(let x=0;x<=Math.PI*2;x+=.01){const ke=16*Math.pow(Math.sin(x),3),$e=-(13*Math.cos(x)-5*Math.cos(2*x)-2*Math.cos(3*x)-Math.cos(4*x)),ae=g+ke*u/16,ne=n+$e*u/16;x===0?t.moveTo(ae,ne):t.lineTo(ae,ne)}break;case"square":t.rect(s+a*.1,r+e*.1,a*.8,e*.8);break;case"rectangle":t.rect(s+a*.05,r+e*.2,a*.9,e*.6);break;case"circle":t.arc(s+a/2,r+e/2,Math.min(a,e)*.4,0,Math.PI*2);break;case"star":const d=s+a/2,f=r+e/2,M=Math.min(a,e)*.4,P=M*.5,_=5;let v=Math.PI/2*3,w=d,q=f,V=d,B=f;t.moveTo(d,f-M);for(let x=0;x<_;x++)w=d+Math.cos(v)*M,q=f+Math.sin(v)*M,t.lineTo(w,q),v+=Math.PI/_,V=d+Math.cos(v)*P,B=f+Math.sin(v)*P,t.lineTo(V,B),v+=Math.PI/_;t.lineTo(d,f-M);break}t.closePath()}async function we(){if(c.value.length===0){D($("tools.heartCollage.errors.noImagesSelected"));return}b.value=[];const t=[...c.value];for(let s=t.length-1;s>0;s--){const r=Math.floor(Math.random()*(s+1));[t[s],t[r]]=[t[r],t[s]]}const o=c.value;switch(c.value=t,J.value){case"random":await be();break;case"grid":ye();break;case"fit":await xe();break}c.value=o,F($("tools.heartCollage.messages.arranged"))}async function be(){if(R.value==="heart")try{const t=document.createElement("canvas");t.width=i.width,t.height=i.height;const o=t.getContext("2d");if(o){const s=new Image;await new Promise((v,w)=>{s.onload=()=>v(),s.onerror=()=>w(new Error("Failed to load heart image")),s.src="/images/heart.png"}),o.clearRect(0,0,t.width,t.height);const a=Math.min(t.width,t.height)*.8,e=(t.width-a)/2,u=(t.height-a)/2;o.drawImage(s,e,u,a,a);const n=o.getImageData(0,0,t.width,t.height).data,d=[],f=Math.max(1,Math.floor(Math.min(i.width,i.height)/50));for(let v=0;v<t.height;v+=f)for(let w=0;w<t.width;w+=f){const q=(v*t.width+w)*4;n[q+3]>0&&d.push({x:w,y:v})}for(let v=d.length-1;v>0;v--){const w=Math.floor(Math.random()*(v+1));[d[v],d[w]]=[d[w],d[v]]}const _=Math.min(Math.max(30,Math.floor(Math.sqrt(i.width*i.height/Math.max(1,c.value.length))*.8)),150);for(let v=0;v<Math.min(c.value.length,d.length);v++){const w=d[v],q=w.x-_/2,V=w.y-_/2,B=Math.max(0,Math.min(i.width-_,q)),x=Math.max(0,Math.min(i.height-_,V));b.value.push({src:c.value[v].previewUrl,name:c.value[v].name,x:B,y:x,width:_,height:_,originalWidth:c.value[v].width,originalHeight:c.value[v].height})}}}catch(t){console.error("Error in heart mask processing:",t),le()}else le()}function ye(){if(c.value.length===0)return;const t=Math.ceil(Math.sqrt(c.value.length)),o=(i.width-I.value*(t-1))/t,s=(i.height-I.value*(t-1))/t,r=Math.min(o,s)*.9;for(let a=0;a<c.value.length;a++){const e=Math.floor(a/t),g=a%t*(o+I.value)+(o-r)/2,n=e*(s+I.value)+(s-r)/2;b.value.push({src:c.value[a].previewUrl,name:c.value[a].name,x:g,y:n,width:r,height:r,originalWidth:c.value[a].width,originalHeight:c.value[a].height})}}async function xe(){if(c.value.length===0)return;const o=i.width*i.height/c.value.length*.8,s=[...c.value].sort((u,g)=>{const n=Math.abs(u.width/u.height-1),d=Math.abs(g.width/g.height-1);return n-d});let r=0,a=0,e=0;for(let u=0;u<s.length;u++){const g=s[u],n=g.width/g.height;let d,f;n>1?(d=Math.sqrt(o*n),f=o/d):(f=Math.sqrt(o/n),d=o/f);const M=Math.min(i.width,i.height)*.3;if(d>M||f>M){const P=M/Math.max(d,f);d*=P,f*=P}d=Math.max(30,d),f=Math.max(30,f),r+d>i.width&&(r=0,a+=e+I.value,e=0),e=Math.max(e,f),a+f<=i.height&&(b.value.push({src:g.previewUrl,name:g.name,x:r,y:a,width:d,height:f,originalWidth:g.width,originalHeight:g.height}),r+=d+I.value)}}function le(){const t=Math.ceil(Math.sqrt(c.value.length)),o=i.width/t,s=i.height/t,r=document.createElement("canvas");r.width=i.width,r.height=i.height;const a=r.getContext("2d");if(a){a.fillStyle="#ffffff",me(a,R.value,0,0,i.width,i.height),a.fill();const e=a.getImageData(0,0,i.width,i.height),u=[];for(let n=0;n<i.height;n+=10)for(let d=0;d<i.width;d+=10){const f=(n*i.width+d)*4;e.data[f]>0&&u.push({x:d,y:n})}for(let n=u.length-1;n>0;n--){const d=Math.floor(Math.random()*(n+1));[u[n],u[d]]=[u[d],u[n]]}const g=Math.min(o,s)*.8;for(let n=0;n<Math.min(c.value.length,u.length);n++){const d=u[n],f=d.x-g/2,M=d.y-g/2,P=Math.max(0,Math.min(i.width-g,f)),_=Math.max(0,Math.min(i.height-g,M));b.value.push({src:c.value[n].previewUrl,name:c.value[n].name,x:P,y:_,width:g,height:g,originalWidth:c.value[n].width,originalHeight:c.value[n].height})}}}let S=null;function Me(t,o){if(t.preventDefault(),t.stopPropagation(),j.value)return;y.value=o,ce.value=Date.now();const s=b.value[o];p.x=t.clientX,p.y=t.clientY,L.x=t.clientX-s.x,L.y=t.clientY-s.y,document.body.style.userSelect="none",document.body.style.cursor="grabbing"}function E(t,o,s){t.preventDefault(),t.stopPropagation(),j.value=!0,W.value=s,y.value=o;const r=b.value[o];p.x=t.clientX,p.y=t.clientY,p.width=r.width,p.height=r.height,document.body.style.cursor=`${s}-resize`,document.body.style.userSelect="none"}function _e(t){y.value!==null&&(S&&cancelAnimationFrame(S),S=requestAnimationFrame(()=>{const o=b.value[y.value];if(j.value&&W.value){const s=t.clientX-p.x,r=t.clientY-p.y;let a=p.width,e=p.height,u=o.x,g=o.y;switch(W.value){case"se":a=Math.max(30,p.width+s),e=Math.max(30,p.height+r);break;case"sw":a=Math.max(30,p.width-s),e=Math.max(30,p.height+r),u=o.x+(p.width-a);break;case"ne":a=Math.max(30,p.width+s),e=Math.max(30,p.height-r),g=o.y+(p.height-e);break;case"nw":a=Math.max(30,p.width-s),e=Math.max(30,p.height-r),u=o.x+(p.width-a),g=o.y+(p.height-e);break}const n=o.originalWidth/o.originalHeight;t.shiftKey&&(a/e>n?e=a/n:a=e*n),a=Math.min(a,i.width-u),e=Math.min(e,i.height-g),u=Math.max(0,Math.min(u,i.width-a)),g=Math.max(0,Math.min(g,i.height-e)),o.x=u,o.y=g,o.width=a,o.height=e}else{let s=t.clientX-L.x,r=t.clientY-L.y;s=Math.max(0,Math.min(i.width-o.width,s)),r=Math.max(0,Math.min(i.height-o.height,r)),o.x=s,o.y=r}S=null}))}function se(){S&&(cancelAnimationFrame(S),S=null),y.value!==null&&(document.body.style.userSelect="",document.body.style.cursor="",j.value=!1,W.value=null,setTimeout(()=>{y.value=null},50))}async function Ce(){if(b.value.length===0){D($("tools.heartCollage.errors.noImagesSelected"));return}try{const t=document.createElement("canvas");t.width=i.width,t.height=i.height;const o=t.getContext("2d");if(o){o.fillStyle=X.value,o.fillRect(0,0,t.width,t.height);const s=b.value.map(e=>new Promise((u,g)=>{const n=new Image;n.onload=()=>u(n),n.onerror=g,n.src=e.src})),r=await Promise.all(s);b.value.forEach((e,u)=>{const g=r[u];if(o.save(),T.value==="circle")o.beginPath(),o.arc(e.x+e.width/2,e.y+e.height/2,e.width/2,0,Math.PI*2),o.clip();else if(T.value==="rounded"){const n=e.width*.1;o.beginPath(),o.moveTo(e.x+n,e.y),o.lineTo(e.x+e.width-n,e.y),o.quadraticCurveTo(e.x+e.width,e.y,e.x+e.width,e.y+n),o.lineTo(e.x+e.width,e.y+e.height-n),o.quadraticCurveTo(e.x+e.width,e.y+e.height,e.x+e.width-n,e.y+e.height),o.lineTo(e.x+n,e.y+e.height),o.quadraticCurveTo(e.x,e.y+e.height,e.x,e.y+e.height-n),o.lineTo(e.x,e.y+n),o.quadraticCurveTo(e.x,e.y,e.x+n,e.y),o.closePath(),o.clip()}if(o.drawImage(g,e.x,e.y,e.width,e.height),Y.value)if(o.restore(),o.strokeStyle="#ffffff",o.lineWidth=2,T.value==="circle")o.beginPath(),o.arc(e.x+e.width/2,e.y+e.height/2,e.width/2-1,0,Math.PI*2),o.stroke();else if(T.value==="rounded"){const n=e.width*.1;o.beginPath(),o.moveTo(e.x+n,e.y),o.lineTo(e.x+e.width-n,e.y),o.quadraticCurveTo(e.x+e.width,e.y,e.x+e.width,e.y+n),o.lineTo(e.x+e.width,e.y+e.height-n),o.quadraticCurveTo(e.x+e.width,e.y+e.height,e.x+e.width-n,e.y+e.height),o.lineTo(e.x+n,e.y+e.height),o.quadraticCurveTo(e.x,e.y+e.height,e.x,e.y+e.height-n),o.lineTo(e.x,e.y+n),o.quadraticCurveTo(e.x,e.y,e.x+n,e.y),o.closePath(),o.stroke()}else o.strokeRect(e.x+1,e.y+1,e.width-2,e.height-2);else o.restore()}),N.value=t.toDataURL("image/png");const a=document.createElement("a");a.download=`collage-${R.value}-${new Date().getTime()}.png`,a.href=N.value,document.body.appendChild(a),a.click(),document.body.removeChild(a),F($("tools.heartCollage.messages.downloadSuccess"))}}catch(t){D($("tools.heartCollage.errors.downloadFailed")),console.error(t)}}return Pe(()=>{te()}),ze(()=>{c.value.forEach(t=>{URL.revokeObjectURL(t.previewUrl)}),S&&cancelAnimationFrame(S),document.body.style.userSelect="",document.body.style.cursor=""}),(t,o)=>(k(),C("div",qe,[l("div",Ae,[l("div",Fe,[l("h1",Le,h(t.$t("tools.heartCollage.title")),1),l("p",je,h(t.$t("tools.heartCollage.description")),1)]),l("div",We,[l("div",{onDrop:ve,onDragover:o[0]||(o[0]=A(()=>{},["prevent"])),onDragenter:o[1]||(o[1]=A(()=>{},["prevent"])),onClick:ue,class:Q(["border-2 border-dashed rounded-lg p-12 text-center cursor-pointer transition-colors",ee.value?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-gray-400"])},[l("input",{ref_key:"fileInput",ref:G,type:"file",multiple:"",accept:"image/*",onChange:ge,class:"hidden"},null,544),l("div",He,[o[9]||(o[9]=l("div",{class:"text-6xl text-gray-400"},"❤️",-1)),l("div",null,[l("h3",Xe,h(t.$t("tools.heartCollage.uploadTitle")),1),l("p",Ye,h(t.$t("tools.heartCollage.uploadDescription")),1),l("p",Ee,h(t.$t("tools.heartCollage.supportedFormats"))+": JPG, PNG, WebP, GIF ",1)]),l("button",Ve,h(t.$t("tools.heartCollage.selectFiles")),1)])],34)]),l("div",Be,[l("h3",Oe,h(t.$t("tools.heartCollage.settings")),1),l("div",Ge,[l("div",null,[l("label",Ne,h(t.$t("tools.heartCollage.canvasSize")),1),U(l("select",{"onUpdate:modelValue":o[2]||(o[2]=s=>H.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"},[l("option",Je,h(t.$t("tools.heartCollage.small"))+" (600×600)",1),l("option",Ke,h(t.$t("tools.heartCollage.medium"))+" (800×800) ",1),l("option",Qe,h(t.$t("tools.heartCollage.large"))+" (1000×1000)",1)],512),[[O,H.value]])]),l("div",null,[l("label",Ze,h(t.$t("tools.heartCollage.shape")),1),U(l("select",{"onUpdate:modelValue":o[3]||(o[3]=s=>R.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"},[l("option",et,h(t.$t("tools.heartCollage.heart")),1),l("option",tt,h(t.$t("tools.heartCollage.square")),1),l("option",ot,h(t.$t("tools.heartCollage.rectangle")),1),l("option",lt,h(t.$t("tools.heartCollage.circle")),1),l("option",st,h(t.$t("tools.heartCollage.star")),1)],512),[[O,R.value]])]),l("div",null,[l("label",at,h(t.$t("tools.heartCollage.imageShape")),1),U(l("select",{"onUpdate:modelValue":o[4]||(o[4]=s=>T.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"},[l("option",nt,h(t.$t("tools.heartCollage.square")),1),l("option",rt,h(t.$t("tools.heartCollage.circle")),1),l("option",it,h(t.$t("tools.heartCollage.rounded")),1)],512),[[O,T.value]])]),l("div",null,[l("label",ht,h(t.$t("tools.heartCollage.arrangement")),1),U(l("select",{"onUpdate:modelValue":o[5]||(o[5]=s=>J.value=s),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"},[l("option",dt,h(t.$t("tools.heartCollage.random")),1),l("option",ct,h(t.$t("tools.heartCollage.grid")),1),l("option",ut,h(t.$t("tools.heartCollage.fitAll")),1)],512),[[O,J.value]])])]),l("div",gt,[l("h4",vt,h(t.$t("tools.heartCollage.additionalOptions")),1),l("div",ft,[l("div",null,[l("label",pt,h(t.$t("tools.heartCollage.spacing"))+": "+h(I.value)+"px ",1),U(l("input",{type:"range",min:"0",max:"20","onUpdate:modelValue":o[6]||(o[6]=s=>I.value=s),class:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"},null,512),[[re,I.value]])]),l("div",null,[l("label",mt,h(t.$t("tools.heartCollage.backgroundColor")),1),l("div",wt,[U(l("input",{type:"color","onUpdate:modelValue":o[7]||(o[7]=s=>X.value=s),class:"w-10 h-10 border border-gray-300 rounded cursor-pointer"},null,512),[[re,X.value]]),l("span",bt,h(X.value),1)])]),l("div",null,[l("label",yt,h(t.$t("tools.heartCollage.borderOptions")),1),l("div",xt,[l("label",Mt,[U(l("input",{type:"checkbox","onUpdate:modelValue":o[8]||(o[8]=s=>Y.value=s),class:"rounded border-gray-300 text-pink-600 shadow-sm focus:border-pink-300 focus:ring focus:ring-pink-200 focus:ring-opacity-50"},null,512),[[Ue,Y.value]]),l("span",_t,h(t.$t("tools.heartCollage.showBorder")),1)])])])])])]),c.value.length>0?(k(),C("div",Ct,[l("div",kt,[l("h3",$t,h(t.$t("tools.heartCollage.canvas"))+" ("+h(c.value.length)+" "+h(t.$t("tools.heartCollage.images"))+") ",1),l("div",It,[l("button",{onClick:we,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"},h(t.$t("tools.heartCollage.autoArrange")),1),l("button",{onClick:Ce,disabled:b.value.length===0,class:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"},h(t.$t("tools.heartCollage.downloadCollage")),9,St),l("button",{onClick:pe,class:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"},h(t.$t("common.clear")),1)])]),l("div",Tt,[l("div",{ref_key:"canvasContainer",ref:de,class:"relative border border-gray-300 rounded-lg overflow-hidden bg-gray-100 select-none",style:Z({width:`${i.width}px`,height:`${i.height}px`}),onMouseup:se,onMouseleave:se,onMousemove:_e},[(k(!0),C(ie,null,he(b.value,(s,r)=>(k(),C("div",{key:r,class:Q(["absolute cursor-move transition-all duration-75 ease-out",y.value===r?"z-10":"z-0"]),style:Z({left:`${s.x}px`,top:`${s.y}px`,width:`${s.width}px`,height:`${s.height}px`,transition:y.value===r?"none":"all 0.1s ease-out"}),onMousedown:a=>Me(a,r)},[l("div",{class:Q(["w-full h-full overflow-hidden relative",T.value==="circle"?"rounded-full":T.value==="rounded"?"rounded-xl":"rounded-none"]),style:Z({border:Y.value?"2px solid white":"none",boxShadow:y.value===r?"0 0 0 2px #3b82f6, 0 4px 12px rgba(0, 0, 0, 0.15)":"none",transform:y.value===r?"scale(1.03)":"scale(1)",transition:"transform 0.1s ease-out, box-shadow 0.1s ease-out"})},[l("img",{src:s.src,alt:s.name,class:"w-full h-full object-cover"},null,8,zt),y.value===r?(k(),C("div",{key:0,class:"absolute w-3 h-3 bg-blue-500 rounded-full cursor-se-resize",style:{right:"-6px",bottom:"-6px"},onMousedown:A(a=>E(a,r,"se"),["stop"])},null,40,Ut)):z("",!0),y.value===r?(k(),C("div",{key:1,class:"absolute w-3 h-3 bg-blue-500 rounded-full cursor-sw-resize",style:{left:"-6px",bottom:"-6px"},onMousedown:A(a=>E(a,r,"sw"),["stop"])},null,40,Dt)):z("",!0),y.value===r?(k(),C("div",{key:2,class:"absolute w-3 h-3 bg-blue-500 rounded-full cursor-nw-resize",style:{left:"-6px",top:"-6px"},onMousedown:A(a=>E(a,r,"nw"),["stop"])},null,40,Rt)):z("",!0),y.value===r?(k(),C("div",{key:3,class:"absolute w-3 h-3 bg-blue-500 rounded-full cursor-ne-resize",style:{right:"-6px",top:"-6px"},onMousedown:A(a=>E(a,r,"ne"),["stop"])},null,40,qt)):z("",!0)],6)],46,Pt))),128)),R.value==="heart"?(k(),C("div",At)):z("",!0)],36),l("div",Ft,h(t.$t("tools.heartCollage.dragInstructions")),1)])])):z("",!0),c.value.length>0?(k(),C("div",Lt,[l("h3",jt,h(t.$t("tools.heartCollage.selectedImages")),1),l("div",Wt,[(k(!0),C(ie,null,he(c.value,(s,r)=>(k(),C("div",{key:r,class:"relative group border border-gray-200 rounded-lg overflow-hidden"},[l("div",Ht,[l("img",{src:s.previewUrl,alt:s.name,class:"object-cover w-full h-full"},null,8,Xt)]),l("div",Yt,[l("button",{onClick:a=>fe(r),class:"p-2 bg-red-600 text-white rounded-full hover:bg-red-700"},[...o[10]||(o[10]=[l("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor"},[l("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)])],8,Et)]),l("div",Vt,h(s.name),1)]))),128))])])):z("",!0)])]))}}),Jt=De(Bt,[["__scopeId","data-v-f0eeaf9d"]]);export{Jt as default};
