import{d as V,u as j,r as m,i as N,c as i,a as e,U as S,f as u,t as o,e as T,g as W,F as x,k as B,Y as D,o as n,W as F}from"./index-CkZTMFXG.js";import{u as R}from"./useToast-virEbLJw.js";const q={class:"min-h-screen bg-dark-950 text-slate-100 py-8"},z={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},A={class:"glass rounded-xl p-6 mb-8 border border-slate-700/50 shadow-dark-lg"},H={class:"text-3xl font-bold text-slate-100 mb-4"},M={class:"text-slate-400 text-lg"},Y={class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},G={class:"text-lg font-semibold text-slate-100 mb-4 border-b border-slate-700/30 pb-2"},J={class:"text-sm text-slate-400 mb-4"},K=["placeholder"],O={key:0,class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},Q={class:"flex flex-wrap items-center gap-4"},X={class:"text-sm text-slate-400"},Z={key:1,class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},tt={class:"text-lg font-semibold text-slate-100 mb-6 border-b border-slate-700/30 pb-2"},et={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"},st={class:"aspect-square relative bg-slate-800/50"},ot=["src","alt","onClick"],lt={key:0,class:"absolute inset-0 bg-slate-700/80 animate-pulse flex items-center justify-center rounded-xl"},rt={class:"text-slate-300 text-sm"},at={key:1,class:"absolute inset-0 bg-red-900/80 flex items-center justify-center p-4 rounded-xl"},it={class:"text-red-200 text-sm text-center"},nt={class:"p-3"},dt=["title"],ct=["title"],ut={key:2,class:"bg-yellow-900/30 border border-yellow-700/50 rounded-xl p-6 text-center"},mt={class:"text-yellow-200"},pt={key:3,class:"bg-slate-800/30 border-2 border-dashed border-slate-600/50 rounded-xl p-12 text-center"},ht={class:"text-lg font-medium text-slate-100 mb-2"},gt={class:"text-slate-400 mb-4"},bt={class:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-12"},ft={class:"glass p-6 rounded-xl border border-slate-700/50"},xt={class:"text-lg font-semibold text-slate-100 mb-3"},vt={class:"text-slate-400 text-sm"},_t={class:"glass p-6 rounded-xl border border-slate-700/50"},yt={class:"text-lg font-semibold text-slate-100 mb-3"},Lt={class:"text-slate-400 text-sm"},$t={class:"glass p-6 rounded-xl border border-slate-700/50"},Pt={class:"text-lg font-semibold text-slate-100 mb-3"},kt={class:"text-slate-400 text-sm"},wt=V({__name:"ImageListProcessor",setup(It){const{t:Ct}=j(),{copySuccess:v,copyError:_}=R(),d=m(""),l=m([]),p=m(!1),g=m(0),y=N(()=>l.value.map(t=>({src:t.url,title:t.filename||h(t.url)})));function L(t){g.value=t,p.value=!0}function $(){p.value=!1}function b(){if(!d.value.trim()){l.value=[];return}const t=d.value.split(`
`).map(s=>s.trim()).filter(s=>s&&!s.startsWith("#")),r=[];for(const s of t)P(s)&&r.push({url:s,filename:h(s),loading:!0,error:!1});const c=r.filter((s,a,f)=>a===f.findIndex(U=>U.url===s.url));l.value=c}function P(t){return!(!t||(t=t.trim(),!t)||!t.startsWith("http://")&&!t.startsWith("https://"))}function h(t){try{return new URL(t).pathname.split("/").pop()||""}catch{return t.split("/").pop()||t}}function k(t){const c=t.target.src,s=l.value.findIndex(a=>a.url===c);s!==-1&&(l.value[s].error=!0,l.value[s].loading=!1)}function w(t){const c=t.target.src,s=l.value.findIndex(a=>a.url===c);s!==-1&&(l.value[s].loading=!1,l.value[s].error=!1)}function I(){d.value="",l.value=[]}function C(){if(l.value.length===0)return;const t=l.value.map(r=>r.url).join(`
`);navigator.clipboard.writeText(t).then(()=>{v()}).catch(()=>{_()})}function E(){d.value=`https://picsum.photos/400/300?random=1
https://picsum.photos/400/300?random=2
https://picsum.photos/400/300?random=3
https://picsum.photos/400/300?random=4
https://picsum.photos/400/300?random=5
https://picsum.photos/400/300?random=6
https://picsum.photos/400/300?random=7
https://picsum.photos/400/300?random=8`,b()}return(t,r)=>{const c=D("vue-easy-lightbox");return n(),i(x,null,[e("div",q,[e("div",z,[e("div",A,[e("h1",H," 🖼️ "+o(t.$t("tools.imageListProcessor.title")),1),e("p",M,o(t.$t("tools.imageListProcessor.description")),1)]),e("div",Y,[e("h3",G,o(t.$t("tools.imageListProcessor.inputTitle")),1),e("p",J,o(t.$t("tools.imageListProcessor.inputNote")),1),T(e("textarea",{"onUpdate:modelValue":r[0]||(r[0]=s=>d.value=s),onInput:b,class:"w-full h-64 p-4 bg-slate-800/30 border border-slate-600/50 rounded-xl resize-vertical focus:ring-2 focus:ring-primary-500 focus:border-primary-500 font-mono text-sm text-slate-100 placeholder-slate-500 transition-all duration-200",placeholder:t.$t("tools.imageListProcessor.inputPlaceholder")},null,40,K),[[W,d.value]])]),l.value.length>0?(n(),i("div",O,[e("div",Q,[e("button",{onClick:I,class:"px-6 py-2 bg-slate-700 text-slate-100 rounded-xl hover:bg-slate-600 transition-all duration-200 cursor-pointer hover-lift"}," 🗑️ "+o(t.$t("common.clear")),1),e("button",{onClick:C,class:"px-6 py-2 bg-primary-600 text-white rounded-xl hover:bg-primary-500 transition-all duration-200 cursor-pointer hover-lift"}," 📋 "+o(t.$t("common.copy"))+" URLs ",1),e("div",X,o(t.$t("common.total"))+": "+o(l.value.length)+" "+o(t.$t("common.items")),1)])])):u("",!0),l.value.length>0?(n(),i("div",Z,[e("h3",tt,o(t.$t("tools.imageListProcessor.imagePreview")),1),e("div",et,[(n(!0),i(x,null,B(l.value,(s,a)=>(n(),i("div",{key:a,class:"bg-slate-800/30 rounded-xl overflow-hidden border border-slate-700/50 hover:border-slate-600/70 transition-all duration-200 hover-lift group"},[e("div",st,[e("img",{referrerpolicy:"no-referrer",src:s.url,alt:s.alt||`Image ${a+1}`,onError:k,onLoad:w,class:"w-full h-full object-contain cursor-pointer group-hover:scale-105 transition-transform duration-200 p-2",onClick:f=>L(a)},null,40,ot),s.loading?(n(),i("div",lt,[e("div",rt,o(t.$t("common.loading")),1)])):u("",!0),s.error?(n(),i("div",at,[e("div",it," ❌ "+o(t.$t("tools.imageListProcessor.imageError")),1)])):u("",!0)]),e("div",nt,[e("div",{class:"text-xs text-slate-300 truncate",title:s.url},o(s.filename||h(s.url)),9,dt),e("div",{class:"text-xs text-slate-500 mt-1 truncate",title:s.url},o(s.url),9,ct)])]))),128))])])):u("",!0),l.value.length===0&&d.value.trim()?(n(),i("div",ut,[r[1]||(r[1]=e("div",{class:"text-yellow-400 text-lg mb-2"},"⚠️",-1)),e("p",mt,o(t.$t("tools.imageListProcessor.noResults")),1)])):u("",!0),d.value.trim()?u("",!0):(n(),i("div",pt,[r[2]||(r[2]=e("div",{class:"text-slate-500 text-6xl mb-4"},"🖼️",-1)),e("h3",ht,o(t.$t("tools.imageListProcessor.emptyState.title")),1),e("p",gt,o(t.$t("tools.imageListProcessor.emptyState.description")),1),e("button",{onClick:E,class:"px-6 py-2 bg-primary-600 text-white rounded-xl hover:bg-primary-500 transition-all duration-200 cursor-pointer hover-lift"},o(t.$t("common.loadExample")),1)])),e("div",bt,[e("div",ft,[e("h3",xt," 🔗 "+o(t.$t("tools.imageListProcessor.features.simple.title")),1),e("p",vt,o(t.$t("tools.imageListProcessor.features.simple.description")),1)]),e("div",_t,[e("h3",yt," 🖼️ "+o(t.$t("tools.imageListProcessor.features.gallery.title")),1),e("p",Lt,o(t.$t("tools.imageListProcessor.features.gallery.description")),1)]),e("div",$t,[e("h3",Pt," ⚡ "+o(t.$t("tools.imageListProcessor.features.fast.title")),1),e("p",kt,o(t.$t("tools.imageListProcessor.features.fast.description")),1)])])])]),S(c,{visible:p.value,imgs:y.value,index:g.value,onHide:$,"move-disabled":!1,"scroll-disabled":!1,drag:!0,wheel:!0,pinch:!0,dblclick:!0},null,8,["visible","imgs","index"])],64)}}}),Vt=F(wt,[["__scopeId","data-v-11cab9d3"]]);export{Vt as default};
