import{d as le,u as re,a1 as ie,a2 as ne,r as v,a4 as M,c as x,a as e,f as k,t as o,S as ae,R as P,e as c,g as h,v as q,$ as de,F as ue,k as ve,a3 as ce,o as y,W as me}from"./index-CkZTMFXG.js";import{u as pe}from"./useToast-virEbLJw.js";import{G as fe}from"./gif-Dup4naTh.js";import"./_commonjsHelpers-DsqdWQfm.js";import"./_commonjs-dynamic-modules-TDtrdbi3.js";const be={class:"min-h-screen bg-dark-950 text-slate-100 py-8"},he={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ge={class:"glass rounded-xl p-6 mb-8 border border-slate-700/50 shadow-dark-lg"},xe={class:"text-3xl font-bold text-slate-100 mb-4"},ye={class:"text-slate-400 text-lg"},_e={class:"mt-4 p-4 bg-primary-900/30 rounded-xl border border-primary-700/50"},we={class:"font-semibold text-primary-300 mb-2"},Te={class:"list-decimal list-inside space-y-1 text-primary-200"},Ce={class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},Ge={class:"text-lg font-semibold text-slate-100 mb-4 border-b border-slate-700/30 pb-2"},$e={class:"mb-6"},ke={class:"text-slate-400 mb-4"},Ue={class:"px-6 py-2 bg-primary-600 text-white rounded-xl hover:bg-primary-500 transition-all duration-200 cursor-pointer hover-lift"},Ve={class:"text-xs text-slate-500 mt-2"},Oe={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"},Fe={class:"block text-sm font-medium text-slate-300 mb-2"},Se={class:"block text-sm font-medium text-slate-300 mb-2"},Ee={value:"high"},Le={value:"medium"},Re={value:"low"},ze={class:"block text-sm font-medium text-slate-300 mb-2"},Ie={key:0,class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},De={class:"text-lg font-semibold text-slate-100 mb-4 border-b border-slate-700/30 pb-2"},Me={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Pe={key:0,class:"flex items-center justify-center w-full h-64 bg-slate-800/50 rounded-xl"},qe={class:"text-center"},je={class:"mt-2 text-slate-400"},Ae=["src"],Be={class:"mt-4 space-y-4"},We={class:"flex gap-2"},Ne=["disabled"],He=["disabled"],Ye=["disabled"],Je={class:"space-y-2"},Ke={class:"block text-sm font-medium text-slate-300"},Qe={class:"flex gap-4 items-center"},Xe={class:"flex items-center gap-2"},Ze={class:"text-sm text-slate-400"},et=["max"],tt={class:"flex items-center gap-2"},ot={class:"text-sm text-slate-400"},st=["min","max"],lt={class:"flex justify-between items-center mb-4"},rt={class:"text-md font-semibold text-slate-100"},it={class:"space-y-4 max-h-64 overflow-y-auto"},nt={class:"flex justify-between items-center"},at={class:"text-sm font-medium text-slate-300"},dt=["onClick"],ut=["onUpdate:modelValue","placeholder"],vt={class:"grid grid-cols-2 gap-2"},ct={class:"block text-xs text-slate-400 mb-1"},mt=["onUpdate:modelValue","max"],pt={class:"block text-xs text-slate-400 mb-1"},ft=["onUpdate:modelValue","min","max"],bt={class:"grid grid-cols-3 gap-2"},ht={class:"block text-xs text-slate-400 mb-1"},gt=["onUpdate:modelValue"],xt={class:"block text-xs text-slate-400 mb-1"},yt=["onUpdate:modelValue"],_t={class:"block text-xs text-slate-400 mb-1"},wt=["onUpdate:modelValue"],Tt={value:"top"},Ct={value:"center"},Gt={value:"bottom"},$t={key:1,class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},kt={class:"text-center"},Ut={class:"text-lg font-semibold text-slate-100 mb-2"},Vt={class:"text-slate-400"},Ot={class:"mt-4 bg-slate-700 rounded-full h-2"},Ft={class:"text-sm text-slate-500 mt-2"},St={key:0,class:"mt-6"},Et={class:"text-md font-medium text-slate-300 mb-2"},Lt=["src"],Rt={key:2,class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},zt={class:"text-lg font-semibold text-slate-100 mb-4 border-b border-slate-700/30 pb-2"},It={class:"text-center"},Dt=["src"],Mt={class:"flex justify-center gap-4"},Pt={class:"bg-warning-900/30 border-l-4 border-warning-500 p-4 mb-8 rounded-r-xl"},qt={class:"flex"},jt={class:"ml-3"},At={class:"text-sm font-medium text-warning-300"},Bt={class:"mt-2 text-sm text-warning-200"},Wt={class:"list-disc list-inside space-y-1"},Nt={class:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-12"},Ht={class:"glass p-6 rounded-xl border border-slate-700/50"},Yt={class:"text-lg font-semibold text-slate-100 mb-3"},Jt={class:"text-slate-400 text-sm"},Kt={class:"glass p-6 rounded-xl border border-slate-700/50"},Qt={class:"text-lg font-semibold text-slate-100 mb-3"},Xt={class:"text-slate-400 text-sm"},Zt={class:"glass p-6 rounded-xl border border-slate-700/50"},eo={class:"text-lg font-semibold text-slate-100 mb-3"},to={class:"text-slate-400 text-sm"},oo=le({__name:"VideoToGifConverter",setup(so){const{t:g}=re(),{success:S,error:j}=pe();ie(()=>{document.addEventListener("paste",R)}),ne(()=>{document.removeEventListener("paste",R)});async function R(t){const s=t.clipboardData?.items;if(s)for(let l=0;l<s.length;l++){const u=s[l];if(u.type.indexOf("video")!==-1){const a=u.getAsFile();if(a){const O=new File([a],`pasted-video-${Date.now()}.mp4`,{type:a.type});L(O),S(g("tools.videoToGifConverter.messages.filePasted"));break}}}}const U=v(null),_=v(""),r=v(),V=v(),E=v(!1),C=v(!1),w=v(!1),T=v(0),p=v(""),f=v(0),G=v(0),z=v([]),m=M({width:300,quality:"medium",fps:15}),i=M({start:0,end:0}),$=v([]);function A(t){t.preventDefault(),E.value=!1;const s=t.dataTransfer?.files;s&&s.length>0&&L(s[0])}function B(t){const s=t.target,l=s.files;l&&l.length>0&&L(l[0]),s&&(s.value="")}function L(t){if(!t.type.startsWith("video/")){alert(g("tools.videoToGifConverter.errors.invalidFile"));return}if(t.size>100*1024*1024){alert(g("tools.videoToGifConverter.errors.fileTooLarge"));return}U.value=t,_.value=URL.createObjectURL(t),I(),S(g("tools.videoToGifConverter.messages.fileLoaded"))}function W(){r.value&&(f.value=r.value.duration,i.start=0,i.end=Math.min(f.value,15))}function N(){r.value&&(G.value=r.value.currentTime)}function H(){i.start=G.value}function Y(){i.end=G.value}function J(){$.value.push({content:"",startTime:G.value,endTime:Math.min(G.value+2,f.value),fontSize:24,color:"#ffffff",position:"bottom"})}function K(t){$.value.splice(t,1)}function Q(){C.value=!0,z.value=[],r.value&&(r.value.currentTime=i.start,r.value.play())}function X(){C.value=!1,r.value&&r.value.pause()}function I(){C.value=!1,z.value=[],p.value="",T.value=0}async function Z(){if(!r.value||!U.value){alert(g("tools.videoToGifConverter.errors.noVideoSelected"));return}if(i.start>=i.end){alert(g("tools.videoToGifConverter.errors.invalidTimeRange"));return}w.value=!0,T.value=0;try{await ee(),S(g("tools.videoToGifConverter.messages.gifGenerated"))}catch(t){console.error("Error generating GIF:",t),j(g("tools.videoToGifConverter.errors.processingFailed")+": "+t.message)}finally{w.value=!1}}async function ee(){if(!r.value)throw new Error("Video player not initialized");if(r.value.readyState<2)throw new Error("Video not loaded properly. Please wait for the video to load completely.");const t=document.createElement("canvas"),s=t.getContext("2d");if(!s)throw new Error("Unable to get canvas context. Your browser may not support this feature.");if(t.width=m.width,t.height=r.value.videoHeight/r.value.videoWidth*m.width,t.width<=0||t.height<=0)throw new Error("Invalid video dimensions. Please check your video file.");const l={high:1,medium:10,low:20},u=new fe({workers:2,quality:l[m.quality],width:t.width,height:t.height,workerScript:"/gif.worker.js"}),a=Math.min(i.end-i.start,15),O=1/m.fps,se=Math.floor(a*m.fps);let D=0;for(let b=i.start;b<i.end&&b<i.start+15;b+=O)try{r.value.currentTime=b,await new Promise(n=>{const d=()=>{r.value?.removeEventListener("seeked",d),n(void 0)};r.value?.addEventListener("seeked",d),setTimeout(n,500)}),s.drawImage(r.value,0,0,t.width,t.height),$.value.forEach(n=>{if(b>=n.startTime&&b<=n.endTime&&n.content){s.save(),s.fillStyle=n.color,s.font=`bold ${n.fontSize}px Arial`,s.textAlign="center",s.strokeStyle="rgba(0,0,0,0.8)",s.lineWidth=2;let d=t.height/2;n.position==="top"&&(d=n.fontSize+20),n.position==="bottom"&&(d=t.height-20);const F=t.width/2;s.strokeText(n.content,F,d),s.fillText(n.content,F,d),s.restore()}}),u.addFrame(t,{copy:!0,delay:O*1e3}),D++,T.value=Math.round(D/se*80)}catch(n){console.error("Error processing frame at time:",b,n)}return new Promise((b,n)=>{u.on("progress",d=>{T.value=80+Math.round(d*20)}),u.on("finished",d=>{const F=URL.createObjectURL(d);p.value=F,T.value=100,b()}),u.on("abort",()=>{n(new Error("GIF generation was aborted"))}),u.on("error",d=>{n(new Error("GIF generation error: "+d.message))});try{u.render()}catch(d){n(new Error("Failed to start GIF rendering: "+d.message))}})}function te(){if(!p.value)return;const t=document.createElement("a");t.href=p.value,t.download=`video-to-gif-${Date.now()}.gif`,document.body.appendChild(t),t.click(),document.body.removeChild(t)}function oe(){U.value=null,_.value="",I(),$.value=[],i.start=0,i.end=0,f.value=0,_.value&&URL.revokeObjectURL(_.value),p.value&&URL.revokeObjectURL(p.value),V.value&&(V.value.value="")}return(t,s)=>(y(),x("div",be,[e("div",he,[e("div",ge,[e("h1",xe," 🎬 "+o(t.$t("tools.videoToGifConverter.title")),1),e("p",ye,o(t.$t("tools.videoToGifConverter.description")),1),e("div",_e,[e("h3",we,o(t.$t("tools.videoToGifConverter.howToUse.title")),1),e("ol",Te,[e("li",null,o(t.$t("tools.videoToGifConverter.howToUse.step1")),1),e("li",null,o(t.$t("tools.videoToGifConverter.howToUse.step2")),1),e("li",null,o(t.$t("tools.videoToGifConverter.howToUse.step3")),1),e("li",null,o(t.$t("tools.videoToGifConverter.howToUse.step4")),1)])])]),e("div",Ce,[e("h3",Ge,o(t.$t("tools.videoToGifConverter.upload.title")),1),e("div",$e,[e("div",{onDrop:A,onDragover:s[0]||(s[0]=P(()=>{},["prevent"])),onDragenter:s[1]||(s[1]=P(()=>{},["prevent"])),class:ae(["border-2 border-dashed rounded-xl p-8 text-center hover:border-primary-500/50 transition-all duration-200 cursor-pointer",{"border-primary-500 bg-primary-500/10":E.value,"border-slate-600/50":!E.value}]),onClick:s[2]||(s[2]=l=>V.value.click())},[s[8]||(s[8]=e("div",{class:"text-slate-500 text-4xl mb-4"},"🎥",-1)),e("p",ke,o(t.$t("tools.videoToGifConverter.upload.dragDrop")),1),e("input",{type:"file",ref_key:"fileInput",ref:V,onChange:B,accept:"video/*",class:"hidden"},null,544),e("button",Ue,o(t.$t("tools.videoToGifConverter.upload.selectFile")),1),e("p",Ve,o(t.$t("tools.videoToGifConverter.upload.supportedFormats")),1)],34)]),e("div",Oe,[e("div",null,[e("label",Fe,o(t.$t("tools.videoToGifConverter.settings.width")),1),c(e("input",{"onUpdate:modelValue":s[3]||(s[3]=l=>m.width=l),type:"number",min:"100",max:"800",class:"w-full px-3 py-2 bg-slate-800/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-slate-100 transition-all duration-200"},null,512),[[h,m.width,void 0,{number:!0}]])]),e("div",null,[e("label",Se,o(t.$t("tools.videoToGifConverter.settings.quality")),1),c(e("select",{"onUpdate:modelValue":s[4]||(s[4]=l=>m.quality=l),class:"w-full px-3 py-2 bg-slate-800/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-slate-100 transition-all duration-200"},[e("option",Ee,o(t.$t("tools.videoToGifConverter.settings.qualityOptions.high")),1),e("option",Le,o(t.$t("tools.videoToGifConverter.settings.qualityOptions.medium")),1),e("option",Re,o(t.$t("tools.videoToGifConverter.settings.qualityOptions.low")),1)],512),[[q,m.quality]])]),e("div",null,[e("label",ze,o(t.$t("tools.videoToGifConverter.settings.fps")),1),c(e("input",{"onUpdate:modelValue":s[5]||(s[5]=l=>m.fps=l),type:"number",min:"5",max:"30",class:"w-full px-3 py-2 bg-slate-800/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-slate-100 transition-all duration-200"},null,512),[[h,m.fps,void 0,{number:!0}]])])])]),U.value?(y(),x("div",Ie,[e("h3",De,o(t.$t("tools.videoToGifConverter.preview.title")),1),e("div",Me,[e("div",null,[_.value&&!f.value?(y(),x("div",Pe,[e("div",qe,[s[9]||(s[9]=e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"},null,-1)),e("p",je,o(t.$t("tools.videoToGifConverter.loadingVideo")),1)])])):k("",!0),c(e("video",{ref_key:"videoPlayer",ref:r,src:_.value,controls:"",onLoadedmetadata:W,onTimeupdate:N,class:"w-full rounded-xl bg-slate-900"},null,40,Ae),[[de,_.value&&f.value]]),e("div",Be,[e("div",We,[e("button",{onClick:Q,disabled:C.value||w.value,class:"px-4 py-2 bg-success-600 text-white rounded-xl hover:bg-success-500 transition-all duration-200 cursor-pointer hover-lift disabled:opacity-50 disabled:cursor-not-allowed"},o(t.$t("tools.videoToGifConverter.actions.startCapture")),9,Ne),e("button",{onClick:X,disabled:!C.value||w.value,class:"px-4 py-2 bg-error-600 text-white rounded-xl hover:bg-error-500 transition-all duration-200 cursor-pointer hover-lift disabled:opacity-50 disabled:cursor-not-allowed"},o(t.$t("tools.videoToGifConverter.actions.stopCapture")),9,He),e("button",{onClick:Z,disabled:w.value,class:"px-4 py-2 bg-primary-600 text-white rounded-xl hover:bg-primary-500 transition-all duration-200 cursor-pointer hover-lift disabled:opacity-50 disabled:cursor-not-allowed"},o(w.value?t.$t("common.loading"):t.$t("tools.videoToGifConverter.actions.generateGif")),9,Ye)]),e("div",Je,[e("label",Ke,o(t.$t("tools.videoToGifConverter.timeRange.title")),1),e("div",Qe,[e("div",Xe,[e("label",Ze,o(t.$t("tools.videoToGifConverter.timeRange.start"))+":",1),c(e("input",{"onUpdate:modelValue":s[6]||(s[6]=l=>i.start=l),type:"number",min:"0",max:f.value,step:"0.1",class:"w-20 px-2 py-1 bg-slate-800/50 border border-slate-600/50 rounded text-sm text-slate-100"},null,8,et),[[h,i.start,void 0,{number:!0}]]),s[10]||(s[10]=e("span",{class:"text-sm text-slate-500"},"s",-1))]),e("div",tt,[e("label",ot,o(t.$t("tools.videoToGifConverter.timeRange.end"))+":",1),c(e("input",{"onUpdate:modelValue":s[7]||(s[7]=l=>i.end=l),type:"number",min:i.start,max:f.value,step:"0.1",class:"w-20 px-2 py-1 bg-slate-800/50 border border-slate-600/50 rounded text-sm text-slate-100"},null,8,st),[[h,i.end,void 0,{number:!0}]]),s[11]||(s[11]=e("span",{class:"text-sm text-slate-500"},"s",-1))]),e("button",{onClick:H,class:"px-3 py-1 bg-primary-600 text-white rounded text-sm hover:bg-primary-500 transition-all duration-200 cursor-pointer"},o(t.$t("tools.videoToGifConverter.timeRange.setStart")),1),e("button",{onClick:Y,class:"px-3 py-1 bg-primary-600 text-white rounded text-sm hover:bg-primary-500 transition-all duration-200 cursor-pointer"},o(t.$t("tools.videoToGifConverter.timeRange.setEnd")),1)])])])]),e("div",null,[e("div",lt,[e("h4",rt,o(t.$t("tools.videoToGifConverter.textOverlay.title")),1),e("button",{onClick:J,class:"px-3 py-1 bg-success-600 text-white rounded text-sm hover:bg-success-500 transition-all duration-200 cursor-pointer"},o(t.$t("tools.videoToGifConverter.textOverlay.add")),1)]),e("div",it,[(y(!0),x(ue,null,ve($.value,(l,u)=>(y(),x("div",{key:u,class:"border border-slate-700/50 rounded-xl p-4 space-y-3"},[e("div",nt,[e("span",at,o(t.$t("tools.videoToGifConverter.textOverlay.text"))+" "+o(u+1),1),e("button",{onClick:a=>K(u),class:"text-error-500 hover:text-error-400 text-sm transition-colors duration-200 cursor-pointer"},o(t.$t("common.remove")),9,dt)]),c(e("input",{"onUpdate:modelValue":a=>l.content=a,type:"text",placeholder:t.$t("tools.videoToGifConverter.textOverlay.placeholder"),class:"w-full px-3 py-2 bg-slate-800/50 border border-slate-600/50 rounded text-sm text-slate-100 placeholder-slate-500 transition-all duration-200"},null,8,ut),[[h,l.content]]),e("div",vt,[e("div",null,[e("label",ct,o(t.$t("tools.videoToGifConverter.textOverlay.startTime")),1),c(e("input",{"onUpdate:modelValue":a=>l.startTime=a,type:"number",min:"0",max:f.value,step:"0.1",class:"w-full px-2 py-1 bg-slate-800/50 border border-slate-600/50 rounded text-xs text-slate-100"},null,8,mt),[[h,l.startTime,void 0,{number:!0}]])]),e("div",null,[e("label",pt,o(t.$t("tools.videoToGifConverter.textOverlay.endTime")),1),c(e("input",{"onUpdate:modelValue":a=>l.endTime=a,type:"number",min:l.startTime,max:f.value,step:"0.1",class:"w-full px-2 py-1 bg-slate-800/50 border border-slate-600/50 rounded text-xs text-slate-100"},null,8,ft),[[h,l.endTime,void 0,{number:!0}]])])]),e("div",bt,[e("div",null,[e("label",ht,o(t.$t("tools.videoToGifConverter.textOverlay.fontSize")),1),c(e("input",{"onUpdate:modelValue":a=>l.fontSize=a,type:"number",min:"12",max:"48",class:"w-full px-2 py-1 bg-slate-800/50 border border-slate-600/50 rounded text-xs text-slate-100"},null,8,gt),[[h,l.fontSize,void 0,{number:!0}]])]),e("div",null,[e("label",xt,o(t.$t("tools.videoToGifConverter.textOverlay.color")),1),c(e("input",{"onUpdate:modelValue":a=>l.color=a,type:"color",class:"w-full h-8 border border-slate-600/50 rounded cursor-pointer"},null,8,yt),[[h,l.color]])]),e("div",null,[e("label",_t,o(t.$t("tools.videoToGifConverter.textOverlay.position")),1),c(e("select",{"onUpdate:modelValue":a=>l.position=a,class:"w-full px-2 py-1 bg-slate-800/50 border border-slate-600/50 rounded text-xs text-slate-100"},[e("option",Tt,o(t.$t("tools.videoToGifConverter.textOverlay.positions.top")),1),e("option",Ct,o(t.$t("tools.videoToGifConverter.textOverlay.positions.center")),1),e("option",Gt,o(t.$t("tools.videoToGifConverter.textOverlay.positions.bottom")),1)],8,wt),[[q,l.position]])])])]))),128))])])])])):k("",!0),w.value?(y(),x("div",$t,[e("div",kt,[s[12]||(s[12]=e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"},null,-1)),e("h3",Ut,o(t.$t("tools.videoToGifConverter.processing.title")),1),e("p",Vt,o(t.$t("tools.videoToGifConverter.processing.description")),1),e("div",Ot,[e("div",{class:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:ce({width:T.value+"%"})},null,4)]),e("p",Ft,o(T.value)+"%",1),p.value?(y(),x("div",St,[e("h4",Et,o(t.$t("tools.videoToGifConverter.processing.preview")),1),e("img",{src:p.value,alt:"GIF Preview",class:"max-w-full h-auto mx-auto rounded-xl shadow-dark-lg",style:{"max-height":"200px"}},null,8,Lt)])):k("",!0)])])):k("",!0),p.value?(y(),x("div",Rt,[e("h3",zt,o(t.$t("tools.videoToGifConverter.result.title")),1),e("div",It,[e("img",{src:p.value,alt:"Generated GIF",class:"max-w-full h-auto mx-auto rounded-xl shadow-dark-lg mb-4"},null,8,Dt),e("div",Mt,[e("button",{onClick:te,class:"px-6 py-2 bg-success-600 text-white rounded-xl hover:bg-success-500 transition-all duration-200 cursor-pointer hover-lift"},o(t.$t("tools.videoToGifConverter.result.download")),1),e("button",{onClick:oe,class:"px-6 py-2 bg-slate-700 text-slate-100 rounded-xl hover:bg-slate-600 transition-all duration-200 cursor-pointer hover-lift"},o(t.$t("tools.videoToGifConverter.result.createNew")),1)])])])):k("",!0),e("div",Pt,[e("div",qt,[s[13]||(s[13]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-warning-500",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])],-1)),e("div",jt,[e("h3",At,o(t.$t("tools.videoToGifConverter.tips.title")),1),e("div",Bt,[e("ul",Wt,[e("li",null,o(t.$t("tools.videoToGifConverter.tips.tip1")),1),e("li",null,o(t.$t("tools.videoToGifConverter.tips.tip2")),1),e("li",null,o(t.$t("tools.videoToGifConverter.tips.tip3")),1),e("li",null,o(t.$t("tools.videoToGifConverter.tips.tip4")),1)])])])])]),e("div",Nt,[e("div",Ht,[e("h3",Yt," 🎬 "+o(t.$t("tools.videoToGifConverter.features.conversion.title")),1),e("p",Jt,o(t.$t("tools.videoToGifConverter.features.conversion.description")),1)]),e("div",Kt,[e("h3",Qt," 📝 "+o(t.$t("tools.videoToGifConverter.features.textOverlay.title")),1),e("p",Xt,o(t.$t("tools.videoToGifConverter.features.textOverlay.description")),1)]),e("div",Zt,[e("h3",eo," ⚡ "+o(t.$t("tools.videoToGifConverter.features.customization.title")),1),e("p",to,o(t.$t("tools.videoToGifConverter.features.customization.description")),1)])])])]))}}),uo=me(oo,[["__scopeId","data-v-d934bd32"]]);export{uo as default};
