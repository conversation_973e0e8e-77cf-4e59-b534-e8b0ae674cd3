import{d as a,c as n,a as e,t as r,b as d,o as m}from"./index-CkZTMFXG.js";const c={class:"min-h-screen bg-gray-50"},x={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},b={class:"text-center mb-12"},p={class:"text-4xl font-bold text-gray-900 mb-4"},v=a({__name:"ComingSoon",props:{toolName:{}},emits:["goBack"],setup(u,{emit:o}){const s=o;function l(){s("goBack")}return(i,t)=>(m(),n("div",c,[e("div",x,[e("div",b,[t[0]||(t[0]=e("div",{class:"text-6xl mb-4"},"🚧",-1)),e("h1",p,r(i.toolName),1),t[1]||(t[1]=e("p",{class:"text-xl text-gray-600 max-w-3xl mx-auto"}," This tool is coming soon! We're working hard to bring you more amazing development utilities. ",-1))]),e("div",{class:"max-w-2xl mx-auto"},[e("div",{class:"bg-white rounded-lg shadow-md p-8 text-center"},[t[2]||(t[2]=d('<div class="text-blue-600 text-4xl mb-4">⏳</div><h2 class="text-2xl font-semibold text-gray-900 mb-4">Under Development</h2><p class="text-gray-600 mb-6"> We&#39;re actively developing this tool to provide you with the best possible experience. Check back soon for updates! </p><div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6"><h3 class="text-sm font-semibold text-blue-800 mb-2">What to expect:</h3><ul class="text-sm text-blue-700 text-left space-y-1"><li>• Intuitive and user-friendly interface</li><li>• Powerful functionality tailored for developers</li><li>• Fast performance and reliable results</li><li>• Modern design with Tailwind CSS</li></ul></div>',4)),e("button",{onClick:l,class:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"}," ← Back to Tools ")])])])]))}});export{v as default};
