import{d as K,u as Q,a1 as X,a2 as Z,r as v,a4 as ee,c as h,a as e,f as F,t as o,S as te,R as q,e as k,g as D,v as oe,F as se,k as ie,a3 as le,o as p,W as re}from"./index-CkZTMFXG.js";import{u as ae}from"./useToast-virEbLJw.js";import{G as de}from"./gif-Dup4naTh.js";import{l as P}from"./index-CNw5tbJV.js";import"./_commonjsHelpers-DsqdWQfm.js";import"./_commonjs-dynamic-modules-TDtrdbi3.js";const ne={class:"min-h-screen bg-gray-50 py-8"},ue={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ge={class:"bg-white rounded-lg shadow-md p-6 mb-8"},ce={class:"text-3xl font-bold text-gray-900 mb-4"},fe={class:"text-gray-600 text-lg"},me={class:"mt-4 p-4 bg-blue-50 rounded-lg"},he={class:"font-semibold text-blue-800 mb-2"},pe={class:"list-decimal list-inside space-y-1 text-blue-700"},ve={class:"bg-white rounded-lg shadow-md p-6 mb-8"},be={class:"text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2"},ye={class:"mb-6"},we={class:"text-gray-600 mb-4"},xe={class:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"},_e={class:"text-xs text-gray-500 mt-2"},Ee={class:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"},$e={class:"block text-sm font-medium text-gray-700 mb-2"},Fe={class:"text-xs text-gray-500 mt-1"},ke={class:"block text-sm font-medium text-gray-700 mb-2"},Ue={value:"high"},Ge={value:"medium"},Ie={value:"low"},Ce={class:"block text-sm font-medium text-gray-700 mb-2"},De={key:0,class:"bg-white rounded-lg shadow-md p-6 mb-8"},Le={class:"text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2"},Re={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},qe={class:"text-md font-semibold text-gray-800 mb-3"},Pe={class:"flex items-center justify-center w-full h-64 bg-gray-100 rounded-lg"},Me=["src","alt"],ze={key:1,class:"text-center"},Te={class:"mt-2 text-gray-600"},Ve={class:"mt-2 text-sm text-gray-500"},je={class:"text-md font-semibold text-gray-800 mb-3"},Oe={class:"space-y-3 max-h-96 overflow-y-auto"},Se={class:"flex-shrink-0 w-16 h-16 bg-gray-100 rounded-md overflow-hidden"},Be=["src","alt"],Ne={class:"ml-3 flex-grow min-w-0"},Ae={class:"text-sm font-medium text-gray-900"},He={class:"text-xs text-gray-500"},We={class:"text-xs text-gray-500"},Ye={class:"flex items-center"},Je=["onUpdate:modelValue","onInput"],Ke=["onClick"],Qe={class:"mt-4 flex flex-wrap gap-2"},Xe=["disabled"],Ze=["disabled"],et=["disabled"],tt=["disabled"],ot={class:"mt-6 flex flex-wrap gap-3"},st=["disabled"],it={key:1,class:"bg-white rounded-lg shadow-md p-6 mb-8"},lt={class:"text-center"},rt={class:"text-lg font-semibold text-gray-900 mb-2"},at={class:"text-gray-600"},dt={class:"mt-4 bg-gray-200 rounded-full h-2"},nt={class:"text-sm text-gray-500 mt-2"},ut={key:0,class:"mt-6"},gt={class:"text-md font-medium text-gray-800 mb-2"},ct=["src"],ft={key:2,class:"bg-white rounded-lg shadow-md p-6 mb-8"},mt={class:"text-lg font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2"},ht={class:"text-center"},pt=["src"],vt={class:"flex justify-center gap-4"},bt={class:"bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-8"},yt={class:"flex"},wt={class:"ml-3"},xt={class:"text-sm font-medium text-yellow-800"},_t={class:"mt-2 text-sm text-yellow-700"},Et={class:"list-disc list-inside space-y-1"},$t={class:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-12"},Ft={class:"bg-white p-6 rounded-lg shadow-md border-l-4 border-blue-500"},kt={class:"text-lg font-semibold text-gray-900 mb-3"},Ut={class:"text-gray-600 text-sm"},Gt={class:"bg-white p-6 rounded-lg shadow-md border-l-4 border-green-500"},It={class:"text-lg font-semibold text-gray-900 mb-3"},Ct={class:"text-gray-600 text-sm"},Dt={class:"bg-white p-6 rounded-lg shadow-md border-l-4 border-purple-500"},Lt={class:"text-lg font-semibold text-gray-900 mb-3"},Rt={class:"text-gray-600 text-sm"},qt=K({__name:"GifEditor",setup(Pt){const{t:n}=Q(),{success:U,error:m}=ae();X(()=>{document.addEventListener("paste",L)}),Z(()=>{document.removeEventListener("paste",L),a.value?.url&&URL.revokeObjectURL(a.value.url),u.value&&URL.revokeObjectURL(u.value)});async function L(t){const s=t.clipboardData?.items;if(s)for(let i=0;i<s.length;i++){const d=s[i];if(d.type==="image/gif"){const r=d.getAsFile();if(r){const g=new File([r],`pasted-gif-${Date.now()}.gif`,{type:r.type});await G(g),U(n("tools.gifEditor.messages.filePasted"));break}}}}const E=v(),R=v(!1),_=v(!1),b=v(0),u=v(""),a=v(null),l=v([]),f=ee({width:300,quality:"medium",fps:10});function M(t){t.preventDefault(),R.value=!1;const s=t.dataTransfer?.files;if(s&&s.length>0){const i=Array.from(s).filter(d=>d.type==="image/gif");i.length>0?G(i[0]):m(n("tools.gifEditor.errors.noGif"))}}function z(t){const s=t.target,i=s.files;if(i&&i.length>0){const d=Array.from(i).filter(r=>r.type==="image/gif");d.length>0?G(d[0]):m(n("tools.gifEditor.errors.noGif"))}s&&(s.value="")}async function G(t){if(t.type!=="image/gif"){m(n("tools.gifEditor.errors.invalidFile"));return}if(t.size>50*1024*1024){m(n("tools.gifEditor.errors.fileTooLarge"));return}try{I();const s=URL.createObjectURL(t);a.value={file:t,url:s,name:t.name,width:0,height:0},await T(t),U(n("tools.gifEditor.messages.fileLoaded"))}catch(s){console.error("Error handling GIF file:",s),m(n("tools.gifEditor.errors.fileProcessing"))}}async function T(t){try{const s=await t.arrayBuffer(),i=P.parseGIF(s);i&&i.lsd&&a.value&&(a.value.width=i.lsd.width,a.value.height=i.lsd.height);const d=P.decompressFrames(i,!0);l.value=[];for(const r of d){if(r.dims.width<=0||r.dims.height<=0){console.warn("Skipping frame with invalid dimensions");continue}const g=document.createElement("canvas");g.width=a.value?.width||r.dims.width,g.height=a.value?.height||r.dims.height;const $=g.getContext("2d",{willReadFrequently:!0});if($){const y=new ImageData(new Uint8ClampedArray(r.patch),r.dims.width,r.dims.height);$.putImageData(y,r.dims.left||0,r.dims.top||0);const w=g.toDataURL("image/png");l.value.push({dataUrl:w,delay:r.delay,imageData:y,left:r.dims.left||0,top:r.dims.top||0,width:r.dims.width,height:r.dims.height,dispose:r.disposalType||0})}}a.value&&(!a.value.width||!a.value.height)&&l.value.length>0&&(a.value.width=l.value[0].imageData.width,a.value.height=l.value[0].imageData.height)}catch(s){console.error("Error parsing GIF frames:",s),m(n("tools.gifEditor.errors.frameParsingFailed"))}}function V(t){l.value.splice(t,1)}function I(){a.value?.url&&URL.revokeObjectURL(a.value.url),u.value&&URL.revokeObjectURL(u.value),a.value=null,l.value=[],u.value="",b.value=0}function j(t,s){const i=s.target,d=parseInt(i.value);!isNaN(d)&&d>=20&&(l.value[t].delay=d)}function O(){if(l.value.length<=1)return;const t=l.value[0];l.value.shift(),l.value.push(t)}function S(){if(l.value.length<=1)return;const t=l.value.pop();t&&l.value.unshift(t)}function B(){l.value.length<=1||l.value.reverse()}function N(){if(!(l.value.length<=1))for(let t=l.value.length-1;t>0;t--){const s=Math.floor(Math.random()*(t+1));[l.value[t],l.value[s]]=[l.value[s],l.value[t]]}}async function A(){if(!a.value||l.value.length===0){m(n("tools.gifEditor.errors.noFrames"));return}if(!l.value.some(t=>t.dataUrl&&t.delay>0)){m(n("tools.gifEditor.errors.noFrames"));return}_.value=!0,b.value=0;try{await H(),U(n("tools.gifEditor.messages.gifGenerated"))}catch(t){console.error("Error generating GIF:",t),m(n("tools.gifEditor.errors.processingFailed")+": "+t.message)}finally{_.value=!1}}async function H(){if(!a.value)return;if(l.value.length===0)throw new Error("No frames to process. Please try another GIF file.");const t=a.value.width,s=a.value.height;if(!t||!s||t<=0||s<=0)throw new Error("Invalid GIF dimensions. Please try another GIF file.");const i=document.createElement("canvas"),d=i.getContext("2d",{willReadFrequently:!0});if(!d)throw new Error("Unable to get canvas context. Your browser may not support this feature.");i.width=t,i.height=s;const r={high:1,medium:10,low:20},g=new de({workers:2,quality:r[f.quality],width:t,height:s,workerScript:"/gif.worker.js",transparent:0}),$=l.value.length;let y=0;for(const w of l.value)try{const c=new Image;if(await new Promise((C,J)=>{c.onload=()=>C(),c.onerror=()=>J(new Error("Failed to load frame image")),c.src=w.dataUrl}),c.width<=0||c.height<=0){console.warn("Skipping frame with invalid dimensions");continue}d.drawImage(c,0,0),g.addFrame(i,{copy:!0,delay:w.delay}),y++,b.value=Math.round(y/$*100)}catch(c){console.error("Error processing frame:",c)}if(y===0)throw new Error("No frames could be processed. Please try another GIF file.");return new Promise((w,c)=>{g.on("finished",x=>{const C=URL.createObjectURL(x);u.value=C,b.value=100,w()}),g.on("abort",()=>{c(new Error("GIF generation was aborted"))}),g.on("error",x=>{c(new Error("GIF generation error: "+x.message))});try{g.render()}catch(x){c(new Error("Failed to start GIF rendering: "+x.message))}})}function W(){if(!u.value)return;const t=document.createElement("a");t.href=u.value,t.download=`edited-gif-${Date.now()}.gif`,document.body.appendChild(t),t.click(),document.body.removeChild(t)}function Y(){I(),f.width=300,f.quality="medium",f.fps=10,E.value&&(E.value.value="")}return(t,s)=>(p(),h("div",ne,[e("div",ue,[e("div",ge,[e("h1",ce,"🎞️ "+o(t.$t("tools.gifEditor.title")),1),e("p",fe,o(t.$t("tools.gifEditor.description")),1),e("div",me,[e("h3",he,o(t.$t("tools.gifEditor.howToUse.title")),1),e("ol",pe,[e("li",null,o(t.$t("tools.gifEditor.howToUse.step1")),1),e("li",null,o(t.$t("tools.gifEditor.howToUse.step2")),1),e("li",null,o(t.$t("tools.gifEditor.howToUse.step3")),1),e("li",null,o(t.$t("tools.gifEditor.howToUse.step4")),1)])])]),e("div",ve,[e("h3",be,o(t.$t("tools.gifEditor.upload.title")),1),e("div",ye,[e("div",{onDrop:M,onDragover:s[0]||(s[0]=q(()=>{},["prevent"])),onDragenter:s[1]||(s[1]=q(()=>{},["prevent"])),class:te(["border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer",{"border-blue-400 bg-blue-50":R.value}]),onClick:s[2]||(s[2]=i=>E.value.click())},[s[6]||(s[6]=e("div",{class:"text-gray-400 text-4xl mb-4"},"🎞️",-1)),e("p",we,o(t.$t("tools.gifEditor.upload.dragDrop")),1),e("input",{type:"file",ref_key:"fileInput",ref:E,onChange:z,accept:"image/gif",class:"hidden"},null,544),e("button",xe,o(t.$t("tools.gifEditor.upload.selectFile")),1),e("p",_e,o(t.$t("tools.gifEditor.upload.supportedFormats")),1)],34)]),e("div",Ee,[e("div",null,[e("label",$e,o(t.$t("tools.gifEditor.settings.width")),1),k(e("input",{"onUpdate:modelValue":s[3]||(s[3]=i=>f.width=i),type:"number",min:"100",max:"800",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500",disabled:""},null,512),[[D,f.width,void 0,{number:!0}]]),e("p",Fe,o(t.$t("tools.gifEditor.settings.preserveOriginal")),1)]),e("div",null,[e("label",ke,o(t.$t("tools.gifEditor.settings.quality")),1),k(e("select",{"onUpdate:modelValue":s[4]||(s[4]=i=>f.quality=i),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},[e("option",Ue,o(t.$t("tools.gifEditor.settings.qualityOptions.high")),1),e("option",Ge,o(t.$t("tools.gifEditor.settings.qualityOptions.medium")),1),e("option",Ie,o(t.$t("tools.gifEditor.settings.qualityOptions.low")),1)],512),[[oe,f.quality]])]),e("div",null,[e("label",Ce,o(t.$t("tools.gifEditor.settings.fps")),1),k(e("input",{"onUpdate:modelValue":s[5]||(s[5]=i=>f.fps=i),type:"number",min:"1",max:"30",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},null,512),[[D,f.fps,void 0,{number:!0}]])])])]),a.value?(p(),h("div",De,[e("h3",Le,o(t.$t("tools.gifEditor.preview.title")),1),e("div",Re,[e("div",null,[e("h4",qe,o(t.$t("tools.gifEditor.preview.originalGif")),1),e("div",Pe,[a.value.url?(p(),h("img",{key:0,src:a.value.url,alt:a.value.name,class:"max-h-full max-w-full object-contain"},null,8,Me)):(p(),h("div",ze,[s[7]||(s[7]=e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"},null,-1)),e("p",Te,o(t.$t("tools.gifEditor.loadingGif")),1)]))]),e("div",Ve,o(t.$t("tools.gifEditor.preview.dimensions"))+": "+o(a.value.width)+" × "+o(a.value.height)+" "+o(t.$t("tools.gifEditor.preview.pixels")),1)]),e("div",null,[e("h4",je,o(t.$t("tools.gifEditor.preview.frames"))+" ("+o(l.value.length)+") ",1),e("div",Oe,[(p(!0),h(se,null,ie(l.value,(i,d)=>(p(),h("div",{key:d,class:"flex items-center p-3 border border-gray-200 rounded-lg"},[e("div",Se,[e("img",{src:i.dataUrl,alt:`Frame ${d+1}`,class:"w-full h-full object-cover"},null,8,Be)]),e("div",Ne,[e("p",Ae,o(t.$t("tools.gifEditor.preview.frame"))+" "+o(d+1),1),e("p",He,o(t.$t("tools.gifEditor.preview.delay"))+": "+o(i.delay)+"ms ",1),e("p",We," Position: ("+o(i.left)+", "+o(i.top)+") "+o(i.width)+"×"+o(i.height),1)]),e("div",Ye,[k(e("input",{"onUpdate:modelValue":r=>i.delay=r,type:"number",min:"20",max:"5000",step:"10",class:"w-16 px-2 py-1 border border-gray-300 rounded text-xs",onInput:r=>j(d,r)},null,40,Je),[[D,i.delay,void 0,{number:!0}]]),s[9]||(s[9]=e("span",{class:"ml-1 text-xs text-gray-500"},"ms",-1)),e("button",{onClick:r=>V(d),class:"ml-2 text-red-500 hover:text-red-700"},[...s[8]||(s[8]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z","clip-rule":"evenodd"})],-1)])],8,Ke)])]))),128))]),e("div",Qe,[e("button",{onClick:O,disabled:l.value.length<=1,class:"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 disabled:opacity-50"},o(t.$t("tools.gifEditor.preview.moveUp")),9,Xe),e("button",{onClick:S,disabled:l.value.length<=1,class:"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 disabled:opacity-50"},o(t.$t("tools.gifEditor.preview.moveDown")),9,Ze),e("button",{onClick:B,disabled:l.value.length<=1,class:"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 disabled:opacity-50"},o(t.$t("tools.gifEditor.preview.reverse")),9,et),e("button",{onClick:N,disabled:l.value.length<=1,class:"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 disabled:opacity-50"},o(t.$t("tools.gifEditor.preview.shuffle")),9,tt)])])]),e("div",ot,[e("button",{onClick:A,disabled:_.value||l.value.length===0,class:"px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"},o(_.value?t.$t("common.loading"):t.$t("tools.gifEditor.actions.generateGif")),9,st),e("button",{onClick:I,class:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"},o(t.$t("common.clear")),1)])])):F("",!0),_.value?(p(),h("div",it,[e("div",lt,[s[10]||(s[10]=e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"},null,-1)),e("h3",rt,o(t.$t("tools.gifEditor.processing.title")),1),e("p",at,o(t.$t("tools.gifEditor.processing.description")),1),e("div",dt,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:le({width:b.value+"%"})},null,4)]),e("p",nt,o(b.value)+"%",1),u.value?(p(),h("div",ut,[e("h4",gt,o(t.$t("tools.gifEditor.processing.preview")),1),e("img",{src:u.value,alt:"GIF Preview",class:"max-w-full h-auto mx-auto rounded-lg shadow-lg",style:{"max-height":"200px"}},null,8,ct)])):F("",!0)])])):F("",!0),u.value?(p(),h("div",ft,[e("h3",mt,o(t.$t("tools.gifEditor.result.title")),1),e("div",ht,[e("img",{src:u.value,alt:"Generated GIF",class:"max-w-full h-auto mx-auto rounded-lg shadow-lg mb-4"},null,8,pt),e("div",vt,[e("button",{onClick:W,class:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"},o(t.$t("tools.gifEditor.result.download")),1),e("button",{onClick:Y,class:"px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"},o(t.$t("tools.gifEditor.result.createNew")),1)])])])):F("",!0),e("div",bt,[e("div",yt,[s[11]||(s[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-yellow-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])],-1)),e("div",wt,[e("h3",xt,o(t.$t("tools.gifEditor.tips.title")),1),e("div",_t,[e("ul",Et,[e("li",null,o(t.$t("tools.gifEditor.tips.tip1")),1),e("li",null,o(t.$t("tools.gifEditor.tips.tip2")),1),e("li",null,o(t.$t("tools.gifEditor.tips.tip3")),1),e("li",null,o(t.$t("tools.gifEditor.tips.tip4")),1)])])])])]),e("div",$t,[e("div",Ft,[e("h3",kt," 🎞️ "+o(t.$t("tools.gifEditor.features.frameEditing.title")),1),e("p",Ut,o(t.$t("tools.gifEditor.features.frameEditing.description")),1)]),e("div",Gt,[e("h3",It," ⚙️ "+o(t.$t("tools.gifEditor.features.customization.title")),1),e("p",Ct,o(t.$t("tools.gifEditor.features.customization.description")),1)]),e("div",Dt,[e("h3",Lt," 🔄 "+o(t.$t("tools.gifEditor.features.animation.title")),1),e("p",Rt,o(t.$t("tools.gifEditor.features.animation.description")),1)])])])]))}}),St=re(qt,[["__scopeId","data-v-aa9675f0"]]);export{St as default};
