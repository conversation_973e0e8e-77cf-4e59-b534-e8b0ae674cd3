var S={},M={},w={},F;function L(){if(F)return w;F=1,Object.defineProperty(w,"__esModule",{value:!0}),w.loop=w.conditional=w.parse=void 0;var B=function c(g,l){var s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},v=arguments.length>3&&arguments[3]!==void 0?arguments[3]:s;if(Array.isArray(l))l.forEach(function(p){return c(g,p,s,v)});else if(typeof l=="function")l(g,s,v,c);else{var d=Object.keys(l)[0];Array.isArray(l[d])?(v[d]={},c(g,l[d],s,v[d])):v[d]=l[d](g,s,v,c)}return s};w.parse=B;var x=function(g,l){return function(s,v,d,p){l(s,v,d)&&p(s,g,v,d)}};w.conditional=x;var t=function(g,l){return function(s,v,d,p){for(var o=[],e=s.pos;l(s,v,d);){var r={};if(p(s,g,v,r),s.pos===e)break;e=s.pos,o.push(r)}return o}};return w.loop=t,w}var y={},R;function O(){if(R)return y;R=1,Object.defineProperty(y,"__esModule",{value:!0}),y.readBits=y.readArray=y.readUnsigned=y.readString=y.peekBytes=y.readBytes=y.peekByte=y.readByte=y.buildStream=void 0;var B=function(e){return{data:e,pos:0}};y.buildStream=B;var x=function(){return function(e){return e.data[e.pos++]}};y.readByte=x;var t=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return function(r){return r.data[r.pos+e]}};y.peekByte=t;var c=function(e){return function(r){return r.data.subarray(r.pos,r.pos+=e)}};y.readBytes=c;var g=function(e){return function(r){return r.data.subarray(r.pos,r.pos+e)}};y.peekBytes=g;var l=function(e){return function(r){return Array.from(c(e)(r)).map(function(i){return String.fromCharCode(i)}).join("")}};y.readString=l;var s=function(e){return function(r){var i=c(2)(r);return e?(i[1]<<8)+i[0]:(i[0]<<8)+i[1]}};y.readUnsigned=s;var v=function(e,r){return function(i,a,n){for(var f=typeof r=="function"?r(i,a,n):r,u=c(e),h=new Array(f),b=0;b<f;b++)h[b]=u(i);return h}};y.readArray=v;var d=function(e,r,i){for(var a=0,n=0;n<i;n++)a+=e[r+n]&&Math.pow(2,i-n-1);return a},p=function(e){return function(r){for(var i=x()(r),a=new Array(8),n=0;n<8;n++)a[7-n]=!!(i&1<<n);return Object.keys(e).reduce(function(f,u){var h=e[u];return h.length?f[u]=d(a,h.index,h.length):f[u]=a[h.index],f},{})}};return y.readBits=p,y}var j;function $(){return j||(j=1,(function(B){Object.defineProperty(B,"__esModule",{value:!0}),B.default=void 0;var x=L(),t=O(),c={blocks:function(r){for(var i=0,a=[],n=r.data.length,f=0,u=(0,t.readByte)()(r);u!==i&&u;u=(0,t.readByte)()(r)){if(r.pos+u>=n){var h=n-r.pos;a.push((0,t.readBytes)(h)(r)),f+=h;break}a.push((0,t.readBytes)(u)(r)),f+=u}for(var b=new Uint8Array(f),k=0,m=0;m<a.length;m++)b.set(a[m],k),k+=a[m].length;return b}},g=(0,x.conditional)({gce:[{codes:(0,t.readBytes)(2)},{byteSize:(0,t.readByte)()},{extras:(0,t.readBits)({future:{index:0,length:3},disposal:{index:3,length:3},userInput:{index:6},transparentColorGiven:{index:7}})},{delay:(0,t.readUnsigned)(!0)},{transparentColorIndex:(0,t.readByte)()},{terminator:(0,t.readByte)()}]},function(e){var r=(0,t.peekBytes)(2)(e);return r[0]===33&&r[1]===249}),l=(0,x.conditional)({image:[{code:(0,t.readByte)()},{descriptor:[{left:(0,t.readUnsigned)(!0)},{top:(0,t.readUnsigned)(!0)},{width:(0,t.readUnsigned)(!0)},{height:(0,t.readUnsigned)(!0)},{lct:(0,t.readBits)({exists:{index:0},interlaced:{index:1},sort:{index:2},future:{index:3,length:2},size:{index:5,length:3}})}]},(0,x.conditional)({lct:(0,t.readArray)(3,function(e,r,i){return Math.pow(2,i.descriptor.lct.size+1)})},function(e,r,i){return i.descriptor.lct.exists}),{data:[{minCodeSize:(0,t.readByte)()},c]}]},function(e){return(0,t.peekByte)()(e)===44}),s=(0,x.conditional)({text:[{codes:(0,t.readBytes)(2)},{blockSize:(0,t.readByte)()},{preData:function(r,i,a){return(0,t.readBytes)(a.text.blockSize)(r)}},c]},function(e){var r=(0,t.peekBytes)(2)(e);return r[0]===33&&r[1]===1}),v=(0,x.conditional)({application:[{codes:(0,t.readBytes)(2)},{blockSize:(0,t.readByte)()},{id:function(r,i,a){return(0,t.readString)(a.blockSize)(r)}},c]},function(e){var r=(0,t.peekBytes)(2)(e);return r[0]===33&&r[1]===255}),d=(0,x.conditional)({comment:[{codes:(0,t.readBytes)(2)},c]},function(e){var r=(0,t.peekBytes)(2)(e);return r[0]===33&&r[1]===254}),p=[{header:[{signature:(0,t.readString)(3)},{version:(0,t.readString)(3)}]},{lsd:[{width:(0,t.readUnsigned)(!0)},{height:(0,t.readUnsigned)(!0)},{gct:(0,t.readBits)({exists:{index:0},resolution:{index:1,length:3},sort:{index:4},size:{index:5,length:3}})},{backgroundColorIndex:(0,t.readByte)()},{pixelAspectRatio:(0,t.readByte)()}]},(0,x.conditional)({gct:(0,t.readArray)(3,function(e,r){return Math.pow(2,r.lsd.gct.size+1)})},function(e,r){return r.lsd.gct.exists}),{frames:(0,x.loop)([g,v,d,l,s],function(e){var r=(0,t.peekByte)()(e);return r===33||r===44})}],o=p;B.default=o})(M)),M}var P={},D;function E(){if(D)return P;D=1,Object.defineProperty(P,"__esModule",{value:!0}),P.deinterlace=void 0;var B=function(t,c){for(var g=new Array(t.length),l=t.length/c,s=function(i,a){var n=t.slice(a*c,(a+1)*c);g.splice.apply(g,[i*c,c].concat(n))},v=[0,4,2,1],d=[8,8,4,2],p=0,o=0;o<4;o++)for(var e=v[o];e<l;e+=d[o])s(e,p),p++;return g};return P.deinterlace=B,P}var q={},G;function K(){if(G)return q;G=1,Object.defineProperty(q,"__esModule",{value:!0}),q.lzw=void 0;var B=function(t,c,g){var l=4096,s=-1,v=g,d,p,o,e,r,i,a,z,n,f,U,u,A,_,I,C,h=new Array(g),b=new Array(l),k=new Array(l),m=new Array(l+1);for(u=t,p=1<<u,r=p+1,d=p+2,a=s,e=u+1,o=(1<<e)-1,n=0;n<p;n++)b[n]=0,k[n]=n;var U,z,A,_,C,I;for(U=z=A=_=C=I=0,f=0;f<v;){if(_===0){if(z<e){U+=c[I]<<z,z+=8,I++;continue}if(n=U&o,U>>=e,z-=e,n>d||n==r)break;if(n==p){e=u+1,o=(1<<e)-1,d=p+2,a=s;continue}if(a==s){m[_++]=k[n],a=n,A=n;continue}for(i=n,n==d&&(m[_++]=A,n=a);n>p;)m[_++]=k[n],n=b[n];A=k[n]&255,m[_++]=A,d<l&&(b[d]=a,k[d]=A,d++,(d&o)===0&&d<l&&(e++,o+=d)),a=i}_--,h[C++]=m[_],f++}for(f=C;f<v;f++)h[f]=0;return h};return q.lzw=B,q}var T;function X(){if(T)return S;T=1,Object.defineProperty(S,"__esModule",{value:!0}),S.decompressFrames=S.decompressFrame=S.parseGIF=void 0;var B=l($()),x=L(),t=O(),c=E(),g=K();function l(o){return o&&o.__esModule?o:{default:o}}var s=function(e){var r=new Uint8Array(e);return(0,x.parse)((0,t.buildStream)(r),B.default)};S.parseGIF=s;var v=function(e){for(var r=e.pixels.length,i=new Uint8ClampedArray(r*4),a=0;a<r;a++){var n=a*4,f=e.pixels[a],u=e.colorTable[f]||[0,0,0];i[n]=u[0],i[n+1]=u[1],i[n+2]=u[2],i[n+3]=f!==e.transparentIndex?255:0}return i},d=function(e,r,i){if(!e.image){console.warn("gif frame does not have associated image.");return}var a=e.image,n=a.descriptor.width*a.descriptor.height,f=(0,g.lzw)(a.data.minCodeSize,a.data.blocks,n);a.descriptor.lct.interlaced&&(f=(0,c.deinterlace)(f,a.descriptor.width));var u={pixels:f,dims:{top:e.image.descriptor.top,left:e.image.descriptor.left,width:e.image.descriptor.width,height:e.image.descriptor.height}};return a.descriptor.lct&&a.descriptor.lct.exists?u.colorTable=a.lct:u.colorTable=r,e.gce&&(u.delay=(e.gce.delay||10)*10,u.disposalType=e.gce.extras.disposal,e.gce.extras.transparentColorGiven&&(u.transparentIndex=e.gce.transparentColorIndex)),i&&(u.patch=v(u)),u};S.decompressFrame=d;var p=function(e,r){return e.frames.filter(function(i){return i.image}).map(function(i){return d(i,e.gct,r)})};return S.decompressFrames=p,S}var Z=X();export{Z as l};
