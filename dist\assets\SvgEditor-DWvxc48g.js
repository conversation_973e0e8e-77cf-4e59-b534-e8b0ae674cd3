import{d as oo,u as io,r as K,i as Mt,a1 as ro,w as Ie,c,o as a,a as n,t as f,e as vt,g as _t,F as te,k as ee,f as m,S as ke,W as no}from"./index-CkZTMFXG.js";import{u as lo}from"./useToast-virEbLJw.js";/*! @license DOMPurify 3.2.6 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.6/LICENSE */const{entries:Xe,setPrototypeOf:Pe,isFrozen:so,getPrototypeOf:co,getOwnPropertyDescriptor:ao}=Object;let{freeze:Y,seal:J,create:je}=Object,{apply:Ee,construct:pe}=typeof Reflect<"u"&&Reflect;Y||(Y=function(s){return s});J||(J=function(s){return s});Ee||(Ee=function(s,x,v){return s.apply(x,v)});pe||(pe=function(s,x){return new s(...x)});const oe=B(Array.prototype.forEach),uo=B(Array.prototype.lastIndexOf),Ue=B(Array.prototype.pop),Ft=B(Array.prototype.push),yo=B(Array.prototype.splice),re=B(String.prototype.toLowerCase),xe=B(String.prototype.toString),Fe=B(String.prototype.match),zt=B(String.prototype.replace),fo=B(String.prototype.indexOf),mo=B(String.prototype.trim),et=B(Object.prototype.hasOwnProperty),G=B(RegExp.prototype.test),Wt=go(TypeError);function B(_){return function(s){s instanceof RegExp&&(s.lastIndex=0);for(var x=arguments.length,v=new Array(x>1?x-1:0),E=1;E<x;E++)v[E-1]=arguments[E];return Ee(_,s,v)}}function go(_){return function(){for(var s=arguments.length,x=new Array(s),v=0;v<s;v++)x[v]=arguments[v];return pe(_,x)}}function w(_,s){let x=arguments.length>2&&arguments[2]!==void 0?arguments[2]:re;Pe&&Pe(_,null);let v=s.length;for(;v--;){let E=s[v];if(typeof E=="string"){const Q=x(E);Q!==E&&(so(s)||(s[v]=Q),E=Q)}_[E]=!0}return _}function ko(_){for(let s=0;s<_.length;s++)et(_,s)||(_[s]=null);return _}function lt(_){const s=je(null);for(const[x,v]of Xe(_))et(_,x)&&(Array.isArray(v)?s[x]=ko(v):v&&typeof v=="object"&&v.constructor===Object?s[x]=lt(v):s[x]=v);return s}function Ht(_,s){for(;_!==null;){const v=ao(_,s);if(v){if(v.get)return B(v.get);if(typeof v.value=="function")return B(v.value)}_=co(_)}function x(){return null}return x}const ze=Y(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),ve=Y(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),_e=Y(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),xo=Y(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),we=Y(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),vo=Y(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),We=Y(["#text"]),He=Y(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),be=Y(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),Ge=Y(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),ie=Y(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),_o=J(/\{\{[\w\W]*|[\w\W]*\}\}/gm),wo=J(/<%[\w\W]*|[\w\W]*%>/gm),bo=J(/\$\{[\w\W]*/gm),Eo=J(/^data-[\-\w.\u00B7-\uFFFF]+$/),po=J(/^aria-[\-\w]+$/),Ve=J(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp|matrix):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),$o=J(/^(?:\w+script|data):/i),To=J(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),qe=J(/^html$/i),Oo=J(/^[a-z][.\w]*(-[.\w]+)+$/i);var Ye=Object.freeze({__proto__:null,ARIA_ATTR:po,ATTR_WHITESPACE:To,CUSTOM_ELEMENT:Oo,DATA_ATTR:Eo,DOCTYPE_NAME:qe,ERB_EXPR:wo,IS_ALLOWED_URI:Ve,IS_SCRIPT_OR_DATA:$o,MUSTACHE_EXPR:_o,TMPLIT_EXPR:bo});const Gt={element:1,text:3,progressingInstruction:7,comment:8,document:9},Ao=function(){return typeof window>"u"?null:window},Mo=function(s,x){if(typeof s!="object"||typeof s.createPolicy!="function")return null;let v=null;const E="data-tt-policy-suffix";x&&x.hasAttribute(E)&&(v=x.getAttribute(E));const Q="dompurify"+(v?"#"+v:"");try{return s.createPolicy(Q,{createHTML(st){return st},createScriptURL(st){return st}})}catch{return console.warn("TrustedTypes policy "+Q+" could not be created."),null}},Be=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function Ke(){let _=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Ao();const s=y=>Ke(y);if(s.version="3.2.6",s.removed=[],!_||!_.document||_.document.nodeType!==Gt.document||!_.Element)return s.isSupported=!1,s;let{document:x}=_;const v=x,E=v.currentScript,{DocumentFragment:Q,HTMLTemplateElement:st,Node:St,Element:Yt,NodeFilter:ut,NamedNodeMap:wt=_.NamedNodeMap||_.MozNamedAttrMap,HTMLFormElement:ne,DOMParser:F,trustedTypes:d}=_,p=Yt.prototype,le=Ht(p,"cloneNode"),yt=Ht(p,"remove"),ct=Ht(p,"nextSibling"),ft=Ht(p,"childNodes"),ot=Ht(p,"parentNode");if(typeof st=="function"){const y=x.createElement("template");y.content&&y.content.ownerDocument&&(x=y.content.ownerDocument)}let k,z="";const{implementation:W,createNodeIterator:se,createDocumentFragment:Bt,getElementsByTagName:ce}=x,{importNode:ae}=v;let L=Be();s.isSupported=typeof Xe=="function"&&typeof ot=="function"&&W&&W.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:ht,ERB_EXPR:I,TMPLIT_EXPR:Ct,DATA_ATTR:X,ARIA_ATTR:N,IS_SCRIPT_OR_DATA:q,ATTR_WHITESPACE:Xt,CUSTOM_ELEMENT:de}=Ye;let{IS_ALLOWED_URI:Rt}=Ye,A=null;const jt=w({},[...ze,...ve,..._e,...we,...We]);let S=null;const at=w({},[...He,...be,...Ge,...ie]);let O=Object.seal(je(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),mt=null,j=null,Vt=!0,qt=!0,Kt=!1,Zt=!0,h=!1,Dt=!0,tt=!1,Lt=!1,gt=!1,r=!1,e=!1,t=!1,i=!0,g=!1;const b="user-content-";let $=!0,C=!1,M={},H=null;const bt=w({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let kt=null;const Jt=w({},["audio","video","img","source","image","track"]);let Nt=null;const Qt=w({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Et="http://www.w3.org/1998/Math/MathML",pt="http://www.w3.org/2000/svg",Z="http://www.w3.org/1999/xhtml";let dt=Z,It=!1,Pt=null;const ue=w({},[Et,pt,Z],xe);let $t=w({},["mi","mo","mn","ms","mtext"]),Tt=w({},["annotation-xml"]);const ye=w({},["title","style","font","a","script"]);let xt=null;const Ze=["application/xhtml+xml","text/html"],Je="text/html";let D=null,Ot=null;const Qe=x.createElement("form"),$e=function(o){return o instanceof RegExp||o instanceof Function},fe=function(){let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(Ot&&Ot===o)){if((!o||typeof o!="object")&&(o={}),o=lt(o),xt=Ze.indexOf(o.PARSER_MEDIA_TYPE)===-1?Je:o.PARSER_MEDIA_TYPE,D=xt==="application/xhtml+xml"?xe:re,A=et(o,"ALLOWED_TAGS")?w({},o.ALLOWED_TAGS,D):jt,S=et(o,"ALLOWED_ATTR")?w({},o.ALLOWED_ATTR,D):at,Pt=et(o,"ALLOWED_NAMESPACES")?w({},o.ALLOWED_NAMESPACES,xe):ue,Nt=et(o,"ADD_URI_SAFE_ATTR")?w(lt(Qt),o.ADD_URI_SAFE_ATTR,D):Qt,kt=et(o,"ADD_DATA_URI_TAGS")?w(lt(Jt),o.ADD_DATA_URI_TAGS,D):Jt,H=et(o,"FORBID_CONTENTS")?w({},o.FORBID_CONTENTS,D):bt,mt=et(o,"FORBID_TAGS")?w({},o.FORBID_TAGS,D):lt({}),j=et(o,"FORBID_ATTR")?w({},o.FORBID_ATTR,D):lt({}),M=et(o,"USE_PROFILES")?o.USE_PROFILES:!1,Vt=o.ALLOW_ARIA_ATTR!==!1,qt=o.ALLOW_DATA_ATTR!==!1,Kt=o.ALLOW_UNKNOWN_PROTOCOLS||!1,Zt=o.ALLOW_SELF_CLOSE_IN_ATTR!==!1,h=o.SAFE_FOR_TEMPLATES||!1,Dt=o.SAFE_FOR_XML!==!1,tt=o.WHOLE_DOCUMENT||!1,r=o.RETURN_DOM||!1,e=o.RETURN_DOM_FRAGMENT||!1,t=o.RETURN_TRUSTED_TYPE||!1,gt=o.FORCE_BODY||!1,i=o.SANITIZE_DOM!==!1,g=o.SANITIZE_NAMED_PROPS||!1,$=o.KEEP_CONTENT!==!1,C=o.IN_PLACE||!1,Rt=o.ALLOWED_URI_REGEXP||Ve,dt=o.NAMESPACE||Z,$t=o.MATHML_TEXT_INTEGRATION_POINTS||$t,Tt=o.HTML_INTEGRATION_POINTS||Tt,O=o.CUSTOM_ELEMENT_HANDLING||{},o.CUSTOM_ELEMENT_HANDLING&&$e(o.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(O.tagNameCheck=o.CUSTOM_ELEMENT_HANDLING.tagNameCheck),o.CUSTOM_ELEMENT_HANDLING&&$e(o.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(O.attributeNameCheck=o.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),o.CUSTOM_ELEMENT_HANDLING&&typeof o.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(O.allowCustomizedBuiltInElements=o.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),h&&(qt=!1),e&&(r=!0),M&&(A=w({},We),S=[],M.html===!0&&(w(A,ze),w(S,He)),M.svg===!0&&(w(A,ve),w(S,be),w(S,ie)),M.svgFilters===!0&&(w(A,_e),w(S,be),w(S,ie)),M.mathMl===!0&&(w(A,we),w(S,Ge),w(S,ie))),o.ADD_TAGS&&(A===jt&&(A=lt(A)),w(A,o.ADD_TAGS,D)),o.ADD_ATTR&&(S===at&&(S=lt(S)),w(S,o.ADD_ATTR,D)),o.ADD_URI_SAFE_ATTR&&w(Nt,o.ADD_URI_SAFE_ATTR,D),o.FORBID_CONTENTS&&(H===bt&&(H=lt(H)),w(H,o.FORBID_CONTENTS,D)),$&&(A["#text"]=!0),tt&&w(A,["html","head","body"]),A.table&&(w(A,["tbody"]),delete mt.tbody),o.TRUSTED_TYPES_POLICY){if(typeof o.TRUSTED_TYPES_POLICY.createHTML!="function")throw Wt('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof o.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw Wt('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');k=o.TRUSTED_TYPES_POLICY,z=k.createHTML("")}else k===void 0&&(k=Mo(d,E)),k!==null&&typeof z=="string"&&(z=k.createHTML(""));Y&&Y(o),Ot=o}},Te=w({},[...ve,..._e,...xo]),Oe=w({},[...we,...vo]),to=function(o){let l=ot(o);(!l||!l.tagName)&&(l={namespaceURI:dt,tagName:"template"});const u=re(o.tagName),T=re(l.tagName);return Pt[o.namespaceURI]?o.namespaceURI===pt?l.namespaceURI===Z?u==="svg":l.namespaceURI===Et?u==="svg"&&(T==="annotation-xml"||$t[T]):!!Te[u]:o.namespaceURI===Et?l.namespaceURI===Z?u==="math":l.namespaceURI===pt?u==="math"&&Tt[T]:!!Oe[u]:o.namespaceURI===Z?l.namespaceURI===pt&&!Tt[T]||l.namespaceURI===Et&&!$t[T]?!1:!Oe[u]&&(ye[u]||!Te[u]):!!(xt==="application/xhtml+xml"&&Pt[o.namespaceURI]):!1},it=function(o){Ft(s.removed,{element:o});try{ot(o).removeChild(o)}catch{yt(o)}},At=function(o,l){try{Ft(s.removed,{attribute:l.getAttributeNode(o),from:l})}catch{Ft(s.removed,{attribute:null,from:l})}if(l.removeAttribute(o),o==="is")if(r||e)try{it(l)}catch{}else try{l.setAttribute(o,"")}catch{}},Ae=function(o){let l=null,u=null;if(gt)o="<remove></remove>"+o;else{const R=Fe(o,/^[\r\n\t ]+/);u=R&&R[0]}xt==="application/xhtml+xml"&&dt===Z&&(o='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+o+"</body></html>");const T=k?k.createHTML(o):o;if(dt===Z)try{l=new F().parseFromString(T,xt)}catch{}if(!l||!l.documentElement){l=W.createDocument(dt,"template",null);try{l.documentElement.innerHTML=It?z:T}catch{}}const P=l.body||l.documentElement;return o&&u&&P.insertBefore(x.createTextNode(u),P.childNodes[0]||null),dt===Z?ce.call(l,tt?"html":"body")[0]:tt?l.documentElement:P},Me=function(o){return se.call(o.ownerDocument||o,o,ut.SHOW_ELEMENT|ut.SHOW_COMMENT|ut.SHOW_TEXT|ut.SHOW_PROCESSING_INSTRUCTION|ut.SHOW_CDATA_SECTION,null)},me=function(o){return o instanceof ne&&(typeof o.nodeName!="string"||typeof o.textContent!="string"||typeof o.removeChild!="function"||!(o.attributes instanceof wt)||typeof o.removeAttribute!="function"||typeof o.setAttribute!="function"||typeof o.namespaceURI!="string"||typeof o.insertBefore!="function"||typeof o.hasChildNodes!="function")},Se=function(o){return typeof St=="function"&&o instanceof St};function rt(y,o,l){oe(y,u=>{u.call(s,o,l,Ot)})}const he=function(o){let l=null;if(rt(L.beforeSanitizeElements,o,null),me(o))return it(o),!0;const u=D(o.nodeName);if(rt(L.uponSanitizeElement,o,{tagName:u,allowedTags:A}),Dt&&o.hasChildNodes()&&!Se(o.firstElementChild)&&G(/<[/\w!]/g,o.innerHTML)&&G(/<[/\w!]/g,o.textContent)||o.nodeType===Gt.progressingInstruction||Dt&&o.nodeType===Gt.comment&&G(/<[/\w]/g,o.data))return it(o),!0;if(!A[u]||mt[u]){if(!mt[u]&&Re(u)&&(O.tagNameCheck instanceof RegExp&&G(O.tagNameCheck,u)||O.tagNameCheck instanceof Function&&O.tagNameCheck(u)))return!1;if($&&!H[u]){const T=ot(o)||o.parentNode,P=ft(o)||o.childNodes;if(P&&T){const R=P.length;for(let V=R-1;V>=0;--V){const nt=le(P[V],!0);nt.__removalCount=(o.__removalCount||0)+1,T.insertBefore(nt,ct(o))}}}return it(o),!0}return o instanceof Yt&&!to(o)||(u==="noscript"||u==="noembed"||u==="noframes")&&G(/<\/no(script|embed|frames)/i,o.innerHTML)?(it(o),!0):(h&&o.nodeType===Gt.text&&(l=o.textContent,oe([ht,I,Ct],T=>{l=zt(l,T," ")}),o.textContent!==l&&(Ft(s.removed,{element:o.cloneNode()}),o.textContent=l)),rt(L.afterSanitizeElements,o,null),!1)},Ce=function(o,l,u){if(i&&(l==="id"||l==="name")&&(u in x||u in Qe))return!1;if(!(qt&&!j[l]&&G(X,l))){if(!(Vt&&G(N,l))){if(!S[l]||j[l]){if(!(Re(o)&&(O.tagNameCheck instanceof RegExp&&G(O.tagNameCheck,o)||O.tagNameCheck instanceof Function&&O.tagNameCheck(o))&&(O.attributeNameCheck instanceof RegExp&&G(O.attributeNameCheck,l)||O.attributeNameCheck instanceof Function&&O.attributeNameCheck(l))||l==="is"&&O.allowCustomizedBuiltInElements&&(O.tagNameCheck instanceof RegExp&&G(O.tagNameCheck,u)||O.tagNameCheck instanceof Function&&O.tagNameCheck(u))))return!1}else if(!Nt[l]){if(!G(Rt,zt(u,Xt,""))){if(!((l==="src"||l==="xlink:href"||l==="href")&&o!=="script"&&fo(u,"data:")===0&&kt[o])){if(!(Kt&&!G(q,zt(u,Xt,"")))){if(u)return!1}}}}}}return!0},Re=function(o){return o!=="annotation-xml"&&Fe(o,de)},De=function(o){rt(L.beforeSanitizeAttributes,o,null);const{attributes:l}=o;if(!l||me(o))return;const u={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:S,forceKeepAttr:void 0};let T=l.length;for(;T--;){const P=l[T],{name:R,namespaceURI:V,value:nt}=P,Ut=D(R),ge=nt;let U=R==="value"?ge:mo(ge);if(u.attrName=Ut,u.attrValue=U,u.keepAttr=!0,u.forceKeepAttr=void 0,rt(L.uponSanitizeAttribute,o,u),U=u.attrValue,g&&(Ut==="id"||Ut==="name")&&(At(R,o),U=b+U),Dt&&G(/((--!?|])>)|<\/(style|title)/i,U)){At(R,o);continue}if(u.forceKeepAttr)continue;if(!u.keepAttr){At(R,o);continue}if(!Zt&&G(/\/>/i,U)){At(R,o);continue}h&&oe([ht,I,Ct],Ne=>{U=zt(U,Ne," ")});const Le=D(o.nodeName);if(!Ce(Le,Ut,U)){At(R,o);continue}if(k&&typeof d=="object"&&typeof d.getAttributeType=="function"&&!V)switch(d.getAttributeType(Le,Ut)){case"TrustedHTML":{U=k.createHTML(U);break}case"TrustedScriptURL":{U=k.createScriptURL(U);break}}if(U!==ge)try{V?o.setAttributeNS(V,R,U):o.setAttribute(R,U),me(o)?it(o):Ue(s.removed)}catch{At(R,o)}}rt(L.afterSanitizeAttributes,o,null)},eo=function y(o){let l=null;const u=Me(o);for(rt(L.beforeSanitizeShadowDOM,o,null);l=u.nextNode();)rt(L.uponSanitizeShadowNode,l,null),he(l),De(l),l.content instanceof Q&&y(l.content);rt(L.afterSanitizeShadowDOM,o,null)};return s.sanitize=function(y){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},l=null,u=null,T=null,P=null;if(It=!y,It&&(y="<!-->"),typeof y!="string"&&!Se(y))if(typeof y.toString=="function"){if(y=y.toString(),typeof y!="string")throw Wt("dirty is not a string, aborting")}else throw Wt("toString is not a function");if(!s.isSupported)return y;if(Lt||fe(o),s.removed=[],typeof y=="string"&&(C=!1),C){if(y.nodeName){const nt=D(y.nodeName);if(!A[nt]||mt[nt])throw Wt("root node is forbidden and cannot be sanitized in-place")}}else if(y instanceof St)l=Ae("<!---->"),u=l.ownerDocument.importNode(y,!0),u.nodeType===Gt.element&&u.nodeName==="BODY"||u.nodeName==="HTML"?l=u:l.appendChild(u);else{if(!r&&!h&&!tt&&y.indexOf("<")===-1)return k&&t?k.createHTML(y):y;if(l=Ae(y),!l)return r?null:t?z:""}l&&gt&&it(l.firstChild);const R=Me(C?y:l);for(;T=R.nextNode();)he(T),De(T),T.content instanceof Q&&eo(T.content);if(C)return y;if(r){if(e)for(P=Bt.call(l.ownerDocument);l.firstChild;)P.appendChild(l.firstChild);else P=l;return(S.shadowroot||S.shadowrootmode)&&(P=ae.call(v,P,!0)),P}let V=tt?l.outerHTML:l.innerHTML;return tt&&A["!doctype"]&&l.ownerDocument&&l.ownerDocument.doctype&&l.ownerDocument.doctype.name&&G(qe,l.ownerDocument.doctype.name)&&(V="<!DOCTYPE "+l.ownerDocument.doctype.name+`>
`+V),h&&oe([ht,I,Ct],nt=>{V=zt(V,nt," ")}),k&&t?k.createHTML(V):V},s.setConfig=function(){let y=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};fe(y),Lt=!0},s.clearConfig=function(){Ot=null,Lt=!1},s.isValidAttribute=function(y,o,l){Ot||fe({});const u=D(y),T=D(o);return Ce(u,T,l)},s.addHook=function(y,o){typeof o=="function"&&Ft(L[y],o)},s.removeHook=function(y,o){if(o!==void 0){const l=uo(L[y],o);return l===-1?void 0:yo(L[y],l,1)[0]}return Ue(L[y])},s.removeHooks=function(y){L[y]=[]},s.removeAllHooks=function(){L=Be()},s}var So=Ke();const ho={class:"min-h-screen bg-gray-50 py-8"},Co={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Ro={class:"bg-white rounded-lg shadow-md p-6 mb-8"},Do={class:"text-3xl font-bold text-gray-900 mb-4"},Lo={class:"text-gray-600 text-lg"},No={class:"mt-4 p-4 bg-blue-50 rounded-lg"},Io={class:"font-semibold text-blue-800 mb-2"},Po={class:"list-decimal list-inside space-y-1 text-blue-700"},Uo={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},Fo={class:"bg-white rounded-lg shadow-md p-6"},zo={class:"flex justify-between items-center mb-4"},Wo={class:"text-lg font-semibold text-gray-900"},Ho={class:"flex space-x-2"},Go={class:"border border-gray-300 rounded-lg overflow-hidden"},Yo=["placeholder"],Bo={class:"mt-4 flex justify-between"},Xo={class:"text-sm text-gray-500"},jo={class:"bg-white rounded-lg shadow-md p-6"},Vo={class:"text-lg font-semibold text-gray-900 mb-4"},qo={class:"border border-gray-300 rounded-lg overflow-hidden bg-white flex items-center justify-center min-h-96"},Ko=["innerHTML"],Zo={key:1,class:"text-gray-500 text-center p-8"},Jo={class:"mt-4 flex justify-between"},Qo={class:"text-sm text-gray-500"},ti={class:"bg-white rounded-lg shadow-md p-6 mt-8"},ei={class:"text-lg font-semibold text-gray-900 mb-4"},oi={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},ii={class:"md:col-span-1"},ri={class:"font-medium text-gray-800 mb-3"},ni={class:"grid grid-cols-3 gap-2"},li=["onClick","title"],si={class:"text-2xl mb-1"},ci={class:"text-xs"},ai={class:"font-medium text-gray-800 mb-3 mt-6"},di={class:"space-y-2"},ui=["disabled"],yi=["disabled"],fi={class:"font-medium text-gray-800 mb-3 mt-6"},mi={class:"space-y-2"},gi=["disabled"],ki={class:"md:col-span-2 border border-gray-300 rounded-lg bg-gray-50 flex items-center justify-center min-h-[500px] relative"},xi=["width","height"],vi={key:0},_i=["x","y","width","height","fill","stroke","stroke-width","fill-opacity","stroke-opacity","transform","onMousedown"],wi=["x","y","width","height","transform"],bi=["cx","cy","onMousedown"],Ei=["x1","y1","x2","y2"],pi=["cx","cy","onMousedown"],$i={key:1},Ti=["cx","cy","r","fill","stroke","stroke-width","fill-opacity","stroke-opacity","transform","onMousedown"],Oi=["cx","cy","r","transform"],Ai=["cx","cy","onMousedown"],Mi=["x1","y1","x2","y2"],Si=["cx","cy","onMousedown"],hi={key:2},Ci=["cx","cy","rx","ry","fill","stroke","stroke-width","fill-opacity","stroke-opacity","transform","onMousedown"],Ri=["cx","cy","rx","ry","transform"],Di=["cx","cy","onMousedown"],Li=["cx","cy","onMousedown"],Ni=["x1","y1","x2","y2"],Ii=["cx","cy","onMousedown"],Pi={key:3},Ui=["x1","y1","x2","y2","stroke-width","onMousedown"],Fi=["x1","y1","x2","y2","stroke","stroke-width","stroke-opacity"],zi=["x1","y1","x2","y2"],Wi=["cx","cy","onMousedown"],Hi=["cx","cy","onMousedown"],Gi=["x1","y1","x2","y2"],Yi=["cx","cy","onMousedown"],Bi={key:4},Xi=["points","fill","stroke","stroke-width","fill-opacity","stroke-opacity","transform","onMousedown"],ji=["points","transform"],Vi=["x1","y1","x2","y2"],qi=["cx","cy","onMousedown"],Ki={key:5},Zi=["d","fill","stroke","stroke-width","fill-opacity","stroke-opacity","onMousedown"],Ji=["d"],Qi=["cx","cy","onMousedown"],tr={key:6},er=["points","fill","stroke","stroke-width","fill-opacity","stroke-opacity","transform","onMousedown"],or=["points","transform"],ir=["x1","y1","x2","y2"],rr=["cx","cy","onMousedown"],nr={key:7},lr=["points","fill","stroke","stroke-width","fill-opacity","stroke-opacity","transform","onMousedown"],sr=["points","transform"],cr=["x1","y1","x2","y2"],ar=["cx","cy","onMousedown"],dr={key:8},ur=["d","fill","stroke","stroke-width","fill-opacity","stroke-opacity","transform","onMousedown"],yr=["d","transform"],fr=["x1","y1","x2","y2"],mr=["cx","cy","onMousedown"],gr={key:9},kr=["d","stroke-width","onMousedown"],xr=["d","stroke","stroke-width","stroke-opacity"],vr=["d"],_r=["cx","cy","onMousedown"],wr=["cx","cy","onMousedown"],br=["cx","cy","onMousedown"],Er=["x1","y1","x2","y2"],pr=["cx","cy","onMousedown"],$r={key:10},Tr=["d","stroke-width","onMousedown"],Or=["d","stroke","stroke-width","stroke-opacity"],Ar=["d"],Mr=["cx","cy","onMousedown"],Sr=["cx","cy","onMousedown"],hr=["cx","cy","onMousedown"],Cr=["cx","cy","onMousedown"],Rr=["x1","y1","x2","y2"],Dr=["cx","cy","onMousedown"],Lr={key:11},Nr=["d","stroke-width","onMousedown"],Ir=["d","stroke","stroke-width","stroke-opacity"],Pr=["d"],Ur=["cx","cy","onMousedown"],Fr=["cx","cy","onMousedown"],zr=["x1","y1","x2","y2"],Wr=["cx","cy","onMousedown"],Hr={class:"md:col-span-1"},Gr={class:"font-medium text-gray-800 mb-3"},Yr={key:0,class:"space-y-4"},Br={class:"block text-sm font-medium text-gray-700 mb-1"},Xr={class:"flex items-center space-x-2"},jr={class:"text-xs text-gray-500 text-right"},Vr={class:"block text-sm font-medium text-gray-700 mb-1"},qr={class:"flex items-center space-x-2"},Kr={class:"text-xs text-gray-500 text-right"},Zr={class:"block text-sm font-medium text-gray-700 mb-1"},Jr={class:"text-xs text-gray-500 text-right"},Qr={class:"block text-sm font-medium text-gray-700 mb-1"},tn={class:"text-xs text-gray-500 text-right"},en={key:1,class:"text-gray-500 text-sm"},on={class:"bg-white rounded-lg shadow-md p-6 mt-8"},rn={class:"text-lg font-semibold text-gray-900 mb-4"},nn={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},ln={class:"font-medium text-gray-900 mb-2"},sn={class:"text-sm text-gray-600 mb-3"},cn=["onClick"],an={class:"bg-yellow-50 border-l-4 border-yellow-400 p-4 mt-8"},dn={class:"flex"},un={class:"ml-3"},yn={class:"text-sm font-medium text-yellow-800"},fn={class:"mt-2 text-sm text-yellow-700"},mn={class:"list-disc list-inside space-y-1"},gn=oo({__name:"SvgEditor",setup(_){const{t:s}=io(),{success:x,error:v}=lo(),E=K(""),Q=Mt(()=>E.value.split(`
`).length),st=Mt(()=>So.sanitize(E.value,{USE_PROFILES:{svg:!0},ADD_TAGS:["use","symbol","defs","linearGradient","radialGradient","stop","animate","animateTransform"],ADD_ATTR:["viewBox","preserveAspectRatio","xlink:href","gradientUnits","cx","cy","r","fx","fy","spreadMethod"]})),St=Mt(()=>{const r=E.value.match(/width=["'](\d+)["']/);return r?r[1]:"N/A"}),Yt=Mt(()=>{const r=E.value.match(/height=["'](\d+)["']/);return r?r[1]:"N/A"}),ut=K(null),wt=K({width:600,height:500}),ne=[{type:"rectangle",name:s("tools.svgEditor.shapes.rectangle"),icon:"▭"},{type:"circle",name:s("tools.svgEditor.shapes.circle"),icon:"○"},{type:"ellipse",name:s("tools.svgEditor.shapes.ellipse"),icon:"◇"},{type:"line",name:s("tools.svgEditor.shapes.line"),icon:"/"},{type:"triangle",name:s("tools.svgEditor.shapes.triangle"),icon:"△"},{type:"quadraticCurve",name:s("tools.svgEditor.shapes.quadraticCurve"),icon:"⌒"},{type:"cubicCurve",name:s("tools.svgEditor.shapes.cubicCurve"),icon:"⌓"},{type:"arcCurve",name:s("tools.svgEditor.shapes.arcCurve"),icon:"⌓"},{type:"polygon",name:s("tools.svgEditor.shapes.polygon"),icon:"⬟"},{type:"star",name:s("tools.svgEditor.shapes.star"),icon:"★"},{type:"heart",name:s("tools.svgEditor.shapes.heart"),icon:"❤"},{type:"path",name:s("tools.svgEditor.shapes.path"),icon:"✎"}],F=K([]),d=K(null),p=Mt(()=>F.value.find(r=>r.id===d.value)||null),le=Mt(()=>r=>r.type==="path"&&d.value===r.id?r.controlPoints||[]:[]),yt=K(!1),ct=K(!1),ft=K(!1),ot=K(!1),k=K({startX:0,startY:0,shapeId:"",resizeDirection:"",controlPointIndex:-1}),z=K([]),W=K(-1),se=K([{id:1,title:s("tools.svgEditor.tutorials.basicShapes"),description:s("tools.svgEditor.tutorials.basicShapesDesc"),code:`<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <!-- Rectangle -->
  <rect x="10" y="10" width="100" height="50" fill="blue" />

  <!-- Circle -->
  <circle cx="150" cy="50" r="30" fill="red" />

  <!-- Ellipse -->
  <ellipse cx="100" cy="150" rx="60" ry="30" fill="green" />
</svg>`},{id:2,title:s("tools.svgEditor.tutorials.paths"),description:s("tools.svgEditor.tutorials.pathsDesc"),code:`<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
  <!-- Path -->
  <path d="M 20 20 Q 50 50 80 20 T 140 20"
        fill="none" stroke="purple" stroke-width="3" />

  <!-- Complex Path -->
  <path d="M 50 50 L 150 50 L 100 150 Z"
        fill="orange" stroke="brown" stroke-width="2" />
</svg>`},{id:3,title:s("tools.svgEditor.tutorials.gradients"),description:s("tools.svgEditor.tutorials.gradientsDesc"),code:`<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:rgb(255,255,0);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
    </linearGradient>
    <radialGradient id="grad2" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:rgb(0,255,0);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgb(0,150,0);stop-opacity:1" />
    </radialGradient>
  </defs>

  <rect x="10" y="10" width="150" height="50" fill="url(#grad1)" />
  <circle cx="200" cy="100" r="40" fill="url(#grad2)" />
</svg>`}]);function Bt(){E.value=`<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="100%" height="100%" fill="#f0f0f0" />

  <!-- Shapes -->
  <circle cx="150" cy="100" r="50" fill="#3b82f6" />
  <rect x="50" y="50" width="80" height="60" fill="#ef4444" rx="10" />
  <line x1="200" y1="50" x2="280" y2="150" stroke="#10b981" stroke-width="3" />
</svg>`,x(s("tools.svgEditor.messages.exampleLoaded"))}function ce(){E.value="",x(s("tools.svgEditor.messages.editorCleared"))}async function ae(){try{navigator.clipboard.writeText(E.value),x(s("tools.svgEditor.messages.codeCopied"))}catch(r){console.error("Failed to copy to clipboard:",r),v(s("tools.svgEditor.errors.copyFailed"))}}function L(){if(!E.value){v(s("tools.svgEditor.errors.noSvg"));return}const r=new Blob([E.value],{type:"image/svg+xml"}),e=URL.createObjectURL(r),t=document.createElement("a");t.href=e,t.download=`svg-${Date.now()}.svg`,document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(e),x(s("tools.svgEditor.messages.svgDownloaded"))}function ht(r){let e;switch(r){case"rectangle":e={id:`shape-${Date.now()}`,type:"rectangle",x:50,y:50,width:100,height:80,fill:"#3b82f6",fillOpacity:1,stroke:"#1e40af",strokeOpacity:1,strokeWidth:2,rotation:0};break;case"circle":e={id:`shape-${Date.now()}`,type:"circle",cx:100,cy:100,r:40,fill:"#3b82f6",fillOpacity:1,stroke:"#1e40af",strokeOpacity:1,strokeWidth:2,rotation:0};break;case"ellipse":e={id:`shape-${Date.now()}`,type:"ellipse",cx:100,cy:100,rx:60,ry:40,fill:"#3b82f6",fillOpacity:1,stroke:"#1e40af",strokeOpacity:1,strokeWidth:2,rotation:0};break;case"line":e={id:`shape-${Date.now()}`,type:"line",x1:50,y1:50,x2:150,y2:100,fill:"#3b82f6",fillOpacity:1,stroke:"#1e40af",strokeOpacity:1,strokeWidth:2,rotation:0};break;case"triangle":e={id:`shape-${Date.now()}`,type:"triangle",points:"100,50 50,150 150,150",fill:"#3b82f6",fillOpacity:1,stroke:"#1e40af",strokeOpacity:1,strokeWidth:2,rotation:0};break;case"quadraticCurve":e={id:`shape-${Date.now()}`,type:"quadraticCurve",x1:50,y1:100,cx:100,cy:50,x2:150,y2:100,fill:"none",fillOpacity:0,stroke:"#3b82f6",strokeOpacity:1,strokeWidth:2,rotation:0};break;case"cubicCurve":e={id:`shape-${Date.now()}`,type:"cubicCurve",x1:50,y1:100,cx1:75,cy1:50,cx2:125,cy2:150,x2:150,y2:100,fill:"none",fillOpacity:0,stroke:"#3b82f6",strokeOpacity:1,strokeWidth:2,rotation:0};break;case"arcCurve":e={id:`shape-${Date.now()}`,type:"arcCurve",x1:50,y1:100,rx:50,ry:50,xAxisRotation:0,largeArcFlag:0,sweepFlag:1,x2:150,y2:100,fill:"none",fillOpacity:0,stroke:"#3b82f6",strokeOpacity:1,strokeWidth:2,rotation:0};break;case"polygon":e={id:`shape-${Date.now()}`,type:"polygon",points:"100,50 150,75 130,130 70,130 50,75",fill:"#3b82f6",fillOpacity:1,stroke:"#1e40af",strokeOpacity:1,strokeWidth:2,rotation:0};break;case"star":e={id:`shape-${Date.now()}`,type:"star",cx:100,cy:100,outerRadius:50,innerRadius:25,points:5,fill:"#3b82f6",fillOpacity:1,stroke:"#1e40af",strokeOpacity:1,strokeWidth:2,rotation:0};break;case"heart":e={id:`shape-${Date.now()}`,type:"heart",cx:100,cy:100,size:50,fill:"#ef4444",fillOpacity:1,stroke:"#dc2626",strokeOpacity:1,strokeWidth:2,rotation:0};break;case"path":e={id:`shape-${Date.now()}`,type:"path",d:"M 50 100 C 75 50, 125 150, 150 100",controlPoints:[{x:50,y:100},{x:75,y:50},{x:125,y:150},{x:150,y:100}],fill:"#3b82f6",fillOpacity:1,stroke:"#1e40af",strokeOpacity:1,strokeWidth:2,rotation:0};break;default:throw new Error(`Unknown shape type: ${r}`)}F.value.push(e),d.value=e.id,at(),j()}function I(r){const e=r.split(" ").map(g=>{const[b,$]=g.split(",").map(Number);return{x:b,y:$}}),t=e.reduce((g,b)=>g+b.x,0)/e.length,i=e.reduce((g,b)=>g+b.y,0)/e.length;return{x:t,y:i}}function Ct(r){const e=r.target,t=e.closest("rect, circle, ellipse, line, polygon, path"),i=e===r.currentTarget||e.tagName==="rect"&&e.getAttribute("fill")==="url(#grid)";!t&&i&&(d.value=null)}function X(r,e){r.preventDefault(),yt.value=!0,d.value=e,k.value={startX:r.clientX,startY:r.clientY,shapeId:e,resizeDirection:"",controlPointIndex:-1}}function N(r,e,t){r.preventDefault(),r.stopPropagation(),ct.value=!0,d.value=e,k.value={startX:r.clientX,startY:r.clientY,shapeId:e,resizeDirection:t,controlPointIndex:-1}}function q(r,e){r.preventDefault(),r.stopPropagation(),ft.value=!0,d.value=e;const t=F.value.find(b=>b.id===e);if(!t)return;let i=0,g=0;switch(t.type){case"rectangle":i=t.x+t.width/2,g=t.y+t.height/2;break;case"circle":i=t.cx,g=t.cy;break;case"ellipse":i=t.cx,g=t.cy;break;case"line":i=(t.x1+t.x2)/2,g=(t.y1+t.y2)/2;break;case"triangle":const b=I(t.points);i=b.x,g=b.y;break;case"quadraticCurve":i=(t.x1+t.x2)/2,g=(t.y1+t.y2)/2;break;case"cubicCurve":i=(t.x1+t.x2)/2,g=(t.y1+t.y2)/2;break;case"arcCurve":i=(t.x1+t.x2)/2,g=(t.y1+t.y2)/2;break;case"polygon":const $=h(t.points);i=$.x,g=$.y;break;case"star":i=t.cx,g=t.cy;break;case"heart":i=t.cx,g=t.cy;break;case"path":t.controlPoints&&t.controlPoints.length>0&&(i=t.controlPoints[0].x,g=t.controlPoints[0].y);break}k.value={startX:r.clientX,startY:r.clientY,shapeId:e,resizeDirection:"",controlPointIndex:-1,initialRotation:t.rotation||0,centerX:i,centerY:g}}function Xt(r,e,t){r.preventDefault(),r.stopPropagation(),ot.value=!0,d.value=e,k.value={startX:r.clientX,startY:r.clientY,shapeId:e,resizeDirection:"",controlPointIndex:t}}function de(r){if(!yt.value&&!ct.value&&!ft.value&&!ot.value)return;const e=F.value.find(g=>g.id===k.value.shapeId);if(!e)return;const t=r.clientX-k.value.startX,i=r.clientY-k.value.startY;if(yt.value)switch(e.type){case"rectangle":e.x+=t,e.y+=i;break;case"circle":e.cx+=t,e.cy+=i;break;case"ellipse":e.cx+=t,e.cy+=i;break;case"line":e.x1+=t,e.y1+=i,e.x2+=t,e.y2+=i;break;case"triangle":const g=e.points.split(" ");e.points=g.map($=>{const[C,M]=$.split(",").map(Number);return`${C+t},${M+i}`}).join(" ");break;case"polygon":const b=e.points.split(" ");e.points=b.map($=>{const[C,M]=$.split(",").map(Number);return`${C+t},${M+i}`}).join(" ");break;case"star":e.cx+=t,e.cy+=i;break;case"heart":e.cx+=t,e.cy+=i;break;case"path":e.controlPoints&&(e.controlPoints.forEach($=>{$.x+=t,$.y+=i}),Rt(e));break}else if(ct.value)switch(e.type){case"rectangle":k.value.resizeDirection==="se"&&(e.width+=t,e.height+=i,e.width<5&&(e.width=5),e.height<5&&(e.height=5));break;case"circle":k.value.resizeDirection==="e"&&(e.r+=t,e.r<5&&(e.r=5));break;case"ellipse":k.value.resizeDirection==="e"?(e.rx+=t,e.rx<5&&(e.rx=5)):k.value.resizeDirection==="s"&&(e.ry+=i,e.ry<5&&(e.ry=5));break;case"line":k.value.resizeDirection==="start"?(e.x1+=t,e.y1+=i):k.value.resizeDirection==="end"&&(e.x2+=t,e.y2+=i);break;case"quadraticCurve":k.value.resizeDirection==="start"?(e.x1+=t,e.y1+=i):k.value.resizeDirection==="end"?(e.x2+=t,e.y2+=i):k.value.resizeDirection==="control"&&(e.cx+=t,e.cy+=i);break;case"cubicCurve":k.value.resizeDirection==="start"?(e.x1+=t,e.y1+=i):k.value.resizeDirection==="end"?(e.x2+=t,e.y2+=i):k.value.resizeDirection==="control1"?(e.cx1+=t,e.cy1+=i):k.value.resizeDirection==="control2"&&(e.cx2+=t,e.cy2+=i);break;case"arcCurve":k.value.resizeDirection==="start"?(e.x1+=t,e.y1+=i):k.value.resizeDirection==="end"&&(e.x2+=t,e.y2+=i);break}else if(ft.value){const g=k.value.centerX||0,b=k.value.centerY||0,$=Math.atan2(k.value.startY-b,k.value.startX-g);let M=(Math.atan2(r.clientY-b,r.clientX-g)-$)*(180/Math.PI);M=(M+180)%360-180;const H=k.value.initialRotation||0;e.rotation=H+M,e.rotation=(e.rotation+360)%360}else if(ot.value&&e.type==="path"&&e.controlPoints){const g=e.controlPoints[k.value.controlPointIndex];g&&(g.x+=t,g.y+=i,Rt(e))}k.value.startX=r.clientX,k.value.startY=r.clientY,j()}function Rt(r){if(r.controlPoints&&r.controlPoints.length>=2){const e=r.controlPoints;let t=`M ${e[0].x} ${e[0].y}`;if(e.length>=3){t=`M ${e[0].x} ${e[0].y}`;for(let i=1;i<e.length;i++)if(i<e.length-1){const g=e[i],b=e[i+1],$=g.x,C=g.y,M=b.x,H=b.y;t+=` C ${$} ${C}, ${M} ${H}, ${b.x} ${b.y}`}}else t+=` L ${e[1].x} ${e[1].y}`;r.d=t}}function A(){(yt.value||ct.value||ft.value||ot.value)&&(yt.value=!1,ct.value=!1,ft.value=!1,ot.value=!1,at(),setTimeout(()=>{},10))}function jt(){if(!d.value)return;const r=F.value.findIndex(e=>e.id===d.value);r!==-1&&(F.value.splice(r,1),d.value=null,at(),j(),x(s("tools.svgEditor.messages.shapeDeleted")))}function S(){F.value=[],d.value=null,at(),j(),x(s("tools.svgEditor.messages.canvasCleared"))}function at(){W.value<z.value.length-1&&(z.value=z.value.slice(0,W.value+1)),z.value.push(JSON.parse(JSON.stringify(F.value))),W.value=z.value.length-1}function O(){W.value>0&&(W.value--,F.value=JSON.parse(JSON.stringify(z.value[W.value])),j())}function mt(){W.value<z.value.length-1&&(W.value++,F.value=JSON.parse(JSON.stringify(z.value[W.value])),j())}function j(){let r=`<svg width="${wt.value.width}" height="${wt.value.height}" xmlns="http://www.w3.org/2000/svg">
`;F.value.forEach(e=>{switch(e.type){case"rectangle":const t=e.fillOpacity===0?"none":e.fill,i=e.strokeOpacity===0?"none":e.stroke;r+=`  <rect x="${e.x}" y="${e.y}" width="${e.width}" height="${e.height}" fill="${t}" fill-opacity="${e.fillOpacity}" stroke="${i}" stroke-opacity="${e.strokeOpacity}" stroke-width="${e.strokeWidth}" transform="rotate(${e.rotation||0} ${e.x+e.width/2} ${e.y+e.height/2})" />
`;break;case"circle":const g=e.fillOpacity===0?"none":e.fill,b=e.strokeOpacity===0?"none":e.stroke;r+=`  <circle cx="${e.cx}" cy="${e.cy}" r="${e.r}" fill="${g}" fill-opacity="${e.fillOpacity}" stroke="${b}" stroke-opacity="${e.strokeOpacity}" stroke-width="${e.strokeWidth}" transform="rotate(${e.rotation||0} ${e.cx} ${e.cy})" />
`;break;case"ellipse":const $=e.fillOpacity===0?"none":e.fill,C=e.strokeOpacity===0?"none":e.stroke;r+=`  <ellipse cx="${e.cx}" cy="${e.cy}" rx="${e.rx}" ry="${e.ry}" fill="${$}" fill-opacity="${e.fillOpacity}" stroke="${C}" stroke-opacity="${e.strokeOpacity}" stroke-width="${e.strokeWidth}" transform="rotate(${e.rotation||0} ${e.cx} ${e.cy})" />
`;break;case"line":const M=e.strokeOpacity===0?"none":e.stroke;r+=`  <line x1="${e.x1}" y1="${e.y1}" x2="${e.x2}" y2="${e.y2}" stroke="${M}" stroke-opacity="${e.strokeOpacity}" stroke-width="${e.strokeWidth}" transform="rotate(${e.rotation||0} ${(e.x1+e.x2)/2} ${(e.y1+e.y2)/2})" />
`;break;case"triangle":const H=e.fillOpacity===0?"none":e.fill,bt=e.strokeOpacity===0?"none":e.stroke,kt=I(e.points);r+=`  <polygon points="${e.points}" fill="${H}" fill-opacity="${e.fillOpacity}" stroke="${bt}" stroke-opacity="${e.strokeOpacity}" stroke-width="${e.strokeWidth}" transform="rotate(${e.rotation||0} ${kt.x} ${kt.y})" />
`;break;case"quadraticCurve":const Jt=e.strokeOpacity===0?"none":e.stroke;r+=`  <path d="M ${e.x1} ${e.y1} Q ${e.cx} ${e.cy} ${e.x2} ${e.y2}" fill="none" stroke="${Jt}" stroke-opacity="${e.strokeOpacity}" stroke-width="${e.strokeWidth}" transform="rotate(${e.rotation||0} ${(e.x1+e.x2)/2} ${(e.y1+e.y2)/2})" />
`;break;case"cubicCurve":const Nt=e.strokeOpacity===0?"none":e.stroke;r+=`  <path d="M ${e.x1} ${e.y1} C ${e.cx1} ${e.cy1} ${e.cx2} ${e.cy2} ${e.x2} ${e.y2}" fill="none" stroke="${Nt}" stroke-opacity="${e.strokeOpacity}" stroke-width="${e.strokeWidth}" transform="rotate(${e.rotation||0} ${(e.x1+e.x2)/2} ${(e.y1+e.y2)/2})" />
`;break;case"arcCurve":const Qt=e.strokeOpacity===0?"none":e.stroke;r+=`  <path d="M ${e.x1} ${e.y1} A ${e.rx} ${e.ry} ${e.xAxisRotation} ${e.largeArcFlag} ${e.sweepFlag} ${e.x2} ${e.y2}" fill="none" stroke="${Qt}" stroke-opacity="${e.strokeOpacity}" stroke-width="${e.strokeWidth}" transform="rotate(${e.rotation||0} ${(e.x1+e.x2)/2} ${(e.y1+e.y2)/2})" />
`;break;case"polygon":const Et=e.fillOpacity===0?"none":e.fill,pt=e.strokeOpacity===0?"none":e.stroke,Z=h(e.points);r+=`  <polygon points="${e.points}" fill="${Et}" fill-opacity="${e.fillOpacity}" stroke="${pt}" stroke-opacity="${e.strokeOpacity}" stroke-width="${e.strokeWidth}" transform="rotate(${e.rotation||0} ${Z.x} ${Z.y})" />
`;break;case"star":const dt=e.fillOpacity===0?"none":e.fill,It=e.strokeOpacity===0?"none":e.stroke,Pt=tt(e);r+=`  <polygon points="${Pt}" fill="${dt}" fill-opacity="${e.fillOpacity}" stroke="${It}" stroke-opacity="${e.strokeOpacity}" stroke-width="${e.strokeWidth}" transform="rotate(${e.rotation||0} ${e.cx} ${e.cy})" />
`;break;case"heart":const ue=e.fillOpacity===0?"none":e.fill,$t=e.strokeOpacity===0?"none":e.stroke,Tt=gt(e);r+=`  <path d="${Tt}" fill="${ue}" fill-opacity="${e.fillOpacity}" stroke="${$t}" stroke-opacity="${e.strokeOpacity}" stroke-width="${e.strokeWidth}" transform="rotate(${e.rotation||0} ${e.cx} ${e.cy})" />
`;break;case"path":const ye=e.fillOpacity===0?"none":e.fill,xt=e.strokeOpacity===0?"none":e.stroke;r+=`  <path d="${e.d}" fill="${ye}" fill-opacity="${e.fillOpacity}" stroke="${xt}" stroke-opacity="${e.strokeOpacity}" stroke-width="${e.strokeWidth}" />
`;break}}),r+="</svg>",E.value=r}function Vt(r){E.value=r.code,x(s("tools.svgEditor.messages.tutorialLoaded"))}ro(()=>{Bt(),at()}),Ie(F,()=>{j()},{deep:!0}),Ie(p,()=>{p.value&&j()},{deep:!0});function qt(r){return r}function Kt(){p.value&&(p.value.fillOpacity=0,j())}function Zt(){p.value&&(p.value.strokeOpacity=0,j())}function h(r){const e=r.split(" ").map(g=>{const[b,$]=g.split(",").map(Number);return{x:b,y:$}}),t=e.reduce((g,b)=>g+b.x,0)/e.length,i=e.reduce((g,b)=>g+b.y,0)/e.length;return{x:t,y:i}}function Dt(r){return r}function tt(r){const e=[],t=r.outerRadius,i=r.innerRadius,g=r.cx,b=r.cy,$=r.points;for(let C=0;C<$*2;C++){const M=C%2===0?t:i,H=Math.PI/2+C*Math.PI/$,bt=g+M*Math.cos(H),kt=b-M*Math.sin(H);e.push(`${bt},${kt}`)}return e.join(" ")}function Lt(r){const e={...r,outerRadius:r.outerRadius+2,innerRadius:r.innerRadius+2};return tt(e)}function gt(r){const e=r.cx,t=r.cy,i=r.size;return`M ${e} ${t+i*.6}
          C ${e-i*.4} ${t+i*.2}, ${e-i*.6} ${t-i*.2}, ${e} ${t-i*.6}
          C ${e+i*.6} ${t-i*.2}, ${e+i*.4} ${t+i*.2}, ${e} ${t+i*.6}
          Z`}return(r,e)=>(a(),c("div",ho,[n("div",Co,[n("div",Ro,[n("h1",Do,"🎨 "+f(r.$t("tools.svgEditor.title")),1),n("p",Lo,f(r.$t("tools.svgEditor.description")),1),n("div",No,[n("h3",Io,f(r.$t("tools.svgEditor.howToUse.title")),1),n("ol",Po,[n("li",null,f(r.$t("tools.svgEditor.howToUse.step1")),1),n("li",null,f(r.$t("tools.svgEditor.howToUse.step2")),1),n("li",null,f(r.$t("tools.svgEditor.howToUse.step3")),1),n("li",null,f(r.$t("tools.svgEditor.howToUse.step4")),1)])])]),n("div",Uo,[n("div",Fo,[n("div",zo,[n("h3",Wo,f(r.$t("tools.svgEditor.editor.title")),1),n("div",Ho,[n("button",{onClick:Bt,class:"px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300"},f(r.$t("tools.svgEditor.editor.loadExample")),1),n("button",{onClick:ce,class:"px-3 py-1 bg-red-200 text-red-700 rounded text-sm hover:bg-red-300"},f(r.$t("tools.svgEditor.editor.clear")),1)])]),n("div",Go,[vt(n("textarea",{"onUpdate:modelValue":e[0]||(e[0]=t=>E.value=t),class:"w-full h-96 font-mono text-sm p-4 resize-none focus:outline-none",placeholder:r.$t("tools.svgEditor.editor.placeholder")},null,8,Yo),[[_t,E.value]])]),n("div",Bo,[n("div",Xo,f(r.$t("tools.svgEditor.editor.lines"))+": "+f(Q.value),1),n("button",{onClick:ae,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"},f(r.$t("tools.svgEditor.editor.copy")),1)])]),n("div",jo,[n("h3",Vo,f(r.$t("tools.svgEditor.preview.title")),1),n("div",qo,[E.value?(a(),c("div",{key:0,class:"w-full h-full flex items-center justify-center p-4",innerHTML:st.value},null,8,Ko)):(a(),c("div",Zo,[e[7]||(e[7]=n("div",{class:"text-4xl mb-4"},"🎨",-1)),n("p",null,f(r.$t("tools.svgEditor.preview.empty")),1)]))]),n("div",Jo,[n("div",Qo,f(r.$t("tools.svgEditor.preview.dimensions"))+": "+f(St.value)+" × "+f(Yt.value),1),n("button",{onClick:L,class:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"},f(r.$t("tools.svgEditor.preview.download")),1)])])]),n("div",ti,[n("h3",ei,f(r.$t("tools.svgEditor.visualEditor.title")),1),n("div",oi,[n("div",ii,[n("h4",ri,f(r.$t("tools.svgEditor.visualEditor.shapes")),1),n("div",ni,[(a(),c(te,null,ee(ne,t=>n("button",{key:t.type,onClick:i=>ht(t.type),class:"p-3 border border-gray-300 rounded-lg hover:bg-gray-100 flex flex-col items-center",title:t.name},[n("div",si,f(t.icon),1),n("div",ci,f(t.name),1)],8,li)),64))]),n("h4",ai,f(r.$t("tools.svgEditor.visualEditor.history")),1),n("div",di,[n("button",{onClick:O,disabled:W.value<=0,class:"w-full p-2 border border-gray-300 rounded-lg hover:bg-gray-100 text-left text-sm disabled:opacity-50"}," ↶ "+f(r.$t("tools.svgEditor.visualEditor.undo")),9,ui),n("button",{onClick:mt,disabled:W.value>=z.value.length-1,class:"w-full p-2 border border-gray-300 rounded-lg hover:bg-gray-100 text-left text-sm disabled:opacity-50"}," ↷ "+f(r.$t("tools.svgEditor.visualEditor.redo")),9,yi)]),n("h4",fi,f(r.$t("tools.svgEditor.visualEditor.tools")),1),n("div",mi,[n("button",{onClick:jt,disabled:!d.value,class:"w-full p-2 border border-gray-300 rounded-lg hover:bg-gray-100 text-left text-sm disabled:opacity-50"}," 🗑️ "+f(r.$t("tools.svgEditor.visualEditor.delete")),9,gi),n("button",{onClick:S,class:"w-full p-2 border border-gray-300 rounded-lg hover:bg-gray-100 text-left text-sm"}," 🧹 "+f(r.$t("tools.svgEditor.visualEditor.clear")),1)])]),n("div",ki,[(a(),c("svg",{ref_key:"svgCanvas",ref:ut,width:wt.value.width,height:wt.value.height,class:"bg-white border border-gray-200",onClick:Ct,onMousemove:de,onMouseup:A,onMouseleave:A},[e[8]||(e[8]=n("defs",null,[n("pattern",{id:"grid",width:"20",height:"20",patternUnits:"userSpaceOnUse"},[n("path",{d:"M 20 0 L 0 0 0 20",fill:"none",stroke:"#e5e7eb","stroke-width":"0.5"})])],-1)),e[9]||(e[9]=n("rect",{width:"100%",height:"100%",fill:"url(#grid)"},null,-1)),(a(!0),c(te,null,ee(F.value,t=>(a(),c("g",{key:t.id},[t.type==="rectangle"?(a(),c("g",vi,[n("rect",{x:t.x,y:t.y,width:t.width,height:t.height,fill:t.fillOpacity===0?"none":t.fill,stroke:t.strokeOpacity===0?"none":t.stroke,"stroke-width":t.strokeWidth,"fill-opacity":t.fillOpacity,"stroke-opacity":t.strokeOpacity,transform:`rotate(${t.rotation||0} ${t.x+t.width/2} ${t.y+t.height/2})`,onMousedown:i=>X(i,t.id),class:ke({"cursor-move":!ct.value})},null,42,_i),d.value===t.id?(a(),c("rect",{key:0,x:t.x-2,y:t.y-2,width:t.width+4,height:t.height+4,fill:"none",stroke:"blue","stroke-width":"1","stroke-dasharray":"5,5",transform:`rotate(${t.rotation||0} ${t.x+t.width/2} ${t.y+t.height/2})`},null,8,wi)):m("",!0),d.value===t.id?(a(),c("circle",{key:1,cx:t.x+t.width,cy:t.y+t.height,r:"5",fill:"white",stroke:"blue","stroke-width":"1",onMousedown:i=>N(i,t.id,"se"),class:"cursor-se-resize"},null,40,bi)):m("",!0),d.value===t.id?(a(),c("line",{key:2,x1:t.x+t.width/2,y1:t.y-30,x2:t.x+t.width/2,y2:t.y,stroke:"red","stroke-width":"1","stroke-dasharray":"3,3"},null,8,Ei)):m("",!0),d.value===t.id?(a(),c("circle",{key:3,cx:t.x+t.width/2,cy:t.y-30,r:"6",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>q(i,t.id),class:"cursor-move"},null,40,pi)):m("",!0)])):t.type==="circle"?(a(),c("g",$i,[n("circle",{cx:t.cx,cy:t.cy,r:t.r,fill:t.fillOpacity===0?"none":t.fill,stroke:t.strokeOpacity===0?"none":t.stroke,"stroke-width":t.strokeWidth,"fill-opacity":t.fillOpacity,"stroke-opacity":t.strokeOpacity,transform:`rotate(${t.rotation||0} ${t.cx} ${t.cy})`,onMousedown:i=>X(i,t.id),class:"cursor-move"},null,40,Ti),d.value===t.id?(a(),c("circle",{key:0,cx:t.cx,cy:t.cy,r:t.r+2,fill:"none",stroke:"blue","stroke-width":"1","stroke-dasharray":"5,5",transform:`rotate(${t.rotation||0} ${t.cx} ${t.cy})`},null,8,Oi)):m("",!0),d.value===t.id?(a(),c("circle",{key:1,cx:t.cx+t.r,cy:t.cy,r:"5",fill:"white",stroke:"blue","stroke-width":"1",onMousedown:i=>N(i,t.id,"e"),class:"cursor-ew-resize"},null,40,Ai)):m("",!0),d.value===t.id?(a(),c("line",{key:2,x1:t.cx,y1:t.cy-t.r-30,x2:t.cx,y2:t.cy-t.r,stroke:"red","stroke-width":"1","stroke-dasharray":"3,3"},null,8,Mi)):m("",!0),d.value===t.id?(a(),c("circle",{key:3,cx:t.cx,cy:t.cy-t.r-30,r:"6",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>q(i,t.id),class:"cursor-move"},null,40,Si)):m("",!0)])):t.type==="ellipse"?(a(),c("g",hi,[n("ellipse",{cx:t.cx,cy:t.cy,rx:t.rx,ry:t.ry,fill:t.fillOpacity===0?"none":t.fill,stroke:t.strokeOpacity===0?"none":t.stroke,"stroke-width":t.strokeWidth,"fill-opacity":t.fillOpacity,"stroke-opacity":t.strokeOpacity,transform:`rotate(${t.rotation||0} ${t.cx} ${t.cy})`,onMousedown:i=>X(i,t.id),class:"cursor-move"},null,40,Ci),d.value===t.id?(a(),c("ellipse",{key:0,cx:t.cx,cy:t.cy,rx:t.rx+2,ry:t.ry+2,fill:"none",stroke:"blue","stroke-width":"1","stroke-dasharray":"5,5",transform:`rotate(${t.rotation||0} ${t.cx} ${t.cy})`},null,8,Ri)):m("",!0),d.value===t.id?(a(),c("circle",{key:1,cx:t.cx+t.rx,cy:t.cy,r:"5",fill:"white",stroke:"blue","stroke-width":"1",onMousedown:i=>N(i,t.id,"e"),class:"cursor-ew-resize"},null,40,Di)):m("",!0),d.value===t.id?(a(),c("circle",{key:2,cx:t.cx,cy:t.cy+t.ry,r:"5",fill:"white",stroke:"blue","stroke-width":"1",onMousedown:i=>N(i,t.id,"s"),class:"cursor-ns-resize"},null,40,Li)):m("",!0),d.value===t.id?(a(),c("line",{key:3,x1:t.cx,y1:t.cy-Math.max(t.rx,t.ry)-30,x2:t.cx,y2:t.cy-Math.max(t.rx,t.ry),stroke:"red","stroke-width":"1","stroke-dasharray":"3,3"},null,8,Ni)):m("",!0),d.value===t.id?(a(),c("circle",{key:4,cx:t.cx,cy:t.cy-Math.max(t.rx,t.ry)-30,r:"6",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>q(i,t.id),class:"cursor-move"},null,40,Ii)):m("",!0)])):t.type==="line"?(a(),c("g",Pi,[n("line",{x1:t.x1,y1:t.y1,x2:t.x2,y2:t.y2,stroke:"transparent","stroke-width":Math.max(12,t.strokeWidth+6),onMousedown:i=>X(i,t.id),class:"cursor-move"},null,40,Ui),n("line",{x1:t.x1,y1:t.y1,x2:t.x2,y2:t.y2,stroke:t.strokeOpacity===0?"none":t.stroke,"stroke-width":t.strokeWidth,"stroke-opacity":t.strokeOpacity,style:{"pointer-events":"none"}},null,8,Fi),d.value===t.id?(a(),c("line",{key:0,x1:t.x1,y1:t.y1,x2:t.x2,y2:t.y2,stroke:"blue","stroke-width":"3","stroke-opacity":"0.3"},null,8,zi)):m("",!0),d.value===t.id?(a(),c("circle",{key:1,cx:t.x1,cy:t.y1,r:"5",fill:"white",stroke:"blue","stroke-width":"1",onMousedown:i=>N(i,t.id,"start"),class:"cursor-move"},null,40,Wi)):m("",!0),d.value===t.id?(a(),c("circle",{key:2,cx:t.x2,cy:t.y2,r:"5",fill:"white",stroke:"blue","stroke-width":"1",onMousedown:i=>N(i,t.id,"end"),class:"cursor-move"},null,40,Hi)):m("",!0),d.value===t.id?(a(),c("line",{key:3,x1:(t.x1+t.x2)/2,y1:(t.y1+t.y2)/2-30,x2:(t.x1+t.x2)/2,y2:(t.y1+t.y2)/2,stroke:"red","stroke-width":"1","stroke-dasharray":"3,3"},null,8,Gi)):m("",!0),d.value===t.id?(a(),c("circle",{key:4,cx:(t.x1+t.x2)/2,cy:(t.y1+t.y2)/2-30,r:"6",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>q(i,t.id),class:"cursor-move"},null,40,Yi)):m("",!0)])):t.type==="triangle"?(a(),c("g",Bi,[n("polygon",{points:t.points,fill:t.fillOpacity===0?"none":t.fill,stroke:t.strokeOpacity===0?"none":t.stroke,"stroke-width":t.strokeWidth,"fill-opacity":t.fillOpacity,"stroke-opacity":t.strokeOpacity,transform:`rotate(${t.rotation||0} ${I(t.points).x} ${I(t.points).y})`,onMousedown:i=>X(i,t.id),class:"cursor-move"},null,40,Xi),d.value===t.id?(a(),c("polygon",{key:0,points:t.points,fill:"none",stroke:"blue","stroke-width":"1","stroke-dasharray":"5,5",transform:`rotate(${t.rotation||0} ${I(t.points).x} ${I(t.points).y})`},null,8,ji)):m("",!0),d.value===t.id?(a(),c("line",{key:1,x1:I(t.points).x,y1:I(t.points).y-30,x2:I(t.points).x,y2:I(t.points).y,stroke:"red","stroke-width":"1","stroke-dasharray":"3,3"},null,8,Vi)):m("",!0),d.value===t.id?(a(),c("circle",{key:2,cx:I(t.points).x,cy:I(t.points).y-30,r:"6",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>q(i,t.id),class:"cursor-move"},null,40,qi)):m("",!0)])):t.type==="path"?(a(),c("g",Ki,[n("path",{d:t.d,fill:t.fillOpacity===0?"none":t.fill,stroke:t.strokeOpacity===0?"none":t.stroke,"stroke-width":t.strokeWidth,"fill-opacity":t.fillOpacity,"stroke-opacity":t.strokeOpacity,onMousedown:i=>X(i,t.id),class:"cursor-move"},null,40,Zi),d.value===t.id?(a(),c("path",{key:0,d:t.d,fill:"none",stroke:"blue","stroke-width":"2","stroke-dasharray":"5,5","stroke-opacity":"0.7"},null,8,Ji)):m("",!0),(a(!0),c(te,null,ee(le.value(t),(i,g)=>(a(),c("circle",{key:g,cx:i.x,cy:i.y,r:"4",fill:"white",stroke:"red","stroke-width":"1",onMousedown:b=>Xt(b,t.id,g),class:"cursor-move"},null,40,Qi))),128))])):t.type==="polygon"?(a(),c("g",tr,[n("polygon",{points:t.points,fill:t.fillOpacity===0?"none":t.fill,stroke:t.strokeOpacity===0?"none":t.stroke,"stroke-width":t.strokeWidth,"fill-opacity":t.fillOpacity,"stroke-opacity":t.strokeOpacity,transform:`rotate(${t.rotation||0} ${h(t.points).x} ${h(t.points).y})`,onMousedown:i=>X(i,t.id),class:"cursor-move"},null,40,er),d.value===t.id?(a(),c("polygon",{key:0,points:t.points,fill:"none",stroke:"blue","stroke-width":"1","stroke-dasharray":"5,5",transform:`rotate(${t.rotation||0} ${h(t.points).x} ${h(t.points).y})`},null,8,or)):m("",!0),d.value===t.id?(a(),c("line",{key:1,x1:h(t.points).x,y1:h(t.points).y-30,x2:h(t.points).x,y2:h(t.points).y,stroke:"red","stroke-width":"1","stroke-dasharray":"3,3"},null,8,ir)):m("",!0),d.value===t.id?(a(),c("circle",{key:2,cx:h(t.points).x,cy:h(t.points).y-30,r:"6",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>q(i,t.id),class:"cursor-move"},null,40,rr)):m("",!0)])):t.type==="star"?(a(),c("g",nr,[n("polygon",{points:tt(t),fill:t.fillOpacity===0?"none":t.fill,stroke:t.strokeOpacity===0?"none":t.stroke,"stroke-width":t.strokeWidth,"fill-opacity":t.fillOpacity,"stroke-opacity":t.strokeOpacity,transform:`rotate(${t.rotation||0} ${t.cx} ${t.cy})`,onMousedown:i=>X(i,t.id),class:"cursor-move"},null,40,lr),d.value===t.id?(a(),c("polygon",{key:0,points:Lt(t),fill:"none",stroke:"blue","stroke-width":"1","stroke-dasharray":"5,5",transform:`rotate(${t.rotation||0} ${t.cx} ${t.cy})`},null,8,sr)):m("",!0),d.value===t.id?(a(),c("line",{key:1,x1:t.cx,y1:t.cy-Math.max(t.outerRadius,t.innerRadius)-30,x2:t.cx,y2:t.cy-Math.max(t.outerRadius,t.innerRadius),stroke:"red","stroke-width":"1","stroke-dasharray":"3,3"},null,8,cr)):m("",!0),d.value===t.id?(a(),c("circle",{key:2,cx:t.cx,cy:t.cy-Math.max(t.outerRadius,t.innerRadius)-30,r:"6",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>q(i,t.id),class:"cursor-move"},null,40,ar)):m("",!0)])):t.type==="heart"?(a(),c("g",dr,[n("path",{d:gt(t),fill:t.fillOpacity===0?"none":t.fill,stroke:t.strokeOpacity===0?"none":t.stroke,"stroke-width":t.strokeWidth,"fill-opacity":t.fillOpacity,"stroke-opacity":t.strokeOpacity,transform:`rotate(${t.rotation||0} ${t.cx} ${t.cy})`,onMousedown:i=>X(i,t.id),class:"cursor-move"},null,40,ur),d.value===t.id?(a(),c("path",{key:0,d:gt(t),fill:"none",stroke:"blue","stroke-width":"2","stroke-dasharray":"5,5","stroke-opacity":"0.7",transform:`rotate(${t.rotation||0} ${t.cx} ${t.cy}) scale(1.1)`},null,8,yr)):m("",!0),d.value===t.id?(a(),c("line",{key:1,x1:t.cx,y1:t.cy-t.size-30,x2:t.cx,y2:t.cy-t.size,stroke:"red","stroke-width":"1","stroke-dasharray":"3,3"},null,8,fr)):m("",!0),d.value===t.id?(a(),c("circle",{key:2,cx:t.cx,cy:t.cy-t.size-30,r:"6",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>q(i,t.id),class:"cursor-move"},null,40,mr)):m("",!0)])):t.type==="quadraticCurve"?(a(),c("g",gr,[n("path",{d:`M ${t.x1} ${t.y1} Q ${t.cx} ${t.cy} ${t.x2} ${t.y2}`,stroke:"transparent","stroke-width":Math.max(12,t.strokeWidth+6),fill:"none",onMousedown:i=>X(i,t.id),class:"cursor-move"},null,40,kr),n("path",{d:`M ${t.x1} ${t.y1} Q ${t.cx} ${t.cy} ${t.x2} ${t.y2}`,stroke:t.strokeOpacity===0?"none":t.stroke,"stroke-width":t.strokeWidth,"stroke-opacity":t.strokeOpacity,fill:"none",style:{"pointer-events":"none"}},null,8,xr),d.value===t.id?(a(),c("path",{key:0,d:`M ${t.x1} ${t.y1} Q ${t.cx} ${t.cy} ${t.x2} ${t.y2}`,stroke:"blue","stroke-width":"3","stroke-opacity":"0.3",fill:"none"},null,8,vr)):m("",!0),d.value===t.id?(a(),c("circle",{key:1,cx:t.cx,cy:t.cy,r:"5",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>N(i,t.id,"control"),class:"cursor-move"},null,40,_r)):m("",!0),d.value===t.id?(a(),c("circle",{key:2,cx:t.x1,cy:t.y1,r:"5",fill:"white",stroke:"blue","stroke-width":"1",onMousedown:i=>N(i,t.id,"start"),class:"cursor-move"},null,40,wr)):m("",!0),d.value===t.id?(a(),c("circle",{key:3,cx:t.x2,cy:t.y2,r:"5",fill:"white",stroke:"blue","stroke-width":"1",onMousedown:i=>N(i,t.id,"end"),class:"cursor-move"},null,40,br)):m("",!0),d.value===t.id?(a(),c("line",{key:4,x1:(t.x1+t.x2)/2,y1:(t.y1+t.y2)/2-30,x2:(t.x1+t.x2)/2,y2:(t.y1+t.y2)/2,stroke:"red","stroke-width":"1","stroke-dasharray":"3,3"},null,8,Er)):m("",!0),d.value===t.id?(a(),c("circle",{key:5,cx:(t.x1+t.x2)/2,cy:(t.y1+t.y2)/2-30,r:"6",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>q(i,t.id),class:"cursor-move"},null,40,pr)):m("",!0)])):t.type==="cubicCurve"?(a(),c("g",$r,[n("path",{d:`M ${t.x1} ${t.y1} C ${t.cx1} ${t.cy1} ${t.cx2} ${t.cy2} ${t.x2} ${t.y2}`,stroke:"transparent","stroke-width":Math.max(12,t.strokeWidth+6),fill:"none",onMousedown:i=>X(i,t.id),class:"cursor-move"},null,40,Tr),n("path",{d:`M ${t.x1} ${t.y1} C ${t.cx1} ${t.cy1} ${t.cx2} ${t.cy2} ${t.x2} ${t.y2}`,stroke:t.strokeOpacity===0?"none":t.stroke,"stroke-width":t.strokeWidth,"stroke-opacity":t.strokeOpacity,fill:"none",style:{"pointer-events":"none"}},null,8,Or),d.value===t.id?(a(),c("path",{key:0,d:`M ${t.x1} ${t.y1} C ${t.cx1} ${t.cy1} ${t.cx2} ${t.cy2} ${t.x2} ${t.y2}`,stroke:"blue","stroke-width":"3","stroke-opacity":"0.3",fill:"none"},null,8,Ar)):m("",!0),d.value===t.id?(a(),c("circle",{key:1,cx:t.cx1,cy:t.cy1,r:"5",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>N(i,t.id,"control1"),class:"cursor-move"},null,40,Mr)):m("",!0),d.value===t.id?(a(),c("circle",{key:2,cx:t.cx2,cy:t.cy2,r:"5",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>N(i,t.id,"control2"),class:"cursor-move"},null,40,Sr)):m("",!0),d.value===t.id?(a(),c("circle",{key:3,cx:t.x1,cy:t.y1,r:"5",fill:"white",stroke:"blue","stroke-width":"1",onMousedown:i=>N(i,t.id,"start"),class:"cursor-move"},null,40,hr)):m("",!0),d.value===t.id?(a(),c("circle",{key:4,cx:t.x2,cy:t.y2,r:"5",fill:"white",stroke:"blue","stroke-width":"1",onMousedown:i=>N(i,t.id,"end"),class:"cursor-move"},null,40,Cr)):m("",!0),d.value===t.id?(a(),c("line",{key:5,x1:(t.x1+t.x2)/2,y1:(t.y1+t.y2)/2-30,x2:(t.x1+t.x2)/2,y2:(t.y1+t.y2)/2,stroke:"red","stroke-width":"1","stroke-dasharray":"3,3"},null,8,Rr)):m("",!0),d.value===t.id?(a(),c("circle",{key:6,cx:(t.x1+t.x2)/2,cy:(t.y1+t.y2)/2-30,r:"6",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>q(i,t.id),class:"cursor-move"},null,40,Dr)):m("",!0)])):t.type==="arcCurve"?(a(),c("g",Lr,[n("path",{d:`M ${t.x1} ${t.y1} A ${t.rx} ${t.ry} ${t.xAxisRotation} ${t.largeArcFlag} ${t.sweepFlag} ${t.x2} ${t.y2}`,stroke:"transparent","stroke-width":Math.max(12,t.strokeWidth+6),fill:"none",onMousedown:i=>X(i,t.id),class:"cursor-move"},null,40,Nr),n("path",{d:`M ${t.x1} ${t.y1} A ${t.rx} ${t.ry} ${t.xAxisRotation} ${t.largeArcFlag} ${t.sweepFlag} ${t.x2} ${t.y2}`,stroke:t.strokeOpacity===0?"none":t.stroke,"stroke-width":t.strokeWidth,"stroke-opacity":t.strokeOpacity,fill:"none",style:{"pointer-events":"none"}},null,8,Ir),d.value===t.id?(a(),c("path",{key:0,d:`M ${t.x1} ${t.y1} A ${t.rx} ${t.ry} ${t.xAxisRotation} ${t.largeArcFlag} ${t.sweepFlag} ${t.x2} ${t.y2}`,stroke:"blue","stroke-width":"3","stroke-opacity":"0.3",fill:"none"},null,8,Pr)):m("",!0),d.value===t.id?(a(),c("circle",{key:1,cx:t.x1,cy:t.y1,r:"5",fill:"white",stroke:"blue","stroke-width":"1",onMousedown:i=>N(i,t.id,"start"),class:"cursor-move"},null,40,Ur)):m("",!0),d.value===t.id?(a(),c("circle",{key:2,cx:t.x2,cy:t.y2,r:"5",fill:"white",stroke:"blue","stroke-width":"1",onMousedown:i=>N(i,t.id,"end"),class:"cursor-move"},null,40,Fr)):m("",!0),d.value===t.id?(a(),c("line",{key:3,x1:(t.x1+t.x2)/2,y1:(t.y1+t.y2)/2-30,x2:(t.x1+t.x2)/2,y2:(t.y1+t.y2)/2,stroke:"red","stroke-width":"1","stroke-dasharray":"3,3"},null,8,zr)):m("",!0),d.value===t.id?(a(),c("circle",{key:4,cx:(t.x1+t.x2)/2,cy:(t.y1+t.y2)/2-30,r:"6",fill:"white",stroke:"red","stroke-width":"1",onMousedown:i=>q(i,t.id),class:"cursor-move"},null,40,Wr)):m("",!0)])):m("",!0)]))),128))],40,xi))]),n("div",Hr,[n("h4",Gr,f(r.$t("tools.svgEditor.visualEditor.properties")),1),p.value?(a(),c("div",Yr,[n("div",null,[n("label",Br,f(r.$t("tools.svgEditor.visualEditor.fill")),1),n("div",Xr,[vt(n("input",{"onUpdate:modelValue":e[1]||(e[1]=t=>p.value.fill=t),type:"color",class:"w-3/4 h-10 border border-gray-300 rounded"},null,512),[[_t,p.value.fill]]),n("button",{onClick:Kt,class:ke(["px-2 py-1 text-xs bg-gray-200 rounded hover:bg-gray-300",{"ring-2 ring-blue-500":p.value.fillOpacity===0}])},f(r.$t("tools.svgEditor.visualEditor.transparent")),3)]),vt(n("input",{"onUpdate:modelValue":e[2]||(e[2]=t=>p.value.fillOpacity=t),type:"range",min:"0",max:"1",step:"0.01",class:"w-full mt-1"},null,512),[[_t,p.value.fillOpacity]]),n("div",jr,f(Math.round(p.value.fillOpacity*100))+"% ",1)]),n("div",null,[n("label",Vr,f(r.$t("tools.svgEditor.visualEditor.stroke")),1),n("div",qr,[vt(n("input",{"onUpdate:modelValue":e[3]||(e[3]=t=>p.value.stroke=t),type:"color",class:"w-3/4 h-10 border border-gray-300 rounded"},null,512),[[_t,p.value.stroke]]),n("button",{onClick:Zt,class:ke(["px-2 py-1 text-xs bg-gray-200 rounded hover:bg-gray-300",{"ring-2 ring-blue-500":p.value.strokeOpacity===0}])},f(r.$t("tools.svgEditor.visualEditor.transparent")),3)]),vt(n("input",{"onUpdate:modelValue":e[4]||(e[4]=t=>p.value.strokeOpacity=t),type:"range",min:"0",max:"1",step:"0.01",class:"w-full mt-1"},null,512),[[_t,p.value.strokeOpacity]]),n("div",Kr,f(Math.round(p.value.strokeOpacity*100))+"% ",1)]),n("div",null,[n("label",Zr,f(r.$t("tools.svgEditor.visualEditor.strokeWidth")),1),vt(n("input",{"onUpdate:modelValue":e[5]||(e[5]=t=>p.value.strokeWidth=t),type:"range",min:"0",max:"10",step:"0.5",class:"w-full"},null,512),[[_t,p.value.strokeWidth,void 0,{number:!0}]]),n("div",Jr,f(p.value.strokeWidth),1)]),n("div",null,[n("label",Qr,f(r.$t("tools.svgEditor.visualEditor.rotation")),1),vt(n("input",{"onUpdate:modelValue":e[6]||(e[6]=t=>p.value.rotation=t),type:"range",min:"0",max:"360",class:"w-full"},null,512),[[_t,p.value.rotation,void 0,{number:!0}]]),n("div",tn,f(p.value.rotation)+"°",1)])])):(a(),c("div",en,f(r.$t("tools.svgEditor.visualEditor.noSelection")),1))])])]),n("div",on,[n("h3",rn,f(r.$t("tools.svgEditor.tutorials.title")),1),n("div",nn,[(a(!0),c(te,null,ee(se.value,t=>(a(),c("div",{key:t.id,class:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"},[n("h4",ln,f(t.title),1),n("p",sn,f(t.description),1),n("button",{onClick:i=>Vt(t),class:"text-blue-600 hover:text-blue-800 text-sm font-medium"},f(r.$t("tools.svgEditor.tutorials.viewTutorial")),9,cn)]))),128))])]),n("div",an,[n("div",dn,[e[10]||(e[10]=n("div",{class:"flex-shrink-0"},[n("svg",{class:"h-5 w-5 text-yellow-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor"},[n("path",{"fill-rule":"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])],-1)),n("div",un,[n("h3",yn,f(r.$t("tools.svgEditor.tips.title")),1),n("div",fn,[n("ul",mn,[n("li",null,f(r.$t("tools.svgEditor.tips.tip1")),1),n("li",null,f(r.$t("tools.svgEditor.tips.tip2")),1),n("li",null,f(r.$t("tools.svgEditor.tips.tip3")),1),n("li",null,f(r.$t("tools.svgEditor.tips.tip4")),1)])])])])])])]))}}),vn=no(gn,[["__scopeId","data-v-a8a63acd"]]);export{vn as default};
