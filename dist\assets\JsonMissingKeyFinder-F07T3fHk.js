import{d as R,u as C,r as f,P as S,Q as O,o as a,a as e,e as v,t,g as V,j as _,c as r,f as y,F as A,k as N}from"./index-CkZTMFXG.js";import{u as E}from"./useToast-virEbLJw.js";import{_ as J}from"./ToolLayout.vue_vue_type_script_setup_true_lang-BsMmX7pX.js";const U={class:"grid lg:grid-cols-2 gap-6"},B={class:"glass rounded-xl border border-slate-700/30 p-6"},T={class:"flex items-center justify-between mb-4"},L={class:"text-lg font-semibold text-slate-100"},z={class:"flex space-x-2"},I=["placeholder"],W={class:"mt-4 space-y-4"},D={class:"font-medium text-slate-100"},P={class:"space-y-3"},q={class:"flex items-center cursor-pointer"},Q={class:"text-slate-300"},G={class:"flex items-center cursor-pointer"},H={class:"text-slate-300"},X={class:"flex items-center cursor-pointer"},Y={class:"text-slate-300"},Z=["disabled"],ee={class:"glass rounded-xl border border-slate-700/30 p-6"},se={class:"flex items-center justify-between mb-4"},te={class:"text-lg font-semibold text-slate-100"},oe={class:"flex space-x-2"},ne={key:0,class:"h-80 flex items-center justify-center text-slate-400 border-2 border-dashed border-slate-700/50 rounded-lg"},le={class:"text-center"},ie={key:1,class:"h-80 flex items-center justify-center"},ae={class:"text-center text-red-400"},re={class:"font-medium"},de={class:"text-sm mt-1"},ue={key:2,class:"space-y-4 max-h-80 overflow-y-auto"},ce={class:"bg-primary-500/10 border border-primary-500/30 rounded-lg p-4"},me={class:"font-medium text-primary-400 mb-2"},pe={class:"text-sm text-primary-300 space-y-1"},ye={key:0},ge={class:"font-medium text-slate-100 mb-2"},fe={class:"space-y-2 max-h-60 overflow-y-auto"},ve={class:"font-medium text-amber-400"},be={class:"text-sm text-amber-300 mt-1"},Ke=R({__name:"JsonMissingKeyFinder",setup(he){const{t:b}=C(),{success:K,copySuccess:$}=E(),d=f(""),u=f(""),g=f(!1),n=f(null),m=f({ignoreNullValues:!1,deepAnalysis:!1,caseSensitive:!1});function k(){const s=[{id:1,name:"John",age:30},{id:2,name:"Jane"},{id:3,age:25,email:"<EMAIL>"},{name:"Bob",email:"<EMAIL>"}];d.value=JSON.stringify(s,null,2),p()}function M(){d.value="",u.value="",g.value=!1,n.value=null}function p(){if(u.value="",n.value=null,!d.value.trim()){g.value=!1;return}try{const s=JSON.parse(d.value);if(!Array.isArray(s))throw new Error(b("tools.jsonMissingKeyFinder.errors.invalidArray"));g.value=!0;const o=new Set;s.forEach(c=>{typeof c=="object"&&c!==null&&Object.keys(c).forEach(h=>{o.add(h)})});const l=[];let i=0;s.forEach(c=>{if(typeof c=="object"&&c!==null){const h=Object.keys(c),x=[];o.forEach(j=>{h.includes(j)||x.push(j)}),x.length>0&&i++,l.push({missingKeys:x})}}),n.value={totalObjects:s.length,allKeys:Array.from(o),keyStatistics:{},missingKeysReport:l,objectsWithMissingKeys:i}}catch(s){u.value=b("tools.jsonMissingKeyFinder.errors.invalidJson")+" "+s.message,g.value=!1}}function w(){if(n.value){const s=JSON.stringify(n.value,null,2);navigator.clipboard.writeText(s),$()}}function F(){if(n.value){const s=JSON.stringify(n.value,null,2),o=new Blob([s],{type:"application/json"}),l=URL.createObjectURL(o),i=document.createElement("a");i.href=l,i.download="missing-keys-analysis.json",document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(l),K(b("toast.downloadSuccess"))}}return(s,o)=>(a(),S(J,{title:s.$t("tools.jsonMissingKeyFinder.title"),description:s.$t("tools.jsonMissingKeyFinder.description"),icon:"🔍",features:[s.$t("tools.jsonMissingKeyFinder.features.keyAnalysis.title"),s.$t("tools.jsonMissingKeyFinder.features.detailedReport.title"),s.$t("tools.jsonMissingKeyFinder.features.exportResults.title")]},{default:O(()=>[e("div",U,[e("div",B,[e("div",T,[e("h3",L,t(s.$t("tools.jsonMissingKeyFinder.inputTitle")),1),e("div",z,[e("button",{onClick:k,class:"px-3 py-1 text-sm bg-slate-800/50 text-slate-300 rounded-lg hover:bg-slate-700/50 hover:text-white transition-all duration-200 cursor-pointer hover-lift"},t(s.$t("common.loadExample")),1),e("button",{onClick:M,class:"px-3 py-1 text-sm bg-slate-800/50 text-slate-300 rounded-lg hover:bg-slate-700/50 hover:text-white transition-all duration-200 cursor-pointer hover-lift"},t(s.$t("common.clear")),1)])]),v(e("textarea",{"onUpdate:modelValue":o[0]||(o[0]=l=>d.value=l),placeholder:s.$t("tools.jsonMissingKeyFinder.inputPlaceholder"),class:"w-full h-64 p-4 bg-slate-800/50 border border-slate-700/50 rounded-lg font-mono text-sm text-slate-100 resize-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200",onInput:p},null,40,I),[[V,d.value]]),e("div",W,[e("h4",D,t(s.$t("tools.jsonMissingKeyFinder.analysisOptions")),1),e("div",P,[e("label",q,[v(e("input",{"onUpdate:modelValue":o[1]||(o[1]=l=>m.value.ignoreNullValues=l),onChange:p,type:"checkbox",class:"mr-3 h-4 w-4 rounded border-slate-600 bg-slate-800 text-primary-500 focus:ring-primary-500 focus:ring-offset-0 cursor-pointer"},null,544),[[_,m.value.ignoreNullValues]]),e("span",Q,t(s.$t("tools.jsonMissingKeyFinder.ignoreNullValues")),1)]),e("label",G,[v(e("input",{"onUpdate:modelValue":o[2]||(o[2]=l=>m.value.deepAnalysis=l),onChange:p,type:"checkbox",class:"mr-3 h-4 w-4 rounded border-slate-600 bg-slate-800 text-primary-500 focus:ring-primary-500 focus:ring-offset-0 cursor-pointer"},null,544),[[_,m.value.deepAnalysis]]),e("span",H,t(s.$t("tools.jsonMissingKeyFinder.deepAnalysis")),1)]),e("label",X,[v(e("input",{"onUpdate:modelValue":o[3]||(o[3]=l=>m.value.caseSensitive=l),onChange:p,type:"checkbox",class:"mr-3 h-4 w-4 rounded border-slate-600 bg-slate-800 text-primary-500 focus:ring-primary-500 focus:ring-offset-0 cursor-pointer"},null,544),[[_,m.value.caseSensitive]]),e("span",Y,t(s.$t("tools.jsonMissingKeyFinder.caseSensitive")),1)])])]),e("button",{onClick:p,disabled:!d.value.trim()||!g.value,class:"w-full mt-4 px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:bg-slate-700 disabled:cursor-not-allowed transition-all duration-200 font-medium hover-lift"},t(s.$t("tools.jsonMissingKeyFinder.analyzeButton")),9,Z)]),e("div",ee,[e("div",se,[e("h3",te,t(s.$t("tools.jsonMissingKeyFinder.analysisResults")),1),e("div",oe,[n.value?(a(),r("button",{key:0,onClick:w,class:"px-3 py-1 text-sm bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 hover:text-blue-300 transition-all duration-200 cursor-pointer hover-lift"},t(s.$t("common.copy")),1)):y("",!0),n.value?(a(),r("button",{key:1,onClick:F,class:"px-3 py-1 text-sm bg-green-500/20 text-green-400 rounded-lg hover:bg-green-500/30 hover:text-green-300 transition-all duration-200 cursor-pointer hover-lift"},t(s.$t("common.download")),1)):y("",!0)])]),!n.value&&!u.value?(a(),r("div",ne,[e("div",le,[o[4]||(o[4]=e("div",{class:"text-3xl mb-2"},"🔍",-1)),e("p",null,t(s.$t("tools.jsonMissingKeyFinder.noResults")),1)])])):y("",!0),u.value?(a(),r("div",ie,[e("div",ae,[o[5]||(o[5]=e("div",{class:"text-3xl mb-2"},"❌",-1)),e("p",re,t(s.$t("toast.error")),1),e("p",de,t(u.value),1)])])):y("",!0),n.value&&!u.value?(a(),r("div",ue,[e("div",ce,[e("h5",me,t(s.$t("common.statistics"))+":",1),e("div",pe,[e("p",null," • "+t(s.$t("tools.jsonMissingKeyFinder.totalObjects"))+": "+t(n.value.totalObjects),1),e("p",null," • "+t(s.$t("tools.jsonMissingKeyFinder.uniqueKeys"))+": "+t(n.value.allKeys.length),1),e("p",null," • "+t(s.$t("tools.jsonMissingKeyFinder.objectsWithMissing"))+": "+t(n.value.objectsWithMissingKeys),1)])]),n.value.missingKeysReport.length>0?(a(),r("div",ye,[e("h5",ge,t(s.$t("tools.jsonMissingKeyFinder.missingKeysReport"))+": ",1),e("div",fe,[(a(!0),r(A,null,N(n.value.missingKeysReport,(l,i)=>(a(),r("div",{key:i,class:"bg-amber-500/10 border border-amber-500/30 rounded-lg p-3"},[e("p",ve,t(s.$t("common.items"))+" "+t(i+1)+":",1),e("p",be,t(s.$t("common.found"))+": "+t(l.missingKeys.join(", ")),1)]))),128))])])):y("",!0)])):y("",!0)])])]),_:1},8,["title","description","features"]))}});export{Ke as default};
