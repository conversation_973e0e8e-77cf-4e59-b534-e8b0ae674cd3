const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/jszip.min-BZakjvyN.js","assets/_commonjsHelpers-DsqdWQfm.js","assets/_commonjs-dynamic-modules-TDtrdbi3.js"])))=>i.map(i=>d[i]);
import{d as re,u as ae,r as h,i as N,w as le,a1 as ie,a2 as ne,c,a as s,f as x,t as r,S as V,R as W,e as A,g as J,v as de,h as _,F as ce,k as me,_ as ue,o as m,a3 as pe,W as ge}from"./index-CkZTMFXG.js";import{u as ve}from"./useToast-virEbLJw.js";import{G as fe}from"./gif-Dup4naTh.js";import{l as B}from"./index-CNw5tbJV.js";import"./_commonjsHelpers-DsqdWQfm.js";import"./_commonjs-dynamic-modules-TDtrdbi3.js";const he={class:"min-h-screen bg-dark-950 text-slate-100"},be={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},xe={class:"text-center mb-12"},we={class:"text-4xl font-bold text-slate-100 mb-4"},_e={class:"text-xl text-slate-400 max-w-3xl mx-auto"},ye={class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},Ce={class:"space-y-4"},ke={class:"text-lg font-medium text-slate-100 mb-2"},$e={class:"text-slate-400"},ze={class:"text-sm text-slate-500 mt-2"},Le={class:"px-6 py-2 bg-primary-600 text-white rounded-xl hover:bg-primary-500 transition-all duration-200 cursor-pointer hover-lift"},Ue={class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},Ie={class:"text-lg font-semibold text-slate-100 mb-4 border-b border-slate-700/30 pb-2"},Se={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Re={class:"block text-sm font-medium text-slate-300 mb-2"},je={class:"flex justify-between text-xs text-slate-500 mt-1"},Be={class:"block text-sm font-medium text-slate-300 mb-2"},Fe={value:"original"},Pe={class:"block text-sm font-medium text-slate-300 mb-2"},Oe={key:0,class:"glass rounded-xl p-6 mb-8 border border-slate-700/50"},Ae={class:"flex justify-between items-center mb-6"},Me={class:"text-lg font-semibold text-slate-100"},Ee={class:"flex gap-3"},Ge=["disabled"],De={key:0,class:"flex items-center"},Te={key:1},Ne=["disabled"],Ve={key:0,class:"mb-6 p-4 bg-success-900/30 rounded-xl border border-success-700/50"},We={class:"grid grid-cols-1 md:grid-cols-3 gap-4 text-center"},Je={class:"text-2xl font-bold text-success-400"},qe={class:"text-sm text-success-300"},Ze={class:"text-2xl font-bold text-success-400"},He={class:"text-sm text-success-300"},Ke={class:"text-2xl font-bold text-success-400"},Qe={class:"text-sm text-success-300"},Xe={class:"space-y-4"},Ye={class:"flex items-center space-x-4"},es={class:"flex-shrink-0"},ss=["src","alt"],ts={class:"flex-1 min-w-0"},os={class:"text-sm font-medium text-slate-100 truncate"},rs={class:"text-sm text-slate-400"},as={class:"flex items-center space-x-4 mt-1"},ls={class:"text-sm text-slate-400"},is={key:0,class:"text-sm text-success-400"},ns={key:1,class:"text-sm font-medium text-success-500"},ds={class:"flex-shrink-0 flex items-center space-x-3"},cs={class:"text-center"},ms={class:"text-xs text-slate-400"},us={key:0,class:"w-16"},ps={class:"bg-slate-700 rounded-full h-2"},gs={class:"flex space-x-2"},vs=["onClick","disabled"],fs=["onClick"],hs=["onClick"],bs=["onClick"],xs={key:1,class:"fixed inset-0 z-50 flex items-center justify-center bg-black/80 backdrop-blur-sm"},ws={class:"relative glass rounded-xl shadow-dark-xl w-full max-w-4xl mx-4 border border-slate-700/50"},_s={class:"flex items-center justify-between p-4 border-b border-slate-700/30 rounded-t-xl"},ys={class:"text-xl font-semibold text-slate-100"},Cs={class:"p-6"},ks={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},$s={class:"text-lg font-medium text-slate-100 mb-2"},zs={class:"border border-slate-700/50 rounded-xl p-2"},Ls=["src","alt"],Us={class:"mt-2 text-sm text-slate-400"},Is={key:0},Ss={class:"text-lg font-medium text-slate-100 mb-2"},Rs={class:"border border-slate-700/50 rounded-xl p-2"},js=["src","alt"],Bs={class:"mt-2 text-sm text-slate-400"},Fs={class:"flex items-center justify-end p-6 border-t border-slate-700/30 rounded-b-xl"},Ps={class:"grid grid-cols-1 md:grid-cols-3 gap-8 mt-12"},Os={class:"glass p-6 rounded-xl border border-slate-700/50"},As={class:"text-lg font-semibold text-slate-100 mb-3"},Ms={class:"text-slate-400 text-sm"},Es={class:"glass p-6 rounded-xl border border-slate-700/50"},Gs={class:"text-lg font-semibold text-slate-100 mb-3"},Ds={class:"text-slate-400 text-sm"},Ts={class:"glass p-6 rounded-xl border border-slate-700/50"},Ns={class:"text-lg font-semibold text-slate-100 mb-3"},Vs={class:"text-slate-400 text-sm"},Ws=re({__name:"ImageCompressor",setup(Js){const{t:y}=ae(),{success:F,error:P}=ve(),C=h(),d=h([]),S=h(!1),z=h(!1),L=h(80),g=h("original"),k=h(null),R=h(!1),u=h(null),U=h(""),q=N(()=>d.value.some(t=>t.status==="completed")),j=N(()=>{const t=d.value.filter(l=>l.status==="completed"),e=t.reduce((l,i)=>l+i.originalSize,0),o=t.reduce((l,i)=>l+(i.compressedSize||0),0),a=e>0?Math.round((e-o)/e*100):0;return{totalOriginalSize:e,totalCompressedSize:o,savedPercentage:a}});le(u,t=>{t&&t.compressedBlob?U.value=URL.createObjectURL(t.compressedBlob):U.value=""});function Z(){C.value?.click()}function H(t){const e=t.target.files;e&&O(Array.from(e)),C.value&&(C.value.value="")}function K(t){t.preventDefault(),S.value=!1;const e=t.dataTransfer?.files;e&&O(Array.from(e))}async function O(t){const e=t.filter(o=>o.type.startsWith("image/"));if(e.length===0){P(y("tools.imageCompressor.errors.noValidImages"));return}for(const o of e){const a=d.value.findIndex(l=>l.name===o.name&&l.originalSize===o.size);if(a!==-1)try{const l=await M(o),i=await E(o),n=await G(o);d.value[a]={name:o.name,file:o,preview:i,originalSize:o.size,dimensions:l,status:"pending",progress:0,isAnimatedGif:n}}catch(l){console.error("Error processing file:",o.name,l)}else try{const l=await M(o),i=await E(o),n=await G(o),f={name:o.name,file:o,preview:i,originalSize:o.size,dimensions:l,status:"pending",progress:0,isAnimatedGif:n};d.value.push(f)}catch(l){console.error("Error processing file:",o.name,l)}}}function M(t){return new Promise((e,o)=>{const a=new Image;a.onload=()=>{e({width:a.width,height:a.height})},a.onerror=o,a.src=URL.createObjectURL(t)})}function E(t){return new Promise((e,o)=>{const a=new FileReader;a.onload=l=>e(l.target?.result),a.onerror=o,a.readAsDataURL(t)})}async function G(t){if(t.type!=="image/gif")return!1;try{const e=await t.arrayBuffer(),o=B.parseGIF(e);return B.decompressFrames(o,!0).length>1}catch(e){return console.error("Error checking if GIF is animated:",e),!1}}async function D(t){const e=d.value[t];if(e){e.status="compressing",e.progress=0;try{if(e.isAnimatedGif&&g.value==="original"){await Q(t);return}const o=document.createElement("canvas"),a=o.getContext("2d"),l=new Image;await new Promise((p,v)=>{l.onload=p,l.onerror=v,l.src=e.preview});let{width:i,height:n}=e.dimensions;k.value&&i>k.value&&(n=n*k.value/i,i=k.value),o.width=i,o.height=n,a?.drawImage(l,0,0,i,n);for(let p=20;p<=80;p+=20)e.progress=p,await new Promise(v=>setTimeout(v,100));let f=e.file.type;g.value!=="original"&&(f=`image/${g.value==="jpg"?"jpeg":g.value}`);const b=await new Promise(p=>{o.toBlob(v=>{p(v)},f,L.value/100)});e.progress=100,e.compressedBlob=b,e.compressedSize=b.size,e.savedPercentage=Math.round((e.originalSize-b.size)/e.originalSize*100),e.status="completed"}catch(o){console.error("Compression error:",o),e.status="error",P(y("tools.imageCompressor.errors.compressionFailed",{filename:e.name}))}}}async function Q(t){const e=d.value[t];if(!(!e||!e.isAnimatedGif))try{const o=await e.file.arrayBuffer(),a=B.parseGIF(o),l=B.decompressFrames(a,!0),i=document.createElement("canvas"),n=i.getContext("2d",{willReadFrequently:!0});if(!n)throw new Error("Unable to get canvas context");i.width=a.lsd.width,i.height=a.lsd.height;const f=Math.max(1,Math.min(30,Math.floor((100-L.value)/3))),b=new fe({workers:2,quality:f,width:i.width,height:i.height,workerScript:"/gif.worker.js"});for(let v=0;v<l.length;v++){const w=l[v];e.progress=Math.round(v/l.length*90);const I=new ImageData(new Uint8ClampedArray(w.patch),w.dims.width,w.dims.height);n.clearRect(0,0,i.width,i.height),n.putImageData(I,w.dims.left,w.dims.top),b.addFrame(n,{copy:!0,delay:w.delay})}const p=await new Promise((v,w)=>{b.on("finished",I=>{v(I)}),b.on("error",I=>{w(I)}),b.render()});e.progress=100,e.compressedBlob=p,e.compressedSize=p.size,e.savedPercentage=Math.round((e.originalSize-p.size)/e.originalSize*100),e.status="completed"}catch(o){console.error("Animated GIF compression error:",o),e.status="error",P(y("tools.imageCompressor.errors.compressionFailed",{filename:e.name}))}}async function X(){z.value=!0;const t=d.value.map((o,a)=>({img:o,index:a})),e=3;for(let o=0;o<t.length;o+=e){const a=t.slice(o,o+e);await Promise.all(a.map(({index:l})=>D(l)))}z.value=!1,F(y("tools.imageCompressor.success.compressionComplete"))}function Y(t){const e=d.value[t];if(!e.compressedBlob)return;const o=URL.createObjectURL(e.compressedBlob),a=document.createElement("a");a.href=o;const l=g.value==="original"?e.name.split(".").pop():g.value==="jpg"?"jpg":g.value,i=e.name.substring(0,e.name.lastIndexOf("."));a.download=`${i}_compressed.${l}`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(o)}async function ee(){const{default:t}=await ue(async()=>{const{default:n}=await import("./jszip.min-BZakjvyN.js").then(f=>f.j);return{default:n}},__vite__mapDeps([0,1,2])),e=new t,o=d.value.filter(n=>n.status==="completed"&&n.compressedBlob);for(const n of o)if(n.compressedBlob){const f=g.value==="original"?n.name.split(".").pop():g.value==="jpg"?"jpg":g.value,p=`${n.name.substring(0,n.name.lastIndexOf("."))}_compressed.${f}`;e.file(p,n.compressedBlob)}const a=await e.generateAsync({type:"blob"}),l=URL.createObjectURL(a),i=document.createElement("a");i.href=l,i.download=`compressed_images_${new Date().toISOString().split("T")[0]}.zip`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(l),F(y("tools.imageCompressor.success.downloadComplete"))}function se(t){u.value=d.value[t],R.value=!0}function $(t){if(t===0)return"0 B";const e=1024,o=["B","KB","MB","GB"],a=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,a)).toFixed(1))+" "+o[a]}function te(t){const e=d.value[t];URL.revokeObjectURL(e.preview),e.compressedBlob&&URL.revokeObjectURL(URL.createObjectURL(e.compressedBlob)),d.value.splice(t,1)}function oe(){d.value.forEach(t=>{URL.revokeObjectURL(t.preview),t.compressedBlob&&URL.revokeObjectURL(URL.createObjectURL(t.compressedBlob))}),d.value=[],C.value&&(C.value.value="")}function T(t){const e=t.clipboardData?.items;if(!e)return;const o=[];for(const a of e)if(a.type.startsWith("image/")){const l=a.getAsFile();l&&o.push(l)}o.length>0&&(O(o),F(y("tools.imageCompressor.success.pasteSuccess")))}return ie(()=>{document.addEventListener("paste",T),document.addEventListener("dragenter",t=>{t.preventDefault(),S.value=!0}),document.addEventListener("dragleave",t=>{t.relatedTarget||(S.value=!1)})}),ne(()=>{document.removeEventListener("paste",T),d.value.forEach(t=>{URL.revokeObjectURL(t.preview),t.compressedBlob&&URL.revokeObjectURL(URL.createObjectURL(t.compressedBlob))}),U.value&&URL.revokeObjectURL(U.value)}),(t,e)=>(m(),c("div",he,[s("div",be,[s("div",xe,[s("h1",we,r(t.$t("tools.imageCompressor.title")),1),s("p",_e,r(t.$t("tools.imageCompressor.description")),1)]),s("div",ye,[s("div",{onDrop:K,onDragover:e[0]||(e[0]=W(()=>{},["prevent"])),onDragenter:e[1]||(e[1]=W(()=>{},["prevent"])),onClick:Z,class:V(["border-2 border-dashed rounded-xl p-12 text-center cursor-pointer transition-all duration-200",S.value?"border-primary-500 bg-primary-500/10":"border-slate-600/50 hover:border-slate-500/70"])},[s("input",{ref_key:"fileInput",ref:C,type:"file",multiple:"",accept:"image/*",onChange:H,class:"hidden"},null,544),s("div",Ce,[e[7]||(e[7]=s("div",{class:"text-6xl text-slate-500"},"📸",-1)),s("div",null,[s("h3",ke,r(t.$t("tools.imageCompressor.uploadTitle")),1),s("p",$e,r(t.$t("tools.imageCompressor.uploadDescription")),1),s("p",ze,r(t.$t("tools.imageCompressor.supportedFormats"))+": JPG, PNG, WebP, GIF ",1)]),s("button",Le,r(t.$t("tools.imageCompressor.selectFiles")),1)])],34)]),s("div",Ue,[s("h3",Ie,r(t.$t("tools.imageCompressor.settings")),1),s("div",Se,[s("div",null,[s("label",Re,r(t.$t("tools.imageCompressor.quality"))+": "+r(L.value)+"% ",1),A(s("input",{type:"range",min:"10",max:"95","onUpdate:modelValue":e[2]||(e[2]=o=>L.value=o),class:"w-full h-2 bg-slate-700 rounded-xl appearance-none cursor-pointer slider"},null,512),[[J,L.value]]),s("div",je,[s("span",null,r(t.$t("tools.imageCompressor.smaller")),1),s("span",null,r(t.$t("tools.imageCompressor.larger")),1)])]),s("div",null,[s("label",Be,r(t.$t("tools.imageCompressor.outputFormat")),1),A(s("select",{"onUpdate:modelValue":e[3]||(e[3]=o=>g.value=o),class:"w-full px-3 py-2 bg-slate-800/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-slate-100 transition-all duration-200"},[s("option",Fe,r(t.$t("tools.imageCompressor.keepOriginal")),1),e[8]||(e[8]=s("option",{value:"jpg"},"JPG",-1)),e[9]||(e[9]=s("option",{value:"png"},"PNG",-1)),e[10]||(e[10]=s("option",{value:"webp"},"WebP",-1))],512),[[de,g.value]])]),s("div",null,[s("label",Pe,r(t.$t("tools.imageCompressor.maxWidth"))+" (px) ",1),A(s("input",{type:"number","onUpdate:modelValue":e[4]||(e[4]=o=>k.value=o),placeholder:"留空保持原尺寸",min:"100",max:"4096",class:"w-full px-3 py-2 bg-slate-800/50 border border-slate-600/50 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-slate-100 placeholder-slate-500 transition-all duration-200"},null,512),[[J,k.value]])])])]),d.value.length>0?(m(),c("div",Oe,[s("div",Ae,[s("h3",Me,r(t.$t("tools.imageCompressor.imageList"))+" ("+r(d.value.length)+") ",1),s("div",Ee,[s("button",{onClick:X,disabled:z.value,class:"px-4 py-2 bg-primary-600 text-white rounded-xl hover:bg-primary-500 transition-all duration-200 cursor-pointer hover-lift disabled:opacity-50 disabled:cursor-not-allowed"},[z.value?(m(),c("span",De,[e[11]||(e[11]=s("svg",{class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},[s("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),s("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1)),_(" "+r(t.$t("tools.imageCompressor.compressing")),1)])):(m(),c("span",Te,r(t.$t("tools.imageCompressor.compressAll")),1))],8,Ge),s("button",{onClick:ee,disabled:!q.value,class:"px-4 py-2 bg-success-600 text-white rounded-xl hover:bg-success-500 transition-all duration-200 cursor-pointer hover-lift disabled:opacity-50 disabled:cursor-not-allowed"},r(t.$t("tools.imageCompressor.downloadAll")),9,Ne),s("button",{onClick:oe,class:"px-4 py-2 bg-slate-700 text-slate-100 rounded-xl hover:bg-slate-600 transition-all duration-200 cursor-pointer hover-lift"},r(t.$t("common.clear")),1)])]),j.value.totalOriginalSize>0?(m(),c("div",Ve,[s("div",We,[s("div",null,[s("div",Je,r($(j.value.totalOriginalSize)),1),s("div",qe,r(t.$t("tools.imageCompressor.originalSize")),1)]),s("div",null,[s("div",Ze,r($(j.value.totalCompressedSize)),1),s("div",He,r(t.$t("tools.imageCompressor.compressedSize")),1)]),s("div",null,[s("div",Ke,r(j.value.savedPercentage)+"% ",1),s("div",Qe,r(t.$t("tools.imageCompressor.spaceSaved")),1)])])])):x("",!0),s("div",Xe,[(m(!0),c(ce,null,me(d.value,(o,a)=>(m(),c("div",{key:a,class:"border border-slate-700/50 rounded-xl p-4 hover:shadow-dark-lg transition-all duration-200"},[s("div",Ye,[s("div",es,[s("img",{src:o.preview,alt:o.name,class:"w-16 h-16 object-cover rounded-xl border border-slate-600/50"},null,8,ss)]),s("div",ts,[s("h4",os,r(o.name),1),s("p",rs,r(o.dimensions.width)+" × "+r(o.dimensions.height)+" px ",1),s("div",as,[s("span",ls,r(t.$t("tools.imageCompressor.original"))+": "+r($(o.originalSize)),1),o.compressedSize?(m(),c("span",is,r(t.$t("tools.imageCompressor.compressed"))+": "+r($(o.compressedSize)),1)):x("",!0),o.savedPercentage?(m(),c("span",ns," -"+r(o.savedPercentage)+"% ",1)):x("",!0)])]),s("div",ds,[s("div",cs,[s("div",{class:V(["w-3 h-3 rounded-full mx-auto mb-1",o.status==="pending"?"bg-slate-500":o.status==="compressing"?"bg-warning-500":o.status==="completed"?"bg-success-500":"bg-error-500"])},null,2),s("span",ms,r(t.$t(`tools.imageCompressor.status.${o.status}`)),1)]),o.status==="compressing"?(m(),c("div",us,[s("div",ps,[s("div",{class:"bg-primary-600 h-2 rounded-full transition-all duration-300",style:pe({width:`${o.progress}%`})},null,4)])])):x("",!0),s("div",gs,[s("button",{onClick:l=>D(a),disabled:z.value,class:"px-3 py-1 text-sm bg-primary-600 text-white rounded hover:bg-primary-500 transition-all duration-200 cursor-pointer hover-lift disabled:opacity-50"},r(t.$t("tools.imageCompressor.compress")),9,vs),o.status==="completed"&&o.compressedBlob?(m(),c("button",{key:0,onClick:l=>Y(a),class:"px-3 py-1 text-sm bg-success-600 text-white rounded hover:bg-success-500 transition-all duration-200 cursor-pointer hover-lift"},r(t.$t("common.download")),9,fs)):x("",!0),o.status==="completed"&&o.compressedBlob?(m(),c("button",{key:1,onClick:l=>se(a),class:"px-3 py-1 text-sm bg-purple-600 text-white rounded hover:bg-purple-500 transition-all duration-200 cursor-pointer hover-lift"},r(t.$t("common.preview")),9,hs)):x("",!0),s("button",{onClick:l=>te(a),class:"px-3 py-1 text-sm bg-slate-700 text-slate-100 rounded hover:bg-slate-600 transition-all duration-200 cursor-pointer hover-lift"},r(t.$t("tools.imageCompressor.remove")),9,bs)])])])]))),128))])])):x("",!0),R.value?(m(),c("div",xs,[s("div",ws,[s("div",_s,[s("h3",ys,r(t.$t("tools.imageCompressor.imagePreview")),1),s("button",{onClick:e[5]||(e[5]=o=>R.value=!1),class:"text-slate-400 bg-transparent hover:bg-slate-800/50 hover:text-slate-100 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center transition-all duration-200 cursor-pointer"},[...e[12]||(e[12]=[s("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)])])]),s("div",Cs,[s("div",ks,[s("div",null,[s("h4",$s,r(t.$t("tools.imageCompressor.originalImage")),1),s("div",zs,[s("img",{src:u.value?.preview,alt:u.value?.name,class:"w-full h-auto max-h-96 object-contain"},null,8,Ls)]),s("div",Us,[_(r(u.value?.name),1),e[13]||(e[13]=s("br",null,null,-1)),_(" "+r(t.$t("tools.imageCompressor.size"))+": "+r($(u.value?.originalSize||0)),1),e[14]||(e[14]=s("br",null,null,-1)),_(" "+r(t.$t("tools.imageCompressor.dimensions"))+": "+r(u.value?.dimensions.width)+" × "+r(u.value?.dimensions.height)+" px ",1)])]),u.value?.compressedBlob?(m(),c("div",Is,[s("h4",Ss,r(t.$t("tools.imageCompressor.compressedImage")),1),s("div",Rs,[s("img",{src:U.value,alt:u.value?.name,class:"w-full h-auto max-h-96 object-contain"},null,8,js)]),s("div",Bs,[_(r(u.value?.name),1),e[15]||(e[15]=s("br",null,null,-1)),_(" "+r(t.$t("tools.imageCompressor.size"))+": "+r($(u.value?.compressedSize||0)),1),e[16]||(e[16]=s("br",null,null,-1)),_(" "+r(t.$t("tools.imageCompressor.saved"))+": "+r(u.value?.savedPercentage)+"% ",1)])])):x("",!0)])]),s("div",Fs,[s("button",{onClick:e[6]||(e[6]=o=>R.value=!1),class:"text-slate-300 bg-slate-700 hover:bg-slate-600 focus:ring-4 focus:ring-slate-600 font-medium rounded-lg text-sm px-5 py-2.5 transition-all duration-200 cursor-pointer hover-lift"},r(t.$t("common.close")),1)])])])):x("",!0),s("div",Ps,[s("div",Os,[s("h3",As," 🚀 "+r(t.$t("tools.imageCompressor.features.efficient.title")),1),s("p",Ms,r(t.$t("tools.imageCompressor.features.efficient.description")),1)]),s("div",Es,[s("h3",Gs," 🔒 "+r(t.$t("tools.imageCompressor.features.secure.title")),1),s("p",Ds,r(t.$t("tools.imageCompressor.features.secure.description")),1)]),s("div",Ts,[s("h3",Ns," ⚡ "+r(t.$t("tools.imageCompressor.features.batch.title")),1),s("p",Vs,r(t.$t("tools.imageCompressor.features.batch.description")),1)])])])]))}}),Ys=ge(Ws,[["__scopeId","data-v-39cd5978"]]);export{Ys as default};
